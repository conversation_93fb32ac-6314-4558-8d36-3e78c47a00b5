<app-icons *ngIf="!isMobile"></app-icons>

<div class="common-toolbar" fxLayout="row" style="width: 100%;height: 50px;" *ngIf="!isMobile">
    <div class="heading-container">
        <app-heading [pagename]="pagename"></app-heading>
    </div>
    <div class="options-container">
        <app-toolbar-options></app-toolbar-options>
    </div>
</div>

<div class="migrate-container" *ngIf="!isMobile">
    <form [formGroup]="archiveForm" class="form">
        <div class="step1-container">
            <div class="row-container">
                <div class="input-container">
                    <div class="text-container">
                        Keyword
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="keyword" autocomplete="off"
                                (keyup)="getKeyword(archiveForm.value.keyword)">
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container">
                    <div class="text-container">
                        Complaint medium
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <mat-select formControlName="comp_medium">
                                <mat-option *ngFor="let medium of compMedium" [value]="medium.ID">
                                    {{medium.COMPLAINT_SOURCE_NAME}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container">
                    <div class="text-container">
                        Complaint status
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <mat-select formControlName="comp_status">
                                <mat-option *ngFor="let status of compStatus" [value]="status.ID">
                                    {{status.COMPLAINT_STATUS_NAME}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
            </div>

            <div class="divider-container" *ngIf="!showFields">
                <mat-divider></mat-divider>
                <button mat-flat-button class="collapse-btn" (click)="collapseForm()">
                    <img class="arrow" src="../../assets/images/chevrons-down.svg">
                </button>
            </div>

            <div *ngIf="showFields">
                <div class="row-container">
                    <div class="input-container">
                        <div class="text-container">
                            Resolution
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <mat-select formControlName="resolution">
                                    <mat-option *ngFor="let resolution of resolutionList" [value]="resolution.ID">
                                        {{resolution.RESOLUTION_STATUS_NAME}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="input-container"
                        *ngIf="(archiveForm.controls['resolution'].value == 1 || archiveForm.controls['resolution'].value == 3
                        || archiveForm.controls['resolution'].value == 5 || archiveForm.controls['resolution'].value == 6 || archiveForm.controls['resolution'].value == 8)">
                        <div class="text-container">
                            Compliance status
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <mat-select formControlName="compliance">
                                    <mat-option *ngFor="let compliance of complianceList" [value]="compliance.ID">
                                        {{compliance.COMPLIANCE_STATUS_NAME}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="input-container">
                        <div class="text-container">
                            Stage
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <mat-select formControlName="stage">
                                    <mat-option *ngFor="let stage of stageList" [value]="stage.ID">
                                        {{stage.STAGE_NAME}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="input-container"
                        *ngIf="!(archiveForm.controls['resolution'].value == 1 || archiveForm.controls['resolution'].value == 3
                    || archiveForm.controls['resolution'].value == 5 || archiveForm.controls['resolution'].value == 6 || archiveForm.controls['resolution'].value == 8)">
                        <div class="text-container">
                            Process
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <mat-select formControlName="process">
                                    <mat-option *ngFor="let process of processes" [value]="process.ID">
                                        {{process.PROCESS_NAME}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>
                </div>

                <div class="row-container"
                    *ngIf="(archiveForm.controls['resolution'].value == 1 || archiveForm.controls['resolution'].value == 3
                || archiveForm.controls['resolution'].value == 5 || archiveForm.controls['resolution'].value == 6 || archiveForm.controls['resolution'].value == 8)">
                    <div class="input-container">
                        <div class="text-container">
                            Process
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field1">
                                <mat-select formControlName="process">
                                    <mat-option *ngFor="let process of processes" [value]="process.ID">
                                        {{process.PROCESS_NAME}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>
                </div>

                <div class="btn-container" fxLayoutAlign="end">
                    <div class="next-container" fxLayoutGap="15px">
                        <button mat-flat-button class="cancel-btn" (click)="clearForm()">
                            <span class="bolder">Clear</span>
                        </button>
                        <button mat-flat-button class="next-btn" (click)="submitForm()">
                            <span class="bolder">Search</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="divider-container" *ngIf="showFields">
                <mat-divider style="margin-top: 25px;"></mat-divider>
                <button mat-flat-button class="collapse-btn" (click)="collapseForm()">
                    <img class="arrow" src="../../assets/images/chevrons-up.svg">
                </button>
            </div>
        </div>
    </form>
    <mat-progress-spinner color="primary" mode="indeterminate" diameter="70" *ngIf="loading"
        style="position: absolute; right: 45%; top: 50%;">
    </mat-progress-spinner>
    <div class="archived-complaints" *ngIf="archived_complaints.length != 0" infiniteScroll 
        [infiniteScrollDistance]="2" [infiniteScrollThrottle]="300" (scrolled)="onScrollDown()" [scrollWindow]="false">
        <div class="icon-container" *ngFor="let comp of archived_complaints; let i=index;">
            <div fxLayout="column" class="details-container" fxLayoutGap="10px" (click)="complaintDetails(comp.ID)">
                <div style="color: #4f5b52; font-size: 15px; font-weight: 600;">{{comp.CASE_ID}} <span
                        *ngIf="comp.OLD_CASE_ID != null && comp.OLD_CASE_ID != ''">({{comp.OLD_CASE_ID}})</span></div>
                <div style="color: #4f5b52; font-size: 15px; font-weight: 600;">{{comp.COMPANY_NAME}} -
                    {{comp.BRAND_NAME}} - {{comp.PRODUCT_NAME}}</div>
                <div style="color: #8f8f8f;"><span
                        *ngIf="comp.RESOLUTION_STATUS_NAME != null && comp.RESOLUTION_STATUS_NAME != ''">{{comp.RESOLUTION_STATUS_NAME}}</span><span
                        *ngIf="comp.RESOLUTION_STATUS_NAME == null || comp.RESOLUTION_STATUS_NAME == ''">-</span> |
                    {{comp.COMPLAINT_TYPE_NAME}} | {{comp.REGISTERED_DATE | date:'dd MMMM yyyy'}}</div>
                <div class="ellipsis" [innerHTML]="safeHTML(comp.COMPLAINT_DESCRIPTION)">{{comp.COMPLAINT_DESCRIPTION}}
                </div>
            </div>
            <div class="divider-container">
                <mat-divider style="margin: 15px 0px;"></mat-divider>
            </div>
        </div>
    </div>
    <div class="zero-complaints" *ngIf="zeroComplaints">
        No complaints found...
    </div>
</div>
<div *ngIf="isMobile">
    {{moveToDashboard()}}
</div>