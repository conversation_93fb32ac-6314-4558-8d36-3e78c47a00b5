import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MaterialModule } from "../material/material.module";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { ThirdPartyInboxRoutingModule } from './third-party-inbox-routing.module';
import { SharedModule } from "../shared/shared.module";
import { FlexLayoutModule } from '@angular/flex-layout';
import { NamsComponent } from './nams/nams.component';
import { NamsComplaintsComponent } from './nams-complaints/nams-complaints.component';
import { NamsDetailsComponent } from './nams-details/nams-details.component';
import { NamsReechComponent } from './nams-reech/nams-reech.component';
import { NamsReechFileUploadComponent } from './nams-reech-file-upload/nams-reech-file-upload.component';
import { ReechDeleteReasonComponent } from './reech-delete-reason/reech-delete-reason.component';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';

@NgModule({
  declarations: [
    NamsComponent,
    NamsComplaintsComponent,
    NamsDetailsComponent,
    NamsReechComponent,
    NamsReechFileUploadComponent,
    ReechDeleteReasonComponent
  ],
  imports: [
    CommonModule,
    ThirdPartyInboxRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    MaterialModule,
    SharedModule,
    FlexLayoutModule,
    InfiniteScrollModule
  ],
  entryComponents: [
    NamsDetailsComponent,
    NamsReechFileUploadComponent,
    ReechDeleteReasonComponent
  ],
  providers: [],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ThirdPartyInboxModule { }