<div>
    <p class="title">Reason of delete/reject ?</p>
</div>
<div>
    <mat-form-field appearance="outline" class="input-field_dept" style="font-style: normal; font-weight: normal;">
        <mat-select style="font-style: normal; font-weight: normal;" [formControl]="reason">
            <mat-option *ngFor="let reason of reasonDelete" [value]="reason.REASON">{{reason.REASON}}
            </mat-option>
        </mat-select>
    </mat-form-field>
</div>
<div class="toolbar2">
    <div fxLayoutAlign="end end">
        <div class="toolbar-btns">
            <div class="search-div">
                <button mat-stroked-button class="update-btn" (click)="onClose()">
                    <span class="bolder">Cancel</span> 
                </button>
            </div>
        </div>
        <div>
            <div class="update-div">
                <button mat-flat-button class="remove-btn" (click)="onSubmit()" [disabled]="reason.invalid">
                    <span class="bolder">{{message.button}}</span> 
                </button>
            </div>
        </div>
    </div>
</div>
