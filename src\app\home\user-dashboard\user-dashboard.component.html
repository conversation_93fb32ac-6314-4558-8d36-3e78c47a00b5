<app-icons *ngIf="!mobile"></app-icons>
<div class="common-toolbar" fxLayout="row" style="width: 100%;height: 50px;" *ngIf="!mobile">
    <div class="heading-container" fxLayout="row">
        <div style="width: 40px; height: 40px;">
            <img src="../assets/images/logo.png">
        </div>
        <div fxFlex="2%"></div>
        <div fxFlex="12%" fxLayoutAlign="start">
            <div fxFlex="4%"></div>
            <h3 matSubheader class="adm-head">{{pagename}}</h3>
        </div>
    </div>
    <div class="options-container">
        <app-toolbar-options></app-toolbar-options>
    </div>
</div>
<div class="common-toolbar" style="width: 100%;height: auto;" *ngIf="mobile && !userComp">
    <app-mobile-header></app-mobile-header>
</div>
<div class="header-container" fxLayout="row" *ngIf="mobile && userComp">
    <div class="headline-container" fxLayout="row" fxLayoutAlign="start center" style="color: #fff;font-size: 20px;">
        <img src="../assets/images/arrow.png" style="padding-right: 10px; padding-top: 3px;"
            (click)="closeCreateComplaint()" />
        Create Complaint
    </div>
    <span style="flex: 1 1 auto;"></span>
    <div fxLayout="row" fxLayoutAlign="end center">
        <img class="bell-button" src="../assets/images/bell-mobile.png" style="padding-right: 14px;padding-top: 2px;"
            (click)="viewNotifications()" *ngIf="roleId == 4 || roleId == 5 || roleId == 7 || roleId == 8" />
        <button [matMenuTriggerFor]="admin" mat-mini-fab class="flex profile-btn">{{roleName}}</button>
    </div>
    <mat-menu #admin="matMenu" class="admin-menu">
        <div class="admin-option-container">
            <button mat-menu-item class="option-btn" (click)="viewProfile(true)"
                *ngIf="roleId == 4 || roleId == 5 || roleId == 7 || roleId == 8">
                <span class="option-text">My profile</span>
            </button>
            <mat-divider class="option-divider" *ngIf="roleId == 4 || roleId == 5 || roleId == 7 || roleId == 8">
            </mat-divider>
            <button mat-menu-item class="option-btn" (click)='logout()'>
                <span class="option-text">Log out</span>
            </button>
        </div>
    </mat-menu>
</div>
<div class="intradashboard-container" fxLayout="row">
    <div class="manage">
        <div class="head-container">
            <div class="comp-head">
                <h3 matSubheader class="head-text">
                    <img src="/assets/images/Vector.png" /> Activities on complaints
                </h3>
            </div>
            <span class="head-spacer"></span>
        </div>
        <mat-divider style="margin-left: -7px; margin-top: 16px;"></mat-divider>
        <div>
            <form class="header-search">
                <!-- <input type="text" name="search" [matAutocomplete]="auto" [formControl]="stateCtrl" placeholder="Search by PO#" (keyup)="applyFilter($event.target.value)"> -->
                <!-- <mat-autocomplete #auto="matAutocomplete" (optionSelected)="applyFilter($event.option.value)"> -->
                <mat-form-field appearance="outline" class="header-search">
                    <mat-select placeholder="Case ID" [(value)]="selectedID"
                        (selectionChange)="selectActivities($event.value)">
                        <mat-option *ngFor="let comp of storeUserCaseId" [value]="comp.ID" style="padding-left: 30px;">
                            {{comp.CASE_ID}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
                <!-- </mat-autocomplete> -->
            </form>
        </div>
        <div class="dashboard-complaint-list">
            <mat-list-item *ngFor="let item of complaintsList">
                <div class="list-item-container" *ngFor="let complaint of item.updates">
                    <div class="item-head-container" fxLayout="column">
                        <div class="message" fxLayout="row">
                            <div class="dot" *ngIf="complaint.label == 'Status moved to' && complaint.value == 'New'"
                                style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #0088CB;">
                            </div>
                            <div class="dot" *ngIf="complaint.label == 'Status moved to' && complaint.value == 'In Progress'"
                                style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #F89E1B;">
                            </div>
                            <div class="dot" *ngIf="complaint.label == 'Status moved to' && complaint.value == 'On Hold'"
                                style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #ED2F45;">
                            </div>
                            <div class="dot" *ngIf="complaint.label == 'Status moved to' && complaint.value == 'Out of remit/Outside ASCI Purview'"
                                style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #000000;">
                            </div>
                            <div class="dot" *ngIf="complaint.label == 'Status moved to' && complaint.value == 'Non-Issue'"
                                style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #000000;">
                            </div>
                            <div class="dot" *ngIf="complaint.label == 'Status moved to' && complaint.value == 'Closed'"
                                style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #000000;">
                            </div>
                            <div class="dot" *ngIf="complaint.label == 'Status moved to' && complaint.value == 'Resolution'"
                                style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #04A585;">
                            </div>
                            <div class="dot" *ngIf="complaint.label == 'Status moved to' && complaint.value == 'Sub-Judice'"
                                style="min-width: 12px; margin-top: 4px; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #000000;">
                            </div>
                            <div class="dot" *ngIf="complaint.label != 'Status moved to'"
                                style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid darkgray;">
                            </div>
                            <div fxLayout="row">
                                {{complaint.label}} {{complaint.value}}
                            </div>
                        </div>
                        <div fxLayout="row" fxLayoutGap="20px" style="margin-top: 2%; margin-left: 5%;">
                            <div>
                                <p style="color: #92A2B1;"> {{item.date}} </p>
                            </div>
                            <!-- <div>
                                <p style="color: #92A2B1;">
                                    <i class="fa fa-circle" fxLayoutGap="5px"
                                        style="font-size: 8px; position: relative; margin-top: -1%;"
                                        aria-hidden="true"></i>
                                    By
                                    {{complaint.solved_by_person}}
                                </p>
                            </div> -->
                        </div>
                    </div>
                </div>
                <mat-divider></mat-divider>
            </mat-list-item>
        </div>
    </div>

    <div class="dashboard-admin">
        <mat-toolbar *ngIf="!newComplaint" style="width: 100%; height: auto;" class="toolbar1">
            <div fxLayout="column" fxFlex="30%" style="margin: 10px 0px">
                <span class="dashboard-admin-heading">
                    Welcome {{user}}!<br>
                </span>
                <span class="dasboard-subheading">
                    Here you can track your complaint(s) status
                </span>
            </div>
            <div fxFlex="55%"></div>
            <div class="create-button" fxflex="15%" fxLayoutAlign="end" *ngIf="userComplainant">
                <button mat-flat-button class="add-button" (click)="createUserComplaint()">
                    <span class="bolder">Create new complaint</span> 
                </button>
            </div>
            <!-- <div class="create-button" fxflex="15%" fxLayoutAlign="end" *ngIf="userAdvertiser && userData.roleId != 5">
                <button mat-flat-button class="add-button" (click)="createAdvertiserComplaint()">
                    Create new complaint
                </button>
            </div> -->
            <div class="create-button" fxflex="15%" fxLayoutAlign="end" *ngIf="userAdvertiser && userData.roleId == 5">
                <button mat-flat-button class="add-button" (click)="openDialog()">
                    <span class="bolder">Create new complaint</span>
                </button>
            </div>
        </mat-toolbar>
        <mat-toolbar *ngIf="newComplaint && !mobile" class="toolbar1">
            <button style="border: none; background-color:#F8F9F9; margin-right: 10px;" (click)="back()"><img
                src="../../assets/images/Back.png"></button>
                Create new complaint
            <div fxFlex="54%"></div>
            <button mat-flat-button class="delete-button" *ngIf="advertiserComp && userData.roleId == 5 && !editable" (click)="deleteComplaint()">
                <span class="bolder">Delete</span>
            </button>
            <button mat-flat-button class="save-button" *ngIf="advertiserComp && userData.roleId == 5 && !editable" (click)="saveDraft()">
                <span class="bolder">Save for later</span>
            </button>
        </mat-toolbar>
        <div class="note" *ngIf="(userTypeId == 2 || userTypeId == 4) && mobile">
            <span style="color: #ED2F45;">Note:</span>
            <p class="note-mob">For creating a complaint, we request you to move to a larger device for seamlessly accessing all functionalities of the system</p>
        </div>
        <div class="create-button-mobile" *ngIf="userTypeId != 2 && userTypeId != 4 && mobile && !hideCreateButton">
            <button mat-flat-button  class="create-button-mobile-blue" (click)="createUserComplaint()">
                <span class="bolder">Create new complaint</span>
            </button>
        </div>
        <mat-divider class="divider-mob"></mat-divider>
        <div class="box" [hidden]="newComplaint || userAdvertiser">
            <div fxLayout="column">
                <div fxLayout="row" class="head-row" *ngIf="!complaintdetails && !mobile">
                    <h3 class="comp-heading" *ngIf="!mobile" style="width:-webkit-fill-available;">Your complaints</h3>
                    <div fxFlex="70%"></div>
                    <mat-form-field class="example-full-width" appearance="outline">
                        <mat-select [formControl]="text_year" placeholder="{{initialValue}}" (selectionChange)="selectByYear($event)">
                            <mat-option *ngFor="let year of years" [value]="year.value" style="padding-left: 7%;">
                                {{year.viewValue}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div  fxLayout="row" fxLayoutAlign="end end" *ngIf="mobile" style="margin-right: 2.5vh;">
                    <mat-form-field class="example-full-width" appearance="outline">
                        <mat-select [formControl]="text_year" placeholder="{{initialValue}}" (selectionChange)="selectByYear($event)">
                            <mat-option *ngFor="let year of years" [value]="year.value" style="padding-left: 7%;">
                                {{year.viewValue}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div *ngIf="!mobile">
                    <div fxLayout="row" class="card-row" *ngFor="let item of userComplaintStats">
                        <mat-card class="card" style="border-bottom: 4px solid #0088CB;">
                            <div fxLayout="row" fxLayoutAlign="center center" >
                                <img src="../../assets/images/New.png" style="position:absolute;left:27px;">
                                <div fxLayout="column" style="margin-right: 16px;">
                                    <div class="digit"><b>{{item.TOTAL_COMPLAINTS}}</b></div>
                                    <div class="category">TOTAL</div>
                                </div>
                            </div>
                        </mat-card>
                        <mat-card class="card" style="border-bottom: 4px solid #F89E1B;">
                            <div fxLayout="row" fxLayoutAlign="center center">
                                <img src="../../assets/images/process.png" style="position:absolute;left:27px;">
                                <div fxLayout="column" style="margin-left: 22px;">
                                    <div class="digit"><b>{{item.IN_PROGRESS}}</b></div>
                                    <div class="category1">IN PROGRESS</div>
                                </div>
                            </div>
                        </mat-card>
                        <mat-card class="card" style="border-bottom: 4px solid #ED2F45;">
                            <div fxLayout="row" fxLayoutAlign="center center">
                                <img src="../../assets/images/hold.png" style="position:absolute;left:27px;">
                                <div fxLayout="column" style="margin-right: 19px;">
                                    <div class="digit"><b>{{item.ON_HOLD}}</b></div>
                                    <div class="category">ON HOLD</div>
                                </div>
                            </div>
                        </mat-card>
                        <mat-card class="card" style="border-bottom: 4px solid #04A585;">
                            <div fxLayout="row" fxLayoutAlign="center center">
                                <img src="../../assets/images/res.png" style="position:absolute;left:27px;">
                                <div fxLayout="column" style="margin-right: 3%;">
                                    <div class="digit"><b>{{item.RESOLVED}}</b></div>
                                    <div class="category">RESOLVED</div>
                                </div>
                            </div>
                        </mat-card>
                    </div>
                </div>
                <div *ngIf="mobile">
                    <div class="card-row1" *ngFor="let item of userComplaintStats">
                        <mat-card class="card" *ngIf="mobile" style="border-bottom: 4px solid #0088CB;">
                            <div fxLayout="row" fxLayoutAlign="center center">
                                <div fxLayout="column" fxLayoutGap="6px">
                                <div class="digit" style="padding-top: 5px;"><b>{{item.TOTAL_COMPLAINTS}}</b></div>
                                <div class="category2" style="margin-top:-8px">TOTAL</div>
                                </div>
                                <img src="../../assets/images/New.png" style="margin-left: auto;width: 25px;position: relative;top: -2px;">
                            </div>    
                        </mat-card>
                        <mat-card class="card" *ngIf="mobile" style="border-bottom: 4px solid #F89E1B;">
                            <div fxLayout="row" fxLayoutAlign="center center">
                                <div fxLayout="column" fxLayoutGap="5px">
                                <div class="digit" style="padding-top: 5px;"><b>{{item.IN_PROGRESS}}</b></div>
                                <div class="category2" style="margin-top:-8px;">IN PROGRESS</div>
                                </div>
                                <img src="../../assets/images/process.png" style="margin-left: auto;width: 25px;position: relative;top: -2px;">
                            </div>            
                        </mat-card>
                        <mat-card class="card" *ngIf="mobile" style="border-bottom: 4px solid #ED2F45;">
                            <div fxLayout="row" fxLayoutAlign="center center">
                                <div fxLayout="column" fxLayoutGap="5px">
                                <div class="digit" style="padding-top: 5px;"><b>{{item.ON_HOLD}}</b></div>
                                <div class="category2" style="margin-top:-16px">ON HOLD</div>
                                </div>
                                <img src="../../assets/images/hold.png" style="margin-left: auto;width: 25px;position: relative;top: -2px;">
                            </div>                   
                        </mat-card>
                        <mat-card class="card" *ngIf="mobile" style="border-bottom: 4px solid #04A585;">
                            <div fxLayout="row" fxLayoutAlign="center center">
                                <div fxLayout="column" fxLayoutGap="5px">
                                <div class="digit" style="padding-top: 5px;"><b>{{item.RESOLVED}}</b></div>
                                <div class="category2">RESOLVED</div>
                                </div>
                                <img src="../../assets/images/res.png" style="margin-left: auto;width: 25px;position: relative;top: -2px;">
                            </div>                     
                        </mat-card>
                    </div>
                </div>
                <div class="complaint-table" *ngIf="!complaintdetails && !mobile && (userData.roleId == 4 || userData.roleId == 8)">
                    <table mat-table [dataSource]="dataSource1" style="width: 100%;">
                        <ng-container matColumnDef="caseID">
                            <th mat-header-cell *matHeaderCellDef style="width: 20%;">Case ID</th>
                            <td mat-cell *matCellDef="let element" style="width: 20%;">
                                <span *ngIf="element.CASE_ID != null && element.CASE_ID != ''">{{element.CASE_ID}}</span>
                                <span *ngIf="element.CASE_ID == null || element.CASE_ID == ''">Not approved</span>
                            </td>
                        </ng-container>
                        <ng-container matColumnDef="brandname">
                            <th mat-header-cell *matHeaderCellDef style="width: 20%;">Brand Name</th>
                            <td mat-cell *matCellDef="let element" style="width: 20%;">
                                {{element.BRAND_NAME}} </td>
                        </ng-container>
                        <ng-container matColumnDef="case">
                            <th mat-header-cell *matHeaderCellDef style="width: 25%;">Case
                            </th>
                            <td mat-cell *matCellDef="let element" style="width: 25%;"><p class="ellipsis" [innerHTML]="safeHTML(element.COMPLAINT_DESCRIPTION)">
                                {{element.COMPLAINT_DESCRIPTION}} </p></td>
                        </ng-container>
                        <ng-container matColumnDef="status">
                            <th mat-header-cell *matHeaderCellDef style="width: 15%;">Status
                            </th>
                            <td mat-cell *matCellDef="let element" style="width: 15%;"
                            [ngClass]="{'prog' :'In Progress' == element.COMPLAINT_STATUS_NAME , 'new' :'New' == element.COMPLAINT_STATUS_NAME , 'hold': 'On Hold' == element.COMPLAINT_STATUS_NAME , 'resolve' : 'Resolution' == element.COMPLAINT_STATUS_NAME, 'close' : 'Closed' == element.COMPLAINT_STATUS_NAME, 'Out-of-remit' : 'Out of remit/Outside ASCI Purview' == element.COMPLAINT_STATUS_NAME || 'Sub-Judice' == element.COMPLAINT_STATUS_NAME, 'invalid' : 'Non-Issue' == element.COMPLAINT_STATUS_NAME}">
                                {{element.COMPLAINT_STATUS_NAME}} </td>
                        </ng-container>
                        <ng-container matColumnDef="duedate">
                            <th mat-header-cell *matHeaderCellDef style="width: 20%;">Due date
                            </th>
                            <td mat-cell *matCellDef="let element" style="width: 20%;">
                                {{element.DUE_DATE | date:'dd/MM/yyyy'}} </td>
                        </ng-container>
                        <tr mat-header-row *matHeaderRowDef="displayColumns; sticky: true"></tr>
                        <tr mat-row *matRowDef="let row; columns: displayColumns;" (click)="selectComplaint(row)">
                        </tr>
                    </table>
                    <!-- <div class="text-message" *ngIf="isEmpty">No data found</div> -->
                </div>
                <mat-accordion class="tables-div" [hidden]="complaintdetails || mobile || userData.roleId == 4 || userData.roleId == 8" multi>
                    <mat-expansion-panel style="margin: 0% 2% 0% 2%; padding-right: 2%; background-color: rgb(248, 246, 246);" *ngIf="!mobile">
                        <mat-expansion-panel-header class="panel-header" style="background-color: rgb(248, 246, 246);">
                            <mat-panel-title class="table-heading bolder">
                                System Complaints
                            </mat-panel-title>
                        </mat-expansion-panel-header>
                        <div class="complaint-table1" *ngIf="!complaintdetails && !mobile">
                            <table mat-table [dataSource]="dataSource1" style="width: 100%;">
                                <ng-container matColumnDef="caseID">
                                    <th mat-header-cell *matHeaderCellDef style="width: 20%;">Case ID</th>
                                    <td mat-cell *matCellDef="let element" style="width: 20%;">
                                        <span *ngIf="element.CASE_ID != null && element.CASE_ID != ''">{{element.CASE_ID}}</span>
                                        <span *ngIf="element.CASE_ID == null || element.CASE_ID == ''">Not approved</span>
                                    </td>
                                </ng-container>
                                <ng-container matColumnDef="brandname">
                                    <th mat-header-cell *matHeaderCellDef style="width: 20%;">Brand Name</th>
                                    <td mat-cell *matCellDef="let element" style="width: 20%;">
                                        {{element.BRAND_NAME}} </td>
                                </ng-container>
                                <ng-container matColumnDef="case">
                                    <th mat-header-cell *matHeaderCellDef style="width: 25%;">Case
                                    </th>
                                    <td mat-cell *matCellDef="let element" style="width: 25%;">
                                        <p class="ellipsis" [innerHTML]="safeHTML(element.COMPLAINT_DESCRIPTION)">
                                            {{element.COMPLAINT_DESCRIPTION}} </p>
                                    </td>
                                </ng-container>
                                <ng-container matColumnDef="status">
                                    <th mat-header-cell *matHeaderCellDef style="width: 15%;">Status
                                    </th>
                                    <td mat-cell *matCellDef="let element" style="width: 15%;"
                                        [ngClass]="{'prog' :'In Progress' == element.COMPLAINT_STATUS_NAME , 'new' :'New' == element.COMPLAINT_STATUS_NAME , 'hold': 'On Hold' == element.COMPLAINT_STATUS_NAME , 'resolve' : 'Resolution' == element.COMPLAINT_STATUS_NAME, 'close' : 'Closed' == element.COMPLAINT_STATUS_NAME, 'Out-of-remit' : 'Out of remit/Outside ASCI Purview' == element.COMPLAINT_STATUS_NAME || 'Sub-Judice' == element.COMPLAINT_STATUS_NAME, 'invalid' : 'Non-Issue' == element.COMPLAINT_STATUS_NAME}">
                                        {{element.COMPLAINT_STATUS_NAME}} </td>
                                </ng-container>
                                <ng-container matColumnDef="duedate">
                                    <th mat-header-cell *matHeaderCellDef style="width: 20%;">Due date
                                    </th>
                                    <td mat-cell *matCellDef="let element" style="width: 20%;">
                                        {{element.DUE_DATE | date:'dd/MM/yyyy'}} </td>
                                </ng-container>
                                <tr mat-header-row *matHeaderRowDef="displayColumns; sticky: true"></tr>
                                <tr mat-row *matRowDef="let row; columns: displayColumns;" (click)="selectComplaint(row)">
                                </tr>
                            </table>
                            <!-- <div class="text-message" *ngIf="isEmpty">No data found</div> -->
                        </div>
                        <mat-paginator [pageSize]="5" [pageIndex]="0" [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>
                    </mat-expansion-panel>
                    <mat-expansion-panel style="margin: 0% 2% 2% 2%; padding-right: 2%; background-color: rgb(248, 246, 246);" *ngIf="!mobile">
                        <mat-expansion-panel-header class="panel-header" style="background-color: rgb(248, 246, 246);">
                            <mat-panel-title class="table-heading bolder">
                                Chatbot Complaints
                            </mat-panel-title>
                        </mat-expansion-panel-header>
                        <div class="complaint-table1" *ngIf="!complaintdetails && !mobile">
                            <table mat-table [dataSource]="dataSource" style="width: 100%;">
                                <ng-container matColumnDef="caseID">
                                    <th mat-header-cell *matHeaderCellDef style="width: 20%;">Case ID</th>
                                    <td mat-cell *matCellDef="let element" style="width: 20%;">
                                        <span *ngIf="element.CASE_ID != null && element.CASE_ID != ''">{{element.CASE_ID}}</span>
                                        <span *ngIf="element.CASE_ID == null || element.CASE_ID == ''">Not approved</span>
                                    </td>
                                </ng-container>
                                <ng-container matColumnDef="brandname">
                                    <th mat-header-cell *matHeaderCellDef style="width: 20%;">Brand Name</th>
                                    <td mat-cell *matCellDef="let element" style="width: 20%;">
                                        {{element.BRAND_NAME}} </td>
                                </ng-container>
                                <ng-container matColumnDef="case">
                                    <th mat-header-cell *matHeaderCellDef style="width: 25%;">Case
                                    </th>
                                    <td mat-cell *matCellDef="let element" style="width: 25%;">
                                        <p class="ellipsis" [innerHTML]="safeHTML(element.COMPLAINT_DESCRIPTION)">
                                            {{element.COMPLAINT_DESCRIPTION}} </p>
                                    </td>
                                </ng-container>
                                <ng-container matColumnDef="status">
                                    <th mat-header-cell *matHeaderCellDef style="width: 15%;">Status
                                    </th>
                                    <td mat-cell *matCellDef="let element" style="width: 15%;"
                                        [ngClass]="{'prog' :'In Progress' == element.COMPLAINT_STATUS_NAME , 'new' :'New' == element.COMPLAINT_STATUS_NAME , 'hold': 'On Hold' == element.COMPLAINT_STATUS_NAME , 'resolve' : 'Resolution' == element.COMPLAINT_STATUS_NAME, 'close' : 'Closed' == element.COMPLAINT_STATUS_NAME, 'Out-of-remit' : 'Out of remit/Outside ASCI Purview' == element.COMPLAINT_STATUS_NAME || 'Sub-Judice' == element.COMPLAINT_STATUS_NAME, 'invalid' : 'Non-Issue' == element.COMPLAINT_STATUS_NAME}">
                                        {{element.COMPLAINT_STATUS_NAME}} </td>
                                </ng-container>
                                <ng-container matColumnDef="duedate">
                                    <th mat-header-cell *matHeaderCellDef style="width: 20%;">Due date
                                    </th>
                                    <td mat-cell *matCellDef="let element" style="width: 20%;">
                                        {{element.DUE_DATE | date:'dd/MM/yyyy'}} </td>
                                </ng-container>
                                <tr mat-header-row *matHeaderRowDef="displayColumns; sticky: true"></tr>
                                <tr mat-row *matRowDef="let row; columns: displayColumns;" (click)="selectChatbotComplaint(row)">
                                </tr>
                            </table>
                            <!-- <div class="text-message" *ngIf="isEmpty">No data found</div> -->
                        </div>
                        <mat-paginator [pageSize]="5" [pageIndex]="0" [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>
                    </mat-expansion-panel>
                </mat-accordion>
                <div *ngIf="mobile" [hidden]="complaintdetails">
                    <h3 matSubheader *ngIf="!isEmpty" style="margin-left: 20px;font-weight: 500;">Outbound Complaints</h3>
                    <div  class="case-detail-box" fxLayout="column" *ngFor="let data of dataSource1.data">
                        <!-- <div *ngFor ="let data of element"> -->
                        <div fxLayout="row" style="padding: 11px; cursor: pointer;" (click)="selectComplaint(data)">
                            <div class="detail-attribute">Case ID :</div>
                            <div class="detail-values" *ngIf="data.CASE_ID != null && data.CASE_ID != ''">{{data.CASE_ID}}</div>
                            <div class="detail-values" *ngIf="data.CASE_ID == null || data.CASE_ID == ''">Not approved</div>
                        </div>
                        <mat-divider></mat-divider>
                        <div fxLayout="row" style="padding: 11px;">
                            <div class="detail-attribute">Case :</div>
                            <div class="detail-values" [innerHTML]="safeHTML(data.COMPLAINT_DESCRIPTION)">{{data.COMPLAINT_DESCRIPTION}}</div>
                        </div>
                        <mat-divider></mat-divider>
                        <div fxLayout="row" style="padding: 11px;">
                            <div class="detail-attribute">Brand Name :</div>
                            <div class="detail-values">{{data.BRAND_NAME}}</div>
                        </div>
                        <mat-divider></mat-divider>
                        <div fxLayout="row" style="padding: 11px;">
                            <div class="detail-attribute">Status :</div>
                            <div class="detail-values" [ngClass]="{'prog' :'In Progress' == data.COMPLAINT_STATUS_NAME , 'new' :'New' == data.COMPLAINT_STATUS_NAME , 'hold': 'On Hold' == data.COMPLAINT_STATUS_NAME , 'resolve' : 'Resolution' == data.COMPLAINT_STATUS_NAME, 'close' : 'Closed' == data.COMPLAINT_STATUS_NAME, 'Out-of-remit' : 'Out of remit/Outside ASCI Purview' == data.COMPLAINT_STATUS_NAME || 'Sub-Judice' == data.COMPLAINT_STATUS_NAME, 'invalid' : 'Non-Issue' == data.COMPLAINT_STATUS_NAME}">{{data.COMPLAINT_STATUS_NAME}}</div>
                        </div>
                        <mat-divider></mat-divider>
                        <div fxLayout="row" style="padding: 11px;">
                            <div class="detail-attribute">Due Date :</div>
                            <div class="detail-values">{{data.DUE_DATE | date:'dd/MM/yyyy'}}</div>
                        </div>
                    </div>
                </div>
                <mat-paginator [hidden]="complaintdetails || userData.roleId == 7" [pageSize]="5" [pageIndex]="0" [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons style="margin: 0% 2% 0% 2%;"></mat-paginator>
                <div *ngIf="complaintdetails && !mobile">
                    <mat-card fxLayout="column" class="comp_details">
                        <div class="details-header" fxLayout="row">
                            <div fxFlex="35%">
                                <button style="border: none; background-color:#F8F9F9; margin-right: 10px;"
                                    (click)="closeDetails()"><img src="../../assets/images/arrow-mobile.png"></button>
                                Case ID : 
                                <span *ngIf="complaintDetails.CASE_ID != null && complaintDetails.CASE_ID != ''">{{complaintDetails.CASE_ID}}</span>
                                <span *ngIf="complaintDetails.CASE_ID == null || complaintDetails.CASE_ID == ''">Not approved</span>
                            </div>
                            <div fxFlex="52%"></div>
                            <!-- <div fxFlex="37%"></div> -->
                            <div class="resolution-container" *ngIf="complaintDetails.REGISTERED == 1">
                                <button mat-icon-button class="float-btn" (click)="openRecommendationorResolutionDialog('resolution')" style="border:1px solid #04A585;">
                                  <img src="../../assets/images/resolution.png" style="margin-bottom: 5px;">
                                </button>
                            </div>
                            <div fxFlex="2%"></div>
                            <div class="recommendation-container" *ngIf="complaintDetails.REGISTERED == 1">
                                <button mat-icon-button class="float-btn" (click)="openRecommendationorResolutionDialog('recommendation')" style="border:1px solid #F89E1B;">
                                  <img src="../../assets/images/recommendation.png" style="margin-bottom: 5px;">
                                </button>
                            </div>
                            <!-- <div fxFlex="2%"></div>
                            <div class="conversation-btn" *ngIf="complaintDetails.REGISTERED == 1">
                                <button mat-flat-button class="msg-button" (click)="createMessage()">
                                    <span class="bolder">
                                        <img style="padding-bottom: 3px;" src="../../../assets/images/msg-white.png"> Conversation
                                    </span> 
                                </button>
                            </div> -->
                        </div>
                        <div>
                            <mat-divider></mat-divider>
                        </div>
                        <div>
                            <div fxLayout="row">
                                <div fxLayout="column" style="width: 70%;" class="details-container">
                                    <div fxLayout="row">
                                        <div class="detail-attribute">Company name</div>
                                        <div class="detail-values">{{complaintDetails?.COMPANY_ID == 0 ? complaintDetails?.SUGGESTED_COMPANY_NAME : complaintDetails?.COMPANY_NAME}}</div>
                                    </div>
                                    <div fxLayout="row" style="padding-top: 3%;">
                                        <div class="detail-attribute">Brand name</div>
                                        <div class="detail-values">{{complaintDetails.BRAND_NAME}}</div>
                                    </div>
                                    <div fxLayout="row" style="padding-top: 3%;height: min-intrinsic;">
                                        <div class="detail-attribute">Complaint description</div>
                                        <div class="detail-values" [innerHTML]="safeHTML(complaintDetails.COMPLAINT_DESCRIPTION)">{{complaintDetails.COMPLAINT_DESCRIPTION}}</div>
                                    </div>
                                    <div fxLayout="row" style="padding-top: 3%;">
                                        <div class="detail-attribute">Status</div>
                                        <div class="detail-values" [ngClass]="{'prog' :'In Progress' == complaintDetails.COMPLAINT_STATUS_NAME , 'new' :'New' == complaintDetails.COMPLAINT_STATUS_NAME , 'hold': 'On Hold' == complaintDetails.COMPLAINT_STATUS_NAME , 'resolve' : 'Resolution' == complaintDetails.COMPLAINT_STATUS_NAME, 'close' : 'Closed' == complaintDetails.COMPLAINT_STATUS_NAME, 'Out-of-remit' : 'Out of remit/Outside ASCI Purview' == complaintDetails.COMPLAINT_STATUS_NAME || 'Sub-Judice' == complaintDetails.COMPLAINT_STATUS_NAME, 'invalid' : 'Non-Issue' == complaintDetails.COMPLAINT_STATUS_NAME}">
                                            {{complaintDetails.COMPLAINT_STATUS_NAME}}</div>
                                    </div>
                                    <div fxLayout="row" style="padding-top: 3%;">
                                        <div class="detail-attribute">Due date</div>
                                        <div class="detail-values">{{complaintDetails.DUE_DATE | date:'dd/MM/yyyy'}}</div>
                                    </div>
                                    <div fxLayout="row" style="padding-top: 3%;">
                                        <div class="detail-attribute">Classification</div>
                                        <div class="detail-values">{{complaintDetails.CLASSIFICATION_NAME}}</div>
                                    </div>
                                    <div fxLayout="row" style="padding-top: 3%;">
                                        <div class="detail-attribute">Priority</div>
                                        <div class="detail-values" [ngClass]="{'urgent' :'Urgent' == complaintDetails.PRIORITY_NAME, 'high' :'High' == complaintDetails.PRIORITY_NAME ,'mid': 'Medium' == complaintDetails.PRIORITY_NAME , 'low' : 'Low' == complaintDetails.PRIORITY_NAME}">
                                            {{complaintDetails.PRIORITY_NAME}}</div>
                                    </div>
                                    <div fxLayout="row" style="padding-top: 3%;">
                                        <div class="detail-attribute">Stage</div>
                                        <div class="detail-values">{{complaintDetails.STAGE_NAME}}</div>
                                    </div>
                                    <div *ngIf="!chatbotCompDetails">
                                        <div fxLayout="row" style="padding-top: 3%;" *ngFor="let source of comp_adsource; let i=index;">
                                            <div class="detail-attribute" *ngIf="comp_adsource.length > 1 && (source.SOURCE_URL != '' && source.SOURCE_URL != null)">Source URL {{i+1}} </div>
                                            <div class="detail-attribute" *ngIf="comp_adsource.length == 1 && (source.SOURCE_URL != '' && source.SOURCE_URL != null)">Source URL </div>
                                            <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(source.SOURCE_URL)" *ngIf="source.SOURCE_URL != '' && source.SOURCE_URL != null"
                                                class="media-anchor" matTooltip="{{source.SOURCE_URL}}">{{source.SOURCE_URL | slice:0: 40}}{{source.SOURCE_URL.length>41? '..':''}} </a>
                                        </div>
                                    </div>
                                    <div fxLayout="row" style="padding-top: 3%;" *ngIf="chatbotCompDetails">
                                        <div class="detail-attribute" *ngIf="complaintDetails.SOURCE_URL != '' && complaintDetails.SOURCE_URL != null">Source URL </div>
                                        <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(complaintDetails.SOURCE_URL)" *ngIf="complaintDetails.SOURCE_URL != '' && complaintDetails.SOURCE_URL != null"
                                            class="media-anchor" matTooltip="{{complaintDetails.SOURCE_URL}}">{{complaintDetails.SOURCE_URL | slice:0: 40}}{{complaintDetails.SOURCE_URL.length>41? '..':''}} </a>
                                    </div>
                                </div>
                                <div class="vertical-line"></div>
                                <div class="tabs-container" style="width: 35%;">
                                    <mat-tab-group (selectedTabChange)="onTabChanged($event)" animationDuration="0ms" class="detail-subtab">
                                        <mat-tab>
                                            <div class="tab-head">
                                                <ng-template mat-tab-label>
                                                    <div class="tab-icon">
                                                        <img src="../../assets/images/Documents.png" style="padding-bottom: 3px;"> Documents
                                                    </div>
                                                </ng-template>
                                            </div>
                                            <div fxLayoutAlign="start" fxLayout="column" fxLayoutGap="20px" class="documenttab1">
                                                <div fxLayout="column" fxLayoutGap="8px" class="docs_attached">
                                                    <div *ngFor="let doc of userComplaintDocs; let index = index"
                                                        fxLayout="row" fxLayoutGap="5px" class="file-container">
                                                        <div fxLayout="column" fxLayoutGap="5px" *ngIf="userComplaintDocs.length != 0">
                                                            <div fxLayout="row" fxLayoutGap="5px"
                                                                style="vertical-align: middle;align-items: center;">
                                                                <div
                                                                    style="height: 40px; width: 40px; background: #3A3A3A; border-radius: 4px; position: relative; top: -1px;">
                                                                    <img src="../assets/images/doc_video.svg"
                                                                        style="margin: 15px 14px;">
                                                                </div>
                                                                <div fxLayout="column">
                                                                    <div style="color: #000000; font-size: 12px" class="doc-caption">
                                                                        <p mat-line matTooltip="{{doc.ATTACHMENT_NAME}}">{{doc.ATTACHMENT_NAME}}</p>
                                                                        <br><span style="margin-left: 6px;">{{doc.DATE |  date: 'dd/MM/yyyy h:mm a'}}</span>
                                                                    </div>
                                                                    <div
                                                                        style="color: rgba(0, 0, 0, 0.6); font-size: 10px">
                                                                        {{doc.duration}}</div>
                                                                </div>
                                                                <div style="left: 5px;">
                                                                    <button mat-icon-button [matMenuTriggerFor]="admin"><mat-icon>more_vert</mat-icon></button>
                                                                </div>
                                                                <mat-menu #admin="matMenu" class="action-buttons">
                                                                    <div class="admin-option-container">
                                                                        <button mat-menu-item class="option-btn" (click)="preview(doc.ATTACHMENT_SOURCE)">
                                                                            <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                            <span class="option-text">Preview</span>
                                                                        </button>
                                                                        <mat-divider class="option-divider">
                                                                        </mat-divider>
                                                                        <button mat-menu-item class="option-btn" (click)="download(doc.ATTACHMENT_NAME, doc.ATTACHMENT_SOURCE)">
                                                                            <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                            <span class="option-text">Download</span>
                                                                        </button>
                                                                    </div>
                                                                </mat-menu>
                                                            </div>
                                                            <div style="width: 100%;">
                                                                <mat-divider class="divider"
                                                                    style="position: relative;"></mat-divider>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div fxLayout="column" fxLayoutGap="5px" class="file-container">
                                                        <div *ngFor="let doc of complainantFiles; let index = index">
                                                            <div fxLayout="column" fxLayoutGap="5px" *ngIf="doc.ATTACHMENT_SOURCE != null || doc.ATTACHMENT_SOURCE != ''">
                                                                <div fxLayout="row" fxLayoutGap="5px"
                                                                    style="vertical-align: middle;align-items: center;">
                                                                    <div
                                                                        style="height: 40px; width: 40px; background: #3A3A3A; border-radius: 4px; position: relative; top: -1px;">
                                                                        <img src="../assets/images/doc_video.svg"
                                                                            style="margin: 15px 14px;">
                                                                    </div>
                                                                    <div fxLayout="column">
                                                                        <div style="color: #000000; font-size: 12px" class="doc-caption">
                                                                            <p mat-line matTooltip="{{doc.ATTACHMENT_SOURCE_NAME}}">{{doc.ATTACHMENT_SOURCE_NAME}}</p>
                                                                            <br><span style="margin-left: 6px;">{{doc.CREATED_DATE |  date: 'dd/MM/yyyy h:mm a'}}</span>
                                                                        </div>
                                                                        <div
                                                                            style="color: rgba(0, 0, 0, 0.6); font-size: 10px">
                                                                            {{doc.duration}}</div>
                                                                    </div>
                                                                    <div style="left: 5px;">
                                                                        <button mat-icon-button [matMenuTriggerFor]="admin"><mat-icon>more_vert</mat-icon></button>
                                                                    </div>
                                                                    <mat-menu #admin="matMenu" class="action-buttons">
                                                                        <div class="admin-option-container">
                                                                            <button mat-menu-item class="option-btn" (click)="preview(doc.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                                <span class="option-text">Preview</span>
                                                                            </button>
                                                                            <mat-divider class="option-divider">
                                                                            </mat-divider>
                                                                            <button mat-menu-item class="option-btn" (click)="download(doc.ATTACHMENT_SOURCE_NAME, doc.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                                <span class="option-text">Download</span>
                                                                            </button>
                                                                        </div>
                                                                    </mat-menu>
                                                                </div>
                                                                <div style="width: 100%;">
                                                                    <mat-divider class="divider"
                                                                        style="position: relative;"></mat-divider>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div *ngFor="let doc of internalFiles; let index = index">
                                                            <div fxLayout="column" fxLayoutGap="5px" *ngIf="doc.ATTACHMENT_SOURCE != null || doc.ATTACHMENT_SOURCE != ''">
                                                                <div fxLayout="row" fxLayoutGap="5px"
                                                                    style="vertical-align: middle;align-items: center;">
                                                                    <div
                                                                        style="height: 40px; width: 40px; background: #3A3A3A; border-radius: 4px; position: relative; top: -1px;">
                                                                        <img src="../assets/images/doc_video.svg"
                                                                            style="margin: 15px 14px;">
                                                                    </div>
                                                                    <div fxLayout="column">
                                                                        <div style="color: #000000; font-size: 12px" class="doc-caption">
                                                                            <p mat-line matTooltip="{{doc.ATTACHMENT_SOURCE_NAME}}">{{doc.ATTACHMENT_SOURCE_NAME}}</p>
                                                                            <br><span style="margin-left: 6px;">{{doc.CREATED_DATE |  date: 'dd/MM/yyyy h:mm a'}}</span>
                                                                        </div>
                                                                        <div
                                                                            style="color: rgba(0, 0, 0, 0.6); font-size: 10px">
                                                                            {{doc.duration}}</div>
                                                                    </div>
                                                                    <div style="left: 5px;">
                                                                        <button mat-icon-button [matMenuTriggerFor]="admin"><mat-icon>more_vert</mat-icon></button>
                                                                    </div>
                                                                    <mat-menu #admin="matMenu" class="action-buttons">
                                                                        <div class="admin-option-container">
                                                                            <button mat-menu-item class="option-btn" (click)="preview(doc.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                                <span class="option-text">Preview</span>
                                                                            </button>
                                                                            <mat-divider class="option-divider">
                                                                            </mat-divider>
                                                                            <button mat-menu-item class="option-btn" (click)="download(doc.ATTACHMENT_SOURCE_NAME, doc.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                                <span class="option-text">Download</span>
                                                                            </button>
                                                                        </div>
                                                                    </mat-menu>
                                                                </div>
                                                                <div style="width: 100%;">
                                                                    <mat-divider class="divider"
                                                                        style="position: relative;"></mat-divider>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </mat-tab>
                                        <mat-tab>
                                            <div class="tab-head">
                                                <ng-template mat-tab-label>
                                                    <div class="tab-icon">
                                                        <img src="../../assets/images/Clock.png"> Timeline
                                                    </div>
                                                </ng-template>
                                            </div>
                                            <div class="time-container-user">
                                                <div *ngIf="!timeLineExist" class="no-timeline">
                                                    No timeline created ....
                                                </div>
                                                <div class="timeline" *ngIf="!timelineLoading && timeLineExist">
                                                    <ul>
                                                        <li *ngFor="let item of complaintTimeline">
                                                            <div mat-line>
                                                                <p *ngFor="let val of item.updates">
                                                                    <span class="time-enents">{{val.label}} &nbsp;</span>
                                                                    <span
                                                                        style="font-style: normal;font-weight: normal;font-size: 14px;color: #000000;">{{val.value}}
                                                                    </span>
                                                                </p>
                                                            </div>
                                                            <div>
                                                                <p style="font-style: normal;font-weight: normal;font-size: 12px;color: #555454;">{{item.date}}</p>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <mat-card *ngIf="timelineLoading"
                                                style="height:240px; display: flex; justify-content: center; align-items: center; background: rgb(238, 237, 237)">
                                                <mat-progress-spinner color="primary" mode="indeterminate" diameter="70">
                                                </mat-progress-spinner>
                                            </mat-card>
                                        </mat-tab>
                                    </mat-tab-group>
                                </div>
                            </div>
                        </div>
                    </mat-card>
                </div>
                <div *ngIf="complaintdetails && mobile">
                    <div class="details-header1" style="padding-left: 15px;">
                        <button style="border: none; background-color:#F8F9F9; margin-right: 10px;"
                            (click)="closeDetails()"><img src="../../assets/images/arrow-mobile.png"></button>
                            <span style="font-size: 16px;position: relative;top: 4px;" *ngIf="complaintDetails.CASE_ID != null && complaintDetails.CASE_ID != ''">Case ID : {{complaintDetails.CASE_ID}}</span>
                            <span style="font-size: 16px;position: relative;top: 4px;" *ngIf="complaintDetails.CASE_ID == null || complaintDetails.CASE_ID == ''">Case ID : Not approved</span>
                    </div>
                    <h3 class="table-heading" style="padding-left: 15px;">Complaint Details</h3>
                    <mat-card class="comp_details-adv" style="width: auto;padding-left: 14px;margin-top: 8px;">
                        <table>
                            <tr style="height: 51px;color: #92A2B1;">
                                <th>Company</th>
                            </tr>
                            <tr style="height: 36px;font-size: 16px;">
                                <td style="padding-bottom: 10px;">{{complaintDetails?.COMPANY_ID == 0 ? complaintDetails?.SUGGESTED_COMPANY_NAME : complaintDetails?.COMPANY_NAME}}</td>
                            </tr>
                            <mat-divider></mat-divider>
                            <tr style="height: 51px;color: #92A2B1;">
                                <th>Status</th>
                            </tr>
                            <tr style="height: 36px;font-size: 16px;">
                                <td style="padding-bottom: 10px;" [ngClass]="{'prog' :'In Progress' == complaintDetails.COMPLAINT_STATUS_NAME , 'new' :'New' == complaintDetails.COMPLAINT_STATUS_NAME , 'hold': 'On Hold' == complaintDetails.COMPLAINT_STATUS_NAME , 'resolve' : 'Resolution' == complaintDetails.COMPLAINT_STATUS_NAME, 'close' : 'Closed' == complaintDetails.COMPLAINT_STATUS_NAME, 'Out-of-remit' : 'Out of remit/Outside ASCI Purview' == complaintDetails.COMPLAINT_STATUS_NAME || 'Sub-Judice' == complaintDetails.COMPLAINT_STATUS_NAME, 'invalid' : 'Non-Issue' == complaintDetails.COMPLAINT_STATUS_NAME}">{{complaintDetails.COMPLAINT_STATUS_NAME}}</td>
                            </tr>
                            <mat-divider></mat-divider>
                            <tr style="height: 51px;color: #92A2B1;">
                                <th>Priority</th>
                            </tr>
                            <tr style="height: 36px;font-size: 16px;">
                                <td style="padding-bottom: 10px;" [ngClass]="{'urgent' :'Urgent' == complaintDetails.PRIORITY_NAME, 'high' :'High' == complaintDetails.PRIORITY_NAME ,'mid': 'Medium' == complaintDetails.PRIORITY_NAME , 'low' : 'Low' == complaintDetails.PRIORITY_NAME}">{{complaintDetails.PRIORITY_NAME}}</td>
                            </tr>
                            <mat-divider></mat-divider>
                            <tr style="height: 51px;color: #92A2B1;">
                                <th>Complaint Description</th>
                            </tr>
                            <tr style="height: 36px;font-size: 16px;">
                                <td style="padding-bottom: 10px;" [innerHTML]="safeHTML(complaintDetails.COMPLAINT_DESCRIPTION)">{{complaintDetails.COMPLAINT_DESCRIPTION}}</td>
                            </tr>
                            <mat-divider></mat-divider>
                            <tr style="height: 51px;color: #92A2B1;">
                                <th>Advertisement Description</th>
                            </tr>
                            <tr style="height: 36px;font-size: 16px;">
                                <td style="padding-bottom: 10px;" [innerHTML]="safeHTML(complaintDetails.ADVERTISEMENT_DESCRIPTION)">{{complaintDetails.ADVERTISEMENT_DESCRIPTION}}</td>
                            </tr>
                            <mat-divider></mat-divider>
                            <tr style="height: 51px;color: #92A2B1;">
                                <th>Classification</th>
                            </tr>
                            <tr style="height: 36px;font-size: 16px;">
                                <td style="padding-bottom: 10px;">{{complaintDetails.CLASSIFICATION_NAME}}</td>
                            </tr>
                            <mat-divider></mat-divider>
                            <tr style="height: 51px;color: #92A2B1;">
                                <th>Stage</th>
                            </tr>
                            <tr style="height: 36px;font-size: 16px;">
                                <td style="padding-bottom: 10px;">{{complaintDetails.STAGE_NAME}}</td>
                            </tr>
                            <mat-divider></mat-divider>
                            <tr style="height: 51px;color: #92A2B1;">
                                <th>Receive Date</th>
                            </tr>
                            <tr style="height: 36px;font-size: 16px;">
                                <td style="padding-bottom: 10px;">{{complaintDetails.CREATED_DATE | date:'dd/MM/yyyy' }}</td>
                            </tr>
                        </table>
                    </mat-card>
                    <mat-card fxLayout="column" class="comp_details-adv" style="width: auto;margin-top: 16px;">
                        <div>
                            <div>
                                <div class="tabs-container">
                                    <mat-tab-group (selectedTabChange)="onTabChanged($event)" animationDuration="0ms" class="detail-subtab">
                                        <mat-tab>
                                            <div class="tab-head">
                                                <ng-template mat-tab-label>
                                                    <div class="tab-icon">
                                                        <img src="../../assets/images/Documents.png" style="padding-bottom: 3px;"> Documents
                                                    </div>
                                                </ng-template>
                                            </div>
                                            <div class="documenttab">
                                                <div fxLayout="row wrap" fxLayoutGap="8px">
                                                    <div *ngFor="let doc of userComplaintDocs; let index = index"
                                                        class="file-container1 doc-wrapper">
                                                        <div *ngIf="userComplaintDocs.length != 0">
                                                            <div fxLayout="row" fxLayoutGap="5px"
                                                                style="vertical-align: middle;align-items: center;">
                                                                <div
                                                                    style="height: 40px; width: 40px; background: #3A3A3A; border-radius: 4px; position: relative; top: -1px;">
                                                                    <img src="../assets/images/doc_video.svg"
                                                                        style="margin: 15px 14px;">
                                                                </div>
                                                                <div fxLayout="column">
                                                                    <div style="color: #000000; font-size: 12px" class="doc-caption1">
                                                                        <p mat-line matTooltip="{{doc.ATTACHMENT_NAME}}">{{doc.ATTACHMENT_NAME}}</p>
                                                                        <br><span style="margin-left: 6px;">{{doc.DATE |  date: 'dd/MM/yyyy h:mm a'}}</span>
                                                                    </div>
                                                                    <!-- <div
                                                                        style="color: rgba(0, 0, 0, 0.6); font-size: 10px">
                                                                        {{doc.duration}} <span
                                                                            style="margin-left: 10px;">By :
                                                                            {{doc.by}}</span></div> -->
                                                                </div>
                                                                <!-- <div class="removeIcon" style="position: relative">
                                                                    <button mat-button *ngIf="doc.FIELD_TAB == 'advertiser'" matSuffix
                                                                        mat-icon-button aria-label="Clear"
                                                                        (click)="removeSelectedFile(file.COMPLAINT_ID, file.ATTACHMENT_SOURCE)">
                                                                        <img src="../assets/images/Trash-icon.svg"
                                                                            style="border: 1px;">
                                                                    </button>
                                                                </div> -->
                                                                <div style="left: 5px;">
                                                                    <button mat-icon-button [matMenuTriggerFor]="admin"><mat-icon>more_vert</mat-icon></button>
                                                                </div>
                                                                <mat-menu #admin="matMenu" class="action-buttons">
                                                                    <div class="admin-option-container">
                                                                        <button mat-menu-item class="option-btn" (click)="preview(doc.ATTACHMENT_SOURCE)">
                                                                            <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                            <span class="option-text">Preview</span>
                                                                        </button>
                                                                        <mat-divider class="option-divider">
                                                                        </mat-divider>
                                                                        <button mat-menu-item class="option-btn" (click)="download(doc.ATTACHMENT_NAME, doc.ATTACHMENT_SOURCE)">
                                                                            <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                            <span class="option-text">Download</span>
                                                                        </button>
                                                                    </div>
                                                                </mat-menu>
                                                            </div>
                                                            <!-- <mat-divider style="position: relative;"></mat-divider> -->
                                                        </div>
                                                    </div>

                                                        <div *ngFor="let doc of complainantFiles; let index = index" class="file-container1 doc-wrapper">
                                                            <div *ngIf="doc.ATTACHMENT_SOURCE != null || doc.ATTACHMENT_SOURCE != ''">
                                                                <div fxLayout="row" fxLayoutGap="5px"
                                                                    style="vertical-align: middle;align-items: center;">
                                                                    <div
                                                                        style="height: 40px; width: 40px; background: #3A3A3A; border-radius: 4px; position: relative; top: -1px;">
                                                                        <img src="../assets/images/doc_video.svg"
                                                                            style="margin: 15px 14px;">
                                                                    </div>
                                                                    <div fxLayout="column">
                                                                        <div style="color: #000000; font-size: 12px" class="doc-caption1">
                                                                            <p mat-line matTooltip="{{doc.ATTACHMENT_SOURCE_NAME}}">{{doc.ATTACHMENT_SOURCE_NAME}}</p>
                                                                            <br><span style="margin-left: 6px;">{{doc.CREATED_DATE |  date: 'dd/MM/yyyy h:mm a'}}</span>
                                                                        </div>
                                                                        <div
                                                                            style="color: rgba(0, 0, 0, 0.6); font-size: 10px">
                                                                            {{doc.duration}}</div>
                                                                    </div>
                                                                    <div style="left: 5px;">
                                                                        <button mat-icon-button [matMenuTriggerFor]="admin"><mat-icon>more_vert</mat-icon></button>
                                                                    </div>
                                                                    <mat-menu #admin="matMenu" class="action-buttons">
                                                                        <div class="admin-option-container">
                                                                            <button mat-menu-item class="option-btn" (click)="preview(doc.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                                <span class="option-text">Preview</span>
                                                                            </button>
                                                                            <mat-divider class="option-divider">
                                                                            </mat-divider>
                                                                            <button mat-menu-item class="option-btn" (click)="download(doc.ATTACHMENT_SOURCE_NAME, doc.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                                <span class="option-text">Download</span>
                                                                            </button>
                                                                        </div>
                                                                    </mat-menu>
                                                                </div>
                                                                <!-- <div style="width: 100%;">
                                                                    <mat-divider style="position: relative;"></mat-divider>
                                                                </div> -->
                                                            </div>
                                                        </div>
                                                        <div *ngFor="let doc of internalFiles; let index = index" class="file-container1 doc-wrapper">
                                                            <div *ngIf="doc.ATTACHMENT_SOURCE != null || doc.ATTACHMENT_SOURCE != ''">
                                                                <div fxLayout="row" fxLayoutGap="5px"
                                                                    style="vertical-align: middle;align-items: center;">
                                                                    <div
                                                                        style="height: 40px; width: 40px; background: #3A3A3A; border-radius: 4px; position: relative; top: -1px;">
                                                                        <img src="../assets/images/doc_video.svg"
                                                                            style="margin: 15px 14px;">
                                                                    </div>
                                                                    <div fxLayout="column">
                                                                        <div style="color: #000000; font-size: 12px" class="doc-caption1">
                                                                            <p mat-line matTooltip="{{doc.ATTACHMENT_SOURCE_NAME}}">{{doc.ATTACHMENT_SOURCE_NAME}}</p>
                                                                            <br><span style="margin-left: 6px;">{{doc.CREATED_DATE |  date: 'dd/MM/yyyy h:mm a'}}</span>
                                                                        </div>
                                                                        <div
                                                                            style="color: rgba(0, 0, 0, 0.6); font-size: 10px">
                                                                            {{doc.duration}}</div>
                                                                    </div>
                                                                    <div style="left: 5px;">
                                                                        <button mat-icon-button [matMenuTriggerFor]="admin"><mat-icon>more_vert</mat-icon></button>
                                                                    </div>
                                                                    <mat-menu #admin="matMenu" class="action-buttons">
                                                                        <div class="admin-option-container">
                                                                            <button mat-menu-item class="option-btn" (click)="preview(doc.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                                <span class="option-text">Preview</span>
                                                                            </button>
                                                                            <mat-divider class="option-divider">
                                                                            </mat-divider>
                                                                            <button mat-menu-item class="option-btn" (click)="download(doc.ATTACHMENT_SOURCE_NAME, doc.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                                <span class="option-text">Download</span>
                                                                            </button>
                                                                        </div>
                                                                    </mat-menu>
                                                                </div>
                                                                <!-- <div style="width: 100%;">
                                                                    <mat-divider style="position: relative;"></mat-divider>
                                                                </div> -->
                                                            </div>
                                                        </div>
                                                </div>
                                            </div>
                                        </mat-tab>
                                        <mat-tab>
                                            <div class="tab-head">
                                                <ng-template mat-tab-label>
                                                    <div class="tab-icon">
                                                        <img src="../../assets/images/Clock.png"> Timeline
                                                    </div>
                                                </ng-template>
                                            </div>
                                            <div class="time-container">
                                                <div *ngIf="!timeLineExist" class="no-timeline">
                                                    No timeline created ....
                                                </div>
                                                <div class="timeline" *ngIf="!timelineLoading && timeLineExist">
                                                    <ul>
                                                        <li *ngFor="let item of complaintTimeline">
                                                            <div mat-line>
                                                                <p *ngFor="let val of item.updates">
                                                                    <span class="time-enents">{{val.label}} &nbsp;</span>
                                                                    <span
                                                                        style="font-style: normal;font-weight: normal;font-size: 14px;color: #000000;">{{val.value}}
                                                                    </span>
                                                                </p>
                                                            </div>
                                                            <div>
                                                                <p style="font-style: normal;font-weight: normal;font-size: 12px;color: #555454;">{{item.date}}</p>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <mat-card *ngIf="timelineLoading"
                                                    style="height:200px; width: 100%; display: flex; justify-content: center; align-items: center; background: rgb(238, 237, 237)">
                                                    <mat-progress-spinner color="primary" mode="indeterminate" diameter="70">
                                                    </mat-progress-spinner>
                                                </mat-card>
                                            </div>
                                        </mat-tab>
                                    </mat-tab-group>
                                </div>
                            </div>
                        </div>
                    </mat-card>
                </div>
            </div>
        </div>

        <div class="box" [hidden]="newComplaint || userComplainant">
            <div fxLayout="column">
                <div fxLayout="row" class="head-row" *ngIf="!complaintdetails && !mobile">
                    <h3 class="comp-heading"  style="width:-webkit-fill-available;">Your complaints</h3>
                    <div fxFlex="70%"></div>
                    <mat-form-field class="example-full-width" appearance="outline">
                        <mat-select [formControl]="text_year" placeholder="{{initialValue}}" (selectionChange)="selectByYear($event)">
                            <mat-option *ngFor="let year of years" [value]="year.value" style="padding-left: 7%;">
                                {{year.viewValue}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div  fxLayout="row" fxLayoutAlign="end end" *ngIf="mobile" style="margin-right: 2.5vh;">
                    <mat-form-field class="example-full-width" appearance="outline">
                        <mat-select [formControl]="text_year" placeholder="{{initialValue}}" (selectionChange)="selectByYear($event)">
                            <mat-option *ngFor="let year of years" [value]="year.value" style="padding-left: 7%;">
                                {{year.viewValue}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div *ngIf="!mobile">
                    <div fxLayout="row" class="card-row1" *ngFor="let item of companyComplaintStats">
                        <mat-card class="card" style="border-bottom: 4px solid #0088CB;">
                            <div fxLayout="row" fxLayoutAlign="center center">
                                <img src="../../assets/images/New.png" style="position: absolute;left:27px;">
                                <div fxLayout="column" style="margin-right: 16px;">
                                    <div class="digit"><b>{{item.TOTAL_COMPLAINTS}}</b></div>
                                    <div class="category">TOTAL</div>
                                </div>
                            </div>
                        </mat-card>
                        <mat-card class="card" style="border-bottom: 4px solid #ED2F45;">
                            <div fxLayout="row" fxLayoutAlign="center center">
                                <img src="../../assets/images/Against 2.png" style="position: absolute;left:27px;">
                                <div fxLayout="column" style="margin-left: 22px;">
                                    <div class="digit"><b>{{item.COMPLAINTS_AGAINST_YOU}}</b></div>
                                    <div class="category1">INBOUND</div>
                                </div>
                            </div>
                        </mat-card>
                        <mat-card class="card" style="border-bottom: 4px solid #F89E1B;">
                            <div fxLayout="row" fxLayoutAlign="center center">
                                <img src="../../assets/images/By 2.png" style="position: absolute;left:27px;">
                                <div fxLayout="column" style="margin-left: 22px;">
                                    <div class="digit"><b>{{item.COMPLAINTS_BY_YOU}}</b></div>
                                    <div class="category">OUTBOUND</div>
                                </div>
                            </div>
                        </mat-card>
                        <mat-card class="card" style="border-bottom: 4px solid #04A585;">
                            <div fxLayout="row" fxLayoutAlign="center center">
                                <img src="../../assets/images/res.png" style="position: absolute;left:27px;">
                                <div fxLayout="column" style="margin-right: 3%;">
                                    <div class="digit"><b>{{item.RESOLVED}}</b></div>
                                    <div class="category">RESOLVED</div>
                                </div>
                            </div>
                        </mat-card>
                    </div>
                </div>
                <div *ngIf="mobile">
                    <div class="card-row1" *ngFor="let item of companyComplaintStats">
                        <mat-card class="card" *ngIf="mobile" style="border-bottom: 4px solid #0088CB;">
                            <div fxLayout="row" fxLayoutAlign="center center">
                            <div fxLayout="column" fxLayoutGap="6px">
                                <div class="digit" style="padding-top: 5px;"><b>{{item.TOTAL_COMPLAINTS}}</b></div>
                                <div class="category" style="margin-top: -8px;">TOTAL</div>
                                </div>
                                <img src="../../assets/images/New.png" style="margin-left: auto;width: 29px;position:relative; top: -2px;"> 
                                </div>
                            </mat-card>
                        <mat-card class="card" *ngIf="mobile" style="border-bottom: 4px solid #ED2F45;">
                                <div fxLayout="row" fxLayoutAlign="center center">
                                    <div fxLayout="column" fxLayoutGap="5px">
                                <div class="digit" style="padding-top: 5px;"><b>{{item.COMPLAINTS_AGAINST_YOU}}</b></div>
                                <div class="category1">INBOUND</div>
                                </div>
                                <img src="../../assets/images/Against 2.png" style="margin-left: auto;width: 29px;position:relative; top: -2px;">
                                </div>
                            
                        </mat-card>
                        <mat-card class="card" *ngIf="mobile" style="border-bottom: 4px solid #F89E1B;">
                            <div fxLayout="row" fxLayoutAlign="center center">
                                <div fxLayout="column" fxLayoutGap="5px">
                                <div class="digit" style="padding-top: 5px;"><b>{{item.COMPLAINTS_BY_YOU}}</b></div>
                                <div class="category">OUTBOUND</div>
                                </div>
                                <img src="../../assets/images/By 2.png" style="margin-left: auto;width: 29px;position:relative; top: -2px;">
                            </div>
                            
                        </mat-card>
                        <mat-card class="card" *ngIf="mobile" style="border-bottom: 4px solid #04A585;">
                            <div fxLayout="row" fxLayoutAlign="center center">
                                <div fxLayout="column" fxLayoutGap="5px">
                                <div class="digit" style="padding-top: 5px;"><b>{{item.RESOLVED}}</b></div>
                                <div class="category">RESOLVED</div>
                                </div>
                                <img src="../../assets/images/res.png" style="margin-left: auto;width: 29px;position:relative; top: -2px;">
                            </div>
                        </mat-card>
                    </div>
                </div>
                <mat-accordion class="tables-div" [hidden]="complaintdetails" multi>
                    <mat-expansion-panel style="margin: 0% 2% 0% 2%; padding-right: 2%; background-color: rgb(248, 246, 246);" *ngIf="!mobile">
                        <mat-expansion-panel-header class="panel-header" style="background-color: rgb(248, 246, 246);">
                            <mat-panel-title class="table-heading bolder">
                                Inbound Complaints
                            </mat-panel-title>
                        </mat-expansion-panel-header>
                        <div class="complaint-table1">
                            <table mat-table [dataSource]="dataSource2" style="width: 100%;">
                                <ng-container matColumnDef="caseID">
                                    <th mat-header-cell *matHeaderCellDef style="width: 20%;">Case ID</th>
                                    <td mat-cell *matCellDef="let element" style="width: 20%;">
                                        {{element.CASE_ID}} </td>
                                </ng-container>
                                <ng-container matColumnDef="brandname">
                                    <th mat-header-cell *matHeaderCellDef style="width: 20%;">Brand Name</th>
                                    <td mat-cell *matCellDef="let element" style="width: 20%;">
                                        {{element.BRAND_NAME}} </td>
                                </ng-container>
                                <ng-container matColumnDef="case">
                                    <th mat-header-cell *matHeaderCellDef style="width: 25%;">Case
                                    </th>
                                    <td mat-cell *matCellDef="let element" style="width: 25%;"><p class="ellipsis" [innerHTML]="safeHTML(element.COMPLAINT_DESCRIPTION)">
                                        {{element.COMPLAINT_DESCRIPTION}} </p></td>
                                </ng-container>
                                <ng-container matColumnDef="status">
                                    <th mat-header-cell *matHeaderCellDef style="width: 15%;">
                                        Status
                                    </th>
                                    <td mat-cell *matCellDef="let element" style="width: 15%;"
                                    [ngClass]="{'prog' :'In Progress' == element.COMPLAINT_STATUS_NAME , 'new' :'New' == element.COMPLAINT_STATUS_NAME , 'hold': 'On Hold' == element.COMPLAINT_STATUS_NAME ,'resolve' : 'Resolution' == element.COMPLAINT_STATUS_NAME, 'close' : 'Closed' == element.COMPLAINT_STATUS_NAME, 'Out-of-remit' : 'Out of remit/Outside ASCI Purview' == element.COMPLAINT_STATUS_NAME || 'Sub-Judice' == element.COMPLAINT_STATUS_NAME, 'invalid' : 'Non-Issue' == element.COMPLAINT_STATUS_NAME}">
                                        {{element.COMPLAINT_STATUS_NAME}} </td>
                                </ng-container>
                                <ng-container matColumnDef="stage">
                                    <th mat-header-cell *matHeaderCellDef style="width: 20%;">Stage
                                    </th>
                                    <td mat-cell *matCellDef="let element" style="width: 20%;">
                                        {{element.STAGE_NAME}} </td>
                                </ng-container>
                                <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
                                <tr mat-row *matRowDef="let row; columns: displayedColumns;"
                                    (click)="selectComplaintAgainst(row)">
                                </tr>
                            </table>
                        </div>
                        <mat-paginator [pageSize]="5" [pageIndex]="0" [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>
                    </mat-expansion-panel>
                    <mat-expansion-panel style="margin: 0% 2% 2% 2%; padding-right: 2%; background-color: rgb(248, 246, 246);" *ngIf="!mobile">
                        <mat-expansion-panel-header class="panel-header" style="background-color: rgb(248, 246, 246);">
                            <mat-panel-title class="table-heading bolder">
                                Outbound Complaints
                            </mat-panel-title>
                        </mat-expansion-panel-header>
                        <div class="complaint-table1">
                            <table mat-table [dataSource]="dataSource1" style="width: 100%;">
                                <ng-container matColumnDef="caseID">
                                    <th mat-header-cell *matHeaderCellDef style="width: 20%;">Case ID</th>
                                    <td mat-cell *matCellDef="let element" style="width: 20%;">
                                        <span *ngIf="element.CASE_ID != null && element.CASE_ID != ''">{{element.CASE_ID}}</span>
                                        <span *ngIf="(element.CASE_ID == null || element.CASE_ID == '') && element.COMPLAINT_STATUS_NAME != 'In Draft'">Not approved</span>
                                        <span *ngIf="(element.CASE_ID == null || element.CASE_ID == '') && element.COMPLAINT_STATUS_NAME == 'In Draft'">Not registered</span>
                                    </td>
                                </ng-container>
                                <ng-container matColumnDef="brandname">
                                    <th mat-header-cell *matHeaderCellDef style="width: 20%;">Brand Name</th>
                                    <td mat-cell *matCellDef="let element" style="width: 20%;">
                                        {{element.BRAND_NAME}} </td>
                                    </ng-container>
                                <ng-container matColumnDef="case">
                                    <th mat-header-cell *matHeaderCellDef style="width: 25%;">Case
                                    </th>
                                    <td mat-cell *matCellDef="let element" style="width: 25%;"><span class="ellipsis1" style="vertical-align: middle;" [innerHTML]="safeHTML(element.COMPLAINT_DESCRIPTION)">
                                        {{element.COMPLAINT_DESCRIPTION}} </span></td>
                                </ng-container>
                                <ng-container matColumnDef="status">
                                    <th mat-header-cell *matHeaderCellDef style="width: 15%;">
                                        Status
                                    </th>
                                    <td mat-cell *matCellDef="let element" style="width: 15%;"
                                    [ngClass]="{'prog' :'In Progress' == element.COMPLAINT_STATUS_NAME , 'new' :'New' == element.COMPLAINT_STATUS_NAME , 'hold': 'On Hold' == element.COMPLAINT_STATUS_NAME , 'resolve' : 'Resolution' == element.COMPLAINT_STATUS_NAME, 'close' : 'Closed' == element.COMPLAINT_STATUS_NAME, 'Out-of-remit' : 'Out of remit/Outside ASCI Purview' == element.COMPLAINT_STATUS_NAME || 'Sub-Judice' == element.COMPLAINT_STATUS_NAME, 'invalid' : 'Non-Issue' == element.COMPLAINT_STATUS_NAME}">
                                        {{element.COMPLAINT_STATUS_NAME}} </td>
                                </ng-container>
                                <ng-container matColumnDef="duedate">
                                    <th mat-header-cell *matHeaderCellDef style="width: 20%;">Complaint date
                                    </th>
                                    <td mat-cell *matCellDef="let element" style="width: 20%;">
                                        {{element.CREATED_DATE | date:'dd/MM/yyyy'}} </td>
                                </ng-container>
                                <tr mat-header-row *matHeaderRowDef="displayColumns; sticky: true"></tr>
                                <tr mat-row *matRowDef="let row; columns: displayColumns;" (click)="selectComplaint(row)">
                                </tr>
                            </table>
                        </div>
                        <mat-paginator [pageSize]="5" [pageIndex]="0" [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>
                    </mat-expansion-panel>
                </mat-accordion>

                <div *ngIf="mobile" [hidden]="complaintdetails">
                    <span class="bolder" *ngIf="!isInboundEmpty" style="font-size: 16px;margin-left: 17px;">Inbound Complaints</span>
                    <div  class="case-detail-box" fxLayout="column" *ngFor="let data of dataSource2.data">
                        <div fxLayout="row" style="padding: 11px; cursor: pointer;" (click)="selectComplaintAgainst(data)">
                            <div class="detail-attribute">Case ID :</div>
                            <div class="detail-values">{{data.CASE_ID}}</div>
                        </div>
                        <mat-divider></mat-divider>
                        <div fxLayout="row" style="padding: 11px;">
                            <div class="detail-attribute">Case :</div>
                            <div class="detail-values" [innerHTML]="safeHTML(data.COMPLAINT_DESCRIPTION)">{{data.COMPLAINT_DESCRIPTION}}</div>
                        </div>
                        <mat-divider></mat-divider>
                        <div fxLayout="row" style="padding: 11px;">
                            <div class="detail-attribute">Brand Name :</div>
                            <div class="detail-values">{{data.BRAND_NAME}}</div>
                        </div>
                        <mat-divider></mat-divider>
                        <div fxLayout="row" style="padding: 11px;">
                            <div class="detail-attribute">Status :</div>
                            <div class="detail-values" [ngClass]="{'prog' :'In Progress' == data.COMPLAINT_STATUS_NAME , 'new' :'New' == data.COMPLAINT_STATUS_NAME , 'hold': 'On Hold' == data.COMPLAINT_STATUS_NAME , 'resolve' : 'Resolution' == data.COMPLAINT_STATUS_NAME, 'close' : 'Closed' == data.COMPLAINT_STATUS_NAME, 'Out-of-remit' : 'Out of remit/Outside ASCI Purview' == data.COMPLAINT_STATUS_NAME || 'Sub-Judice' == data.COMPLAINT_STATUS_NAME, 'invalid' : 'Non-Issue' == data.COMPLAINT_STATUS_NAME}">{{data.COMPLAINT_STATUS_NAME}}</div>
                        </div>
                        <mat-divider></mat-divider>
                        <div fxLayout="row" style="padding: 11px;">
                            <div class="detail-attribute">Due Date :</div>
                            <div class="detail-values">{{data.DUE_DATE | date:'dd/MM/yyyy'}}</div>
                        </div>
                    </div>
                </div>
                <div *ngIf="mobile" [hidden]="complaintdetails">
                    <span class="bolder" *ngIf="!isEmpty" style="font-size: 16px;margin-left: 17px;">Outbound Complaints</span>
                    <div  class="case-detail-box" fxLayout="column" *ngFor="let data of dataSource1.data">
                        <div fxLayout="row"  style="padding: 11px; cursor: pointer;" (click)="selectComplaint(data)">
                            <div class="detail-attribute">Case ID :</div>
                            <div class="detail-values" *ngIf="data.CASE_ID != null && data.CASE_ID != ''">{{data.CASE_ID}}</div>
                            <div class="detail-values" *ngIf="(data.CASE_ID == null || data.CASE_ID == '') && data.COMPLAINT_STATUS_NAME != 'In Draft'">Not approved</div>
                            <div class="detail-values" *ngIf="(data.CASE_ID == null || data.CASE_ID == '') && data.COMPLAINT_STATUS_NAME == 'In Draft'">Not registered</div>
                        </div>
                        <mat-divider></mat-divider>
                        <div fxLayout="row"  style="padding: 11px;">
                            <div class="detail-attribute">Case :</div>
                            <div class="detail-values" [innerHTML]="safeHTML(data.COMPLAINT_DESCRIPTION)">{{data.COMPLAINT_DESCRIPTION}}</div>
                        </div>
                        <mat-divider></mat-divider>
                        <div fxLayout="row" style="padding: 11px;">
                            <div class="detail-attribute">Brand Name :</div>
                            <div class="detail-values">{{data.BRAND_NAME}}</div>
                        </div>
                        <mat-divider></mat-divider>
                        <div fxLayout="row"  style="padding: 11px;">
                            <div class="detail-attribute">Status :</div>
                            <div class="detail-values" [ngClass]="{'prog' :'In Progress' == data.COMPLAINT_STATUS_NAME , 'new' :'New' == data.COMPLAINT_STATUS_NAME , 'hold': 'On Hold' == data.COMPLAINT_STATUS_NAME , 'resolve' : 'Resolution' == data.COMPLAINT_STATUS_NAME, 'close' : 'Closed' == data.COMPLAINT_STATUS_NAME, 'Out-of-remit' : 'Out of remit/Outside ASCI Purview' == data.COMPLAINT_STATUS_NAME || 'Sub-Judice' == data.COMPLAINT_STATUS_NAME, 'invalid' : 'Non-Issue' == data.COMPLAINT_STATUS_NAME}">{{data.COMPLAINT_STATUS_NAME}}</div>
                        </div>
                        <mat-divider></mat-divider>
                        <div fxLayout="row"  style="padding: 11px;">
                            <div class="detail-attribute">Due Date :</div>
                            <div class="detail-values">{{data.DUE_DATE | date:'dd/MM/yyyy'}}</div>
                        </div>
                    </div>
                </div>
                    <!-- <table mat-table [dataSource]="dataSource2" style="width: 100%;">
                        <ng-container matColumnDef="caseID">
                            <th mat-header-cell *matHeaderCellDef style="width: 30%;">Case ID</th>
                            <td mat-cell *matCellDef="let element" style="width: 30%;">
                                {{element.CASE_ID}} </td>
                        </ng-container>

                        <ng-container matColumnDef="case">
                            <th mat-header-cell *matHeaderCellDef style="width: 30%;">Case
                            </th>
                            <td mat-cell *matCellDef="let element" style="width: 30%;"><p class="ellipsis">
                                {{element.COMPLAINT_DESCRIPTION}} </p></td>
                        </ng-container>

                        <ng-container matColumnDef="status">
                            <th mat-header-cell *matHeaderCellDef style="width: 15%;">
                                Status
                            </th>
                            <td mat-cell *matCellDef="let element" style="width: 15%;"
                                [ngClass]="{'prog' :'In Progress' == element.COMPLAINT_STATUS_NAME ,'hold': 'On Hold' == element.COMPLAINT_STATUS_NAME , 'resolve' : 'Resolved' == element.COMPLAINT_STATUS_NAME}">
                                {{element.COMPLAINT_STATUS_NAME}} </td>
                        </ng-container>

                        <ng-container matColumnDef="stage">
                            <th mat-header-cell *matHeaderCellDef style="width: 25%;">Stage
                            </th>
                            <td mat-cell *matCellDef="let element" style="width: 25%;">
                                {{element.STAGE_NAME}} </td>
                        </ng-container>

                        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
                        <tr mat-row *matRowDef="let row; columns: displayedColumns;"
                            (click)="selectComplaintAgainst(row)">
                        </tr>
                    </table> -->


                <div *ngIf="complaintdetails && !mobile">
                    <h3 class="table-heading bolder" *ngIf="against_comp">Inbound Complaint</h3>
                    <h3 class="table-heading bolder" *ngIf="!against_comp">Outbound Complaint</h3>
                    <mat-card fxLayout="column" class="comp_details-adv">
                        <div class="details-header">
                            <button style="border: none; background-color:#F8F9F9; margin-right: 10px;"
                                (click)="closeDetails()"><img src="../../assets/images/Back.png"></button>
                            Case ID : 
                            <span *ngIf="complaintDetails.CASE_ID != null && complaintDetails.CASE_ID != ''">{{complaintDetails.CASE_ID}}</span>
                            <span *ngIf="complaintDetails.CASE_ID == null || complaintDetails.CASE_ID == ''">Not approved</span>
                        </div>
                        <div>
                            <mat-divider></mat-divider>
                        </div>
                        <div>
                            <div fxLayout="row" class="tabs-container" style="width: 100%;">
                                <mat-divider style="width: 12px; position: relative; margin-top: 54px;"></mat-divider>
                                <div class="detail-subtab1">
                                <mat-tab-group (selectedTabChange)="onTabChangedDetails($event)" animationDuration="0ms">
                                    <mat-tab>
                                        <div class="tab-head">
                                            <ng-template mat-tab-label>
                                                <div class="tab-icon">
                                                    Details
                                                </div>
                                            </ng-template>
                                        </div>
                                        <div class="comp-detail-container">
                                            <div class="comp-fxrow-container">
                                                <div style=" margin-left: 10px; padding-top: 18px;">
                                                    <div *ngFor="let source of comp_adsource; let i=index;">
                                                        <div fxLayout="row" fxLayoutGap="100px">
                                                            <div mat-line style="width: 350px;" *ngIf="source.ADVERTISEMENT_SOURCE_ID != 0">
                                                                <p>
                                                                    <span class="detail-attribute" *ngIf="comp_adsource.length > 1">Media source {{i+1}} : </span>
                                                                    <span class="detail-attribute" *ngIf="comp_adsource.length == 1">Media source : </span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 1">Television</span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 2">Radio</span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3">Digital Media</span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 4">Hoardings</span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 5">Print</span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 6">Promotional Material</span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 7">Packaging</span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 8">SMS</span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 9">Others</span>
                                                                </p>
                                                            </div>
                                                            <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3" style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">Platform name : </span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 1">Facebook</span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 2">Instagram</span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 3">YouTube</span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 4">Twitter</span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 5">LinkedIn</span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 6">Website</span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 7">Google Ad</span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 8">Mobile App</span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 9">Others</span>
                                                                </p>
                                                            </div>
                                                            <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID== 1 || source.ADVERTISEMENT_SOURCE_ID == 2"
                                                                style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">Channel name : </span>
                                                                    {{source.SOURCE_NAME}}
                                                                </p>
                                                            </div>
                                                            <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 4 || source.ADVERTISEMENT_SOURCE_ID == 6 "
                                                                style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">Place : </span>
                                                                    {{source.SOURCE_PLACE}}
                                                                </p>
                                                            </div>
                                                            <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 5" style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">Print source : </span>
                                                                    {{source.SOURCE_NAME}}
                                                                </p>
                                                            </div>
                                                            <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 7" style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">MFD/PKD Date : </span>
                                                                    {{source.DATE| date:'dd/MM/yyyy'}}
                                                                </p>
                                                            </div>
                                                            <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 8" style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">Sender : </span>
                                                                    {{source.SOURCE_NAME}}
                                                                </p>
                                                            </div>
                                                            <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 9" style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">Source : </span>
                                                                    {{source.SOURCE_NAME}}
                                                                </p>
                                                            </div>
                                                            <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID != 0 && !source.ADVERTISEMENT_SOURCE_ID"
                                                                style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">Source : </span>
                                                                </p>
                                                            </div>
                                                        </div>
                                                        <div fxLayout="row" fxLayoutGap="100px">
                                                            <div mat-line style="width: 350px;" *ngIf="source.SOURCE_URL != '' && source.SOURCE_URL != null">
                                                                <p>
                                                                    <span class="detail-attribute" *ngIf="comp_adsource.length > 1">Media URL {{i+1}} : </span>
                                                                    <span class="detail-attribute" *ngIf="comp_adsource.length == 1">Media URL : </span>
                                                                    <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(source.SOURCE_URL)" class="media-anchor"
                                                                        matTooltip="{{source.SOURCE_URL}}">{{source.SOURCE_URL | slice:0: 35}}{{source.SOURCE_URL.length>36?
                                                                        '..':''}} </a>
                                                                </p>
                                                            </div>
                                                            <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3 && source.PLATFORM_ID == 9" style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">Source : </span>
                                                                    {{source.SOURCE_PLACE}}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div fxLayout="row" fxLayoutGap="100px">
                                                        <div mat-line style="width:350px">
                                                            <p>
                                                                <span class="detail-attribute">Ad product : </span>
                                                                {{complaintDetails?.COMPANY_ID == 0 ? complaintDetails?.SUGGESTED_COMPANY_NAME : complaintDetails?.COMPANY_NAME}} {{complaintDetails.BRAND_NAME}}
                                                            </p>
                                                        </div>
                                                        <div mat-line>
                                                            <p>
                                                                <span class="detail-attribute">Advertisement seen date : </span>
                                                                {{add_seenDate | date:'dd/MM/yyyy'}}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div fxLayout="row" fxLayoutGap="100px">
                                                        <div mat-line style="width:350px">
                                                            <p>
                                                                <span class="detail-attribute">Status : </span>
                                                                <span class="detail-values"
                                                                    [ngClass]="{'prog' :'In Progress' == complaintDetails.COMPLAINT_STATUS_NAME , 'new' :'New' == complaintDetails.COMPLAINT_STATUS_NAME , 'hold': 'On Hold' == complaintDetails.COMPLAINT_STATUS_NAME , 'resolve' : 'Resolution' == complaintDetails.COMPLAINT_STATUS_NAME, 'close' : 'Closed' == complaintDetails.COMPLAINT_STATUS_NAME, 'Out-of-remit' : 'Out of remit/Outside ASCI Purview' == complaintDetails.COMPLAINT_STATUS_NAME || 'Sub-Judice' == complaintDetails.COMPLAINT_STATUS_NAME, 'invalid' : 'Non-Issue' == complaintDetails.COMPLAINT_STATUS_NAME}">
                                                                    {{complaintDetails.COMPLAINT_STATUS_NAME}}</span>
                                                            </p>
                                                        </div>
                                                        <div mat-line>
                                                            <p *ngIf="noOfDocs != 0">
                                                                <span class="detail-attribute">Media file : </span>
                                                                <a style="font-size: 13px; cursor: pointer;" (click)="openNewTab()" class="media-anchor">{{noOfDocs}} {{noOfDocs
                                                                    > 1 ? 'files': 'file'}} <img src="../../assets/images/media_download.svg"
                                                                        style="position: relative;left:8px;bottom:1px"></a>
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div fxLayout="row" fxLayoutGap="100px">
                                                        <div mat-line style="width:350px">
                                                            <p>
                                                                <span class="detail-attribute">Classification : </span>
                                                                {{complaintDetails.CLASSIFICATION_NAME}}
                                                            </p>
                                                        </div>
                                                        <div mat-line>
                                                            <p>
                                                                <span class="detail-attribute">Priority : </span>
                                                                <span class="detail-values"
                                                                    [ngClass]="{'urgent' :'Urgent' == complaintDetails.PRIORITY_NAME, 'high' :'High' == complaintDetails.PRIORITY_NAME ,'mid': 'Medium' == complaintDetails.PRIORITY_NAME , 'low' : 'Low' == complaintDetails.PRIORITY_NAME}">
                                                                    {{complaintDetails.PRIORITY_NAME}}</span>
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div fxLayout="row" fxLayoutGap="100px">
                                                        <div mat-line style="width:350px">
                                                            <p>
                                                                <span class="detail-attribute">Stage : </span>
                                                                <span class="detail-values">{{complaintDetails.STAGE_NAME}} <img style="margin-left: 2%;"
                                                                        src="../../assets/images/Info.png" [matTooltip]="getTooltipText()" matTooltipPosition="after"
                                                                        matTooltipClass="multiline-tooltip">
                                                                </span>
                                                            </p>
                                                        </div>
                                                        <div mat-line>
                                                            <p>
                                                                <span class="detail-attribute">Due date : </span>
                                                                {{complaintDetails.DUE_DATE | date:'dd/MM/yyyy'}}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div mat-line>
                                                        <p>
                                                            <span class="detail-attribute">Advertisement description :</span>
                                                        </p>
                                                    </div>
                                                    <div mat-line class="comp-msg-container">
                                                        <p class="comp-msg" [innerHTML]="safeHTML(complaintDetails.ADVERTISEMENT_DESCRIPTION)">
                                                            {{complaintDetails.ADVERTISEMENT_DESCRIPTION}}
                                                        </p>
                                                    </div>
                                                    <div mat-line>
                                                        <p>
                                                            <span class="detail-attribute">Objectionable frames :</span>
                                                        </p>
                                                    </div>
                                                    <div mat-line class="comp-msg-container">
                                                        <p class="comp-msg" [innerHTML]="safeHTML(complaintDetails.COMPLAINT_DESCRIPTION)">
                                                            {{complaintDetails.COMPLAINT_DESCRIPTION}}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </mat-tab>
                                    <mat-tab>
                                        <div class="tab-head">
                                            <ng-template mat-tab-label>
                                                <div class="tab-icon">
                                                    Documents
                                                </div>
                                            </ng-template>
                                        </div>
                                        <div class="doc-body" fxLayout="row">
                                            <div class="doc-fxrow-container" fxLayout="row wrap" fxLayoutAlign="flex-start">
                                                <div fxFlex fxLayout="column" fxLayoutGap="10px" *ngIf="against_comp">
                                                    <div fxLayout="row wrap">
                                                        <div *ngFor="let doc of userComplaintDocs; let index = index" fxFlex="30" fxFlex.md="30" fxFlex.sm="20"
                                                            fxFlex.xs="100" fxLayout="column" style="padding: 5px;">
                                                            <mat-card class="mat-card-doc mat-elevation-z1">
                                                                <div class="doc-icon-container">
                                                                    <div class="doc-image-container">
                                                                        <img src="../../../assets/images/File.png" width="20px" height="20px" alt="doc-icon">
                                                                    </div>
                                                                </div>
                                                                <div fxLayout="row">
                                                                    <div fxLayout="column">
                                                                        <div class="doc-caption">
                                                                            <p mat-line matTooltip="{{doc.ATTACHMENT_NAME}}">{{doc.ATTACHMENT_NAME}}</p>
                                                                            <div style="font-size: 11px; color: rgba(0, 0, 0, 0.6); margin-left: 5px;">
                                                                                {{doc.DATE |  date: 'dd/MM/yyyy h:mm a'}}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <button mat-icon-button [matMenuTriggerFor]="admin">
                                                                        <mat-icon>more_vert</mat-icon>
                                                                    </button>
                                                                    <mat-menu #admin="matMenu" class="action-buttons">
                                                                        <div class="admin-option-container">
                                                                            <button mat-menu-item class="option-btn" (click)="preview(doc.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                                <span class="option-text">Preview</span>
                                                                            </button>
                                                                            <mat-divider class="option-divider"></mat-divider>
                                                                            <button mat-menu-item class="option-btn"
                                                                                (click)="download(doc.ATTACHMENT_SOURCE_NAME, doc.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                                <span class="option-text">Download</span>
                                                                            </button>
                                                                        </div>
                                                                    </mat-menu>
                                                                </div>
                                                            </mat-card>
                                                        </div>
                                                        <div *ngFor="let file of complainantFiles; let ind = index" fxFlex="30" fxFlex.md="30" fxFlex.sm="20"
                                                            fxFlex.xs="100" fxLayout="column" style="padding: 5px;">
                                                            <mat-card class="mat-card-doc mat-elevation-z1">
                                                                <div class="doc-icon-container">
                                                                    <div class="doc-image-container">
                                                                        <img src="../../../assets/images/File.png" width="20px" height="20px" alt="doc-icon">
                                                                    </div>
                                                                </div>
                                                                <div fxLayout="row">
                                                                    <div fxLayout="column">
                                                                        <div class="doc-caption">
                                                                            <p mat-line matTooltip="{{file.ATTACHMENT_SOURCE_NAME}}">{{file.ATTACHMENT_SOURCE_NAME}}</p>
                                                                            <div style="font-size: 11px; color: rgba(0, 0, 0, 0.6); margin-left: 5px;">
                                                                                {{file.CREATED_DATE | date:'dd/MM/yyyy h:mm a'}}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <button mat-icon-button [matMenuTriggerFor]="admin">
                                                                        <mat-icon>more_vert</mat-icon>
                                                                    </button>
                                                                    <mat-menu #admin="matMenu" class="action-buttons">
                                                                        <div class="admin-option-container">
                                                                            <button mat-menu-item class="option-btn" (click)="preview(file.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                                <span class="option-text">Preview</span>
                                                                            </button>
                                                                            <mat-divider class="option-divider"></mat-divider>
                                                                            <button mat-menu-item class="option-btn"
                                                                                (click)="download(file.ATTACHMENT_SOURCE_NAME, file.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                                <span class="option-text">Download</span>
                                                                            </button>
                                                                            <mat-divider class="option-divider"></mat-divider>
                                                                            <button mat-menu-item class="option-btn"
                                                                                (click)="removeFile(file.ID, file.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text" style="padding-left: 10px;"><img src="../../../assets/images/Trash.svg" style="font-size: 12px;"></span>
                                                                                <span class="option-text">Delete</span>
                                                                            </button>
                                                                        </div>
                                                                    </mat-menu>
                                                                </div>
                                                            </mat-card>
                                                        </div>
                                                        <div *ngFor="let file of advertiserFiles; let ind = index" fxFlex="30" fxFlex.md="30" fxFlex.sm="20"
                                                            fxFlex.xs="100" fxLayout="column" style="padding: 5px;">
                                                            <mat-card class="mat-card-doc mat-elevation-z1">
                                                                <div class="doc-icon-container">
                                                                    <div class="doc-image-container">
                                                                        <img src="../../../assets/images/File.png" width="20px" height="20px" alt="doc-icon">
                                                                    </div>
                                                                </div>
                                                                <div fxLayout="row">
                                                                    <div fxLayout="column">
                                                                        <div class="doc-caption">
                                                                            <p mat-line matTooltip="{{file.ATTACHMENT_SOURCE_NAME}}">{{file.ATTACHMENT_SOURCE_NAME}}</p>
                                                                            <div style="font-size: 11px; color: rgba(0, 0, 0, 0.6); margin-left: 5px;">
                                                                                {{file.CREATED_DATE | date:'dd/MM/yyyy h:mm a'}}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <button mat-icon-button [matMenuTriggerFor]="admin">
                                                                        <mat-icon>more_vert</mat-icon>
                                                                    </button>
                                                                    <mat-menu #admin="matMenu" class="action-buttons">
                                                                        <div class="admin-option-container">
                                                                            <button mat-menu-item class="option-btn" (click)="preview(file.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                                <span class="option-text">Preview</span>
                                                                            </button>
                                                                            <mat-divider class="option-divider"></mat-divider>
                                                                            <button mat-menu-item class="option-btn"
                                                                                (click)="download(file.ATTACHMENT_SOURCE_NAME, file.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                                <span class="option-text">Download</span>
                                                                            </button>
                                                                            <mat-divider class="option-divider"></mat-divider>
                                                                            <button mat-menu-item class="option-btn"
                                                                                (click)="removeFile(file.ID, file.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text" style="padding-left: 10px;"><img src="../../../assets/images/Trash.svg" style="font-size: 12px;"></span>
                                                                                <span class="option-text">Delete</span>
                                                                            </button>
                                                                        </div>
                                                                    </mat-menu>
                                                                </div>
                                                            </mat-card>
                                                        </div>
                                                        <div *ngFor="let file of internalFiles; let ind = index" fxFlex="30" fxFlex.md="30" fxFlex.sm="20"
                                                            fxFlex.xs="100" fxLayout="column" style="padding: 5px;">
                                                            <mat-card class="mat-card-doc mat-elevation-z1">
                                                                <div class="doc-icon-container">
                                                                    <div class="doc-image-container">
                                                                        <img src="../../../assets/images/File.png" width="20px" height="20px" alt="doc-icon">
                                                                    </div>
                                                                </div>
                                                                <div fxLayout="row">
                                                                    <div fxLayout="column">
                                                                        <div class="doc-caption">
                                                                            <p mat-line matTooltip="{{file.ATTACHMENT_SOURCE_NAME}}">{{file.ATTACHMENT_SOURCE_NAME}}</p>
                                                                            <div style="font-size: 11px; color: rgba(0, 0, 0, 0.6); margin-left: 5px;">
                                                                                {{file.CREATED_DATE | date:'dd/MM/yyyy h:mm a'}}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <button mat-icon-button [matMenuTriggerFor]="admin">
                                                                        <mat-icon>more_vert</mat-icon>
                                                                    </button>
                                                                    <mat-menu #admin="matMenu" class="action-buttons">
                                                                        <div class="admin-option-container">
                                                                            <button mat-menu-item class="option-btn" (click)="preview(file.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                                <span class="option-text">Preview</span>
                                                                            </button>
                                                                            <mat-divider class="option-divider"></mat-divider>
                                                                            <button mat-menu-item class="option-btn"
                                                                                (click)="download(file.ATTACHMENT_SOURCE_NAME, file.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                                <span class="option-text">Download</span>
                                                                            </button>
                                                                        </div>
                                                                    </mat-menu>
                                                                </div>
                                                            </mat-card>
                                                        </div>
                                                        <div class="add-file-container">
                                                            <div class="dropzone" fileDragDrop (filesChangeEmiter)="onFileChange($event)">
                                                                <div class="addfile-text-wrapper">
                                                                    <div class="upload-scope-container">
                                                                        <input type="file" name="file" id="file" (change)="onFileChange($event)"
                                                                            accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt,.jpeg,.png,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv"
                                                                            multiple>
                                                                        <label class="upload-label" for="file" fxLayout="column" fxLayoutAlign="center center">
                                                                            <mat-icon style="font-size: xx-large;font-weight: lighter;">add</mat-icon>
                                                                            <span class="add-textLink">Add files</span>
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div fxFlex fxLayout="column" fxLayoutGap="10px" *ngIf="!against_comp">
                                                    <div fxLayout="row wrap">
                                                        <div *ngFor="let doc of userComplaintDocs; let index = index" fxFlex="30" fxFlex.md="30" fxFlex.sm="20"
                                                            fxFlex.xs="100" fxLayout="column" style="padding: 5px;">
                                                            <mat-card class="mat-card-doc mat-elevation-z1" *ngIf="userComplaintDocs.length != 0">
                                                                <div class="doc-icon-container">
                                                                    <div class="doc-image-container">
                                                                        <img src="../../../assets/images/File.png" width="20px" height="20px" alt="doc-icon">
                                                                    </div>
                                                                </div>
                                                                <div fxLayout="row">
                                                                    <div fxLayout="column">
                                                                        <div class="doc-caption">
                                                                            <p mat-line matTooltip="{{doc.ATTACHMENT_NAME}}">{{doc.ATTACHMENT_NAME}}</p>
                                                                            <div style="font-size: 11px; color: rgba(0, 0, 0, 0.6); margin-left: 5px;">
                                                                                {{doc.DATE |  date: 'dd/MM/yyyy h:mm a'}}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <button mat-icon-button [matMenuTriggerFor]="admin">
                                                                        <mat-icon>more_vert</mat-icon>
                                                                    </button>
                                                                    <mat-menu #admin="matMenu" class="action-buttons">
                                                                        <div class="admin-option-container">
                                                                            <button mat-menu-item class="option-btn" (click)="preview(doc.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                                <span class="option-text">Preview</span>
                                                                            </button>
                                                                            <mat-divider class="option-divider"></mat-divider>
                                                                            <button mat-menu-item class="option-btn"
                                                                                (click)="download(doc.ATTACHMENT_SOURCE_NAME, doc.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                                <span class="option-text">Download</span>
                                                                            </button>
                                                                        </div>
                                                                    </mat-menu>
                                                                </div>
                                                            </mat-card>
                                                        </div>
                                                        <div *ngFor="let doc of complainantFiles; let index = index" fxFlex="30" fxFlex.md="30" fxFlex.sm="20"
                                                            fxFlex.xs="100" fxLayout="column" style="padding: 5px;">
                                                            <div *ngIf="doc.ATTACHMENT_SOURCE != null || doc.ATTACHMENT_SOURCE != ''">
                                                                <mat-card class="mat-card-doc mat-elevation-z1">
                                                                    <div class="doc-icon-container">
                                                                        <div class="doc-image-container">
                                                                            <img src="../../../assets/images/File.png" width="20px" height="20px" alt="doc-icon">
                                                                        </div>
                                                                    </div>
                                                                    <div fxLayout="row">
                                                                        <div fxLayout="column">
                                                                            <div class="doc-caption">
                                                                                <p mat-line matTooltip="{{doc.ATTACHMENT_SOURCE_NAME}}">{{doc.ATTACHMENT_SOURCE_NAME}}</p>
                                                                                <div style="font-size: 11px; color: rgba(0, 0, 0, 0.6); margin-left: 5px;">
                                                                                    {{doc.CREATED_DATE | date:'dd/MM/yyyy h:mm a'}}
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <button mat-icon-button [matMenuTriggerFor]="admin">
                                                                            <mat-icon>more_vert</mat-icon>
                                                                        </button>
                                                                        <mat-menu #admin="matMenu" class="action-buttons">
                                                                            <div class="admin-option-container">
                                                                                <button mat-menu-item class="option-btn" (click)="preview(doc.ATTACHMENT_SOURCE)">
                                                                                    <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                                    <span class="option-text">Preview</span>
                                                                                </button>
                                                                                <mat-divider class="option-divider"></mat-divider>
                                                                                <button mat-menu-item class="option-btn"
                                                                                    (click)="download(doc.ATTACHMENT_SOURCE_NAME, doc.ATTACHMENT_SOURCE)">
                                                                                    <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                                    <span class="option-text">Download</span>
                                                                                </button>
                                                                                <!-- <mat-divider class="option-divider" *ngIf="doc.FIELD_TAB == 'complainant'"></mat-divider>
                                                                                <button mat-menu-item class="option-btn"
                                                                                    (click)="removeFile(doc.ID, doc.ATTACHMENT_SOURCE)"
                                                                                    *ngIf="doc.FIELD_TAB == 'complainant'">
                                                                                    <span class="option-text" style="padding-left: 10px;"><img src="../../../assets/images/Trash.svg" style="font-size: 12px;"></span>
                                                                                    <span class="option-text">Delete</span>
                                                                                </button> -->
                                                                            </div>
                                                                        </mat-menu>
                                                                    </div>
                                                                </mat-card>
                                                            </div>
                                                        </div>
                                                        <div *ngFor="let doc of internalFiles; let index = index" fxFlex="30" fxFlex.md="30" fxFlex.sm="20"
                                                            fxFlex.xs="100" fxLayout="column" style="padding: 5px;">
                                                            <div *ngIf="doc.ATTACHMENT_SOURCE != null || doc.ATTACHMENT_SOURCE != ''">
                                                                <mat-card class="mat-card-doc mat-elevation-z1">
                                                                    <div class="doc-icon-container">
                                                                        <div class="doc-image-container">
                                                                            <img src="../../../assets/images/File.png" width="20px" height="20px" alt="doc-icon">
                                                                        </div>
                                                                    </div>
                                                                    <div fxLayout="row">
                                                                        <div fxLayout="column">
                                                                            <div class="doc-caption">
                                                                                <p mat-line matTooltip="{{doc.ATTACHMENT_SOURCE_NAME}}">{{doc.ATTACHMENT_SOURCE_NAME}}</p>
                                                                                <div style="font-size: 11px; color: rgba(0, 0, 0, 0.6); margin-left: 5px;">
                                                                                    {{doc.CREATED_DATE | date:'dd/MM/yyyy h:mm a'}}
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <button mat-icon-button [matMenuTriggerFor]="admin">
                                                                            <mat-icon>more_vert</mat-icon>
                                                                        </button>
                                                                        <mat-menu #admin="matMenu" class="action-buttons">
                                                                            <div class="admin-option-container">
                                                                                <button mat-menu-item class="option-btn" (click)="preview(doc.ATTACHMENT_SOURCE)">
                                                                                    <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                                    <span class="option-text">Preview</span>
                                                                                </button>
                                                                                <mat-divider class="option-divider"></mat-divider>
                                                                                <button mat-menu-item class="option-btn"
                                                                                    (click)="download(doc.ATTACHMENT_SOURCE_NAME, doc.ATTACHMENT_SOURCE)">
                                                                                    <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                                    <span class="option-text">Download</span>
                                                                                </button>
                                                                            </div>
                                                                        </mat-menu>
                                                                    </div>
                                                                </mat-card>
                                                            </div>
                                                        </div>
                                                        <div class="add-file-container">
                                                            <div class="dropzone" fileDragDrop (filesChangeEmiter)="onComplainantFile($event)">
                                                                <div class="addfile-text-wrapper">
                                                                    <div class="upload-scope-container">
                                                                        <input type="file" name="file" id="file" (change)="onComplainantFile($event)"
                                                                            accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt,.jpeg,.png,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv"
                                                                            multiple>
                                                                        <label class="upload-label" for="file" fxLayout="column" fxLayoutAlign="center center">
                                                                            <mat-icon style="font-size: xx-large;font-weight: lighter;">add</mat-icon>
                                                                            <span class="add-textLink">Add files</span>
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>            
                                    </mat-tab>
                                    <mat-tab>
                                        <div class="tab-head">
                                            <ng-template mat-tab-label>
                                                <div class="tab-icon">
                                                    Timeline
                                                </div>
                                            </ng-template>
                                        </div>
                                        <div class="time-container">
                                            <div *ngIf="!timeLineExist" class="no-timeline">
                                                No timeline created ....
                                            </div>
                                            <div class="timeline" *ngIf="!timelineLoading && timeLineExist">
                                                <ul>
                                                    <li *ngFor="let item of complaintTimeline">
                                                        <div mat-line>
                                                            <p *ngFor="let val of item.updates">
                                                                <span class="time-enents">{{val.label}} &nbsp;</span>
                                                                <span
                                                                    style="font-style: normal;font-weight: normal;font-size: 14px;color: #000000;">{{val.value}}
                                                                </span>
                                                            </p>
                                                        </div>
                                                        <div>
                                                            <p style="font-style: normal;font-weight: normal;font-size: 12px;color: #555454;">{{item.date}}</p>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                            <mat-card *ngIf="timelineLoading"
                                                style="height:200px; width: 100%; display: flex; justify-content: center; align-items: center; background: rgb(238, 237, 237)">
                                                <mat-progress-spinner color="primary" mode="indeterminate" diameter="70">
                                                </mat-progress-spinner>
                                            </mat-card>
                                        </div>
                                    </mat-tab>
                                    <mat-tab>
                                        <div class="tab-head">
                                            <ng-template mat-tab-label>
                                                <div class="tab-icon">
                                                    Claims
                                                </div>
                                            </ng-template>
                                        </div>
                                        <div class="no-claims" *ngIf="complaintClaims.length == 0 && complaintCodeViolated.length == 0">
                                            No claims raised...
                                        </div>
                                        <div fxLayout="column" fxLayoutGap="10px" *ngIf="complaintClaims.length != 0 || complaintCodeViolated.length != 0" class="claims-tab">
                                            <mat-accordion>
                                                <mat-expansion-panel class="intra-expansion" (opened)="panelOpenState = true" (closed)="panelOpenState = false">
                                                    <mat-expansion-panel-header class="panel-header" style="height: 55px;">
                                                        <mat-panel-title class="panel-title">
                                                            Claim Details
                                                        </mat-panel-title>
                                                    </mat-expansion-panel-header>
                                                    <div class="intra-divider" style="width: 100%; border-radius: 1px; margin-bottom: 2%;">
                                                        <mat-divider></mat-divider>
                                                    </div>
                                                    <div class="intra-body" fxLayout="row" fxLayoutGap="10px">
                                                        <div class="raised-container" fxLayout="column">
                                                            <div class="head-container1" fxLayout="row" fxLayoutGap="10px">
                                                                <div class="arrow-icon-container">
                                                                    <img class="arrow-img-icon" src="../assets/images/arrow_circle-icon.svg " />
                                                                </div>
                                                                <div>
                                                                    <h3 class="intra-h3">Claims raised</h3>
                                                                </div>
                                                            </div>
                                                            <div class="claim-challenges-container" fxLayout="column" fxLayoutGap="10px" style="margin-top: 1%;">
                                                                <div class="challenge-container" *ngFor="let item of complaintClaims; let in = index;">
                                                                    <div class="panel-title">Claim challenged {{in+1}} </div>
                                                                    <div fxLayout="column">
                                                                        <div fxLayout="row" fxLayoutGap="10px">
                                                                            <div class="attribute-container width50">
                                                                                <p>
                                                                                    <span class="grey-text">Name :</span><br> {{item.CLAIM_CHALLENGED}}
                                                                                </p>
                                                                            </div>
                                                                            <div class="attribute-container width50">
                                                                                <p>
                                                                                    <span class="grey-text">Annexure no. :</span><br> {{item.ANNEXURE_NO}}
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                        <div class="attribute-container">
                                                                            <p>
                                                                                <span class="grey-text"> Key objection :</span><br>
                                                                                <span [innerHTML]="safeHTML(item.KEY_OBJECTION)">{{item.KEY_OBJECTION}}</span>
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="violated-container" fxLayout="column" fxLayoutGap="5px">
                                                            <div class="head-container1" fxLayout="row" fxLayoutGap="10px">
                                                                <div class="arrow-icon-container">
                                                                    <img class="arrow-img-icon" src="../assets/images/arrow_circle-icon.svg " />
                                                                </div>
                                                                <div>
                                                                    <h3 class="intra-h3">ASCI code violated </h3>
                                                                </div>
                                                            </div>
                                                            <div class="chapters-container">
                                                                <div class="chapter-text" *ngFor="let item of complaintCodeViolated">
                                                                    <div fxLayout="column">
                                                                        <span>
                                                                            <mat-icon class="circle-icon">lens</mat-icon>Chapter {{item.CHAPTER_ID}}
                                                                            <ng-container *ngIf="item.CLAUSES_ID"> : </ng-container>
                                                                        </span>
                                                                        <span style="width:90%;margin-left: 25px;">{{item.CLAUSES_ID}}</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                            
                                                            <div class="head-container1" fxLayout="row" fxLayoutGap="10px">
                                                                <div class="arrow-icon-container">
                                                                    <img class="arrow-img-icon" src="../assets/images/arrow_circle-icon.svg " />
                                                                </div>
                                                                <div>
                                                                    <h3 class="intra-h3">ASCI Guideline code violated </h3>
                                                                </div>
                                                            </div>
                                                            <div class="chapters-container">
                                                                <div class="chapter-text" *ngFor="let item of guidelines">
                                                                    <div fxLayout="column">
                                                                        <span>
                                                                            <mat-icon class="circle-icon">lens</mat-icon>Guideline <span
                                                                                *ngIf="item.G_CHAPTER_ID != 1">{{item.G_CHAPTER_ID - 1}}</span>
                                                                            <span *ngIf="item.G_CHAPTER_ID == 1">N/A</span>
                                                                            <ng-container
                                                                                *ngIf="item.G_CHAPTER_ID != 1 && item.G_CHAPTER_ID != 2 && item.G_CHAPTER_ID != 7 && item.G_CHAPTER_ID != 8">
                                                                                : </ng-container>
                                                                        </span>
                                                                        <span style="width:90%;margin-left: 25px;" *ngIf="item.G_CHAPTER_ID != 1">
                                                                            {{item.G_CLAUSES_ID}}
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                            
                                                            <div fxLayout="column" fxLayoutGap="10px" fxLayoutAlign="center center">
                                                                <button mat-button (click)="viewDocument()" class="violated-popups">
                                                                    <div>
                                                                        <span class="bolder">
                                                                            <img src="..\assets\images\folder-icon.svg">&nbsp; View documents
                                                                        </span>
                                                                    </div>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </mat-expansion-panel>
                                            </mat-accordion>
                                        </div>              
                                    </mat-tab>
                                    <mat-tab>
                                        <div class="tab-head">
                                            <ng-template mat-tab-label>
                                                <div class="tab-icon">
                                                    Similar complaints
                                                </div>
                                            </ng-template>
                                        </div>
                                        <mat-card *ngIf="similarCompLoading"
                                            style="display: flex; justify-content: center; align-items: center; background: white;">
                                            <mat-progress-spinner color="primary" mode="indeterminate" diameter="70">
                                            </mat-progress-spinner>
                                        </mat-card>
                                        <div fxLayout="row" *ngIf="!similarCompLoading">
                                            <div *ngIf="similarComplaints.length == 0">
                                                <div class="message" fxLayout="row" style="padding: 10px 0px 0px 10px;">
                                                  <p> No match found </p>
                                                </div>
                                            </div>
                                            <div fxFlex="32%" class="left-panel">
                                                <mat-list-item *ngFor="let item of similarComplaints">
                                                    <div class="list-item-container1" (click)="selectDetails(item.CASE_ID)">
                                                        <div class="item-head-container1" fxLayout="column">
                                                            <div class="message" matTooltip="{{item.COMPLAINT_DESCRIPTION}}">
                                                                <p class="ellipsis_desc"> {{item.COMPLAINT_DESCRIPTION}} </p>
                                                            </div>
                                                            <div fxLayout="row">
                                                            <div fxLayout="column" style="font-size: 11px;" fxFlex="65%" fxLayoutGap="8px">
                                                                <div fxLayout="row">
                                                                    <div fxFlex="25%">C.ID : </div><div fxFlex="75%">{{item.CASE_ID}}</div>
                                                                </div>
                                                            </div>
                                                            <div fxLayout="column" style="font-size: 11px;" fxLayoutGap="8px" fxFlex="35%">
                                                                <div class="date-container" fxLayout="row">
                                                                    <mat-icon class="item-icon1">calendar_today</mat-icon>
                                                                    <span matSuffix style="position: relative; left:3px; bottom: 1px; font-size: 11px;"> {{item.CREATED_DATE | date:'dd/MM/yyyy'}} </span>
                                                                </div>
                                                            </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <mat-divider></mat-divider>
                                                </mat-list-item>
                                            </div>
                                            <div class="divider1" *ngIf="similarComplaints.length != 0"></div>
                                            <div fxFlex="68%" class="right-panel" *ngIf="similarComplaints.length != 0">
                                                <div fxLayout="column">
                                                    <div class="contents-scroll" *ngIf="compDetails.length == 0">
                                                        <div class="details-container">
                                                            <p style="font-size: 15px; color: rgb(121, 120, 120); padding-left: 300px; padding-top: 20px;">Complaint does not exist...</p>
                                                        </div>
                                                    </div>
                                                <div>
                                                <div style=" margin-left: 10px;" *ngIf="compDetails.length != 0">
                                                    <div fxLayout="row" fxLayoutGap="100px">
                                                        <div mat-line style="width:250px">
                                                            <p>
                                                                <span class="detail-attribute">Case ID : </span>
                                                                {{compDetails.CASE_ID}}
                                                            </p>
                                                        </div>
                                                        <div mat-line>
                                                            <p>
                                                                <span class="detail-attribute">Tracking ID : </span>
                                                                {{compDetails.ID}}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div fxLayout="row" fxLayoutGap="100px">
                                                        <div mat-line style="width:250px" *ngIf="detail_company">
                                                            <p>
                                                                <span class="detail-attribute">Product : </span>
                                                                {{detail_company}}
                                                            </p>
                                                        </div>
                                                        <div mat-line *ngIf="classification_name">
                                                            <p>
                                                                <span class="detail-attribute">Classification : </span>
                                                                {{classification_name}}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div fxLayout="row" fxLayoutGap="100px" *ngFor="let comp of company_details; let i=index;">
                                                        <div mat-line style="width: 250px;">
                                                            <p>
                                                                <span class="detail-attribute">Advertiser company {{i+2}} : </span>
                                                                {{comp.COMPANY_NAME}}
                                                            </p>
                                                        </div>
                                                        <div mat-line>
                                                            <p>
                                                                <span class="detail-attribute">Advertiser company {{i+2}} <br> email : </span>
                                                                {{!!comp.EMAIL_ID ? comp.EMAIL_ID : 'Not available'}}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div *ngFor="let source of detail_adsource; let i=index;">
                                                        <div fxLayout="row" fxLayoutGap="100px">
                                                            <div mat-line style="width: 250px;" *ngIf="source.ADVERTISEMENT_SOURCE_ID != 0">
                                                                <p>
                                                                    <span class="detail-attribute" *ngIf="detail_adsource.length > 1">Media source {{i+1}} :
                                                                    </span>
                                                                    <span class="detail-attribute" *ngIf="detail_adsource.length == 1">Media source : </span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 1">Television</span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 2">Radio</span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3">Digital Media</span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 4">Hoardings</span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 5">Print</span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 6">Promotional Material</span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 7">Packaging</span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 8">SMS</span>
                                                                    <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 9">Others</span>
                                                                </p>
                                                            </div>
                                                            <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3" style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">Platform name : </span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 1">Facebook</span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 2">Instagram</span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 3">YouTube</span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 4">Twitter</span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 5">LinkedIn</span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 6">Website</span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 7">Google Ad</span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 8">Mobile App</span>
                                                                    <span *ngIf="source.PLATFORM_ID  == 9">Others</span>
                                                                </p>
                                                            </div>
                                                            <div mat-line
                                                                *ngIf="source.ADVERTISEMENT_SOURCE_ID== 1 || source.ADVERTISEMENT_SOURCE_ID == 2"
                                                                style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">Channel name : </span>
                                                                    {{source.SOURCE_NAME}}
                                                                </p>
                                                            </div>
                                                            <div mat-line
                                                                *ngIf="source.ADVERTISEMENT_SOURCE_ID == 4 || source.ADVERTISEMENT_SOURCE_ID == 6 "
                                                                style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">Place : </span>
                                                                    {{source.SOURCE_PLACE}}
                                                                </p>
                                                            </div>
                                                            <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 5" style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">Print source : </span>
                                                                    {{source.SOURCE_NAME}}
                                                                </p>
                                                            </div>
                                                            <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 7" style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">MFD/PKD Date : </span>
                                                                    {{source.DATE| date:'dd/MM/yyyy'}}
                                                                </p>
                                                            </div>
                                                            <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 8" style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">Sender : </span>
                                                                    {{source.SOURCE_NAME}}
                                                                </p>
                                                            </div>
                                                            <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 9" style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">Source : </span>
                                                                    {{source.SOURCE_NAME}}
                                                                </p>
                                                            </div>
                                                            <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID != 0 && !source.ADVERTISEMENT_SOURCE_ID"
                                                                style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">Source : </span>
                                                                </p>
                                                            </div>
                                                        </div>
                                                        <div fxLayout="row" fxLayoutGap="100px"
                                                          *ngIf="complaint_source_id != 7 && complaint_source_id != 8">
                                                            <div mat-line style="width: 250px;"
                                                                *ngIf="source.SOURCE_URL != '' && source.SOURCE_URL != null">
                                                                <p>
                                                                    <span class="detail-attribute" *ngIf="detail_adsource.length > 1">Media URL {{i+1}} :
                                                                    </span>
                                                                    <span class="detail-attribute" *ngIf="detail_adsource.length == 1">Media URL : </span>
                                                                    <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(source.SOURCE_URL)"
                                                                        class="media-anchor" matTooltip="{{source.SOURCE_URL}}">{{source.SOURCE_URL | slice:0:
                                                                        25}}{{source.SOURCE_URL.length>26? '..':''}} </a>
                                                                </p>
                                                            </div>
                                                            <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3 && source.PLATFORM_ID == 9"
                                                                style="margin-left: 100px;">
                                                                <p>
                                                                    <span class="detail-attribute">Source : </span>
                                                                    {{source.SOURCE_PLACE}}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div fxLayout="row" fxLayoutGap="100px">
                                                        <div mat-line style="width: 250px;">
                                                            <p>
                                                                <span class="detail-attribute">Complaint via : </span>
                                                                {{compDetails.COMPLAINT_SOURCE_NAME }}
                                                            </p>
                                                        </div>
                                                        <div mat-line>
                                                            <p>
                                                                <span class="detail-attribute">Registered date : </span>
                                                                {{comp_date | date:'dd/MM/yyyy'}} - {{comp_date | date:'HH:mm'}}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div fxLayout="row" fxLayoutGap="100px">
                                                        <div mat-line style="width: 250px;">
                                                            <p>
                                                                <span class="detail-attribute">Created Date : </span>
                                                                {{detail_date | date:'dd/MM/yyyy'}} - {{detail_date | date:'HH:mm'}}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div fxLayout="row" fxLayoutGap="100px">
                                                        <div mat-line style="width: 291px;" *ngIf="detail_addate">
                                                            <p>
                                                                <span class="detail-attribute">Advertisement Seen Date : </span>
                                                                {{detail_addate| date:'dd/MM/yyyy'}} <span *ngIf="detail_adtime">- {{detail_adtime}}</span>
                                                            </p>
                                                        </div>
                                                        <div mat-line style="width: 250px;" *ngIf="seen_date && (detail_addate == '' || detail_addate == null)">
                                                            <p>
                                                                <span class="detail-attribute">Advertisement Seen Date : </span>
                                                                {{seen_date| date:'dd/MM/yyyy'}} <span *ngIf="seen_time">- {{seen_time}}</span>
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div fxLayout="row" fxLayoutGap="100px">
                                                        <div mat-line *ngIf="complaint_source_id != 7 && complaint_source_id != 8">
                                                            <p *ngIf="noOfDocs != 0">
                                                                <span class="detail-attribute">Media file : </span>
                                                                <a style="font-size: 13px; cursor: pointer;" (click)="openNewTab()"
                                                                class="media-anchor">{{noOfDocs}} {{noOfDocs > 1 ? 'files': 'file'}} <img
                                                                    src="../../assets/images/media_download.svg"
                                                                    style="position: relative;left:8px;bottom:1px"></a>
                                                            </p>
                                                        </div>
                                                        <div mat-line style="width: 250px;" *ngIf="complaint_source_id == 7">
                                                            <p>
                                                                <span class="detail-attribute">Translation hyperlink : </span>
                                                                <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(translation_hyper)"
                                                                class="media-anchor" matTooltip="{{translation_hyper}}">{{translation_hyper | slice:0:
                                                                25}}{{longText5}}</a>
                                                            </p>
                                                        </div>
                                                        <div mat-line *ngIf="complaint_source_id == 7">
                                                            <p>
                                                                <span class="detail-attribute">Creative Id : </span>
                                                                {{creative_id}}
                                                            </p>
                                                        </div>
                                                        <div mat-line style="width: 250px;" *ngIf="complaint_source_id == 8">
                                                            <p>
                                                                <span class="detail-attribute">Engagements : </span>
                                                                {{engagements}}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div fxLayout="row" fxLayoutGap="100px" *ngIf="complaint_source_id == 7">
                                                        <div mat-line style="width:250px">
                                                            <p>
                                                                <span class="detail-attribute">Media outlet : </span>
                                                                <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(media_outlet)"
                                                                class="media-anchor" matTooltip="{{media_outlet}}">{{media_outlet | slice:0:
                                                                27}}{{longText8}}</a>
                                                            </p>
                                                        </div>
                                                        <div mat-line>
                                                            <p>
                                                                <span class="detail-attribute">Media : </span>
                                                                {{media}}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div fxLayout="row" fxLayoutGap="100px" *ngIf="complaint_source_id == 8">
                                                        <div mat-line style="width:250px">
                                                            <p>
                                                                <span class="detail-attribute">Publication URL : </span>
                                                                <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(publication_url)"
                                                                class="media-anchor" matTooltip="{{publication_url}}">{{publication_url | slice:0:
                                                                26}}{{publication_url.length>27? '..':''}}</a>
                                                            </p>
                                                        </div>
                                                        <div mat-line>
                                                            <p>
                                                                <span class="detail-attribute">Profile URL : </span>
                                                                <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(profile_url)"
                                                                class="media-anchor" matTooltip="{{profile_url}}">{{profile_url | slice:0:
                                                                24}}{{profile_url.length>25? '..':''}}</a>
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div fxLayout="row" *ngIf="complaint_source_id == 7" fxLayoutGap="100px">
                                                        <div mat-line style="width:250px" *ngIf="suppliment != null">
                                                            <p>
                                                                <span class="detail-attribute">Supplement : </span>
                                                                {{suppliment}}
                                                            </p>
                                                        </div>
                                                        <div mat-line *ngIf="edition != null">
                                                            <p>
                                                                <span class="detail-attribute">Edition : </span>
                                                                {{edition}}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div fxLayout="row" *ngIf="complaint_source_id == 7" fxLayoutGap="100px">
                                                        <div mat-line style="width:250px">
                                                            <p>
                                                                <span class="detail-attribute">Ad language : </span>
                                                                {{ad_language}}
                                                            </p>
                                                        </div>
                                                        <div mat-line>
                                                            <p matTooltip="{{super_category}}">
                                                                <span class="detail-attribute">Product category : </span>
                                                                {{super_category | slice:0:30}}{{super_category.length>31? '..':''}}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div fxLayout="row" fxLayoutGap="100px"
                                                        *ngIf="complaint_source_id == 7 || complaint_source_id == 8">
                                                        <div mat-line style="width:250px" *ngIf="noOfDocs != 0">
                                                            <p>
                                                                <span class="detail-attribute">Media file : </span>
                                                                <a style="font-size: 13px; cursor: pointer;" (click)="openNewTab()"
                                                                class="media-anchor">{{noOfDocs}} {{noOfDocs > 1 ? 'files': 'file'}} <img
                                                                    src="../../assets/images/media_download.svg"
                                                                    style="position: relative;left:8px;bottom:1px"></a>
                                                            </p>
                                                        </div>
                                                        <div mat-line *ngIf="noOfDocs != 0">
                                                            <p *ngIf="similar_detail_link">
                                                                <span class="detail-attribute">Media URL : </span>
                                                                <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(similar_detail_link)"
                                                                class="media-anchor" matTooltip="{{similar_detail_link}}">{{similar_detail_link | slice:0:
                                                                28}}{{longText4}}</a>
                                                            </p>
                                                        </div>
                                                        <div mat-line *ngIf="noOfDocs == 0">
                                                            <p *ngIf="similar_detail_link">
                                                                <span class="detail-attribute">Media URL : </span>
                                                                <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(similar_detail_link)"
                                                                class="media-anchor" matTooltip="{{similar_detail_link}}">{{similar_detail_link | slice:0:
                                                                28}}{{longText4}}</a>
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div mat-line *ngIf="detail_advert">
                                                        <p>
                                                            <span class="detail-attribute">Advertisement description :</span>
                                                        </p>
                                                    </div>
                                                    <div mat-line class="comp-msg-container" *ngIf="detail_advert">
                                                        <p class="comp-msg" [innerHTML]="safeHTML(detail_advert)">
                                                          {{detail_advert}}
                                                        </p>
                                                    </div>
                                                    <div mat-line>
                                                        <p>
                                                            <span class="detail-attribute">Objectionable frames :</span>
                                                        </p>
                                                    </div>
                                                    <div mat-line class="comp-msg-container">
                                                        <p class="comp-msg" [innerHTML]="safeHTML(detail_complaint)">
                                                          {{detail_complaint}}
                                                        </p>
                                                    </div>
                                                    <div mat-line class="comp-msg-container">
                                                        <p class="comp-msg"></p>
                                                    </div>
                                                    </div>   
                                                    </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </mat-tab>
                                    </mat-tab-group>
                                </div>
                                <!-- <div fxFlex="10%"></div> -->
                                <div  style="position: absolute;right: 22%;top: 19%;" *ngIf="complaintDetails.REGISTERED == 1">
                                    <button mat-icon-button class="float-btn" (click)="openRecommendationorResolutionDialog('resolution')" style="border:1px solid #04A585;">
                                      <img src="../../assets/images/resolution.png" style="margin-bottom: 5px;">
                                    </button>
                                </div>
                                <div fxFlex="2%"></div>
                                <div style="position: absolute;right: 16.5%;top: 19%;" *ngIf="complaintDetails.REGISTERED == 1">
                                    <button mat-icon-button class="float-btn" (click)="openRecommendationorResolutionDialog('recommendation')" style="border:1px solid #F89E1B;">
                                      <img src="../../assets/images/recommendation.png" style="margin-bottom: 5px;">
                                    </button>
                                </div>
                                <div class="conversation-btn" fxLayout="row" style="position: absolute" *ngIf="complaintDetails.REGISTERED == 1">
                                    <button mat-flat-button class="msg-button" (click)="createMessage()">
                                        <span class="bolder">
                                            <img style="padding-bottom: 3px;" src="../../../assets/images/msg-white.png"> Conversation
                                        </span> 
                                    </button>
                                </div>
                            </div>
                        </div>
                    </mat-card>
                </div>

                <div *ngIf="complaintdetails && mobile">
                    <div class="details-header1" style="padding-left: 15px;">
                        <button style="border: none; background-color:#F8F9F9; margin-right: 10px;"
                            (click)="closeDetails()"><img src="../../assets/images/arrow-mobile.png"></button>
                        <span style="font-size: 16px;position: relative;top: 3px;" *ngIf="complaintDetails.CASE_ID != null && complaintDetails.CASE_ID != ''">Case ID : {{complaintDetails.CASE_ID}}</span>
                        <span style="font-size: 16px;position: relative;top: 3px;" *ngIf="complaintDetails.CASE_ID == null || complaintDetails.CASE_ID == ''">Case ID : Not approved</span>
                    </div>
                    <h3 class="table-heading" style="padding-left: 15px;">Complaint Details</h3>
                    <mat-card class="comp_details-adv" style="width: auto;padding-left: 14px;padding-right: 14px;">
                        <table>
                            <tr style="height: 51px;color: #92A2B1;">
                                <th>Company</th>
                            </tr>
                            <tr style="height: 36px;font-size: 16px;">
                                <td style="padding-bottom: 10px;">{{complaintDetails?.COMPANY_ID == 0 ? complaintDetails?.SUGGESTED_COMPANY_NAME : complaintDetails?.COMPANY_NAME}}</td>
                            </tr>
                            <mat-divider></mat-divider>
                            <tr style="height: 51px;color: #92A2B1;">
                                <th>Status</th>
                            </tr>
                            <tr style="height: 36px;font-size: 16px;">
                                <td style="padding-bottom: 10px;" [ngClass]="{'prog' :'In Progress' == complaintDetails.COMPLAINT_STATUS_NAME , 'new' :'New' == complaintDetails.COMPLAINT_STATUS_NAME , 'hold': 'On Hold' == complaintDetails.COMPLAINT_STATUS_NAME ,'resolve' : 'Resolution' == complaintDetails.COMPLAINT_STATUS_NAME, 'close' : 'Closed' == complaintDetails.COMPLAINT_STATUS_NAME, 'Out-of-remit' : 'Out of remit/Outside ASCI Purview' == complaintDetails.COMPLAINT_STATUS_NAME || 'Sub-Judice' == complaintDetails.COMPLAINT_STATUS_NAME, 'invalid' : 'Non-Issue' == complaintDetails.COMPLAINT_STATUS_NAME}">{{complaintDetails.COMPLAINT_STATUS_NAME}}</td>
                            </tr>
                            <mat-divider></mat-divider>
                            <tr style="height: 51px;color: #92A2B1;">
                                <th>Priority</th>
                            </tr>
                            <tr style="height: 36px;font-size: 16px;">
                                <td style="padding-bottom: 10px;" [ngClass]="{'urgent' :'Urgent' == complaintDetails.PRIORITY_NAME, 'high' :'High' == complaintDetails.PRIORITY_NAME ,'mid': 'Medium' == complaintDetails.PRIORITY_NAME , 'low' : 'Low' == complaintDetails.PRIORITY_NAME}">{{complaintDetails.PRIORITY_NAME}}</td>
                            </tr>
                            <mat-divider></mat-divider>
                            <tr style="height: 51px;color: #92A2B1;">
                                <th>Complaint Description</th>
                            </tr>
                            <tr style="height: 36px;font-size: 16px;">
                                <td style="padding-bottom: 10px; width: 100%;" [innerHTML]="safeHTML(complaintDetails.COMPLAINT_DESCRIPTION)">{{complaintDetails.COMPLAINT_DESCRIPTION}}</td>
                            </tr>
                            <mat-divider></mat-divider>
                            <tr style="height: 51px;color: #92A2B1;">
                                <th>Advertisement Description</th>
                            </tr>
                            <tr style="height: 36px;font-size: 16px;">
                                <td style="padding-bottom: 10px; width: 100%;" [innerHTML]="safeHTML(complaintDetails.ADVERTISEMENT_DESCRIPTION)">{{complaintDetails.ADVERTISEMENT_DESCRIPTION}}</td>
                            </tr>
                            <mat-divider></mat-divider>
                            <tr style="height: 51px;color: #92A2B1;">
                                <th>Classification</th>
                            </tr>
                            <tr style="height: 36px;font-size: 16px;">
                                <td style="padding-bottom: 10px;">{{complaintDetails.CLASSIFICATION_NAME}}</td>
                            </tr>
                            <mat-divider></mat-divider>
                            <tr style="height: 51px;color: #92A2B1;">
                                <th>Stage</th>
                            </tr>
                            <tr style="height: 36px;font-size: 16px;">
                                <td style="padding-bottom: 10px;">{{complaintDetails.STAGE_NAME}}</td>
                            </tr>
                            <mat-divider></mat-divider>
                            <tr style="height: 51px;color: #92A2B1;">
                                <th>Receive Date</th>
                            </tr>
                            <tr style="height: 36px;font-size: 16px;">
                                <td style="padding-bottom: 10px;">{{complaintDetails.CREATED_DATE | date:'dd/MM/yyyy' }}</td>
                            </tr>
                        </table>
                    </mat-card>
                    <mat-card fxLayout="column" class="comp_details-adv" style="width: auto;margin-top: 16px;">
                        <div>
                            <div>
                                <div class="tabs-container">
                                    <mat-tab-group (selectedTabChange)="onTabChanged($event)" animationDuration="0ms" class="detail-subtab">
                                        <mat-tab>
                                            <div class="tab-head">
                                                <ng-template mat-tab-label>
                                                    <div class="tab-icon">
                                                        <!-- <img src="../../assets/images/Documents.png" style="padding-bottom: 3px;"> -->
                                                        Documents
                                                    </div>
                                                </ng-template>
                                            </div>
                                            <div class="documenttab">
                                                <div fxLayout="row wrap" fxLayoutGap="8px" *ngIf="against_comp">
                                                    <div *ngFor="let doc of userComplaintDocs; let index = index"
                                                        class="file-container1 doc-wrapper">
                                                        <div *ngIf="userComplaintDocs.length != 0">
                                                            <div fxLayout="row" fxLayoutGap="5px"
                                                                style="vertical-align: middle;align-items: center;">
                                                                <div
                                                                    style="height: 40px; width: 40px; background: #3A3A3A; border-radius: 4px; position: relative; top: -1px;">
                                                                    <img src="../assets/images/doc_video.svg"
                                                                        style="margin: 15px 14px;">
                                                                </div>
                                                                <div fxLayout="column">
                                                                    <div style="color: #000000; font-size: 12px" class="doc-caption1">
                                                                        <p mat-line matTooltip="{{doc.ATTACHMENT_NAME}}">{{doc.ATTACHMENT_NAME}}</p>
                                                                        <br><span style="margin-left: 6px;">{{doc.DATE |  date: 'dd/MM/yyyy h:mm a'}}</span>
                                                                    </div>
                                                                    <!-- <div
                                                                        style="color: rgba(0, 0, 0, 0.6); font-size: 10px">
                                                                        {{doc.duration}} <span
                                                                            style="margin-left: 10px;">By :
                                                                            {{doc.by}}</span></div> -->
                                                                </div>
                                                                <div style="left: 5px;">
                                                                    <button mat-icon-button [matMenuTriggerFor]="admin"><mat-icon>more_vert</mat-icon></button>
                                                                </div>
                                                                <mat-menu #admin="matMenu" class="action-buttons">
                                                                    <div class="admin-option-container">
                                                                        <button mat-menu-item class="option-btn" (click)="preview(doc.ATTACHMENT_SOURCE)">
                                                                            <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                            <span class="option-text">Preview</span>
                                                                        </button>
                                                                        <mat-divider class="option-divider">
                                                                        </mat-divider>
                                                                        <button mat-menu-item class="option-btn" (click)="download(doc.ATTACHMENT_NAME, doc.ATTACHMENT_SOURCE)">
                                                                            <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                            <span class="option-text">Download</span>
                                                                        </button>
                                                                    </div>
                                                                </mat-menu>
                                                                <!-- <div class="removeIcon" style="position: relative">
                                                                    <button mat-button *ngIf="doc.FIELD_TAB == 'advertiser'" matSuffix
                                                                        mat-icon-button aria-label="Clear"
                                                                        (click)="removeSelectedFile(file.COMPLAINT_ID, file.ATTACHMENT_SOURCE)">
                                                                        <img src="../assets/images/Trash-icon.svg"
                                                                            style="border: 1px;">
                                                                    </button>
                                                                </div> -->
                                                            </div>
                                                            <!-- <mat-divider style="position: relative;"></mat-divider> -->
                                                        </div>
                                                    </div>
                                                        <div *ngFor="let file of complainantFiles; let index = index" class="file-container1 doc-wrapper">
                                                            <div>
                                                                <div fxLayout="row" fxLayoutGap="5px"
                                                                    style="vertical-align: middle;align-items: center;">
                                                                    <div
                                                                        style="height: 40px; width: 40px; background: #3A3A3A; border-radius: 4px; position: relative; top: -1px;">
                                                                        <img src="../assets/images/doc_video.svg"
                                                                            style="margin: 15px 14px;">
                                                                    </div>
                                                                    <div fxLayout="column">
                                                                        <div style="color: #000000; font-size: 12px" class="doc-caption1">
                                                                            <p mat-line matTooltip="{{file.ATTACHMENT_SOURCE_NAME}}">{{file.ATTACHMENT_SOURCE_NAME}}</p>
                                                                            <br><span style="margin-left: 6px;">{{file.CREATED_DATE|  date: 'dd/MM/yyyy h:mm a'}}</span>
                                                                        </div>

                                                                        <!-- <div
                                                                            style="color: rgba(0, 0, 0, 0.6); font-size: 10px">
                                                                            0.30s -->
                                                                            <!-- <span style="margin-left: 10px;">By : You</span>
                                                                                {{doc.by}}</span></div> -->

                                                                    </div>
                                                                    <div style="left: 5px;">
                                                                        <button mat-icon-button [matMenuTriggerFor]="admin"><mat-icon>more_vert</mat-icon></button>
                                                                    </div>
                                                                    <mat-menu #admin="matMenu" class="action-buttons">
                                                                        <div class="admin-option-container">
                                                                            <button mat-menu-item class="option-btn" (click)="preview(file.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                                <span class="option-text">Preview</span>
                                                                            </button>
                                                                            <mat-divider class="option-divider">
                                                                            </mat-divider>
                                                                            <button mat-menu-item class="option-btn" (click)="download(file.ATTACHMENT_SOURCE_NAME, file.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                                <span class="option-text">Download</span>
                                                                            </button>
                                                                        </div>
                                                                    </mat-menu>
                                                                    <div class="removeIcon" style="position: relative;">
                                                                        <button mat-button *ngIf="value" matSuffix
                                                                            mat-icon-button aria-label="Clear" style="margin-left: -15px;"
                                                                            (click)="removeFile(file.ID, file.ATTACHMENT_SOURCE)">
                                                                            <img src="../../../assets/images/delete-red.png"
                                                                                style="border: 1px;">
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                                <!-- <mat-divider style="position: relative;"></mat-divider> -->
                                                            </div>
                                                        </div>
                                                        <div *ngFor="let file of advertiserFiles; let index = index" class="file-container1 doc-wrapper">
                                                            <div>
                                                                <div fxLayout="row" fxLayoutGap="5px"
                                                                    style="vertical-align: middle;align-items: center;">
                                                                    <div
                                                                        style="height: 40px; width: 40px; background: #3A3A3A; border-radius: 4px; position: relative; top: -1px;">
                                                                        <img src="../assets/images/doc_video.svg"
                                                                            style="margin: 15px 14px;">
                                                                    </div>
                                                                    <div fxLayout="column">
                                                                        <div style="color: #000000; font-size: 12px" class="doc-caption1">
                                                                            <p mat-line matTooltip="{{file.ATTACHMENT_SOURCE_NAME}}">{{file.ATTACHMENT_SOURCE_NAME}}</p>
                                                                            <br><span style="margin-left: 6px;">{{file.CREATED_DATE|  date: 'dd/MM/yyyy h:mm a'}}</span>
                                                                        </div>

                                                                        <!-- <div
                                                                            style="color: rgba(0, 0, 0, 0.6); font-size: 10px">
                                                                            0.30s -->
                                                                            <!-- <span style="margin-left: 10px;">By : You</span>
                                                                                {{doc.by}}</span></div> -->

                                                                    </div>
                                                                    <div style="left: 5px;">
                                                                        <button mat-icon-button [matMenuTriggerFor]="admin"><mat-icon>more_vert</mat-icon></button>
                                                                    </div>
                                                                    <mat-menu #admin="matMenu" class="action-buttons">
                                                                        <div class="admin-option-container">
                                                                            <button mat-menu-item class="option-btn" (click)="preview(file.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                                <span class="option-text">Preview</span>
                                                                            </button>
                                                                            <mat-divider class="option-divider">
                                                                            </mat-divider>
                                                                            <button mat-menu-item class="option-btn" (click)="download(file.ATTACHMENT_SOURCE_NAME, file.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                                <span class="option-text">Download</span>
                                                                            </button>
                                                                        </div>
                                                                    </mat-menu>
                                                                    <div class="removeIcon" style="position: relative;">
                                                                        <button mat-button *ngIf="value" matSuffix
                                                                            mat-icon-button aria-label="Clear" style="margin-left: -15px;"
                                                                            (click)="removeFile(file.ID, file.ATTACHMENT_SOURCE)">
                                                                            <img src="../../../assets/images/delete-red.png"
                                                                                style="border: 1px;">
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                                <!-- <mat-divider style="position: relative;"></mat-divider> -->
                                                            </div>
                                                        </div>
                                                        <div *ngFor="let file of internalFiles; let index = index" class="file-container1 doc-wrapper">
                                                            <div>
                                                                <div fxLayout="row" fxLayoutGap="5px"
                                                                    style="vertical-align: middle;align-items: center;">
                                                                    <div
                                                                        style="height: 40px; width: 40px; background: #3A3A3A; border-radius: 4px; position: relative; top: -1px;">
                                                                        <img src="../assets/images/doc_video.svg"
                                                                            style="margin: 15px 14px;">
                                                                    </div>
                                                                    <div fxLayout="column">
                                                                        <div style="color: #000000; font-size: 12px" class="doc-caption1">
                                                                            <p mat-line matTooltip="{{file.ATTACHMENT_SOURCE_NAME}}">{{file.ATTACHMENT_SOURCE_NAME}}</p>
                                                                            <br><span style="margin-left: 6px;">{{file.CREATED_DATE|  date: 'dd/MM/yyyy h:mm a'}}</span>
                                                                        </div>

                                                                        <!-- <div
                                                                            style="color: rgba(0, 0, 0, 0.6); font-size: 10px">
                                                                            0.30s -->
                                                                            <!-- <span style="margin-left: 10px;">By : You</span>
                                                                                {{doc.by}}</span></div> -->

                                                                    </div>
                                                                    <div style="left: 5px;">
                                                                        <button mat-icon-button [matMenuTriggerFor]="admin"><mat-icon>more_vert</mat-icon></button>
                                                                    </div>
                                                                    <mat-menu #admin="matMenu" class="action-buttons">
                                                                        <div class="admin-option-container">
                                                                            <button mat-menu-item class="option-btn" (click)="preview(file.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                                <span class="option-text">Preview</span>
                                                                            </button>
                                                                            <mat-divider class="option-divider">
                                                                            </mat-divider>
                                                                            <button mat-menu-item class="option-btn" (click)="download(file.ATTACHMENT_SOURCE_NAME, file.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                                <span class="option-text">Download</span>
                                                                            </button>
                                                                        </div>
                                                                    </mat-menu>
                                                                </div>
                                                                <!-- <mat-divider style="position: relative;"></mat-divider> -->
                                                            </div>
                                                        </div>
                                                </div>
                                                <span style="flex: 1 1 auto;"></span>
                                                <div fxLayoutAlign="end end" class="attach_doc" *ngIf="against_comp">
                                                    <img src="../../assets/images/Upload_attachment_new.png"
                                                        (click)="fileInput.click()" style="cursor: pointer;">
                                                    <input style="display: none" #attachments type="file"
                                                        (change)="onFileChange($event)" #fileInput
                                                        accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt,.jpeg,.png,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv"
                                                        multiple="true">
                                                </div>
                                                <div fxLayout="row wrap" fxLayoutGap="8px" class="docs_attached_by"
                                                    *ngIf="!against_comp">
                                                    <div *ngFor="let doc of userComplaintDocs; let index = index"
                                                        class="file-container1 doc-wrapper">
                                                        <div fxLayoutGap="5px" *ngIf="userComplaintDocs.length != 0">
                                                            <div fxLayout="row" fxLayoutGap="5px"
                                                                style="vertical-align: middle;align-items: center;">
                                                                <div
                                                                    style="height: 40px; width: 40px; background: #3A3A3A; border-radius: 4px; position: relative; top: -1px;">
                                                                    <img src="../assets/images/doc_video.svg"
                                                                        style="margin: 15px 14px;">
                                                                </div>
                                                                <div fxLayout="column">
                                                                    <div style="color: #000000; font-size: 12px" class="doc-caption1">
                                                                        <p mat-line matTooltip="{{doc.ATTACHMENT_NAME}}">{{doc.ATTACHMENT_NAME}}</p>
                                                                        <br><span style="margin-left: 6px;">{{doc.DATE |  date: 'dd/MM/yyyy h:mm a'}}</span>
                                                                    </div>
                                                                    <div
                                                                        style="color: rgba(0, 0, 0, 0.6); font-size: 10px">
                                                                        {{doc.duration}}</div>
                                                                </div>
                                                                <div style="left: 5px;">
                                                                    <button mat-icon-button [matMenuTriggerFor]="admin"><mat-icon>more_vert</mat-icon></button>
                                                                </div>
                                                                <mat-menu #admin="matMenu" class="action-buttons">
                                                                    <div class="admin-option-container">
                                                                        <button mat-menu-item class="option-btn" (click)="preview(doc.ATTACHMENT_SOURCE)">
                                                                            <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                            <span class="option-text">Preview</span>
                                                                        </button>
                                                                        <mat-divider class="option-divider">
                                                                        </mat-divider>
                                                                        <button mat-menu-item class="option-btn" (click)="download(doc.ATTACHMENT_NAME, doc.ATTACHMENT_SOURCE)">
                                                                            <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                            <span class="option-text">Download</span>
                                                                        </button>
                                                                    </div>
                                                                </mat-menu>
                                                            </div>
                                                            <div style="width: 100%;">
                                                                <!-- <mat-divider class="divider" style="position: relative;"></mat-divider> -->
                                                            </div>
                                                        </div>
                                                    </div>
                                                        <div *ngFor="let doc of complainantFiles; let index = index" class="file-container1 doc-wrapper">
                                                            <div fxLayoutGap="5px" *ngIf="doc.ATTACHMENT_SOURCE != null || doc.ATTACHMENT_SOURCE != ''">
                                                                <div fxLayout="row" fxLayoutGap="5px"
                                                                    style="vertical-align: middle;align-items: center;">
                                                                    <div
                                                                        style="height: 40px; width: 40px; background: #3A3A3A; border-radius: 4px; position: relative; top: -1px;">
                                                                        <img src="../assets/images/doc_video.svg"
                                                                            style="margin: 15px 14px;">
                                                                    </div>
                                                                    <div fxLayout="column">
                                                                        <div style="color: #000000; font-size: 12px" class="doc-caption1">
                                                                            <p mat-line matTooltip="{{doc.ATTACHMENT_SOURCE_NAME}}">{{doc.ATTACHMENT_SOURCE_NAME}}</p>
                                                                            <br><span style="margin-left: 6px;">{{doc.CREATED_DATE |  date: 'dd/MM/yyyy h:mm a'}}</span>
                                                                        </div>
                                                                        <div
                                                                            style="color: rgba(0, 0, 0, 0.6); font-size: 10px">
                                                                            {{doc.duration}}</div>
                                                                    </div>
                                                                    <div style="left: 5px;">
                                                                        <button mat-icon-button [matMenuTriggerFor]="admin"><mat-icon>more_vert</mat-icon></button>
                                                                    </div>
                                                                    <mat-menu #admin="matMenu" class="action-buttons">
                                                                        <div class="admin-option-container">
                                                                            <button mat-menu-item class="option-btn" (click)="preview(doc.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                                <span class="option-text">Preview</span>
                                                                            </button>
                                                                            <mat-divider class="option-divider">
                                                                            </mat-divider>
                                                                            <button mat-menu-item class="option-btn" (click)="download(doc.ATTACHMENT_SOURCE_NAME, doc.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                                <span class="option-text">Download</span>
                                                                            </button>
                                                                        </div>
                                                                    </mat-menu>
                                                                </div>
                                                                <div style="width: 100%;">
                                                                    <!-- <mat-divider class="divider" style="position: relative;"></mat-divider> -->
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div *ngFor="let doc of internalFiles; let index = index" class="file-container1 doc-wrapper">
                                                            <div fxLayoutGap="5px" *ngIf="doc.ATTACHMENT_SOURCE != null || doc.ATTACHMENT_SOURCE != ''">
                                                                <div fxLayout="row" fxLayoutGap="5px"
                                                                    style="vertical-align: middle;align-items: center;">
                                                                    <div
                                                                        style="height: 40px; width: 40px; background: #3A3A3A; border-radius: 4px; position: relative; top: -1px;">
                                                                        <img src="../assets/images/doc_video.svg"
                                                                            style="margin: 15px 14px;">
                                                                    </div>
                                                                    <div fxLayout="column">
                                                                        <div style="color: #000000; font-size: 12px" class="doc-caption1">
                                                                            <p mat-line matTooltip="{{doc.ATTACHMENT_SOURCE_NAME}}">{{doc.ATTACHMENT_SOURCE_NAME}}</p>
                                                                            <br><span style="margin-left: 6px;">{{doc.CREATED_DATE |  date: 'dd/MM/yyyy h:mm a'}}</span>
                                                                        </div>
                                                                        <div
                                                                            style="color: rgba(0, 0, 0, 0.6); font-size: 10px">
                                                                            {{doc.duration}}</div>
                                                                    </div>
                                                                    <div style="left: 5px;">
                                                                        <button mat-icon-button [matMenuTriggerFor]="admin"><mat-icon>more_vert</mat-icon></button>
                                                                    </div>
                                                                    <mat-menu #admin="matMenu" class="action-buttons">
                                                                        <div class="admin-option-container">
                                                                            <button mat-menu-item class="option-btn" (click)="preview(doc.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                                                                <span class="option-text">Preview</span>
                                                                            </button>
                                                                            <mat-divider class="option-divider">
                                                                            </mat-divider>
                                                                            <button mat-menu-item class="option-btn" (click)="download(doc.ATTACHMENT_SOURCE_NAME, doc.ATTACHMENT_SOURCE)">
                                                                                <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                                                                                <span class="option-text">Download</span>
                                                                            </button>
                                                                        </div>
                                                                    </mat-menu>
                                                                </div>
                                                                <!-- <div style="width: 100%;">
                                                                    <mat-divider class="divider" style="position: relative;"></mat-divider>
                                                                </div> -->
                                                            </div>
                                                        </div>
                                                </div>
                                            </div>
                                        </mat-tab>
                                        <mat-tab>
                                            <div class="tab-head">
                                                <ng-template mat-tab-label>
                                                    <div class="tab-icon">
                                                        <!-- <img src="../../assets/images/Clock.png"> -->
                                                        Timeline
                                                    </div>
                                                </ng-template>
                                            </div>
                                            <div class="time-container">
                                                <div *ngIf="!timeLineExist" class="no-timeline">
                                                    No timeline created ....
                                                </div>
                                                <div class="timeline" *ngIf="!timelineLoading && timeLineExist">
                                                    <ul>
                                                        <li *ngFor="let item of complaintTimeline">
                                                            <div mat-line>
                                                                <p *ngFor="let val of item.updates">
                                                                    <span class="time-enents">{{val.label}} &nbsp;</span>
                                                                    <span
                                                                        style="font-style: normal;font-weight: normal;font-size: 14px;color: #000000;">{{val.value}}
                                                                    </span>
                                                                </p>
                                                            </div>
                                                            <div>
                                                                <p style="font-style: normal;font-weight: normal;font-size: 12px;color: #555454;">{{item.date}}</p>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <mat-card *ngIf="timelineLoading"
                                                    style="height:200px; width: 100%; display: flex; justify-content: center; align-items: center; background: rgb(238, 237, 237)">
                                                    <mat-progress-spinner color="primary" mode="indeterminate" diameter="70">
                                                    </mat-progress-spinner>
                                                </mat-card>
                                            </div>
                                        </mat-tab>
                                        <mat-tab>
                                            <div class="tab-head">
                                                <ng-template mat-tab-label>
                                                    <div class="tab-icon">
                                                        Claims
                                                    </div>
                                                </ng-template>
                                            </div>
                                            <div class="no-claims" *ngIf="complaintClaims.length == 0 && complaintCodeViolated.length == 0">
                                                No claims raised...
                                            </div>
                                            <div fxLayout="column" fxLayoutGap="10px" *ngIf="complaintClaims.length != 0 || complaintCodeViolated.length != 0" class="claims-tab">
                                                <mat-accordion>
                                                    <mat-expansion-panel class="intra-expansion" (opened)="panelOpenState = true" (closed)="panelOpenState = false">
                                                        <mat-expansion-panel-header class="panel-header" style="height: 55px;">
                                                            <mat-panel-title class="panel-title">
                                                                Claim Details
                                                            </mat-panel-title>
                                                        </mat-expansion-panel-header>
                                                        <div class="intra-divider" style="width: 100%; border-radius: 1px; margin-bottom: 2%;">
                                                            <mat-divider></mat-divider>
                                                        </div>
                                                        <div class="intra-body" fxLayout="column" fxLayoutGap="10px">
                                                            <div class="raised-container1" fxLayout="column">
                                                                <div class="head-container1" fxLayout="row" fxLayoutGap="10px">
                                                                    <div class="arrow-icon-container">
                                                                        <img class="arrow-img-icon" src="../assets/images/arrow_circle-icon.svg " />
                                                                    </div>
                                                                    <div>
                                                                        <h3 class="intra-h3">Claims raised</h3>
                                                                    </div>
                                                                </div>
                                                                <div class="claim-challenges-container" fxLayout="column" fxLayoutGap="10px" style="margin-top: 1%;">
                                                                    <div class="challenge-container" *ngFor="let item of complaintClaims; let in = index;">
                                                                        <div class="panel-title">Claim challenged {{in+1}} </div>
                                                                        <div fxLayout="column">
                                                                            <div class="attribute-container width50">
                                                                                <p>
                                                                                    <span class="grey-text">Name :</span><br> {{item.CLAIM_CHALLENGED}}
                                                                                </p>
                                                                            </div>
                                                                            <div class="attribute-container width50">
                                                                                <p>
                                                                                    <span class="grey-text">Annexure no. :</span><br> {{item.ANNEXURE_NO}}
                                                                                </p>
                                                                            </div>
                                                                            <div class="attribute-container">
                                                                                <p>
                                                                                    <span class="grey-text"> Key objection :</span><br>
                                                                                    <span [innerHTML]="safeHTML(item.KEY_OBJECTION)">{{item.KEY_OBJECTION}}</span>
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="violated-container1" fxLayout="column" fxLayoutGap="5px">
                                                                <div>
                                                                    <div class="head-container1" fxLayout="row" fxLayoutGap="10px">
                                                                        <div class="arrow-icon-container">
                                                                            <img class="arrow-img-icon" src="../assets/images/arrow_circle-icon.svg " />
                                                                        </div>
                                                                        <div>
                                                                            <h3 class="intra-h3">ASCI code violated </h3>
                                                                        </div>
                                                                    </div>
                                                                    <div class="chapters-container">
                                                                        <div class="chapter-text" *ngFor="let item of complaintCodeViolated">
                                                                            <div fxLayout="column">
                                                                                <span>
                                                                                    <mat-icon class="circle-icon">lens</mat-icon>Chapter {{item.CHAPTER_ID}}
                                                                                    <ng-container *ngIf="item.CLAUSES_ID"> : </ng-container>
                                                                                </span>
                                                                                <span style="width:90%;margin-left: 25px;">{{item.CLAUSES_ID}}</span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div>
                                                                    <div class="head-container1" fxLayout="row" fxLayoutGap="10px">
                                                                        <div class="arrow-icon-container">
                                                                            <img class="arrow-img-icon" src="../assets/images/arrow_circle-icon.svg " />
                                                                        </div>
                                                                        <div>
                                                                            <h3 class="intra-h3">ASCI Guideline code violated </h3>
                                                                        </div>
                                                                    </div>
                                                                    <div class="chapters-container">
                                                                        <div class="chapter-text" *ngFor="let item of guidelines">
                                                                            <div fxLayout="column">
                                                                                <span>
                                                                                    <mat-icon class="circle-icon">lens</mat-icon>Guideline <span
                                                                                        *ngIf="item.G_CHAPTER_ID != 1">{{item.G_CHAPTER_ID - 1}}</span>
                                                                                    <span *ngIf="item.G_CHAPTER_ID == 1">N/A</span>
                                                                                    <ng-container
                                                                                        *ngIf="item.G_CHAPTER_ID != 1 && item.G_CHAPTER_ID != 2 && item.G_CHAPTER_ID != 7 && item.G_CHAPTER_ID != 8">
                                                                                        : </ng-container>
                                                                                </span>
                                                                                <span style="width:90%;margin-left: 25px;" *ngIf="item.G_CHAPTER_ID != 1">
                                                                                    {{item.G_CLAUSES_ID}}
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                
                                                            <div fxLayout="column" fxLayoutGap="10px">
                                                                <button mat-button (click)="viewDocument()" class="violated-popups">
                                                                    <div>
                                                                        <span class="bolder">
                                                                            <img src="..\assets\images\folder-icon.svg">&nbsp; View documents
                                                                        </span>
                                                                    </div>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </mat-expansion-panel>
                                                </mat-accordion>
                                            </div>              
                                        </mat-tab>
                                    </mat-tab-group>
                                </div>
                            </div>
                        </div>
                    </mat-card>
                </div>
            </div>
        </div>

        <div *ngIf="newComplaint && userComp">
          <app-general-public (outFilter)="setFromFilterOutput($event)"></app-general-public>
        </div>
        <div *ngIf="newComplaint && advertiserComp">
            <app-intra-industry (outFilter)="setFromFilterOutput($event)"></app-intra-industry>
        </div>
    </div>
</div>