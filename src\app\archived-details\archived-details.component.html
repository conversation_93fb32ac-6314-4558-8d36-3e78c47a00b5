<div class="complaint-container">
    <mat-card *ngIf="loadingDetails"
        style="display: flex; justify-content: center; align-items: center; position: relative; top: 172px; width: 98%; box-shadow: none;background: transparent;">
        <mat-progress-spinner color="primary" mode="indeterminate" diameter="70">
        </mat-progress-spinner>
    </mat-card>
    <div class="mat-card-container" *ngIf="!loadingDetails" fxLayout="column" fxLayoutGap="1%">
        <div class="mat-card-scroll" fxLayout="column" fxLayoutGap="3%">
            <div class="details-content-container" fxLayout="column">
                <div class="comp-head-container" fxLayout="row">
                    <div class="comp-head" style="padding-top: 14px; width: 20%;">ARCHIVED COMPLAINT
                    </div>
                    <div fxFlex="82%"></div>
                    <div class="close-div">
                        <button mat-icon-button class="close-btn" mat-dialog-close>
                            <mat-icon style="margin-bottom: 4px;">close</mat-icon>
                        </button>
                    </div>
                </div>
                <div class="top-divider-container">
                    <mat-divider class="classfy-head-divider"></mat-divider>
                </div>
                <div style=" background-color:#F8F9F9;margin-top: 2%">
                    <div fxLayout="row" style="padding-top: 14px; padding-bottom: 18px;">
                        <div>
                            <span style="color: #999999; margin-left: 13px;">Case ID : {{archived_details?.CASE_ID}} </span><span style="color: #999999;" *ngIf="archived_details.OLD_CASE_ID != null && archived_details.OLD_CASE_ID != ''">({{archived_details?.OLD_CASE_ID}})</span>
                        </div>
                    </div>
                    <div class="contents-scroll">
                        <div class="details-container" fxLayout="column" fxLayoutGap="1%">
                            <div class="classfy-head">
                                <div class="comp-head" style="font-weight: 550;">COMPLAINT DETAILS</div>
                                <div class="divider-container" style="width: 94%; margin-left: 72px;">
                                    <mat-divider class="classfy-head-divider" [inset]="true"></mat-divider>
                                </div>
                            </div>
                            <div>
                                <div fxLayout="row">
                                    <div class="detail-left-container" fxLayout="column" fxLayoutGap="2px">
                                        <div mat-line>
                                            <p>
                                                <span class="detail-attribute">Company : </span>
                                                <span class="detail-value">{{archived_details?.COMPANY_NAME}} </span>
                                            </p>
                                        </div>
                                        <div mat-line>
                                            <p>
                                                <span class="detail-attribute">Product : </span>
                                                <span class="detail-value">{{archived_details?.PRODUCT_NAME}} </span>
                                            </p>
                                        </div>
                                        <div mat-line>
                                            <p>
                                                <span class="detail-attribute">Complaint type : </span>
                                                <span class="detail-value">{{archived_details?.COMPLAINT_TYPE_NAME}} </span>
                                            </p>
                                        </div>
                                        <div mat-line>
                                            <p>
                                                <span class="detail-attribute">Complaint status : </span>
                                                <span class="detail-value">{{archived_details?.COMPLAINT_STATUS_NAME}} </span>
                                            </p>
                                        </div>
                                        <div mat-line>
                                            <p>
                                                <span class="detail-attribute">Resolution status : </span>
                                                <span class="detail-value">{{archived_details?.RESOLUTION_STATUS_NAME}} </span>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="detail-right-container" fxLayout="column" fxLayoutGap="2px">
                                        <div mat-line>
                                            <p>
                                                <span class="detail-attribute">Brand : </span>
                                                <span class="detail-value">{{archived_details?.BRAND_NAME}} </span>
                                            </p>
                                        </div>
                                        <div mat-line>
                                            <p>
                                                <span class="detail-attribute">Product category : </span>
                                                <span class="detail-value">{{archived_details?.PRODUCT_CATEGORY}} </span>
                                            </p>
                                        </div>
                                        <div mat-line>
                                            <p>
                                                <span class="detail-attribute">Complaint source : </span>
                                                <span class="detail-value">{{archived_details?.COMPLAINT_SOURCE_NAME}} </span>
                                            </p>
                                        </div>
                                        <div mat-line>
                                            <p>
                                                <span class="detail-attribute">Registered date : </span>
                                                <span class="detail-value">{{archived_details?.REGISTERED_DATE | date:'dd-MM-yyyy'}} </span>
                                            </p>
                                        </div>
                                        <div mat-line *ngIf="archived_details?.COMPLIANCE_STATUS_NAME != null && archived_details?.COMPLIANCE_STATUS_NAME != ''">
                                            <p>
                                                <span class="detail-attribute">Compliance : </span>
                                                <span class="detail-value">{{archived_details?.COMPLIANCE_STATUS_NAME}} </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div fxLayout="column" fxLayoutGap="2px">
                                    <div mat-line>
                                        <p>
                                            <span class="detail-attribute">Advertisement description :</span>
                                        </p>
                                    </div>
                                    <div mat-line class="comp-msg-container">
                                        <p class="comp-msg" [innerHTML]="safeHTML(archived_details?.ADVERTISEMENT_DESCRIPTION)">
                                            {{archived_details?.ADVERTISEMENT_DESCRIPTION}}
                                        </p>
                                    </div>
                                    <div mat-line>
                                        <p>
                                            <span class="detail-attribute">Complaint description :</span>
                                        </p>
                                    </div>
                                    <div mat-line class="comp-msg-container">
                                        <p class="comp-msg" [innerHTML]="safeHTML(archived_details?.COMPLAINT_DESCRIPTION)">
                                            {{archived_details?.COMPLAINT_DESCRIPTION}}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="details-container" fxLayout="column" fxLayoutGap="2%">
                            <div class="classfy-head">
                                <div class="comp-head" style="font-weight: 550;">RECOMMENDATIONS</div>
                                <div class="divider-container" style="width: 94%; margin-left: 71px;">
                                    <mat-divider class="classfy-head-divider" [inset]="true"></mat-divider>
                                </div>
                            </div>
                            <div *ngIf="recommendations.length == 0">
                                No recommendations...
                            </div>
                            <div fxLayout="column" fxLayoutGap="1%" *ngIf="recommendations.length != 0">
                                <div *ngFor="let rec of recommendations; let in = index;" fxLayout="column" fxLayoutGap="10px" class="box-type">
                                    <p>
                                        <span class="detail-attribute">Subject : </span>
                                        <span class="detail-value">{{rec.SUBJECT}}</span>
                                    </p>
                                    <p>
                                        <span class="detail-attribute">Description : </span>
                                        <span class="detail-value">{{rec.RECOMMENDATION}}</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="details-container" fxLayout="column" fxLayoutGap="2%">
                            <div class="classfy-head">
                                <div class="comp-head" style="font-weight: 550;">RESOLUTIONS</div>
                                <div class="divider-container" style="width: 98%; margin-left: 28px;">
                                    <mat-divider class="classfy-head-divider" [inset]="true"></mat-divider>
                                </div>
                            </div>
                            <div *ngIf="resolutions.length == 0">
                                No resolutions...
                            </div>
                            <div fxLayout="column" fxLayoutGap="1%" *ngIf="resolutions.length != 0">
                                <div *ngFor="let res of resolutions; let in = index;" fxLayout="column" fxLayoutGap="10px" class="box-type">
                                    <p>
                                        <span class="detail-attribute">Subject : </span>
                                        <span class="detail-value">{{res.SUBJECT}}</span>
                                    </p>
                                    <p>
                                        <span class="detail-attribute">Description : </span>
                                        <span class="detail-value">{{res.RESOLUTION}}</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>