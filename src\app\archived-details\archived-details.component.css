.complaint-container {
    width: 100% !important;
    height: 70vh;
}

.mat-card-scroll {
    height: 70vh;
}

.comp-head {
    font-size: 14px;
    line-height: 16px;
    color: #000000;
    width: -webkit-fill-available;
    margin-bottom: 5px;
    font-weight: 550;
}

.close-btn {
    color: solid rgba(47, 57, 65, 0.6);
    background: #F3F3F3;
    border: 1px solid rgba(47, 57, 65, 0.6);
}

.top-divider-container {
    position: relative;
    top: 23px;
    width: 100%;
    margin-left: 0px !important;
}

.classfy-head-divider {
    position: relative;
}

.contents-scroll {
    height: 65vh;
    -ms-overflow-style: none;
    scrollbar-width: none;
    overflow-y: scroll;
    overflow-x: hidden;
    margin-right: 2px;
}

.contents-scroll::-webkit-scrollbar {
    display: none;
}

.details-container {
    border: 1px solid #D8DCDE;
    background: #ffffff;
    margin-right: 12px;
    padding: 21px;
    box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.08);
    margin-bottom: 21px;
    margin-left: 12px;
}

.classfy-head {
    position: relative;
    margin-top: 10px;
    display: flex;
}

.divider-container {
    position: absolute;
    top: 8px;
}

.classfy-head-divider {
    position: relative;
}

.detail-left-container {
    width: 60%;
}

.detail-right-container {
    width: 40%;
}

.detail-attribute {
    color: rgba(0, 0, 0, 0.4);
    font-size: 14px;
    line-height: 19px;
    font-weight: bolder;
}

.detail-value {
    color: #2F3941;
    line-height: 19px;
    font-size: 14px;
    color: #000000
}

.comp-msg-container {
    width: 95%;
    text-align: justify;
    font-size: 14px;
    line-height: 19px;
}

.box-type {
    border: 1px solid #D8DCDE;
    border-radius: 10px;
    padding: 10px 10px 0px 10px;
}