.file-container {
  width: 100% !important;
  height: 70vh;
}
.mat-card-scroll {
  height: 50vh;
}
.file-head-container{
    position: relative;
    margin-top: 10px;
    display: flex;
    /* grid-gap: 10px; */
}
.file-head {
  font-size: 15px;
  line-height: 16px;
  color: #000000;
  margin-bottom: 5px;
  font-weight: 550;
}
.close-btn {
  color: solid rgba(47, 57, 65, 0.6);
  background: #F3F3F3;
  border: 1px solid rgba(47, 57, 65, 0.6);
}
.classfy-head-divider {
  position: relative;
}
.file-upload{
  margin-top: 20px;
  border: 1px solid rgba(0,0,0,.12);
}
.browse-files {
  margin:24px;
  border: 1px dotted rgba(0,0,0,.12);
}
.browse-file-heading {
  position: relative;
  bottom: 25px;
  margin-left: 127px;
}
.panel-body1{
  background: #FFFFFF;
  border: 1px solid #CFD7DF;
  box-sizing: border-box;
  border-radius: 4px;
  width: 480px;
  height: 28px;
  margin-left: 23px;
  margin-bottom: 36px;
}
.doc-btn{
  background: rgba(0, 136, 203, 0.25);
  border-radius: 3px 0px 0px 3px;
  width: 26px;
  height: 26px;
}
.link-container{
  width: 87%;
  padding-left: 7px;
  padding-top: 2px;
}
.upload-buttons{
  margin-left: 83%;
  margin-top: 10px;
  margin-bottom: 10px;
  /* margin-right: 8px; */
}
.upload-button {
  width: 80px;
  border-radius: 12px;
  background-color: #0088CB;
  color: white;
  height: 35px;
}
.file-history {
  margin-left: 36px;
}
.nams-table{
  height: 400px;
  /* overflow-y: scroll;
  overflow-x: hidden; */
  overflow: scroll;
}
table {
  width: 100%;
  border: 1px solid rgba(0,0,0,.12);
  margin-top: 21px;
}
.close-icon{
  width: 13px;
  height: 13px;
  position: relative;
  bottom: 3px;
  margin: 0px 10px 10px 10px;
}
