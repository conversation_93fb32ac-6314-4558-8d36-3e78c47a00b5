.comp-head {
  margin-left: 20px;
  color: #000000;
  font-style: normal;
  font-weight: 700;
  font-size: 13px;
  line-height: 16px;
  padding-top: 10px;
}

.comp-head-mandatory {
  color: red;
  margin-left: 20px;
  position: relative;
  top: 7px;
  font-style: normal;
  font-weight: normal;
  font-size: 11px;
  line-height: 13px;
}

.row-container {
  display: flex;
  flex-direction: row;
  /* gap:30px; */
  margin-left: 20px;
  /* margin-right: 30px; */
}

.filename-holder {
  border: 1px solid #436ab3;
  width: 82%;
  padding: 6.5px;
}

.progress-bar-upload {
  margin-left: 20px;
  width: 82%;
}

@keyframes spinner {
  to {transform: rotate(360deg);}
}

.spinner:before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  margin-left: -10px;
  border-radius: 50%;
  border: 2px solid #ccc;
  border-top-color: #000;
  animation: spinner .6s linear infinite;
}

.row-container>div {
  margin-bottom: 8px;
  margin-right: 30px;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 0px;
  /* width: 300px; */
}

.text-container {
  height: 20px;
  margin-bottom: 0px;
  /* border: 1px solid palevioletred; */
}

.control-container {
  /* height: 40px; */
  margin-top: 0px;
  /* border: 1px solid green; */
}

.input-field {
  width: 240px !important;
}

.adv-input-field{
  width: 300px !important;
}

.upload-container {
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.upload-btn {
  background-color: rgb(190, 202, 212);
  color: rgb(88, 88, 88);
  width: 160px;
  height: 35px;
  /* display: inline-block; */
}

.upload-container input[type=file] {
  font-size: 100px;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
}

#url-icon {
  /* border: 1px solid brown; */
  font-size: small;
  margin-bottom: 10px;
  position: relative;
  top: -7px;
  /* vertical-align: middle; */
}

.textarea-field {
  width: 783px !important;
}

.divider-container {
  /* border: aquamarine; */
  width: 100%;
  margin: 10px 20px 10px 20px;
  /* TRBL */
}

.btn-container {
  display: flex;
  flex-direction: row;
  /* gap:10px; */
  margin-left: 20px;
}

.btn-container>div {
  margin-right: 10px;
}

.next-btn {
  width: 80px;
  border-radius: 15px;
  background-color: #0088CB;
  color: white;
}

.back-btn {
  width: 80px;
  border-radius: 15px;
}

.outer-btn-container {
  /* border: 1px solid rgb(40, 70, 10); */
  margin-bottom: 10px;
  width: 85%;
  color: #8b8b8b;
}

.outer-row-container {
  background: #F8F9F9;
  border: 1px solid #CFD7DF;
  box-sizing: border-box;
  border-radius: 4px;

  margin-left: 20px;
  margin-right: 20px;
  padding-right: 20px;
  padding-top: 10px;
  margin-bottom: 3%;
}

.delete-red {
  background: #FFFFFF;
  border: 1px solid #CFD7DF;
  box-sizing: border-box;
  border-radius: 4px;
  height: 30px;
  margin-top: -1%;
}

.inner-row-container {
  display: flex;
  flex-direction: row;
  gap: 30px;
  margin-left: 20px;
}

.add-source-button {
  background: #FFFFFF;
  color: #5A6F84;
  border: 1px solid #CFD7DF;
  box-sizing: border-box;
  border-radius: 12px;
  justify-content: center;
  margin-bottom: 10px;
}

.reech-link {
  width: 588px
}

.tams-link {
  width: 320px;
}

