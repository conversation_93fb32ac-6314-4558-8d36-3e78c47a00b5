import { HttpClient, HttpEvent, HttpEventType, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class UploadService {
  public resourceUrl = `${environment.API_BASE_URL}`;

  constructor(private http: HttpClient) { }

  /**
  * GET SIGNED URL FOR CLAIMS DOC
  * @param obj = {id-compalintID,
  *  section- realted(claims or attachement doc, or media doc etc..)
  * filename - upload file name for Path} 
  * @returns = {SINED_URL(using this url to upload file), PATH(location where we uploaded)}
  */
  getSignedUrl(obj: Object) {
    let options = { headers: this.getHeaders() };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint/get-presigned-url?ID=${obj['id']}&SECTION=${obj['section']}&FILE_NAME=${obj['filename']}&FILE_TYPE=${obj['type']}`, options)
      .toPromise();
  }

    /**
  * GET SIGNED URL FOR CLAIMS DOC
  * @param obj = {id-compalintID,
  *  section- realted(claims or attachement doc, or media doc etc..)
  * filename - upload file name for Path} 
  * @returns = {SINED_URL(using this url to upload file), PATH(location where we uploaded)}
  */
    getSignedUrlForTask(obj: Object) {
      let options = { headers: this.getHeaders() };
      return this.http.get<any>(`${this.resourceUrl}/web/asci/task/get-presigned-url?ID=${obj['id']}&TASK_ID=${obj['taskid']}&FILE_NAME=${obj['filename']}&FILE_TYPE=${obj['type']}`, options)
        .toPromise();
    }

    getSignedUrlForChatbot(obj: Object) {
      let options = { headers: this.getHeaders() };
      return this.http.get<any>(`${this.resourceUrl}/web/asci/chatbot/complaint/get-presigned-url?ID=${obj['id']}&SECTION=${obj['section']}&FILE_NAME=${obj['filename']}&FILE_TYPE=${obj['type']}`, options)
        .toPromise();
    }

  /**
 * UPLOAD FILE TO S3 USING SINGED URL
 * @param obj = {ulr- singedURL of s3,
 *  file - file which we selected in input
 * onprogress - track progress of the file } 
 * @returns ={Http event will send progress of file and return once its done}
 */
  uploadFilethroughSignedUrl(obj: Object, onProgress: (p: number) => void): Promise<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
      'x-amz-acl': 'bucket-owner-full-control'
    });
  
    return this.http.put<any>(obj['url'], obj['file'], { headers, reportProgress: true, observe: 'events' })
      .pipe(
        tap((event: HttpEvent<any>) => {
          if (event.type !== HttpEventType.UploadProgress) return;
          const progress = Math.round(event.loaded * 100 / event.total);
          onProgress(progress);
        }),
      )
      .toPromise();
  }

  /**
   * DELETE OBJECT FROM S3 
   * @param obj = {ID- comaplint ID
   *  Key - path(key) of which file we want to delete} 
   * @returns 
   */
  deleteObjectFromS3(body: Object) {
    let options = { headers: this.getHeaders() };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/document/delete`, body, options)
  }

  getHeaders() {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    return headers;
  }

  getTotalFileSize(array: Array<any>) {
    let size = 0;
    for (let elem of array) {
      size += (elem['size'] ? elem['size'] : 0)
    }
    return size;
  }

  validateFileExtension(event: any) {
    let isValid = true;
    if (event['target']['files'] && event.target.files['length'] > 0) {
      for (let i = 0; i <= event.target.files.length - 1; i++) {
        let fileName = event.target.files[i];
        const allowedExtensions = ['gif', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'xlsx', 'pptx', 'PPTX', 'pdf', 'csv', 
        'txt', 'xlsx', 'xls', 'xlsm', 'xlsb', "mp3","mp4","m4a","flac","wav","mov","wmv"];
        const regEx = new RegExp('([a-zA-Z0-9\s_\\.\-:])+(' + allowedExtensions.join('|') + ')$');
        if (!regEx.test(fileName['name'])) {
          isValid = false;
        } else {
          isValid = true;
        }
      }
    }
    return isValid;
  }
}
