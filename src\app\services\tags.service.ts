import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class TagsService {

  public resourceUrl =`${environment.API_BASE_URL}`;
  public userInfo = JSON.parse(window.localStorage.getItem('userInfo'));

  constructor(private http:HttpClient,
    private router: Router) { }

  getMasterSubTagList(){
    return this.http.get<any>(`${this.resourceUrl}/web/asci/masters-data?FIELD=sub-tags`, {});
  }

  getSubTagsList(pageNumber, limit, searchKey){
    let options = { headers: this.getHeaders() };
    let search = encodeURIComponent(searchKey);
    return this.http.get<any>(`${this.resourceUrl}/web/asci/sub-tags?SEARCH_KEY=${search}&limit=${limit}&PAGE=${pageNumber}`, options);
  }

  getSubTagById(id){
    let options = { headers: this.getHeaders() };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/sub-tags?ID=${id}`, options);
  }

  createSubTag(model):Observable<any>{
    let options = { headers: this.getHeaders() };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/sub-tags`, model, options);
  }

  updateSubTag(model,subTagId):Observable<any>{
    let options = { headers: this.getHeaders() };
    model['ID'] = subTagId
    return this.http.put<any>(`${this.resourceUrl}/web/asci/sub-tags`, model, options);
  }
  
  removeTag(id):Observable<any>{
    let obj = {
      ID : id
    }
    let options = { headers: this.getHeaders() };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/sub-tags/delete`, obj, options);
  }

  getHeaders() {
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(this.userInfo?.access_token),
      'id-token': encodeURIComponent(this.userInfo?.id_token)
    });
    return headers;
  }
}
