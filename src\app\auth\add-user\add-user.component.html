<div fxLayout="row">
    <mat-toolbar class="toolbar">
        <div fxFlex="35%" fxLayoutAlign="start">
            <h2 mat-dialog-title fxLayoutAlign="start" style="font-size: 16px;">
                <mat-icon style="position: relative; top: 7px; font-size: 16px;">add</mat-icon>Add new user
            </h2>
        </div>
        <div fxFlex="60%"></div>
        <div fxFlex="5%" fxLayoutAlign="end">
                <button mat-icon-button class="search-btn" mat-dialog-close>
                    <mat-icon style="margin-bottom: 4px;">close</mat-icon>
                </button>
        </div>
    </mat-toolbar>
</div>

<mat-divider></mat-divider>

<div class="form">
    <form [formGroup]="addform">
        <div class="contents">
            <div class="names" fxLayout="row">
                <div>Title <span style="color: #ff0000;">*</span></div>
                <div fxFlex="15%"></div>
                <div> First name <span style="color: #ff0000;">*</span></div>
                <div fxFlex="25%"></div>
                <div>Last name <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" style="width: 110px;">
                    <mat-select formControlName="salutation">
                        <mat-option *ngFor="let title of titles" [value]="title.ID" style="color:black;"> {{title.SALUTATION_NAME}}</mat-option>
                    </mat-select>
                    <mat-error class="salutation-error" *ngIf="addform.get('salutation').touched && addform.controls['salutation'].errors?.required">
                        Salutation is required
                    </mat-error>   
                </mat-form-field>
                <mat-form-field appearance="outline" class="input-field-name">
                    <input matInput formControlName="fname" style="font-style: normal;font-weight: normal;" autocomplete="off">
                    <mat-error class="error-msg" *ngIf="addform.controls['fname'].errors?.required">
                        First name is required
                        </mat-error>
                        <mat-error class="error-msg" *ngIf="addform.controls['fname'].errors?.pattern">
                        Only text is allowed
                        </mat-error>
                        <mat-error class="error-msg" style="width: max-content;" *ngIf="addform.controls['fname'].errors?.minlength">
                        First name should be of min length 3 & max length 25
                        </mat-error>
                        <mat-error class="error-msg" style="width: max-content;" *ngIf="addform.controls['fname'].errors?.maxlength">
                        First name should be of min length 3 & max length 25
                        </mat-error>
                </mat-form-field>
                <mat-form-field appearance="outline" class="input-field-name">
                    <input matInput formControlName="lname" style="font-style: normal;font-weight: normal;" autocomplete="off">
                    <mat-error class="error-msg" *ngIf="addform.controls['lname'].errors?.required">
                    Last Name is required
                    </mat-error>
                    <mat-error class="error-msg" *ngIf="addform.controls['lname'].errors?.pattern">
                    Only text is allowed
                    </mat-error>
                    <mat-error class="error-msg" *ngIf="addform.controls['lname'].errors?.maxlength">
                    Last Name should be of max length 25
                    </mat-error>
                </mat-form-field>
            </div>
            <div class="names1" style="margin-top: 5px;">
                <div>Phone number <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" class="input-field_phno">
                    <input matInput type="number" formControlName="phone" style="font-style: normal;font-weight: normal;" autocomplete="off">
                    <mat-error class="error-msg" *ngIf="addform.controls['phone'].errors?.required">
                        Phone number is required
                    </mat-error> 
                    <mat-error class="error-msg" *ngIf="addform.controls['phone'].errors?.pattern">
                      Enter valid phone number with length 10
                  </mat-error> 
                </mat-form-field>
            </div>
            <div class="names1">
                <div>Email address <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" class="input-field_phno">
                    <input matInput formControlName="emailId" style="
                    font-style: normal;
                    font-weight: normal;" autocomplete="off">
                    <mat-error class="error-msg" *ngIf="addform.controls['emailId'].errors?.required">
                        Email id is required
                    </mat-error>   
                    <mat-error class="error-msg" *ngIf="addform.controls['emailId'].errors?.pattern">
                        Please Enter Valid Email id
                    </mat-error> 
                </mat-form-field>
            </div>
            <div class="names" fxLayout="row">
                <div> Department <span style="color: #ff0000;">*</span></div>
                <div fxFlex="36%"></div>
                <div>Role <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" class="input-field_dept" style="font-style: normal;font-weight: normal;width: 48%;">
                    <mat-select style="font-style: normal;font-weight: normal;" formControlName="department">
                        <mat-option *ngFor="let dept of deptList" [value]="dept.ID">{{dept.DEPARTMENT_TYPE_NAME}}
                        </mat-option>
                    </mat-select>
                    <mat-error class="error-msg" *ngIf="addform.controls['department'].errors?.required">
                        Please Choose Department
                    </mat-error> 
                </mat-form-field>
                <mat-form-field appearance="outline" class="input-field_lastname" style="font-style: normal;font-weight: normal;width: 48%;margin-left: 9px;">
                    <mat-select style="font-style: normal;font-weight: normal;" formControlName="role">
                        <mat-option *ngFor="let role of userRoles" [value]="role.ID">{{role.ROLE_NAME}}</mat-option>
                    </mat-select>
                    <mat-error class="error-msg" *ngIf="addform.controls['role'].errors?.required">
                        Please Choose Role
                    </mat-error> 
                </mat-form-field>
            </div>
            <div class="names1">
                <div>Password <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" class="angularformcss">
                    <input matInput formControlName="password" style="font-style: normal;font-weight: normal;" autocomplete="off">
                    <img matSuffix class="doc-btn flex" [cdkCopyToClipboard]="value">
                    <mat-error class="error-msg" *ngIf="addform.controls['password'].errors?.required">
                        Password is required
                    </mat-error>   
                    <mat-error class="pswd-error" *ngIf="addform.controls['password'].errors?.pattern">
                        Password should contain at least one uppercase letter, one lowercase letter, one number and one special character (#?!@$%^&*-) and should be of length 8-15 characters
                    </mat-error> 
                </mat-form-field>
            </div>
            <div class="gen-btn">
                <button mat-button class="generate-btn bolder" (click)="Password()"> 
                    <span class="bolder">Generate password</span>
                </button>
            </div>
        </div>
        <div class="lastdivdr">
            <mat-divider></mat-divider>
        </div>
        <div class="toolbar-btns2">
            <div>
                <mat-dialog-actions>
                    <button mat-flat-button class="cancel-btn" mat-dialog-close>
                        <span class="bolder">Cancel</span>
                    </button>
                    <button mat-flat-button class="register-btn" [disabled]="addform.invalid"
                        (click)="register(addform.value)">
                        <span class="bolder">Register user</span>
                    </button>
                </mat-dialog-actions>
            </div>
        </div>
    </form>
</div>