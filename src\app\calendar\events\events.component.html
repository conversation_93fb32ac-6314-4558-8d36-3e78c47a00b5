<!-- <p>events works!</p> -->
<div fxLayout="row">
    <mat-toolbar class="toolbar">
        <div fxFlex="35%" fxLayoutAlign="start">
            <h2 mat-dialog-title fxLayoutAlign="start" style="font-size: 16px;">
                <!-- <mat-icon style="position: relative; top: 7px; font-size: 16px;">add</mat-icon> -->
                Events
            </h2>
        </div>
        <div fxFlex="60%"></div>
        <!-- <div fxFlex="5%" fxLayoutAlign="end">
            <mat-dialog-actions mat-dialog-close style="position: relative; top: -10px; left: 15px;">
                <button mat-icon-button class="search-btn" mat-dialog-close>
                    <mat-icon style="margin-bottom: 4px;">close</mat-icon>
                </button>
            </mat-dialog-actions>
        </div> -->
        <!-- <button> <mat-icon style="margin-bottom: 4px;">close</mat-icon></button> -->

        <!-- Original mat-icon  below of 3 lines -->
        <!-- <button mat-icon-button class="search-btn" mat-dialog-close>
            <mat-icon style="margin-bottom: 4px;">close</mat-icon>
        </button> -->
        <button mat-icon-button class="search-btn" (click)="onClose()" >
            <mat-icon style="margin-bottom: 4px;">close</mat-icon>
        </button>
        <!-- Till here -->
        <!-- <button class="close-button"> -->
            <!-- <mat-icon  (click)="onClose()" style="margin-top: 7px;cursor: pointer;">close</mat-icon> -->
            <!-- <mat-icon  (click)="onClose()" style="margin-top: 0px;cursor: pointer;">close</mat-icon> -->
        <!-- </button> -->
    </mat-toolbar>
</div>
<!-- <mat-divider></mat-divider> -->
<hr>
<div fxLayout="row">
    <mat-toolbar class="toolbar">
        <div fxFlex="35%" fxLayoutAlign="start">
            <h2 mat-dialog-title fxLayoutAlign="start" style="font-size: 16px;">
                
            No events listed
            <!-- {{data.name}} -->
           <!-- {{receivedRow.title}} -->
            </h2>
        </div>

    </mat-toolbar>
</div>
<!-- <hr> -->
<!-- <div fxLayout="row">
    <mat-toolbar class="events-toolbar">
        <div fxFlex="35%" fxLayoutAlign="start">
            
            No events listed
            {{data.name}}
           
        </div>
        <div fxFlex="60%"></div>
    </mat-toolbar>
</div> -->

<mat-toolbar class="toolbar2" fxLayout="row">
    <div>
        <!-- <button mat-button class="add-btn" (click)="addUser()" style="width: 140px; margin-top: 5px;"> -->
        <!-- <button mat-button class="add-btn" style="width: 140px; margin-top: 5px;">
        <mat-icon style="font-size: 16px; margin-top: 4px; margin-left: -5px;" (click)="addUser()">add</mat-icon>Add New Event
        </button> -->
        <button mat-button (click)="addUser()" class="add-btn" style="width: 140px; margin-top: 5px;">
            <!-- <mat-icon style="font-size: 16px; margin-top: 4px; margin-left: -5px;" >add</mat-icon> -->
            Add New Event</button>
        <!-- </button> -->
    </div>
</mat-toolbar>
