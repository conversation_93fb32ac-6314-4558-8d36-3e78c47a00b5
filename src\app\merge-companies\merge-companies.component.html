<div fxLayout="column">
<div fxLayout="row">
    <mat-toolbar class="toolbar">
        <div fxFlex="35%" fxLayoutAlign="start">
            <h2 mat-dialog-title fxLayoutAlign="start" style="font-size: 16px;">
                <img src="../assets/images/edit-icon.svg">&nbsp; Merge Companies
            </h2>
        </div>
        <div fxFlex="60%"></div>
        <div fxFlex="5%" fxLayoutAlign="end">
                <button mat-icon-button class="search-btn" mat-dialog-close>
                    <mat-icon style="margin-bottom: 4px;">close</mat-icon>
                </button>
        </div>
    </mat-toolbar>
</div>
<mat-divider></mat-divider>
<div fxLayout="row" class="content">
    <div class="form">
        <form [formGroup]="mergeCompanyForm">
            <div class="contents">
                <div class="names1" style="padding-top: 15px;">
                    <div>Company name</div>
                </div>
                <div class="compname">
                    <input class="input-field_phno" id="companyname" formControlName="cname" style="height: 35px;border: 1px solid #CFD7DF;border-radius: 4px;width: 90%;" autocomplete="off" *ngIf="!autoComplete">
                    <mat-error class="error-msg" style="font-size: 12px;" *ngIf="mergeCompanyForm.get('cname').touched && mergeCompanyForm.controls['cname'].errors?.required">
                        Company Name is required
                    </mat-error>
                </div>
                <div class="names1" style="margin-top: 11px;">
                    <div>Reachable Contact Details</div>
                </div>
                <div class="compname">
                    <input class="input-field_phno" style="height: 35px;border: 1px solid #CFD7DF;border-radius: 4px;width: 90%;margin-bottom: 15px;" *ngIf="getControls().length == 0" value="No contact details available" readonly>
                </div>
                <div fxLayout="column"  formArrayName="contact_info" class="contact-info" fxLayoutGap="1%" *ngIf="getControls().length!= 0">
                <div *ngFor="let contact_info of getControls(); let i =index" formGroupName="{{i}}">
                    <div fxLayout="row">
                    <mat-form-field appearance="outline" class="input-field_phno" style="width: 42%;">
                        <input matInput style="
                        font-style: normal;
                        font-weight: normal; height: 20px;" formControlName="EMAIL_ID" autocomplete="off" readonly>
                    </mat-form-field>
                    <div fxFlex="2%"></div>
                    <mat-form-field appearance="outline" class="input-field_phno" style="width: 42%;">
                        <input matInput style="
                        font-style: normal;
                        font-weight: normal; height: 20px;" formControlName="MOBILE" autocomplete="off" readonly>
                    </mat-form-field>
                    <div fxFlex="2%"></div>
                    <button class="remove-box"(click)="removeContactInfo(i)">
                        <img src="../../../assets/images/delete-red.png">
                    </button>
                    </div>
                    </div>
                    </div>
                    <span *ngIf="getControls().length > 3" style="color: #ED2F45;font-size: 12px;position: relative;bottom: 11px;">Please choose any 3 out of these</span>
                    <div class="names1">
                        <div>Company address</div>
                    </div>
                    <div class="address">
                        <mat-form-field appearance="outline" class="input-field_phno" style="width: 90%;">
                            <input matInput formControlName="caddress" style="
                            font-style: normal;
                            font-weight: normal; height: 20px;" autocomplete="off">
                        </mat-form-field>
                    </div>
                    <div class="names1">
                        <div>About company</div>
                    </div>
                    <div class="about">
                        <mat-form-field class="input-field_phno" appearance="outline" style="width: 90%;">
                            <textarea matInput formControlName="about_company"
                                style="font-size: 14px; height: 50px;" autocomplete="off"></textarea>
                        </mat-form-field>
                    </div>
            </div>
        </form>
    </div>
    <div class="merge">
        Merging Companies
        <div class="merge-box">
            <div class="merge-company-details" *ngFor="let info of mergeCompanyInfo; let i =index">
                <div *ngFor="let data of info" fxLayout="column" style="padding: 16px;width: 100%;">
                        <div fxLayout="row">
                            <div class="material-icons-outlined">arrow_circle_right</div> 
                            <div fxFlex="1%"></div>
                            <div style="color:#0088CB">{{data.COMPANY_NAME}}</div>
                            <mat-divider class="divider"></mat-divider>
                        </div>
                        <div fxLayout="row" class="company-info" *ngIf="data.ADDRESS">
                            <span class="material-icons-outlined" style="color: rgba(0, 0, 0, 0.6);margin-right: 7px;">location_on</span>{{data.ADDRESS}}
                        </div>
                        <div fxLayout="row" class="company-info" *ngIf="data.COMPANY_DESCRIPTION">
                            <span class="material-icons-outlined" style="color: rgba(0, 0, 0, 0.6);margin-right: 7px;">edit</span>{{data.COMPANY_DESCRIPTION}}
                        </div>
                        <div fxLayout="row" class="company-info">
                            <span *ngIf="data.CONTACT_INFO.length != 0" class="material-icons-outlined" style="color: rgba(0, 0, 0, 0.6);margin-right: 7px;margin-top: -1px;">group</span><span style="color:#0088CB;margin-right: 3px;margin-left: 2px;" *ngIf="data.CONTACT_INFO.length != 0">{{data.CONTACT_INFO.length}}</span> 
                            <span *ngIf="data.CONTACT_INFO.length != 0">employees registered</span>
                            <span *ngIf="data.CONTACT_INFO.length == 0">No employees registered</span>
                        </div>
                        <mat-accordion *ngIf="data.CONTACT_INFO.length != 0">
                            <mat-expansion-panel>
                              <mat-expansion-panel-header>
                              </mat-expansion-panel-header>
                                <div fxLayout="column" class="contact-info" style="margin-left: 218px;">
                                    <div *ngFor="let val of data.CONTACT_INFO">
                                    <div fxLayout="row">
                                        <mat-form-field appearance="outline" class="input-field_phno" style="width: 47%;">
                                            <input matInput style="
                                            font-style: normal;
                                            font-weight: normal; height: 20px;" value="{{val.EMAIL_ID}}" autocomplete="off" readonly>
                                        </mat-form-field>
                                        <div fxFlex="2%"></div>
                                        <mat-form-field appearance="outline" class="input-field_phno" style="width: 47%;">
                                            <input matInput style="
                                            font-style: normal;
                                            font-weight: normal; height: 20px;" value="{{val.MOBILE}}" autocomplete="off" readonly>
                                        </mat-form-field>
                                    </div>
                            </div>
                        </div>
                        </mat-expansion-panel>
                        </mat-accordion>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="lastdivdr">
    <mat-divider></mat-divider>
</div>
<div class="toolbar-btns" fxLayout="row" fxLayoutAlign="end">
    <div>
        <mat-dialog-actions>
            <button mat-flat-button class="cancel-btn" mat-dialog-close>
                <span class="bolder">Cancel</span>
            </button>
            <button mat-flat-button class="theme-blue-button-admin" (click)="merge(mergeCompanyForm.value)"
                [disabled]="mergeCompanyForm.invalid || getControls().length > 3">
                <span class="bolder">Merge</span>
            </button>
        </mat-dialog-actions>
    </div>
</div>
</div>