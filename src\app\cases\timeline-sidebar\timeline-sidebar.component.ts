import { DatePipe } from '@angular/common';
import { Component, OnInit, ElementRef, Renderer2, HostListener } from '@angular/core';
import { id } from 'date-fns/locale';
import { first } from 'rxjs/operators';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';

@Component({
  selector: 'app-timeline-sidebar',
  templateUrl: './timeline-sidebar.component.html',
  styleUrls: ['./timeline-sidebar.component.scss']
})
export class TimelineSidebarComponent implements OnInit {

  taskTimeline = [];
  timelineLoading: boolean = false;
  timeLineExist: boolean = true;

  constructor(private el: ElementRef,
    private renderer: Renderer2,
    private cs: ComplaintsService,
    private datePipe: DatePipe,
    private notify: NotificationService
  ) { }

  ngOnInit(): void {
    this.timelineLoading = true;
    this.timeLineExist = true;
    this.getTaskTimeline();
  }

  getTaskTimeline() {
    this.cs.currentComplaintTimeline.pipe(first()).subscribe(id => {
      this.cs.getComplaintTimeline(id).subscribe((timeline: any) => {
        this.timelineLoading = false;
        this.timeLineExist = true;
        this.taskTimeline = timeline.data;
        for (let item of this.taskTimeline) {
          item.date = this.datePipe.transform(item.date, 'EEE, d MMM y - h:mm a');
          for (let val of item.updates) {
            if (val.label == 'Complaint Received On') {
              val.value = this.datePipe.transform(val.value, 'dd/MM/yyyy, h:mm a');
            }
            if (val.label == 'Due date changed to' || val.label == 'Advertiser due date changed to') {
              val.value = this.datePipe.transform(val.value, 'dd/MM/yyyy');
            }
          }
        }
      }, err => {
        this.timelineLoading = false;
        this.timeLineExist = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    })
  }

}
