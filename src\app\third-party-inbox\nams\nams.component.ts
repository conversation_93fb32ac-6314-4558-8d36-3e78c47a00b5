import { SelectionModel } from '@angular/cdk/collections';
import { F } from '@angular/cdk/keycodes';
import { CdkVirtualScrollViewport, ScrollDispatcher } from '@angular/cdk/scrolling';
import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { isThursday } from 'date-fns';
import { th } from 'date-fns/locale';
import { filter } from 'rxjs/operators';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { ThirdPartyService } from 'src/app/services/third-party.service';
import { colorObj } from 'src/app/shared/color-object';
import { environment } from 'src/environments/environment';

export interface Message {
  id: number;
  name: string;
  phoneno: Number;
  msg: string;
  date: string;
  subject: string;
  company: string;
  relname: string;
  relemail: string;
  rellocation: string;
  relmsg: string;
  person1: string;
  person2: string;
  person3: string;
  tag1: string;
  tag2: string;
  tag3: string;
  tag4: string;

  medium: string;
  creative_id: string;
  brand: string;
  media_link: string;
  advertiser: string;
  super_catry: string;
  outlet: string;
  edition: string;
  suppliment: string;
  detail_date: string;
  language: string;
  translation_link: string;
  product: string;
  description: string;
  isselected: boolean;
}

@Component({
  selector: 'app-nams',
  templateUrl: './nams.component.html',
  styleUrls: ['./nams.component.css']
})
export class NamsComponent implements OnInit {

  namsComplaints: any[];
  adSource: any[];
  complaintSource: any[];
  userType: any[];
  namsDetails = [];
  complaintTypeId: number;
  chatbotDetails = [];
  idArray = [];
  selectedTab;
  chatbotComplaints: any[];
  first: boolean = true;
  select_all = false;
  pagename: String;
  public msgvalue:any = [];
  public classificationTag: string = '';
  setmsg_count;
  confirmationMsg: any = {};
  @ViewChild(CdkVirtualScrollViewport) virtualScroll: CdkVirtualScrollViewport;
  searchPageNumber = 1;
  listLoading: boolean = false;
  lastData: number;
  scrollIndex = 10;
  num: number = 0;
  selection = new SelectionModel<any>(true, []);
  userInfo: any;
  blankComplaintID: number;
  mobile: number;
  chatbotComplaintId: number = 0;
  namComplaintId: number = 0;
  is_FTC: number = 0;
  public bucketUrl = `${environment.BUCKET_URL}`;
  docURL;
  showURL;
  matchFound:boolean = true;
  extraSimilarComplaints = 0;
  similarityComplaintList: any[];
  similarCompLoading: boolean = true;
  complaint_attachment_data: string;
  compSourceId: number;
  id: number;
  loading: boolean = false;

  msg = [];
  activePage;
  translation_link: string;
  limit: number;

  constructor(private router: Router,
    private cs: ComplaintsService,
    private namsService: ThirdPartyService,
    private notify: NotificationService,
    public dialog: MatDialog,
    private scrollDispatcher: ScrollDispatcher,
    private cd: ChangeDetectorRef) { }

  async ngOnInit(): Promise<void> {
    this.pagename = "NAMS";
    this.selectedTab = 0;
    this.first = true;
    this.loading = true;
    await this.listNamsComplaints(this.searchPageNumber);
    this.adSource = JSON.parse(window.localStorage.getItem('advertisementSource'));
    this.complaintSource = JSON.parse(window.localStorage.getItem('complaintSource'));
    this.userType = JSON.parse(window.localStorage.getItem('userType'));
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));

    this.activePage = "NAMS";
    this.cs.setActivePage(this.activePage);
  }

  ngAfterViewInit(): void {
    // this.scrollDispatcher.scrolled().pipe(
    //   filter(event => this.virtualScroll.measureScrollOffset('bottom') === 0)
    // ).subscribe(event => {
    //   this.listLoading = true;
    //   this.searchPageNumber ++;
    //   this.getNextSet(this.searchPageNumber);
    // })
  }

  onScrollDown() {
    this.listLoading = true;
    this.searchPageNumber++;
    this.getNextSet(this.searchPageNumber);
  }

  onNavigate() {
    // your logic here.... like set the url
    const url = 'https://www.google.com';
    window.open(url, '_blank');
  }

  listNamsComplaints(pageNumber) {
    this.limit = 5;
    this.namsService.listComplaintsList(pageNumber,this.limit).subscribe((namsList: any) => {
      this.namsComplaints = namsList.data;
      this.loading = false;
      this.lastData = namsList.data.length;
      this.namComplaintId = this.namsComplaints[0].ID;
      this.getComplaintDetails(this.namComplaintId);
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  getComplaintDetails(id) {
    this.similarCompLoading = true;
    this.namComplaintId = id;
    this.namsService.getDetails(id).subscribe((res: any) => {
      this.namsDetails = res.data;
      this.complaintTypeId = res.data.COMPLAINT_TYPE_ID;
      this.compSourceId = this.namsDetails['COMPLAINT_SOURCE_ID'];
      if(this.namsDetails['TRANSLATION_HYPERLINK'] != ''){
        this.translation_link = this.bucketUrl + this.namsDetails['TRANSLATION_HYPERLINK'];
      }
      this.id = this.namsDetails['ID'];
      this.getClassificationTag(this.namsDetails['COMPLAINT'], this.namsDetails['CREATIVE_HYPERLINK'], this.namsDetails['BRAND'],
      this.namsDetails['PRODUCT'], this.namsDetails['COMPANY'], this.namsDetails['COMPLAINT_SOURCE_ID'], this.namsDetails['ID'],
      this.namsDetails['CREATIVE_ID']);
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }
  previewLink(source){
    if(source.indexOf("https") == -1){
      window.open("https://" + source, "_blank");
    } else {
      window.open(source, "_blank");
    }
  }

  getClassificationTag(desc, hyperLink, brand, product, company, compSourceId, id, creativeId){
    this.cs.getComplaintClassification(desc,compSourceId, id).subscribe((res)=>{
      this.classificationTag = res['data']['ml_classification_name'];
      this.getSimilarityCheck(this.classificationTag, desc, hyperLink, brand, product, company, compSourceId, id, creativeId)
    }, (err) => {
      if(err.error.status == 401){
        window.localStorage.clear();
        this.router.navigate(['auth/login']);
      }
      this.classificationTag = "-";
      this.getSimilarityCheck('', desc, hyperLink, brand, product, company, compSourceId, id, creativeId)
    })
  }

  getSimilarityCheck(classificationTag, desc, hyperLink, brand, product, company, compSourceId, id, creativeId){
    this.cs.getSimilarityCheck(classificationTag, desc, hyperLink, brand, product, company, compSourceId, id, creativeId).subscribe((res) => {
      this.similarCompLoading = false;
      if(Array.isArray(res.data.classification) && res.data.classification[1] != undefined){
        this.msgvalue = res.data.classification[1];
        this.complaint_attachment_data = res.data.classification[0].complaint_attachment_data;
        this.similarityComplaintList = JSON.parse(JSON.stringify(res.data.classification));
        this.similarityComplaintList.shift();
        if(Array.isArray(res.data.classification) && res.data.classification.length > 2){
          this.extraSimilarComplaints = res.data.classification.length - 2;
        }
        if(this.msgvalue['complaint_identifiation'] === "No Match found"){
          this.matchFound = false;
        }else{
          this.matchFound = true;
        }
      }
    }, (err) => {
      this.similarCompLoading = false;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  addNewComplaint(similarComp){
    this.loading = true;
    this.cs.addNewComplaint(similarComp, this.complaint_attachment_data, this.compSourceId, this.id).subscribe((res) => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      this.similarCompLoading = true;
      if (this.selectedTab == 0) {
        this.first = true;
        this.searchPageNumber = 1;
        this.listNamsComplaints(this.searchPageNumber);
      }
      else if (this.selectedTab == 1) {
        this.first = false;
        this.searchPageNumber = 1;
        this.listChatbotComplaints(this.searchPageNumber);
      }
    },(err) => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  listChatbotComplaints(pageNumber) {
    this.namsService.listChatbotList(pageNumber).subscribe((chatbotList: any) => {
      this.chatbotComplaints = chatbotList.data;
      this.loading = false;
      this.lastData = chatbotList.data.length;
      this.chatbotComplaintId = this.chatbotComplaints[0].ID;
      this.getChatbotDetails(this.chatbotComplaintId);
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  getChatbotDetails(id) {
    this.similarCompLoading = true;
    this.chatbotComplaintId = id;
    this.namsService.getChatbotDetails(id).subscribe((res: any) => {
      this.chatbotDetails = res.data;
      let file = [];
      if(this.chatbotDetails['ATTACHMENT_SOURCE']!= null) {
      file.push(this.chatbotDetails['ATTACHMENT_SOURCE'].split("/"));
      this.showURL = file[0][file[0].length - 1];
      }
      if(!!this.chatbotDetails['ATTACHMENT_SOURCE']){
        this.docURL = this.bucketUrl + this.chatbotDetails['ATTACHMENT_SOURCE'];
      }else{
        this.docURL = '';
      }
      this.mobile = res.data.MOBILE;
      this.compSourceId = this.chatbotDetails['COMPLAINT_SOURCE_ID'];
      this.id = this.chatbotDetails['ID'];
      this.getClassificationTag(this.chatbotDetails['COMPLAINT_DESCRIPTION'], this.docURL, this.chatbotDetails['BRAND_NAME'],
      '', this.chatbotDetails['COMPANY_NAME'], this.chatbotDetails['COMPLAINT_SOURCE_ID'], this.chatbotDetails['ID'], '');
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  selectComplaint(value, id) {
    let ID = id.toString();
    if (value == true) {
      this.num++;
      this.idArray.push(ID);
      if (this.num == this.namsComplaints.length) {
        this.select_all = true;
      } else {
        this.select_all = false;
      }
    }
    else if (value == false) {
      this.num--;
      for (let i = 0; i < this.idArray.length; i++) {
        if (this.idArray[i] == ID) {
          this.idArray[i] = null;
        }
      }
      if (this.num == this.namsComplaints.length) {
        this.select_all = true;
      } else {
        this.select_all = false;
      }
    }
  }

  deleteComplaints() {
    this.confirmationMsg.title = 'Are you sure you want to delete the complaint(s) ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.idArray, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.namsService
          .deleteNamsComplaint(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              this.listNamsComplaints(1);
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  getNextSet(pageNumber) {
    this.limit=5;
    if (this.selectedTab == 0) {
      if (this.lastData == 10) {
        this.namsService.listComplaintsList(pageNumber,this.limit).subscribe(res => {
          this.listLoading = false;
          this.lastData = res.data.length;
          this.namsComplaints = this.namsComplaints.concat(res.data);
          this.cd.detectChanges();
        }, err => {
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        });
      }
    }
    else if (this.selectedTab == 1) {
      if (this.lastData == 10) {
        this.namsService.listChatbotList(pageNumber).subscribe(res => {
          this.listLoading = false;
          this.lastData = res.data.length;
          this.chatbotComplaints = this.chatbotComplaints.concat(res.data);
          this.cd.detectChanges();
        }, err => {
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        });
      }
    }
  }

  async onTabChanged(event) {
    this.selectedTab = event.index;
    if (this.selectedTab == 0) {
      this.first = true;
      this.searchPageNumber = 1;
      this.listNamsComplaints(this.searchPageNumber);
    }
    else if (this.selectedTab == 1) {
      this.first = false;
      this.searchPageNumber = 1;
      await this.listChatbotComplaints(this.searchPageNumber);
    }
  }

  showFilter(data) {
    this.msg = data;
    this.setmsg_count = 1;
    this.cs.setMsg(this.msg, this.setmsg_count);
    this.router.navigate(['/cases/manage-cases']);
  }

  createComp() {
    if (this.selectedTab == 0) {
      // this.cs.createBlankComplaint(this.complaintTypeId, this.userInfo.userId, this.userInfo.userId, this.is_FTC).subscribe(res => {
      //   this.blankComplaintID = res.data.COMPLAINT_ID;
        // this.cs.updateBlankComplaintId(this.blankComplaintID);
        this.cs.updateNamsComplaintId = this.namComplaintId;
        this.cs.updateStep('nams');
        this.router.navigate(['/cases/manage-cases'], { state: { from: "NAMS" } });
      // }, err => {
      //   this.notify.showNotification(
      //     err.error.message,
      //     "top",
      //     (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
      //     err.error.status
      //   )
      // })
    } else if (this.selectedTab == 1) {
      this.cs.updateChatbotComplaintId(this.chatbotComplaintId);
      this.cs.updateStep('chatbot');
      this.router.navigate(['/cases/manage-cases'], { state: { from: "NAMS" } });
    }
  }

  searchComp() {
    this.router.navigate(['/cases/manage-cases']);
  }

  openNewTab(source) {
    window.open(source, 'window 1', '');
  }

  onSelectAll(e: any): void {
    for (let i = 0; i < this.namsComplaints.length; i++) {
      const item = this.namsComplaints[i];
      item.isselected = e;
      if (item.isselected == true) {
        this.idArray.push(item.ID.toString());
      }
      else if (item.isselected == false) {
        for (let i = 0; i < this.idArray.length; i++) {
          if (this.idArray[i] == item.ID) {
            this.idArray[i] = null;
          }
        }
      }
    }
  }

  deleteComp() {
    if (this.selectedTab == 0) {
      this.idArray.push(this.namComplaintId.toString());
      this.confirmationMsg.title = 'Are you sure you want to delete the complaint ?';
      const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
        data: { id: this.idArray, title: this.confirmationMsg.title },
        disableClose: true
      });
      dialogRef.afterClosed().subscribe(dialogResult => {
        if (dialogResult && dialogResult.state) {
          this.namsService
            .deleteNamsComplaint(dialogResult.id)
            .subscribe(res => {
              if (res) {
                this.notify.showNotification(
                  res.message,
                  "top",
                  (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                  res.status
                )
                this.listNamsComplaints(1);
              }
            }, err => {
              this.notify.showNotification(
                err.error.message,
                "top",
                (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                err.error.status
              )
            });
        }
      });
    }
    else if (this.selectedTab == 1) {
      this.idArray.push(this.chatbotComplaintId.toString());
      this.confirmationMsg.title = 'Are you sure you want to delete the complaint ?';
      const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
        data: { id: this.idArray, title: this.confirmationMsg.title },
        disableClose: true
      });
      dialogRef.afterClosed().subscribe(dialogResult => {
        if (dialogResult && dialogResult.state) {
          this.namsService
            .deleteChatbotComplaint(dialogResult.id)
            .subscribe(res => {
              if (res) {
                this.notify.showNotification(
                  res.message,
                  "top",
                  (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                  res.status
                )
                this.listChatbotComplaints(1);
              }
            }, err => {
              this.notify.showNotification(
                err.error.message,
                "top",
                (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                err.error.status
              )
            });
        }
      });
    }
  }

}
