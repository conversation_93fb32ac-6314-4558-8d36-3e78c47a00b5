.flex {
    display: flex;
    align-items: center;
    justify-content: center;
}
.flex-end {
    display: flex;
    justify-content: flex-end;
}
.scroll::-webkit-scrollbar {
    display: none;
}       
.scroll { 
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.black-text {
    color: #000000;
}
.grey-text {
    color: rgba(0, 0, 0, 0.6);
}
.large { 
    font-size: 14px;
    line-height: 19px;
    font-weight: 600;
}
.small {
    font-size: 12px;
    line-height: 16px;
    font-weight: normal;
}
.heading {
    color: #000000;
    font-weight: 500;
    font-size: 16px;
    line-height: 21px;
}
.dialog-body {
    background: #FFFFFF;
    border-radius: 6px;
}
.header {
    border-radius: 4px 4px 0px 0px;
    width: 100%;
    height: 65px;
}
mat-toolbar { 
    background-color: rgb(255, 255, 255);
}
.close-btn {
    background: #F3F3F3;
    border: 1px solid rgba(47, 57, 65, 0.6);
}
.close-icon {
    color: rgba(47, 57, 65, 0.6);
    margin-bottom: 3px;
}
.body {
    height: 300px;
}
.mat-tab-label,
.mat-tab-link {
    color: rgba(0, 0, 0, .87);
    padding: 20px 10px;
}
:host ::ng-deep .tabs .mat-tab-header,
.mat-tab-nav-bar {
    border-bottom: 0 !important;
}
.mat-tab {
    padding: 20px 10px;
}
:host ::ng-deep .tabs .mat-tab-label,
.mat-tab-link {
  opacity: 1 !important;
  color: #000000;
  background-color: rgba(238, 238, 238, 0.568) !important;
  border-color: #D8DCDE;
  border-width: 1px 1px 0 1px;
  border-style: solid;
  border-radius: 3px 3px 0px 0px;
  font-weight: 600;
  font-size: 12px;
  line-height: 16px;
  padding: 20px 10px;
}
:host ::ng-deep .tabs .mat-tab-label-active {
  background-color: #ffffff !important;
  opacity: 1 !important;
  color: #000000;
}
:host ::ng-deep .tabs .mat-ink-bar {
  display: none !important;
}
.tab-body {
    min-height: 310px;
    max-height: 310px;
    overflow-y: scroll;
    overflow-x: hidden;
    padding: 20px 15px;
}
.files-container {
    padding-bottom: 10px;
}
.uploaded-file {
    min-width: 211px;
    width: fit-content;
    height: 47px;
    background: #FFFFFF;
    border: 1px solid #D8DCDE;
    border-radius: 4px;
}
.icon-container {
    width: 48px;
    height: 46px;
    background: #3A3A3A;
    border-radius: 4px;
}
.caption {
    width: fit-content;
    padding: 0px 15px;
}
::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: #f5afaf;
}
:host ::ng-deep .mat-checkbox .mat-checkbox-checkmark-path {
    stroke:  #e00346 !important;
}
:host ::ng-deep .mat-checkbox-ripple .mat-ripple-element {
    background-color: #e00346 !important;
}
:host ::ng-deep .mat-checkbox-frame {
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}
.footer {
    height: 66px;
    width: 100%;
    border-top: 1px solid #D8DCDE;
}
.footer-btns {
    padding: 15px 0px;
}
.cancel-btn {
    height: 31px;
    color: #5A6F84;
    background: #CFD7DF;
    border-radius: 12px;
}
.upload-btn {
    height: 31px;
    color: #FFFFFF;
    background: #0088CB;
    border-radius: 12px;
}
.doc-fxrow-container {
    width: 100%;
    margin-top: 2%;
    height: 40vh;
    overflow-y: scroll;
    overflow-x: hidden;
}
.doc-fxrow-container::-webkit-scrollbar {
    display: none;
}
.doc-fxrow-container {
    -ms-overflow-style: none;
    scrollbar-width: none; 
}
.dropzone {
    height: 25px;
    display: table;
    width: 250px;
    border: 1px dashed #aaa;
    border-radius: 5px;
    margin-left: 10px;
    margin-bottom: 15px;
    margin-right: 10px;
}
.addfile-text-wrapper {
    width: calc(30% - 6px);
    height: 50px;
    display: table-cell;
    vertical-align: middle;
}
input[type="file"] {
    display: none;
}
.upload-scope-container {
    height: max-content;
    width: 100%;
    text-align: center;
}
.upload-label {
    color: rgb(170, 170, 170);
}
.upload-label>span {
    font-size: small;
    font-weight: normal;
    padding-top: 5px;
}
.doc-caption {
    background-color: rgb(255, 255, 255);
    height: fit-content;
}
.doc-caption>p {
    height: 15px;
    width: 94px;
    padding: 0;
    overflow: hidden;
    position: relative;
    display: inline-block;
    margin: 0 5px 0 5px;
    text-align: center;
    font-size: 12px;
    text-decoration: none;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #000;
    font-weight: 700;
}