import { Component, HostListener, Inject, OnInit, Pipe } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';
import { environment } from 'src/environments/environment';
import { ViewMediaFileComponent } from '../view-media-file/view-media-file.component';
import { saveAs as importedSaveAs } from "file-saver";
import { DomSanitizer } from '@angular/platform-browser';
import { ViewDocumentComponent } from '../view-document/view-document.component';

@Component({
  selector: 'app-related-complaints',
  templateUrl: './related-complaints.component.html',
  styleUrls: ['./related-complaints.component.scss']
})
export class RelatedComplaintsComponent implements OnInit {
  @Pipe({ name: 'safeHtml' })
  similarityComplaintList: any[] = [];
  classificationTag: string = '';
  complaintObj: any;
  msgvalue: any[];
  matchFound: boolean = false;
  loading: boolean = true;
  disableSubmit: boolean = true;
  ml_data: any;
  retry: boolean = false;
  hyperLink: string = '';
  compDetails: any = [];
  similarComplaintId;
  parent_id: any;
  noOfDocs = 0;
  complaint_id: any;
  detail_company: any;
  classification_name: any;
  complaint_source_id: number;
  seen_date: any;
  detail_advert: string;
  transcription: string;
  duration: string;
  adDocs: any;
  adMedium: any;
  comp_date: any;
  detail_date: any;
  longText1: string;
  detail_complaint: string;
  detail_adsource: any;
  detail_platform: any;
  detail_channel: any;
  detail_addate: any;
  complaintClaims: any = [];
  complaintCodeViolated: any = [];
  guidelines: any = [];
  similar_detail_link: any;
  longText4: string;
  docUrl;
  public bucketUrl = `${environment.BUCKET_URL}`;
  detail_place: any;
  media_outlet: any;
  media: any;
  super_category: any;
  edition: any;
  suppliment: any;
  ad_language: any;
  creative_id: any;
  translation_hyper: any;
  influencer_name: any;
  longText5: string;
  engagements: any;
  publication_url: any;
  profile_url: any;
  influencer_contact: any;
  influencer_email: any;
  company_details: any;
  imgURL: string;
  url: string;
  docFileList: any[] = [];
  complainantFiles = [];
  advertiserFiles = [];
  internalFiles = [];
  panelOpenState = false;
  advIndex;
  isMobile: boolean;
  innerWidth: number;
  network: any;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private cs: ComplaintsService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private matDialog: MatDialog,
    public dialogRef: MatDialogRef<RelatedComplaintsComponent>,
    private notify: NotificationService,
    private sanitized: DomSanitizer
  ) {
    this.complaintObj = this.data.complaintObj;
  }

  safeHTML(unsafe: string) {
    return this.sanitized.bypassSecurityTrustHtml(unsafe);
  }

  ngOnInit(): void {
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
    if (this.complaintObj['files_attached'] === 'Yes') {
      this.hyperLink = `${environment.BUCKET_URL}complaint/registered/${this.complaintObj['ID']}/`
    }
    this.getClassificationTag(this.complaintObj['COMPLAINT_DESCRIPTION'], this.hyperLink, this.complaintObj['BRAND_NAME'],
      this.complaintObj['PRODUCT_NAME'], this.complaintObj['COMPANY_NAME'], this.complaintObj['COMPLAINT_SOURCE_ID'], this.complaintObj['ID']);
  }

  getClassificationTag(desc, hyperLink, brand, product, company, compSourceId, id) {
    this.loading = true;
    this.cs.getComplaintClassification(desc, this.complaint_source_id, this.complaintObj['ID']).subscribe((res) => {
      this.classificationTag = res['data']['ml_classification_name'];
      this.getSimilarityCheck(this.classificationTag, desc, hyperLink, brand, product, company, compSourceId, id)
    }, (err) => {
      if (err.error.status == 401) {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      } else {
        this.classificationTag = '';
        this.getSimilarityCheck(this.classificationTag, desc, hyperLink, brand, product, company, compSourceId, id)
      }
    })
  }

  getSimilarityCheck(classificationTag, desc, hyperLink, brand, product, company, compSourceId, id) {
    let creativeId = '';
    if (this.complaintObj['COMPLAINT_SOURCE_ID'] == 7) {
      creativeId = this.complaintObj['CREATIVE_ID'];
    }
    this.cs.getSimilarityCheck(classificationTag, desc, hyperLink, brand, product, company, compSourceId, id, creativeId).subscribe((res) => {
      this.loading = false;
      if (Array.isArray(res.data.classification) && res.data.classification[1] != undefined) {
        this.disableSubmit = false;
        this.retry = false;
        this.msgvalue = res.data.classification[1];
        this.ml_data = res.data.classification[0];
        this.similarityComplaintList = JSON.parse(JSON.stringify(res.data.classification));
        this.similarityComplaintList.shift();
        if (!!this.similarityComplaintList && this.similarityComplaintList.length > 0) {
          this.similarComplaintId = this.similarityComplaintList[0].complaint_identification;
          this.selectDetails(this.similarComplaintId, 0);
        }
        if (this.msgvalue['complaint_identification'] === "No Match found") {
          this.matchFound = false;
        } else {
          this.matchFound = true;
        }
      } else {
        this.disableSubmit = true;
        this.retry = true;
      }
    }, (err) => {
      this.loading = false;
      this.disableSubmit = true;
      this.retry = true;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    });
  }

  submit() {
    this.dialogRef.close({ 'cond': 'submit', 'ml_data': this.ml_data, 'parent_id': null });
  }

  retry_btn() {
    this.loading = true;
    this.getClassificationTag(this.complaintObj['COMPLAINT_DESCRIPTION'], this.hyperLink, this.complaintObj['BRAND_NAME'],
      this.complaintObj['PRODUCT_NAME'], this.complaintObj['COMPANY_NAME'], this.complaintObj['COMPLAINT_SOURCE_ID'], this.complaintObj['ID']);
  }

  cancel() {
    this.dialogRef.close({ 'cond': 'cancel' });
  }

  selectDetails(case_id, i) {
    this.parent_id = "";
    this.advIndex = i;
    this.noOfDocs = 0;
    this.cs.getSimilarComplaints(this.parent_id, case_id).subscribe(res => {
      this.compDetails = res.data;
      if (this.compDetails.length != 0) {
        this.complaint_id = this.compDetails.ID;
        this.detail_company = this.compDetails.BRAND_NAME;
        this.classification_name = this.compDetails.CLASSIFICATION_NAME;
        this.complaint_source_id = this.compDetails.COMPLAINT_SOURCE_ID;
        this.seen_date = this.compDetails.DATE;
        this.transcription = this.compDetails.TRANSCRIPTION;
        this.duration = this.compDetails.DURATION;
        this.detail_advert = this.compDetails.ADVERTISEMENT_DESCRIPTION;
        this.adDocs = this.compDetails.ADVERTISEMENT_MEDIUM_DOCUMENT;
        this.adMedium = res.data.ADVERTISEMENT_MEDIUM;
        this.comp_date = this.compDetails.REGISTERED_DATE;
        this.detail_date = this.compDetails.CREATED_DATE;
        for (let i = 0; i < this.compDetails.ADVERTISEMENT_MEDIUM_DOCUMENT.length; i++) {
          if (this.compDetails.ADVERTISEMENT_MEDIUM_DOCUMENT[i].ATTACHMENT_SOURCE !== '') {
            this.noOfDocs += 1;
          }
        }
        if (this.detail_advert.length > 10) {
          this.longText1 = ' ...'
        }
        this.detail_complaint = this.compDetails.COMPLAINT_DESCRIPTION;
        this.network = this.compDetails.NETWORK;
        if (this.compDetails.ADVERTISEMENT_MEDIUM.length != 0) {
          this.detail_adsource = this.compDetails.ADVERTISEMENT_MEDIUM;
          this.detail_platform = this.compDetails.ADVERTISEMENT_MEDIUM[0].PLATFORM_ID;
          this.detail_channel = this.compDetails.ADVERTISEMENT_MEDIUM[0].SOURCE_NAME;
          this.detail_addate = this.compDetails.ADVERTISEMENT_MEDIUM[0].DATE;
          this.complaintClaims = res.data.CLAIMS;
          this.complaintCodeViolated = res.data.CODEVIOLATED;
          this.guidelines = res.data.GUIDELINES;
          this.similar_detail_link = this.compDetails.ADVERTISEMENT_MEDIUM[0].SOURCE_URL;
          if (this.similar_detail_link) {
            if (this.similar_detail_link.length > 28) {
              this.longText4 = '..';
            } else {
              this.longText4 = ' ';
            }
          }
          this.docUrl = this.bucketUrl + this.similar_detail_link;
          this.detail_place = this.compDetails.ADVERTISEMENT_MEDIUM[0].SOURCE_PLACE;
        }
        for (let i = 0; i <= this.complaintCodeViolated.length - 1; i++) {
          if (this.complaintCodeViolated[i].CLAUSES_ID != null) {
            this.complaintCodeViolated[i].CLAUSES_ID = ((this.complaintCodeViolated[i].CLAUSES_ID).replace(/"/g, '')).slice(1, -1);
          }
        }
        for (let i = 0; i <= this.guidelines.length - 1; i++) {
          if (this.guidelines[i].G_CLAUSES_ID != null) {
            this.guidelines[i].G_CLAUSES_ID = ((this.guidelines[i].G_CLAUSES_ID).replace(/"/g, '')).slice(1, -1);
          }
        }
        if (this.compDetails.COMPLAINT_SOURCE_ID == 7) {
          this.media_outlet = this.compDetails.MEDIA_OUTLET;
          this.media = this.compDetails.MEDIA;
          this.edition = this.compDetails.EDITION;
          this.super_category = this.compDetails.PRODUCT_CATEGORY;
          this.ad_language = this.compDetails.AD_LANGUAGE;
          this.suppliment = this.compDetails.SUPPLIMENT;
          this.creative_id = this.compDetails.CREATIVE_ID;
          this.translation_hyper = this.compDetails.TRANSLATION_HYPERLINK;
          if (this.translation_hyper) {
            if (this.translation_hyper.length > 28) {
              this.longText5 = '..';
            } else {
              this.longText5 = '..';
            }
          }
        }
        if (this.compDetails.COMPLAINT_SOURCE_ID == 8) {
          this.influencer_name = this.compDetails.INFLUENCER_NAME;
          this.engagements = this.compDetails.ENGAGEMENTS;
          this.publication_url = this.compDetails.PUBLICATION_URL;
          this.profile_url = this.compDetails.PROFILE_URL;
          this.influencer_contact = this.compDetails.INFLUENCER_MOBILE;
          this.influencer_email = this.compDetails.INFLUENCER_EMAIL
        }
        if (this.compDetails.COMPANY_INFO.length != 0) {
          this.company_details = this.compDetails.COMPANY_INFO;
        }
        this.getDocuments(this.complaint_id);
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  getDocuments(id) {
    this.cs.getDocumentsByComplaint(id).subscribe(res => {
      this.docFileList = res.data;
      this.complainantFiles = this.docFileList['COMPLAINANT'];
      this.advertiserFiles = this.docFileList['ADVERTISER'];
      this.internalFiles = this.docFileList['INTERNAL'];
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  preview(source) {
    this.imgURL = this.bucketUrl + source;
    window.open(this.imgURL, 'window 1', '');
  }

  previewLink(source) {
    if (source.indexOf("https") == -1) {
      window.open("https://" + source, "_blank");
    } else {
      window.open(source, "_blank");
    }
  }

  download(doc, source) {
    this.url = this.bucketUrl + source;
    this.cs.downloadFile(this.url).subscribe(data => {
      importedSaveAs(data, doc)
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  openNewTab() {
    const dialogRef = this.matDialog.open(ViewMediaFileComponent, {
      width: '476px',
      height: 'auto',
      data: { adMedium: this.adMedium, adDocs: this.adDocs },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
      }
    );
  }

  viewDocument() {
    const dialogRef = this.matDialog.open(ViewDocumentComponent, {
      width: '525px',
      height: '472px',
      data: this.complaint_id,
      disableClose: true
    });
  }

  addNewComplaint(similarComp) {
    this.dialogRef.close({ 'cond': 'submit', 'ml_data': this.ml_data, 'parent_id': similarComp['complaint_identification'] });
  }

}