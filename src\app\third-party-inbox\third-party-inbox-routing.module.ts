import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuardService } from '../services/auth-guard.service';
import { NamsComplaintsComponent } from './nams-complaints/nams-complaints.component';
import { NamsReechComponent } from './nams-reech/nams-reech.component';

const routes: Routes = [
  {
    path: '',
    component: NamsComplaintsComponent,
    children: [
      {
        path: 'nams',
        canActivate: [AuthGuardService],
        data: {
          role: [1,2,3,6]
        },
        component: NamsComplaintsComponent
      }
    ]
  },{
    path: 'nams-reech',
    component: NamsReechComponent,
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ThirdPartyInboxRoutingModule { }
