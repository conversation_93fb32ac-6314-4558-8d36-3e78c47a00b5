.complaint-container {
    width: 100% !important;
    height: 70vh;
}
.comp-matcard{
    background-color: #FFFFFF;
    height: 70vh;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #D8DCDE;
}
.mat-card-scroll {
    height: 70vh;
}
.contents-scroll::-webkit-scrollbar {
    display: none;
}
.attachment-box {
    background: #FFFFFF;
    border: 1px solid #F5F5F7;
    box-sizing: border-box;
    border-radius: 4px;
    width: fit-content;
    padding: 26px;
    padding: 13px;
    width: 349px;
    margin-right: 10px;
}
.panel-body{
    background: #FFFFFF;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
    height: 28px;
    margin-bottom: 4px;
}
.details-container {
    border: 1px solid #D8DCDE;
    background: #ffffff;
    margin-right: 12px;
    padding: 21px;
    box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.08);
    margin-bottom: 21px;
    margin-left: 12px;
}
.contents-scroll {
    height: 65vh;
    -ms-overflow-style: none;
    scrollbar-width: none;
    overflow-y: scroll;
    overflow-x: hidden;
    margin-right: 2px;
}
.classfy-head {
    position: relative;
    margin-top: 10px;
    display: flex;
    /* grid-gap: 10px; */
}
.divider-container {
    position:absolute;
    top: 8px;
}
.classfy-head-divider {
    position: relative;
}
.detail-left-container {
    width: 60%;
}
.detail-attribute {
    color: rgba(0, 0, 0, 0.4);
    font-size: 14px;
    line-height: 19px;
    font-weight: bolder;
}
.detail-value {
    color: #2F3941;
    line-height: 19px;
    font-size: 14px;
    color: #000000
}
.comp-msg-container {
    width: 90%;
    /* word-wrap: break-word;
    word-break: normal;
    overflow: hidden; */
    font-size: 14px;
    line-height: 19px;
    text-align: justify;
}
/* .comp-msg-container p {
    text-overflow: ellipsis !important;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
} */
.media-attribute {
    width: 18%;
    word-wrap: normal;
}
.media-container {
    width: 74%;
}
.media-anchor {
    color: #0088CB;
    word-break: break-all;
    cursor: pointer;
}
.media-anchor1 {
    color: #0088CB;
    word-break: break-all;
    cursor: pointer;
    text-overflow: ellipsis !important;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
.detail-right-container {
    width: 40%;
}
.comp-head {
    font-size: 14px;
    line-height: 16px;
    color: #000000;
    width: -webkit-fill-available;
    margin-bottom: 5px;
    font-weight: 550;
}
.comp-divider-container {
    position:absolute;
    top: 23px;
    width: 82%;
    margin-left: 125px;
}
.classification-container {
    display: inline;
}
.classify-content-container {
    border: 1px solid #D8DCDE;
    background: #ffffff;
    padding: 21px;
    box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.08);
    margin-bottom: 21px;
    margin-right: 12px;
    margin-left: 12px;
}
.comp-chips {
    color: #4DA1FF;
    background-color: #E9F5FF;
    width: auto;
    font-size: 12px;
    line-height: 16px;
    font-style: normal;
    font-weight: normal;
    border: 1px solid #4DA1FF;
    border-radius: 5px;
}
.classfy-container {
    max-height: fit-content;
    margin-top: 20px;
}
.related-name-container {
    text-indent: 0px;
    height: 25px;
    font-size: 14px;
    padding-bottom: 35px;
    margin-top: 9px;
}
.related-name {
    color: #2AB2FF;
    font-weight: bold;
    font-size: 14px;
    padding-left: 5px;
}
.related-detail-container {
    text-indent: 20px;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 19px;
}
.related-attribute {
    color: #CFD7DF;
}
.related-value {
    color: #000000;
}
.add-btn {
    color: rgb(163, 163, 163);
    font-weight: normal;
    padding-left: 5px;
    padding-top: 4px;
    line-height: 15px;
    border: 1px dashed rgb(163, 163, 163) ;
    border-radius: 20px;
    height: 32px;
    margin: 7px 0px 20px 20px;
}
.chips-btn-container {
    margin-left: 1%;
    position:relative;
    padding-bottom: 30px;
}
.person-chips {
    font-size: 10px;
    line-height: 13px;
    display:flex;
    align-items: center;
    border-radius: 50px;
}
#person-chip1 {
    color: #8B97A3;
    background-color: #CFD7DF;
    z-index: 3;
}
.footer-fixed {
    height: 40px;
}
.footer-content-container {
    width: 100% !important;
}
.footer-btn-create {
    border-color: #0088CB;
    color: #0088CB;
    border-radius: 12px;
}
.footer-btn {
    color: #5A6F84;
    border-radius: 12px;
}
.footer-right-btn {
    display: flex;
    justify-content: flex-end;
}
.delete-icon {
    font-size: large;
    vertical-align: baseline !important;
    margin-right: 10px;
}
.close-btn {
    color: solid rgba(47, 57, 65, 0.6);
    background: #F3F3F3;
    border: 1px solid rgba(47, 57, 65, 0.6);
}
.top-divider-container {
    position: relative;
    top: 23px;
    width: 100%;
    margin-left: 0px !important;
}
.footer-btn-create[disabled] {
    color: #92A2B1;
    background: #F8F9F9;
    border: 1px solid #92A2B1;
    border-radius: 12px;
}
.ellipsis {
  overflow: hidden;
  width: 320px;
  display: inline-block;
  display: -webkit-inline-box;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  white-space: normal;
  -webkit-box-orient: vertical;
  vertical-align: middle;
  position: relative;
  margin-top: 10px;
  height: 40px !important;
}
.company-btn {
  position: relative;
  top: 3px;
  left: 3px;
}
.not-exist-btn {
  width: 74px;
  height: 18px;
  background-color: rgba(252, 41, 41, 0.13);
  border: 0.5px solid #EA2D2D;
  box-sizing: border-box;
  border-radius: 3px;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
}
.not-exist {
  position: relative;
  top: -3px;
  color: #FC2929;
}
.comp-tab-container::-webkit-scrollbar {
    display: none;
}  
.comp-tab-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
} 
.comp-tab-container {
    height: 65%;
    width: 99%;
    overflow-y: scroll;
    overflow-x: hidden;
}
::ng-deep .detail-subtab.mat-tab-header,
.mat-tab-nav-bar {
    border-bottom: 0;
}
:host ::ng-deep .detail-subtab .mat-tab-label,
.mat-tab-link {
  width: 18%;
  border-width: 1px 1px 0 1px;
  border-style: solid;
  border-color: #D8DCDE;
  border-radius: 3px 3px 0px 0px;
  font-weight: 600 !important;
  font-size: 12px;
  line-height: 16px;
  color: #000000;
  opacity: 1 !important;
  background-color: rgba(238, 238, 238, 0.568) !important;
}
:host ::ng-deep .detail-subtab .mat-tab-label-active {
    background-color:  #0088CB !important;
    opacity: 1 !important;
    color: #ffffff;
    font-weight: 600 !important;
}  
:host ::ng-deep .detail-subtab .mat-ink-bar {
    display: none !important;
}
:host ::ng-deep .detail-subtab .mat-tab-label .mat-tab-label-content {
    font-weight: 600;
}  
.detail-subtab {
    padding-left: 1%;
    padding-top: 2%;
}
:host ::ng-deep .detail-subtab-group .mat-ink-bar {
    display: none !important;
}
.doc-container {
    padding-top: 10px;
    padding-bottom: 10px;
    width: 100%;
}
.content-head-container {
    width: 100%;
    height: 100%;
    margin-bottom: 30px;
}
.intra-expansion {
    border: 1px solid #D8DCDE;
    padding: 0% 2% 0% 2%;
}
:host ::ng-deep .panel-header>.mat-expansion-indicator:after {
    color: #0088CB;
}
.panel-title {
    color: #000000;
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
}
.attribute-container1 {
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
    height: 20px;
    padding: 2px 0px;
    width: 236px;
}
.attribute-container2 {
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
    height: 20px;
    padding: 3px 0px;
}
.grey-text {
    color: #92A2B1;
    font-size: 14px;
    line-height: 19px;
}
.flex {
    display: flex;
    align-items: center;
    justify-content: center;
}
.doc-fxrow-container {
    width: 100%;
    margin-top: 2%;
}
.mat-card-doc {
    padding: 0px;
    width: fit-content;
    height: fit-content;
}
.doc-icon-container {
    background-color:#3a3a3a;
    height: 86px;
    width: 176px;
    border-radius: 5px;
    display: flex;
    justify-content: center;
}
.doc-icon-container>span {
    color: rgb(168, 168, 168);
}
.doc-image-container {
    display: flex;
    justify-content: center;
    align-items: center;
}
.doc-caption {
    background-color: rgb(255, 255, 255);
    height: fit-content;
    padding: 13px;
}
.doc-caption>p {
    height: 18px;
    width: 94px;
    padding: 0;
    overflow: hidden;
    position: relative;
    display: inline-block;
    margin: 0 5px 0 5px;
    text-align: center;
    font-size: 12px;
    text-decoration: none;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #000;
    font-weight: 700;
}
.action-buttons {
    border: 1px solid gainsboro;
    position: relative;
    bottom: 17px;
    left: 122px;
    background: white;
    width: 104px;
    height: 64px;
    padding-left: 13px;
    padding-top: 10px;
    z-index: 1;
}
.action-buttons-internal {
    border: 1px solid gainsboro;
    position: relative;
    bottom: 32px;
    left: 122px;
    background: white;
    width: 112px;
    height: 81px;
    padding-left: 19px;
    padding-top: 9px;
}
.option-btn {
    line-height: 25%;
    height: 550%;
}  
.option-text {
    line-height: 200%;
    padding-left: 8px;
}
.raised-container {
    width: 65%;
}
.head-container {
    padding: 5px 0px;
}  
.head-container>div {
    max-height: 20px;
}
.arrow-img-icon {
    vertical-align: super !important;
}
.intra-h3 {
    color: #000000;
    font-weight: normal;
    font-size: 14px;
    line-height: 19px;
}
.challenge-container {
    border: 1px solid #D8DCDE;
    border-radius: 4px;
    padding: 10px;
}
.attribute-container {
    padding: 10px 0px 0px;
} 
.grey-text {
    color: #92A2B1;
    font-size: 14px;
    line-height: 19px;
}  
.violated-popups {
    color: #0088CB;
    text-align: center;
    width: 100%;
    height: 30px;
    background: #FFFFFF;
    border: 1px solid #0088CB;
    box-sizing: border-box;
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
}
.violated-container {
    width: 37%;
}
.chapters-container {
    padding: 10px;
    overflow-wrap: anywhere;
    padding-top: 0px;
}  
.chapter-text {
    color: #000000;
    font-weight: normal;
    font-size: 14px;
    margin-bottom: 11px;
    line-height: 19px;
    width:192px;
    text-align: -webkit-auto;
}
.width50 {
    width: 50%;
}  
.circle-icon {
    font-size: 4px;
    vertical-align: sub;
    height: 16px;
    margin-top: 8px;
    bottom: 6px;
    position: relative;
}  
.no-claims {
    color: rgb(160, 160, 156);
    margin-top: 10px;
    margin-left: 10px;
    font-size: 15px;
}
.theme-blue-button-admin[disabled] {
    opacity: 0.4;
}
  :host ::ng-deep .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar {
    background-color: rgb(144, 212, 144) ;
  }
  :host ::ng-deep  .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb {
    background-color: rgb(28, 187, 28) ;
  }
