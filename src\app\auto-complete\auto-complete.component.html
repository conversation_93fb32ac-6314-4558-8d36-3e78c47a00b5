<form class="example-form">
    <mat-form-field class="example-full-width" appearance="fill">
      <input type="text"
             placeholder="Pick one"
             matInput
             [formControl]="myControl"
             [matAutocomplete]="auto">
      <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete" (optionSelected)='getValue($event.option.value)'>
        <mat-option *ngFor="let option of filteredOptions | async" [value]="option">
          {{option}}
        </mat-option>
      </mat-autocomplete>
    </mat-form-field>
  </form>