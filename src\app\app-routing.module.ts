import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { AutoCompleteComponent } from "@syncfusion/ej2-angular-dropdowns";
import { CalendarComponent } from "./calendar/calendar.component";
import { HeadingComponent } from "./heading/heading.component";
import { IconsComponent } from "./icons/icons.component";
import { InboxToolbarComponent } from "./inbox-toolbar/inbox-toolbar.component";
import { InboxComponent } from "./inbox/inbox.component";
import { AuthGuardService } from "./services/auth-guard.service";
import { ToolbarOptionsComponent } from "./toolbar-options/toolbar-options.component";
import { MobileHeaderComponent } from './mobile-header/mobile-header.component';
import { MobileNotificationsComponent } from "./mobile-notifications/mobile-notifications.component";
import { MeetingComponent } from "./meeting/meeting.component";
import { MigrateDataComponent } from "./migrate-data/migrate-data.component";
import { ArchivedDataComponent } from "./archived-data/archived-data.component";

const routes: Routes = [
  {
    path: 'auth',
    loadChildren: () => import('./auth/auth.module').then(m => m.AuthModule)
  },
  {
    path: 'tasks',
    loadChildren: () => import('./task-board/task-board.module').then(m => m.TaskBoardModule)
  },
  {
    path: 'home',
    loadChildren: () => import('./home/<USER>').then(m => m.HomeModule)
  },
  {
    path: 'cases',
    loadChildren: () => import('./cases/cases.module').then(m => m.CaseModule)
  },
  {
    path: 'third-party',
    loadChildren: () => import('./third-party-inbox/third-party-inbox.module').then(m => m.ThirdPartyInboxModule)
  },
  {
    path: 'inbox', canActivate: [AuthGuardService],
    data: {
      role: [1, 2, 3, 6]
    },
    component: InboxComponent
  },
  {
    path: 'calendar', canActivate: [AuthGuardService],
    data: {
      role: [1, 2, 3, 6]
    },
    component: CalendarComponent
  },
  {
    path: 'meeting', canActivate: [AuthGuardService],
    data: {
      role: [1, 2, 3, 6]
    },
    component: MeetingComponent
  },
  {
    path: 'archive', canActivate: [AuthGuardService],
    data: {
      role: [1, 2, 3, 6]
    },
    component: MigrateDataComponent
  },
  {
    path: 'archived-data', canActivate: [AuthGuardService],
    data: {
      role: [4, 5, 7, 8]
    },
    component: ArchivedDataComponent
  },
  { path: 'mobile-notifications', component: MobileNotificationsComponent },
  { path: 'inbox-toolbar', canActivate: [AuthGuardService], component: InboxToolbarComponent },
  { path: 'icons', canActivate: [AuthGuardService], component: IconsComponent },
  { path: 'auto', canActivate: [AuthGuardService], component: AutoCompleteComponent },
  { path: 'toolbar-options', canActivate: [AuthGuardService], component: ToolbarOptionsComponent },
  { path: 'heading', canActivate: [AuthGuardService], component: HeadingComponent },
  { path: 'mobile-header', canActivate: [AuthGuardService], component: MobileHeaderComponent },
  {
    path: '',
    redirectTo: '/auth/login',
    pathMatch: 'full'
  }
]

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }