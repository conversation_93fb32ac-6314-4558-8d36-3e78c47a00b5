<app-icons *ngIf="!mobile && roleId == 5 && industryMemberRole == 1"></app-icons>
<div class="common-toolbar" fxLayout="row" style="width: 100%; height: 50px;" *ngIf="!mobile">
  <div class="heading-container" fxLayout="row">
    <div style="width: 40px; height: 40px;">
      <img src="../assets/images/logo.png">
    </div>
    <div fxFlex="2%"></div>
    <div fxFlex="36%" fxLayoutAlign="start">
      <div fxFlex="1%"></div>
      <h3 matSubheader class="adm-head">{{pageName}}</h3>
    </div>
  </div>
  <div class="options-container">
    <app-toolbar-options></app-toolbar-options>
  </div>
</div>
<div *ngIf="!mobile">
  <mat-toolbar class="toolbar2" fxLayout="row" fxLayoutGap="10px">
    <span class="industry-heading">Industry Members</span>
    <div fxFlex="73%"></div>
    <div class="header-search">
      <input type="text" name="search" placeholder="Search.." [formControl]="KEYWORD"
        (keyup)="applyFilter($event.target.value)" style="
          font-style: normal;
          font-weight: normal;
          font-size: 14px; color: #2F3941;" autocomplete="off">
    </div>
    <div>
      <button mat-button class="add-btn" (click)="addIntraIndustryUser()" style="width: 140px; margin-top: 5px;">
        <mat-icon style="font-size: 16px; padding-top: 3px; margin-left: -5px;">add</mat-icon>
        <span class="bolder">Add New User</span>
      </button>
    </div>
  </mat-toolbar>
  <div class="table-scroll">
    <table mat-table class="admin-table" [dataSource]="dataSource" matSort #table>
      <div>
        <ng-container matColumnDef="first_name">
          <th mat-header-cell *matHeaderCellDef style="width: 15%;"><b>First Name</b>
            <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;"
              *ngIf="!fname_asc && !fname_desc" (click)="sortIntraIndustryMembers('FIRST_NAME', 'ASC')">
            <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="fname_asc"
              (click)="sortIntraIndustryMembers('FIRST_NAME', 'DESC')">
            <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;"
              *ngIf="fname_desc" (click)="sortIntraIndustryMembers('', '')">
          </th>
          <td mat-cell *matCellDef="let element" class="borderleft"> {{element.FIRST_NAME}} </td>
        </ng-container>

        <ng-container matColumnDef="last_name">
          <th mat-header-cell *matHeaderCellDef style="width: 15%;"><b>Last Name</b>
            <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;"
              *ngIf="!lname_asc && !lname_desc" (click)="sortIntraIndustryMembers('LAST_NAME', 'ASC')">
            <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="lname_asc"
              (click)="sortIntraIndustryMembers('LAST_NAME', 'DESC')">
            <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;"
              *ngIf="lname_desc" (click)="sortIntraIndustryMembers('', '')">
          </th>
          <td mat-cell *matCellDef="let element"> {{element.LAST_NAME}} </td>
        </ng-container>

        <ng-container matColumnDef="email">
          <th mat-header-cell *matHeaderCellDef style="width: 23%;"><b>Email ID</b>
            <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;"
              *ngIf="!email_asc && !email_desc" (click)="sortIntraIndustryMembers('EMAIL_ID', 'ASC')">
            <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="email_asc"
              (click)="sortIntraIndustryMembers('EMAIL_ID', 'DESC')">
            <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;"
              *ngIf="email_desc" (click)="sortIntraIndustryMembers('', '')">
          </th>
          <td mat-cell *matCellDef="let element"> {{element.EMAIL_ID}} </td>
        </ng-container>

        <ng-container matColumnDef="role">
          <th mat-header-cell *matHeaderCellDef style="width: 15%;"><b>Role</b>
            <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;"
              *ngIf="!role_asc && !role_desc" (click)="sortIntraIndustryMembers('ROLE_NAME', 'ASC')">
            <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="role_asc"
              (click)="sortIntraIndustryMembers('ROLE_NAME', 'DESC')">
            <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="role_desc"
              (click)="sortIntraIndustryMembers('', '')">
          </th>
          <td mat-cell *matCellDef="let element">
            <span *ngIf="element.MEMBER_ROLE_ID == 0">{{element.ROLE_NAME}} </span>
            <span *ngIf="element.MEMBER_ROLE_ID != 0">{{element.MEMBER_ROLE_ID == 1? 'Admin': 'General'}} </span>
          </td>
        </ng-container>

        <ng-container matColumnDef="contact">
          <th mat-header-cell *matHeaderCellDef style="width: 15%;"><b>Contact no.</b></th>
          <td mat-cell *matCellDef="let element"> {{element.MOBILE}} </td>
        </ng-container>

        <ng-container matColumnDef="action">
          <th mat-header-cell *matHeaderCellDef style="width: 10%;" class="borderright"><b>Action</b></th>
          <td mat-cell *matCellDef="let element">
            <div fxLayout="row" fxLayoutGap="10px">
              <div fxLayout="column" *ngIf="element.status == 'Confirmed'" style="position: relative; top: 5px;">
                <mat-slide-toggle (change)="onValChange($event, element.C_ID, element.ID)" [checked]="element.actionVal"
                  class="slider">
                </mat-slide-toggle>
                <p *ngIf="element.actionVal == true" style="font-style: normal;
                font-weight: normal;
                font-size: 12px;color: rgba(0, 0, 0, 0.6); margin-top: -2px;">Enabled</p>
                <p *ngIf="element.actionVal== false" style="
                font-style: normal;
                font-weight: normal;
                font-size: 12px;color: rgba(0, 0, 0, 0.6); margin-top: -2px;">Disabled</p>
              </div>
              <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                (click)="approve(element, 'Internal')"
                style="position: relative; top: 10px; display: flex; align-items: center;">
                <img src="../../assets/images/Admin-Approve.png" width="15px">
                <p style="
                font-style: normal;
                font-weight: normal;
                font-size: 12px;color: rgba(4, 165, 133, 0.6);">Approve</p>
              </div>
              <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                (click)="rejectUser(element, 'Internal')"
                style="position: relative; top: 10px; left: 0px; display: flex; align-items: center">
                <img src="../../assets/images/reject-red.png" width="15px">
                <p style="
                font-style: normal;
                font-weight: normal;
                font-size: 12px;color: rgba(237, 47, 69, 0.6);">Reject</p>
              </div>
              <div fxLayout="column" fxLayoutGap="3px" (click)="editAdvertiser(element)"
                style="position: relative;top: 10px;  left: 7px;">
                <img src="../assets/images/edit-icon.svg" width="13px">
                <p style="
                font-style: normal;
                font-weight: normal;
                font-size: 12px;color: rgba(0, 0, 0, 0.6);">Edit</p>
              </div>
            </div>
          </td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;">
        </tr>
      </div>
    </table>
  </div>
  <mat-paginator (page)="changePage($event)" [pageSize]="10" [pageIndex]="0" *ngIf="!noData"></mat-paginator>
  <span class="label" *ngIf="!noData"> {{rangeLabel}} of {{totalCount}}</span>
  <div class="text-message" *ngIf="noData">
    <span>
      <br>
      No Data available ....
      <br>
    </span>
  </div>
</div>
<div *ngIf="mobile">
  {{moveToDashboard()}}
</div>