.chart-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.loader-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 450px;
}

.zoomable-sunburst svg {
    width: 100%;
    height: auto;
    display: block;
}

.sunburst-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 6px 10px;
    font-size: 12px;
    border-radius: 4px;
    pointer-events: none;
    transition: opacity 0.2s ease-in-out;
}

button {
    margin-top: 10px;
    margin-right: 5px;
    padding: 6px 12px;
    font-size: 13px;
    cursor: pointer;
    background-color: #007acc;
    color: white;
    border: none;
    border-radius: 4px;
}

button:hover {
    background-color: #005fa3;
}