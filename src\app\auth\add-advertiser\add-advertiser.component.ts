import { Component, HostListener, Inject, OnInit, Renderer2 } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { colorObj } from 'src/app/shared/color-object';
import { environment } from 'src/environments/environment';
import { AuthService } from '../../services/auth.service';
import { NotificationService } from '../../services/notification.service';

@Component({
  selector: 'app-add-advertiser',
  templateUrl: './add-advertiser.component.html',
  styleUrls: ['./add-advertiser.component.scss']
})

export class AddAdvertiserComponent implements OnInit {
  addform: FormGroup;
  value: any;
  emailPattern = environment.emailPatterm;
  mobilenopattern = /^((\\+91-?)|0)?[0-9]{10}$/;
  usernamepattern = /^[a-z A-Z]+$/;
  passwordPattern = /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,15}$/;
  pincodepattern = /^[0-9]{6}$/;
  companyList: any[];
  titles: any[];
  companyName: any;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(private fb: FormBuilder,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private authService: AuthService,
    private renderer: Renderer2,
    private notify: NotificationService,
    private dialogRef: MatDialogRef<AddAdvertiserComponent>) {
    this.addform = this.fb.group({
      company: ['', [Validators.required]],
      companyId: [''],
      salutation: ['', [Validators.required]],
      address: ['', [Validators.required]],
      pin: ['', [Validators.required, Validators.pattern(this.pincodepattern)]],
      password: ['', [Validators.required, Validators.pattern(this.passwordPattern)]],
      fname: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(25), Validators.pattern(this.usernamepattern)]],
      lname: ['', [Validators.required, Validators.maxLength(25), Validators.pattern(this.usernamepattern)]],
      emailId: ['', [Validators.required, Validators.pattern(this.emailPattern)]],
      phone: ['', [Validators.required, Validators.pattern(this.mobilenopattern)]],
    })
  }

  ngOnInit(): void {
    if (this.data) {
      this.addform.get('company').patchValue(this.data.companyName);
      this.addform.get('companyId').patchValue(this.data.companyId);
      this.addform.controls['company'].disable();
    }
    this.titles = JSON.parse(window.localStorage.getItem('salutation'));
    this.addform.get('company')!.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0) {
          this.getCompanyList(value);
        }
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  getCompanyList(value) {
    this.authService.getCompanies(value).subscribe(res => {
      this.companyList = res.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  onSelectionChange(event) {
    this.addform.patchValue({ 'company': event.option.value })
    this.companyList.forEach(element => {
      if (event.option.value === element.COMPANY_NAME) {
        this.addform.controls['companyId'].setValue(element.ID);
      }
    });
  }

  Password() {
    var numberChars = "0123456789";
    var specialChars = "@#%&!$*"
    var upperChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    var lowerChars = "abcdefghijklmnopqrstuvwxyz";
    var allChars = numberChars + upperChars + lowerChars;
    var randPasswordArray = Array(16);
    randPasswordArray[0] = numberChars;
    randPasswordArray[1] = upperChars;
    randPasswordArray[2] = lowerChars;
    randPasswordArray[3] = specialChars;
    randPasswordArray = randPasswordArray.fill(allChars, 5);
    this.addform.patchValue({
      'password': this.shuffleArray(randPasswordArray.map(function (x) { return x[Math.floor(Math.random() * x.length)] })).join('')
    })
  }

  shuffleArray(array) {
    for (var i = array.length - 1; i > 0; i--) {
      var j = Math.floor(Math.random() * (i + 1));
      var temp = array[i];
      array[i] = array[j];
      array[j] = temp;
    }
    return array;
  }

  register(model) {
    if (model.companyId == '' || model.companyId == null) {
      model.companyId = 0;
    }
    this.authService.createAdvertiserByAdmin(model).subscribe(res => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      this.dialogRef.close('refresh');
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

}