@media only screen and (max-width: 1250px) {
    .common-toolbar-mobile {
        border-bottom: 1px solid #D8DCDE;
        border-top: 0;
        border-left: none;
        border-right: none;
    }

    .dashboard-admin-heading {
        padding-left: 0;
        padding-top: 0;
        font-weight: 600;
        font-size: 18px;
        line-height: 23.94px;
        color: #ED2F45;
        padding-left: 20px;
        padding-top: 6px;
    }

    .dashboard-container {
        width: 100%;
        height: 90%;
        background: #E5E5E5;
    }

    .dasboard-subheading {
        padding-right: 20px;
        padding-top: 0px;
        font-weight: 400;
        font-size: 14px;
        line-height: 18.62px;
        padding-left: 20px;
        padding-top: 3px;
    }
}

.common-toolbar {
    border-bottom: 1px solid #D8DCDE;
    border-top: 0;
    border-left: none;
    border-right: none;
}

.heading-container {
    width: 60%;
}

.options-container {
    width: 40%;
}

.migration-container {
    width: 100%;
    padding-left: 4%;
    background: #F8F9F9;
    height: 90%;
    overflow-y: scroll;
    overflow-x: hidden;
}

.migration-container::-webkit-scrollbar {
    display: none;
}

.migration-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

::ng-deep .mat-form-field-flex>.mat-form-field-infix {
    padding: 0.4em 0px !important;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-label {
    margin-top: -5px;
    margin-bottom: -5px;
}

::ng-deep label.ng-star-inserted {
    transform: translateY(-0.59375em) scale(.75) !important;
}

.mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0em 139px 0em 2px;
}

::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: #b0ebf0;
}

:host ::ng-deep .mat-checkbox .mat-checkbox-checkmark-path {
    stroke: #0088CB !important;
}

.comp-head {
    margin-left: 20px;
    color: #000000;
    font-style: normal;
    font-weight: 700;
    font-size: 13px;
    line-height: 16px;
    padding-top: 10px;
}

.comp-head-mandatory {
    color: red;
    margin-left: 20px;
    position: relative;
    top: 7px;
    font-style: normal;
    font-weight: normal;
    font-size: 11px;
    line-height: 13px;
}

.step1-container {
    width: 100%;
    margin: 17px 0px;
}

.row-container {
    display: flex;
    flex-direction: row;
    margin-left: 19px;
}

.input-container {
    display: flex;
    flex-direction: column;
    gap: 0px;
    width: 100%;
}

.input-container1 {
    display: flex;
    flex-direction: column;
    gap: 0px;
    width: 33.3%;
}

.text-container {
    margin-bottom: 0px;
}

.control-container {
    margin-top: 0px;
}

.basic-input-field {
    width: 73%;
}

.basic-input-field1 {
    width: 91%;
}

.basic-input-field2 {
    width: 24.3%;
}

.outer-btn-container {
    margin-left: 19px;
    margin-bottom: 10px;
    width: 100%;
    display: flex;
    flex-direction: row;
}

.outer-row-container {
    background: #F8F9F9;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
    width: 90%;
    padding-right: 20px;
    padding-top: 10px;
    margin-bottom: 2%;
    display: flex;
    flex-direction: column;
}

.delete-red {
    background: #FFFFFF;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
    height: 30px;
    margin-top: -1%;
}

.inner-row-container {
    display: flex;
    flex-direction: row;
    gap: 30px;
    margin-left: 19px;
}

.input-field {
    width: 98%;
}

#url-icon {
    font-size: small;
    margin-bottom: 10px;
}

.url-container-text {
    position: relative;
    top: -8px;
}

.example-boundary {
    width: 82vw;
    height: 12vh;
    background: #FEFEFE;
    border: 1px dashed #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
    padding: 2% 35% 2% 37%;
}

.filename-holder {
    border: 1px solid #436ab3;
    width: 1188px;
    padding: 6.5px;
}

.progress-bar-upload {
    margin-left: 19px;
    width: 1188px;
}

#progress-id {
    width: 102%;
    margin-left: 1.33px;
}

.progress-spinner {
    margin: 0 10px;
}

.adv_docs {
    width: 1100px;
    background: #FFFFFF;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
}

.adv-file-text {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 820px;
    margin: 0px;
    padding: 7px;
}

.add-source-button {
    background: #FFFFFF;
    color: #5A6F84;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 12px;
    justify-content: center;
    margin-bottom: 10px;
}

.textarea-field {
    width: 91%;
    height: 150px;
    max-width: 100%;
    box-sizing: border-box;
    border-radius: 4px;
    margin-bottom: 70px;
    color: #000000;
}

.outer-btn-container1 {
    margin-left: 19px;
    margin-bottom: 10px;
    width: 100%;
    display: flex;
    flex-direction: row;
}

.outer-row-container1 {
    background: #F8F9F9;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
    width: 89%;
    padding-right: 20px;
    padding-top: 10px;
    margin-bottom: 3%;
}

.textarea-field1 {
    width: 836px;
    height: 150px;
    max-width: 100%;
    box-sizing: border-box;
    border-radius: 4px;
}

[data-placeholder]:empty:before {
    content: attr(data-placeholder);
    color: rgba(0, 0, 0, 0.6);
}

.claims-input-field {
    width: 315px !important;
}

.divider-container {
    width: 100%;
    margin: 10px 20px 30px 20px;
}

.upload-container {
    position: relative;
    overflow: hidden;
    display: inline-block;
}

.upload-btn {
    background-color: #CFD7DF;
    color: rgba(0, 0, 0, 0.6);
    width: 225px;
    height: 35px;
    margin-top: 5px;
}

.upload-container input[type=file] {
    font-size: 100px;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
}

.btn-container {
    display: flex;
    flex-direction: row;
    gap: 10px;
    margin-left: 20px;
    margin-bottom: 3%;
}

.next-btn,
.submit-btn {
    width: 80px;
    border-radius: 15px;
    background-color: #0088CB;
    color: white;
}

.cancel-btn,
.back-btn {
    width: 80px;
    border-radius: 15px;
}

.example-boundary1 {
    width: 91%;
    height: 12vh;
    background: #F8F9F9;
    border: 1px dashed #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
    padding: 2% 3% 2% 3%;
    margin-bottom: 5%;
}

*,
*:before,
*:after {
    box-sizing: border-box;
}

.objection-text {
    box-sizing: border-box;
    position: relative;
    color: #92A2B1;
    height: 150px !important;
    border-radius: 4px !important;
    background-position: 5%;
    justify-content: center !important;
    align-items: center !important;
    margin-bottom: 70px;
}

.link-text {
    color: #0088CB;
    text-decoration: underline;
    cursor: pointer;
}

::ng-deep .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
    border-color: rgb(66, 134, 244) !important;
}

::ng-deep .mat-radio-button.mat-accent .mat-radio-inner-circle {
    color: rgb(41, 123, 255) !important;
    background-color: rgb(66, 134, 244) !important;
}