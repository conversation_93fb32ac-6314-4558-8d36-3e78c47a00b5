import { Component, ElementRef, HostListener, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource, _MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { element } from 'protractor';
import { BehaviorSubject, Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { AuthService } from 'src/app/services/auth.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';
import { AddAdvertiserComponent } from '../add-advertiser/add-advertiser.component';
import { EditAdvertiserComponent } from '../edit-advertiser/edit-advertiser.component';

@Component({
  selector: 'app-intra-administration',
  templateUrl: './intra-administration.component.html',
  styleUrls: ['./intra-administration.component.scss']
})
export class IntraAdministrationComponent implements OnInit {
  pageName: String;
  userData: any;
  roleId: any;
  industryMemberRole: any;
  loading: boolean = true;
  checked: boolean = false;
  intraRespArray: Array<any> = [];
  dataSource: any = [];
  noData: boolean = false;
  intraPageNumber: number = 0;
  private request$: Observable<any>;
  KEYWORD = new FormControl(null);
  private _intraUserData = new BehaviorSubject<any[]>([]);
  private IntraUserDataStore: { $intraUserData: any[] } = { $intraUserData: [] };
  readonly $intraUserData = this._intraUserData.asObservable();
  totalCount: any;
  startIndex: number;
  pageSize: number = 10;
  lastIndex: number;
  rangeLabel: string;
  limit: number = 0;
  intraFilterArray: any[];
  intraSortedArray: any[];
  fname_asc: boolean = false;
  fname_desc: boolean = false;
  lname_asc: boolean = false;
  lname_desc: boolean = false;
  email_asc: boolean = false;
  email_desc: boolean = false;
  role_asc: boolean = false;
  role_desc: boolean = false;
  intraFilter: boolean = false;
  sortingKey: any = '';
  sortingOrder: any = '';
  mobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.mobile = true;
    } else {
      this.mobile = false;
    }
  }

  constructor(public dialog: MatDialog,
    private router: Router,
    private authService: AuthService,
    private notify: NotificationService,
    private el: ElementRef<HTMLElement>) { }
  @ViewChildren(MatPaginator) paginator = new QueryList<MatPaginator>();
  displayedColumns: string[] = ['first_name', 'last_name', 'email', 'role', 'contact', 'action'];

  ngOnInit(): void {
    this.pageName = 'Administration : Member Management';
    this.userData = JSON.parse(window.localStorage.getItem('userInfo'));
    this.roleId = this.userData.roleId;
    this.industryMemberRole = this.userData.industryMemberRoleId;
    this.KEYWORD.setValue('');
    this.getIntraIndustryMembers();
  }

  getIntraIndustryUsersList(searchVal) {
    this.limit = 20;
    this.intraPageNumber++;
    this.request$ = this.authService.getIntraIndustryMembersList(this.intraPageNumber, searchVal, this.limit, this.sortingKey, this.sortingOrder);
    return this.request$;
  }

  getIntraIndustryMembers() {
    this.getIntraIndustryUsersList(this.KEYWORD.value)
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((res: any) => {
        this.loading = false;
        if (res.data.length > 0) {
          this.totalCount = res.data[0].TOTAL_COUNT;
          if (this.intraPageNumber == 1) {
            if (this.pageSize > this.totalCount) {
              this.rangeLabel = 1 + ' - ' + this.totalCount;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.intraRespArray = this.intraRespArray.concat(res.data[0].DATA);
          if (this.intraRespArray.length == 0) {
            this.noData = true;
          } else {
            this.noData = false;
          }
          this.IntraUserDataStore.$intraUserData = this.intraRespArray;
          this.dataSource.data = this.IntraUserDataStore.$intraUserData;
          this.dataSource = new MatTableDataSource<any>(this.IntraUserDataStore.$intraUserData);
          this._intraUserData.next(Object.assign({}, this.IntraUserDataStore).$intraUserData);
          this.dataSource.paginator = this.paginator.toArray()[0];
          this.dataSource.data.forEach(element => {
            if (element['USER_CONFIRMED'] == 1 && element['COMPANY_ID'] != 0) {
              element['status'] = 'Confirmed';
            } else if ((element['USER_CONFIRMED'] == 0 && element['COMPANY_ID'] != 0)) {
              element['status'] = 'Unconfirmed';
            } else {
              element['status'] = 'Not exist';
            }

            if (element['USER_ENABLED'] == 1) {
              element['actionVal'] = true;
            } else {
              element['actionVal'] = false;
            }

            if (element['REGISTERED'] == 1) {
              element['pro_member'] = true;
            } else {
              element['pro_member'] = false;
            }
          });
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      })
  }

  private onFinalize(): void {
    this.request$ = null;
  }

  applyFilter(filterValue: string) {
    this.startIndex = null;
    this.lastIndex = null;
    this.pageSize = 10;
    this.totalCount = null;
    if (filterValue.length == 0) {
      this.intraPageNumber = 0;
      this.intraFilterArray = [];
      this.dataSource.data = '';
      this.noData = false;
      this.KEYWORD.setValue('');
      this.intraFilter = false;
      this.getIntraIndustryMembers();
    } else {
      this.intraFilter = true;
      this.intraRespArray = [];
      this.intraPageNumber = 0;
      this.noData = false;
      this.KEYWORD.setValue(filterValue);
      this.filterIntraIndustryMembers();
    }
  }

  filterIntraIndustryMembers() {
    this.noData = false;
    this.dataSource.data = '';
    this.getIntraIndustryUsersList(this.KEYWORD.value)
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((res: any) => {
        this.intraRespArray = [];
        this.intraSortedArray = [];
        this.intraFilterArray = [];
        if (res.data.length > 0) {
          this.totalCount = res.data[0].TOTAL_COUNT;
          if (this.intraPageNumber == 1) {
            if (res.data[0].TOTAL_COUNT < this.pageSize) {
              this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.noData = false;
          this.intraFilterArray = this.intraFilterArray.concat(res.data[0].DATA);
          this.IntraUserDataStore.$intraUserData = this.intraFilterArray;
          this.dataSource.data = this.IntraUserDataStore.$intraUserData;
          this.dataSource = new MatTableDataSource<any>(this.IntraUserDataStore.$intraUserData);
          this.dataSource.paginator = this.paginator.toArray()[0];
          this._intraUserData.next(Object.assign({}, this.IntraUserDataStore).$intraUserData);
          this.dataSource.data.forEach(element => {
            if (element['USER_CONFIRMED'] == 1) {
              element['status'] = 'Confirmed';
            } else {
              element['status'] = 'Unconfirmed';
            }
            if (element['USER_ENABLED'] == 1) {
              element['actionVal'] = true;
            } else {
              element['actionVal'] = false;
            }
          });
        } else {
          this.noData = true;
          this.intraFilterArray = [];
          this.dataSource.data = '';
        }
      })
  }

  sortIntraIndustryMembers(key, order) {
    this.sortingKey = key;
    this.sortingOrder = order;
    this.intraPageNumber = 0;
    if (order != '') {
      if (order == 'ASC') {
        if (key == 'FIRST_NAME') {
          this.fname_asc = true;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.role_asc = false;
          this.role_desc = false;
        }
        else if (key == 'LAST_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = true;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.role_asc = false;
          this.role_desc = false;
        }
        else if (key == 'EMAIL_ID') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = true;
          this.email_desc = false;
          this.role_asc = false;
          this.role_desc = false;
        }
        else if (key == 'ROLE_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.role_asc = true;
          this.role_desc = false;
        }
      }
      else if (order == 'DESC') {
        if (key == 'FIRST_NAME') {
          this.fname_asc = false;
          this.fname_desc = true;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.role_asc = false;
          this.role_desc = false;
        }
        else if (key == 'LAST_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = true;
          this.email_asc = false;
          this.email_desc = false;
          this.role_asc = false;
          this.role_desc = false;
        }
        else if (key == 'EMAIL_ID') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = true;
          this.role_asc = false;
          this.role_desc = false;
        }
        else if (key == 'ROLE_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.role_asc = false;
          this.role_desc = true;
        }
      }
      this.noData = false;
      this.dataSource.data = '';
      if (this.intraFilter == true) {
        this.filterIntraIndustryMembers();
      }
      else {
        this.noData = false;
        this.dataSource.data = '';
        this.getIntraIndustryUsersList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            this.intraRespArray = [];
            this.intraFilterArray = [];
            this.intraSortedArray = [];
            if (res.data.length > 0) {
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.intraPageNumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              this.noData = false;
              this.intraSortedArray = this.intraSortedArray.concat(res.data[0].DATA);
              this.IntraUserDataStore.$intraUserData = this.intraSortedArray;
              this.dataSource.data = this.IntraUserDataStore.$intraUserData;
              this.dataSource = new MatTableDataSource<any>(this.IntraUserDataStore.$intraUserData);
              this.dataSource.paginator = this.paginator.toArray()[0];
              this._intraUserData.next(Object.assign({}, this.IntraUserDataStore).$intraUserData);
              this.dataSource.data.forEach(element => {
                if (element['USER_CONFIRMED'] == 1) {
                  element['status'] = 'Confirmed';
                } else {
                  element['status'] = 'Unconfirmed';
                }
                if (element['USER_ENABLED'] == 1) {
                  element['actionVal'] = true;
                } else {
                  element['actionVal'] = false;
                }
              });
            }
          })
      }
    }
    else if (order == '') {
      this.fname_asc = false;
      this.fname_desc = false;
      this.lname_asc = false;
      this.lname_desc = false;
      this.email_asc = false;
      this.email_desc = false;
      this.role_asc = false;
      this.role_desc = false;
      if (this.intraFilter == true) {
        this.filterIntraIndustryMembers();
      }
      else {
        this.dataSource.data = [];
        this.intraRespArray = [];
        this.noData = false;
        this.intraPageNumber = 0;
        this.getIntraIndustryMembers();
      }
    }
  }

  changePage(event) {
    this.startIndex = (event.pageIndex * this.pageSize) + 1;
    this.lastIndex = this.startIndex + (this.pageSize - 1);
    if (this.lastIndex > this.totalCount) {
      this.lastIndex = this.totalCount;
    }
    this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
    if (this.intraRespArray.length != 0) {
      this.getIntraIndustryMembers();
    }
    else if (this.intraFilter == true) {
      this.getIntraIndustryUsersList(this.KEYWORD.value)
        .pipe(finalize(() => this.onFinalize()))
        .subscribe((res: any) => {
          if (res.data.length > 0) {
            this.intraRespArray = [];
            this.totalCount = res.data[0].TOTAL_COUNT;
            if (this.intraPageNumber == 1) {
              if (res.data[0].TOTAL_COUNT < this.pageSize) {
                this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
              } else {
                this.rangeLabel = 1 + ' - ' + this.pageSize;
              }
            }
            // if (this.internalFilterArray.length == 0) {
            //   this.noData = true;
            // } else {
            //   this.noData = false;
            // }
            if (this.intraFilterArray.length != 0 && this.intraSortedArray.length == 0) {
              this.intraFilterArray = this.intraFilterArray.concat(res.data[0].DATA);
              this.IntraUserDataStore.$intraUserData = this.intraFilterArray;
            }
            else if (this.intraFilterArray.length == 0 && this.intraSortedArray.length != 0) {
              this.intraSortedArray = this.intraSortedArray.concat(res.data[0].DATA);
              this.IntraUserDataStore.$intraUserData = this.intraSortedArray;
            }
            this.dataSource.data = this.IntraUserDataStore.$intraUserData;
            this.dataSource = new MatTableDataSource<any>(this.IntraUserDataStore.$intraUserData);
            this.dataSource.paginator = this.paginator.toArray()[0];
            this.dataSource.data.forEach(element => {
              if (element['USER_CONFIRMED'] == 1) {
                element['status'] = 'Confirmed';
              } else {
                element['status'] = 'Unconfirmed';
              }
              if (element['USER_ENABLED'] == 1) {
                element['actionVal'] = true;
              } else {
                element['actionVal'] = false;
              }
            });
            this._intraUserData.next(Object.assign({}, this.IntraUserDataStore).$intraUserData);
          }
        }, err => {
          this.loading = false;
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        })
    }
    else {
      this.getIntraIndustryUsersList(this.KEYWORD.value)
        .pipe(finalize(() => this.onFinalize()))
        .subscribe((res: any) => {
          if (res.data.length > 0) {
            this.intraRespArray = [];
            this.totalCount = res.data[0].TOTAL_COUNT;
            if (this.intraPageNumber == 1) {
              if (res.data[0].TOTAL_COUNT < this.pageSize) {
                this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
              } else {
                this.rangeLabel = 1 + ' - ' + this.pageSize;
              }
            }
            this.intraSortedArray = this.intraSortedArray.concat(res.data[0].DATA);
            this.IntraUserDataStore.$intraUserData = this.intraSortedArray;
            this.dataSource.data = this.IntraUserDataStore.$intraUserData;
            this.dataSource = new MatTableDataSource<any>(this.IntraUserDataStore.$intraUserData);
            this.dataSource.paginator = this.paginator.toArray()[0];
            this.dataSource.data.forEach(element => {
              if (element['USER_CONFIRMED'] == 1) {
                element['status'] = 'Confirmed';
              } else {
                element['status'] = 'Unconfirmed';
              }
              if (element['USER_ENABLED'] == 1) {
                element['actionVal'] = true;
              } else {
                element['actionVal'] = false;
              }
            });
            this._intraUserData.next(Object.assign({}, this.IntraUserDataStore).$intraUserData);
          }
        }, err => {
          this.loading = false;
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        })
    }
  }

  onValChange(value, C_ID, id) {
    let action = 'enable';
    if (value.checked == false) {
      action = 'disable';
    }
    this.authService.enableDisableIntraIndustryUser(C_ID, action).subscribe(res => {
      this.getIntraIndustryMemberById(id);
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "warning"),
        err.error.status
      )
      return;
      // value.checked = !value.checked;
    })
  }

  addIntraIndustryUser() {
    const dialogRef = this.dialog.open(AddAdvertiserComponent, {
      data: { companyName: this.userData.companyName, companyId: this.userData.companyId },
      width: '610px',
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'refresh') {
          this.intraPageNumber = 0;
          this.intraRespArray = [];
          this.dataSource.data = '';
          this.rangeLabel = 1 + ' - ' + this.pageSize;
          this.getIntraIndustryMembers();
        }
      }
    );
  }

  editAdvertiser(row) {
    const dialogRef = this.dialog.open(EditAdvertiserComponent, {
      width: '610px',
      data: { isEditByCompanyAdmin: true, row },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'u-refresh') {
          this.getIntraIndustryMemberById(row.ID);
          dialogRef.close('refresh');
        }
        if (data === 'd-refresh') {
          this.removeIntraIndustryMemberFromStore(row.ID);
          dialogRef.close('refresh');
        }
      }
    );
  }

  getIntraIndustryMemberById(userId) {
    this.authService.getIntraIndustryMemberDetailsByAdmin(userId).subscribe(res => {
      let userObj = res.data[0];
      this.IntraUserDataStore.$intraUserData.forEach((t: any, i) => {
        if (t.ID === userObj.ID) {
          this.IntraUserDataStore.$intraUserData[i] = userObj;
        }
      });
      this._intraUserData.next(Object.assign({}, this.IntraUserDataStore).$intraUserData);
      this.updateIntraIndustryUsersTableData(userObj.ID);
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  updateIntraIndustryUsersTableData(id) {
    this.$intraUserData.subscribe((res) => {
      this.intraRespArray = res;
      this.dataSource.data = res;
      this.dataSource = new MatTableDataSource(res);
      this.dataSource.paginator = this.paginator.toArray()[0];
      this.dataSource.data.forEach(element => {
        if (id == element.ID) {
          if (element['USER_CONFIRMED'] == 1 && element['COMPANY_ID'] != 0) {
            element['status'] = 'Confirmed';
          } else if ((element['USER_CONFIRMED'] == 0 && element['COMPANY_ID'] != 0)) {
            element['status'] = 'Unconfirmed';
          } else {
            element['status'] = 'Not exist';
          }
          if (element['USER_ENABLED'] == 1) {
            element['actionVal'] = true;
          } else {
            element['actionVal'] = false;
          }
        }
      });
    })
  }

  moveToDashboard() {
    this.router.navigateByUrl("/home/<USER>")
  }

  removeIntraIndustryMemberFromStore(id) {
    this.IntraUserDataStore.$intraUserData.forEach((t: any, i) => {
      if (t.ID === id) {
        this.IntraUserDataStore.$intraUserData.splice(i, 1);
      }
    });
    this.totalCount = this.totalCount - 1;
    if (this.totalCount !== 0) {
      if (this.pageSize > this.totalCount) {
        this.pageSize = this.totalCount;
        this.rangeLabel = 1 + ' - ' + this.pageSize;
      }
      if (this.lastIndex > this.totalCount) {
        this.lastIndex = this.totalCount;
        this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
      }
    } else {
      this.noData = true;
    }
    this._intraUserData.next(Object.assign({}, this.IntraUserDataStore).$intraUserData);
    this.dataSource = new MatTableDataSource(this.IntraUserDataStore.$intraUserData);
    this.dataSource.paginator = this.paginator.toArray()[0];
  }

}