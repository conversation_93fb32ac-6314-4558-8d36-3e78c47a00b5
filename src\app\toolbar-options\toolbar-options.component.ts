import { Component, OnInit, Input } from '@angular/core';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup, FormControl, Validators } from '@angular/forms';
import { ComplaintsService } from '../services/complaints.service';
import { Complaints } from '../model/complaints';
import { Observable } from 'rxjs';
import { startWith, map } from 'rxjs/operators';
import { MatDialog, MatDialogConfig } from "@angular/material/dialog";
import { AuthService } from '../services/auth.service';
import { ConfirmationPopupComponent } from '../confirmation-popup/confirmation-popup.component';
import { NotificationService } from '../services/notification.service';
export interface User {
  name: string;
  value: string;
}
@Component({
  selector: 'app-toolbar-options',
  templateUrl: './toolbar-options.component.html',
  styleUrls: ['./toolbar-options.component.css']
})
export class ToolbarOptionsComponent implements OnInit {

  @Input() pagename: String;
  confirmationMsg: any = {};
  complaints: Complaints[];
  pageNumber = 1;
  roleName: string;
  roleList: any[];
  userInfo: any;
  hiddenSearch: boolean = true;
  searchContainer: boolean = true;
  search_control = new FormControl();
  search_input = 'Angular';
  searchKey;
  searchKeyword;
  searchForm = new FormControl();
  options: User[] = [
    { name: "Tanishq promoting communal disharmony", value: "1" },
    { name: "Coco-cola Promoting communal Disharmony", value: "2" },
    { name: "Coco-cola Misleading the new generation", value: "3" },
  ];
  filteredOptions: Observable<User[]>

  constructor(private router: Router,
    private cs: ComplaintsService,
    // private route: ActivatedRoute,
    // private router: Router,
    // public dialog: MatDialog,
    private notify: NotificationService,
    private dialog: MatDialog,
    private formBuilder: FormBuilder,
    private authService: AuthService) { }
    

  async ngOnInit(): Promise<void>  {
    this.roleList = JSON.parse(window.localStorage.getItem('role'));
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    if (!(this.userInfo.roleId == 7 || this.userInfo.roleId == 9)) {
      this.roleList.forEach(element => {
        if(this.userInfo.roleId == element.ID){
          this.roleName = element.ROLE_NAME;
        }
      });
    }
    else if (this.userInfo.roleId == 7 || this.userInfo.roleId == 9) {
      this.roleName = this.userInfo.firstName;
    }

    this.hiddenSearch = true;;
    this.filteredOptions = this.searchForm.valueChanges.pipe(
      startWith(""),
      map(value => (typeof value === "string" ? value : value.name)),
      map(name => (name ? this._filter(name) : this.options.slice()))
    );
    
  }

  displayFn(user?: User): string | undefined {
    return user ? user.name : undefined;
  }
  returnFn(user?: User): string | undefined {
    return user ? user.value : undefined;
  }

  private _filter(name: string): User[] {
    const filterValue = name.toLowerCase();

    return this.options.filter(
      option => option.name.toLowerCase().indexOf(filterValue) === 0
    );
  }

  expandSearch() {
    this.hiddenSearch = false;
    this.searchContainer = false;
    
  }
  clearSearchFilter() {
    this.hiddenSearch = true;
    this.searchContainer = true;
    // this.searchForm.reset();
   
  }
  SearchFilter(){
    this.searchContainer = false;
  }

  logout() {
    this.confirmationMsg.title = 'Are you sure you want to logout ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.authService.logout().subscribe(
          (res) => {
            window.localStorage.clear();
            this.router.navigate(['auth/login']);
          },
          (err) => {
            window.localStorage.clear();
            this.router.navigate(['auth/login']);
          }
        );
      }
    });
  }

  viewProfile(flag) {
    this.authService.profileFlag = flag;
    this.router.navigateByUrl('auth/my-profile');
  }
 

}
