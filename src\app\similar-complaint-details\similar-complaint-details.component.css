.complaint-container {
    width: 100% !important;
    height: 70vh;
}
.mat-card-scroll {
    height: 70vh;
}
.comp-head-container,
.classfy-head {
    position: relative;
    margin-top: 10px;
    display: flex;
}
.comp-head {
    font-size: 14px;
    line-height: 16px;
    color: #000000;
    width: -webkit-fill-available;
    margin-bottom: 5px;
    font-weight: 550;
}
.classfy-head-divider {
    position: relative;
}
.contents-scroll {
    -ms-overflow-style: none;
    scrollbar-width: none;
    height: 67vh;
    overflow-y: scroll;
    overflow-x: hidden;
    margin-right: 2px;
}
.contents-scroll::-webkit-scrollbar {
    display: none;
}
.top-divider-container {
    margin-top: 3%;
    width: 100%;
    margin-left: 0px !important;
}
.details-container {
    border: 1px solid #D8DCDE;
    background: #ffffff;
    margin-right: 12px;
    padding: 21px;
    box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.08);
    margin-bottom: 21px;
    margin-left: 12px;
    margin-top: 21px;
}
.classfy-head {
    position: relative;
    flex-direction: row;
    box-sizing: border-box;
    display: flex;
    height: 30px;
    width: 100%;
}
.divider-container {
    position:absolute;
    top: 7px;
    width: 80%;
    margin-left: 170px;
}
.detail-left-container {
    width: 60%;
}
.detail-attribute {
    color: rgba(0, 0, 0, 0.4);
    font-size: 14px;
    line-height: 19px;
}
.detail-value {
    color: #2F3941;
    line-height: 19px;
    font-size: 14px;
    color: #000000
}
.comp-msg-container {
    width: 90%;
    text-align: justify;
    font-size: 14px;
    line-height: 19px;
}
.detail-right-container {
    width: 40%;
}
.media-anchor {
    color: #0088CB;
    word-break: break-all;
    cursor: pointer;
}
.attachment-box {
    background: #FFFFFF;
    border: 1px solid #F5F5F7;
    box-sizing: border-box;
    border-radius: 4px;
    padding: 13px !important;
    width: 275px;
    align-items: center;
    vertical-align: middle;
    margin-bottom: 15px;
    margin-right: 15px;
}
.media-anchor1 {
    color: #0088CB;
    word-break: break-all;
    cursor: pointer;
    text-overflow: ellipsis !important;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
.close-btn {
    color: solid rgba(47, 57, 65, 0.6);
    background: #F3F3F3;
    margin-top: -42px;
    margin-right: -21px;
    border: 1px solid rgba(47, 57, 65, 0.6);
}