<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

<div class="common-toolbar-mobile" style="width: 100%; height: auto;" *ngIf="mobile">
  <app-mobile-header></app-mobile-header>
</div>
<span class="dashboard-admin-heading" *ngIf="mobile">
  <p style="text-align: center;margin-top: 20px;margin-bottom: 3px;"> Welcome {{userName}}!</p>
</span>
<div class="dasboard-subheading" *ngIf="mobile">
  <p style="text-align: center;">Dear ASCI Officers, please move to a larger device for seamlessly accessing all
    functionalities of the system</p>
</div>
<div class="dashboard-container" *ngIf="mobile">
</div>

<app-icons *ngIf="!mobile"></app-icons>

<app-mat-spinner-overlay overlay="true" *ngIf="loading && !mobile">
</app-mat-spinner-overlay>

<div fxLayout="row" style="height: 100%; width: 100%;" *ngIf="!mobile">
  <div class="manage">
    <div class="head-container">
      <div class="comp-head" *ngIf="hiddenSearch">
        <h3 matSubheader class="head-text"> Complaints</h3>
      </div>
      <span class="head-spacer" *ngIf="hiddenSearch"></span>
      <div class="search-container">
        <button mat-icon-button class="search-btn" (click)='expandSearch()' *ngIf="hiddenSearch && searchContainer">
          <img class="hide-icon" src="../assets/images/search-icon.png"
            style="width: 14px;position: relative;bottom: 2px;" />
        </button>
        <div [hidden]="hiddenSearch" style="padding-left: 5px;">
          <form [formGroup]="searchForm">
            <div class="search-box-container" fxLayout="row">
              <div class="flex" style="width: 80% !important;">
                <input type="text" class="search-box" id="search"
                  placeholder="Search: Enter Keyword / Case ID/ Tracking" formControlName="searchKey"
                  autocomplete="off">
              </div>
              <div class="xy flex" style="width: 10% !important;">
                <span aria-hidden="true" (click)="clearSearchFilter()" style="cursor: pointer;">
                  <img src="../assets/images/search_close.svg">
                </span>
              </div>
              <div class="xy flex"
                style="width: 10% !important;background-color: #0088CB;border-radius: 0px 12px 12px 0px;">
                <span class="ab" class="fa fa-search" aria-hidden="true" (click)="onSubmit()"
                  style="cursor: pointer;color: #ffffff;"></span>
              </div>
            </div>
          </form>
        </div>
      </div>
      <div class="filter-container" *ngIf="hiddenSearch">
        <button mat-icon-button class="filter-btn" (click)='clearFilter()' [popoverOnHover]="false"
          [popover]="myPopover">
          <img class="hide-icon" src="../assets/images/filter-icon.png"
            style="width: 14px;position: relative;bottom: 2px;right: 1px;" />
        </button>

        <popover-content #myPopover class="filter-popover" placement="left bottom" [animation]="true"
          [closeOnClickOutside]="false" [closeOnMouseOutside]="false">
          <form [formGroup]="filterForm" class="filterForm" (ngSubmit)="onSubmit()">
            <div class="form-group" fxLayout="column">
              <div class="filter-head-container">
                <p class="filter-head">Apply filter based on</p>
              </div>
              <div class="input-container" fxLayout="column" fxLayoutGap="9px">
                <div class="text-container">
                  <mat-label class="filter-label">
                    <img class="filter-img-icon" src="../assets/images/tree.png " />
                    &nbsp;Similar Case ID
                  </mat-label>
                </div>
                <!-- <div class="control-container" fxLayout="row" style="border:1px solid #D8DCDE;border-radius: 2px;"> -->
                <!-- <mat-form-field appearance="outline" class="filter-field"> -->
                <input matInput class="company-input" type="text" formControlName="similar_case" />
                <!-- <button matSuffix mat-icon-button type="button"> -->
                <!-- <mat-icon class="input-icon">search</mat-icon> -->
                <!-- <img class="brand-search-icon" src="../assets/images/filter_brand-icon.svg "/>
                        </button> -->
                <!-- </mat-form-field> -->
                <!-- <input type="text" class="company-input">
                    <img class="brand-search-icon" src="../assets/images/filter_brand-icon.svg "/>
                  </div> -->
              </div>
              <div class="input-container" fxLayout="column" fxLayoutGap="9px">
                <div class="text-container">
                  <!-- <span id="label-icon" class="material-icons-outlined">bar_chart</span> -->
                  <mat-label class="filter-label">
                    <img class="filter-img-icon" src="../assets/images/brand_icon.svg " />
                    &nbsp;Brand Name
                  </mat-label>
                </div>
                <!-- <div class="control-container" fxLayout="row" style="border:1px solid #D8DCDE;border-radius: 2px;"> -->
                <!-- <mat-form-field appearance="outline" class="filter-field"> -->
                <input matInput class="company-input" type="text" formControlName="company"
                  [matAutocomplete]="autoCompany" />
                <mat-autocomplete #autoCompany="matAutocomplete">
                  <mat-option *ngFor="let company of companyList;" [value]="company.COMPANY_NAME" style="
                              font-style: normal;
                              font-weight: normal;">
                    {{company.COMPANY_NAME}}
                  </mat-option>
                </mat-autocomplete>
                <!-- <button matSuffix mat-icon-button type="button"> -->
                <!-- <mat-icon class="input-icon">search</mat-icon> -->
                <!-- <img class="brand-search-icon" src="../assets/images/filter_brand-icon.svg "/>
                        </button> -->
                <!-- </mat-form-field> -->
                <!-- <input type="text" class="company-input">
                    <img class="brand-search-icon" src="../assets/images/filter_brand-icon.svg "/>
                  </div> -->
              </div>
              <div class="input-container" fxLayout="column" fxLayoutGap="9px">
                <div class="text-container">
                  <!-- <span id="label-icon" class="material-icons-outlined">flag</span>  -->
                  <mat-label class="filter-label">
                    <img class="filter-img-icon" src="../assets/images/filter_status-icon.svg " />
                    &nbsp;Complaint Status
                  </mat-label>
                </div>
                <!-- <div class="control-container"> -->
                <!-- <mat-form-field appearance="outline" class="filter-field">
                      <mat-select formControlName="status" class="tag-select" placeholder="Select">
                          <mat-option value="New" *ngFor="let status of complaintStatusList" class="filter-label" style="padding-left: 5%;" [value]="status.ID">
                            {{status.COMPLAINT_STATUS_NAME}}
                          </mat-option> -->
                <!-- <mat-option>Select</mat-option> -->
                <!-- </mat-select> -->
                <!-- <mat-icon matSuffix class="select-icon">keyboard_arrow_down</mat-icon> -->
                <!-- </mat-form-field> -->
                <mat-select formControlName="status" class="filter-select" placeholder="Select">
                  <mat-option [value]="status.ID" *ngFor="let status of complaintStatusList">
                    {{status.COMPLAINT_STATUS_NAME}}</mat-option>
                </mat-select>
                <!-- </div> -->
              </div>

              <div class="input-container" fxLayout="column" fxLayoutGap="9px">
                <div class="text-container">
                  <!-- <span id="label-icon" class="material-icons-outlined">flag</span>  -->
                  <mat-label class="filter-label">
                    <img class="filter-img-icon" src="../assets/images/source.png " />
                    &nbsp;Complaint Source
                  </mat-label>
                </div>
                <mat-select formControlName="source" class="filter-select" placeholder="Select">
                  <mat-option [value]="source.ID" *ngFor="let source of complaintSource">
                    {{source.COMPLAINT_SOURCE_NAME}}</mat-option>
                </mat-select>
                <!-- </div> -->
              </div>
              <div class="input-container" fxLayout="column" fxLayoutGap="9px">
                <div class="text-container">
                  <mat-label class="filter-label">
                    <img class="filter-img-icon" src="../assets/images/filter_tag-icon.svg " />
                    &nbsp;Classification Tag
                  </mat-label>
                </div>
                <!-- <div class="control-container"> -->
                <!-- <mat-form-field appearance="outline" class="filter-field">
                          <mat-select formControlName="tag" class="tag-select">
                            <mat-option *ngFor="let classify of classification" value="Disharmony" [value]="classify.ID" class="filter-label">
                              {{classify.CLASSIFICATION_NAME}}
                            </mat-option>
                          </mat-select> -->
                <!-- <mat-icon matSuffix class="select-icon">keyboard_arrow_down</mat-icon> -->
                <!-- </mat-form-field> -->
                <mat-select formControlName="tag" class="filter-select">
                  <mat-option *ngFor="let classify of classification" [value]="classify.ID">
                    {{classify.CLASSIFICATION_NAME}}
                  </mat-option>
                </mat-select>
              </div>

              <div class="btn-container" fxLayoutAlign="end end">
                <button mat-button type="submit" (click)='myPopover.hide()' class="apply-btn">
                  <span class="bolder">
                    Apply
                  </span>
                </button>
              </div>
            </div> <!-- form-group -->
          </form>
        </popover-content>
      </div> <!-- filter-container -->
    </div>

    <div class="complaints-filter-container" *ngIf="filterContainer">
      <!-- <input class="search-tags" type="text"> -->
      <!-- <mat-form-field appearance="outline" for="search"> -->
      <div class="filter-tag-container">
        <mat-chip-list class="filter-chip-container">
          <mat-chip class="filter-chips" [removable]="removable" (removed)="remove(attribute)"
            *ngFor="let attribute of search;let in of index">
            <div class="chip-icon-container">
              <img id="chip-tag-icon" [ngStyle]="{'visibility': showTagIcon(attribute)}" class="filter-img-icon"
                style="width: 13px;" src="../assets/images/filter_tag-icon.svg " />
              <img id="chip-tag-icon" [ngStyle]="{'visibility':showSimilarCaseIdIcon(attribute)}" class="filter-img-icon"
                src="../assets/images/tree.png " />
                <img id="chip-tag-icon" [ngStyle]="{'visibility':showCompanyIcon(attribute)}" class="filter-img-icon"
                src="../assets/images/brand_icon.svg " />
              <img id="chip-tag-icon" [ngStyle]="{'visibility':showSourceIcon(attribute)}" class="filter-img-icon"
                src="../assets/images/source.png " />
              <img id="chip-tag-icon" [ngStyle]="{'visibility':showStatusIcon(attribute)}" class="filter-img-icon"
                src="../assets/images/filter_status-icon.svg " />

              <!-- <span id="chip-tag-icon"  [ngStyle]="{'visibility': showTagIcon(attribute)}"  class="material-icons-outlined">sell</span>
                <span id="chip-tag-icon" [ngStyle]="{'visibility':showCompanyIcon(attribute)}" class="material-icons-outlined">bar_chart</span>
                <span id="chip-tag-icon"  [ngStyle]="{'visibility':showStatusIcon(attribute)}" class="material-icons-outlined">flag</span>   -->
            </div>
            <!-- <div> -->
            <span>{{attribute.name}}</span>
            <mat-icon class="chip-close-icon" matChipRemove *ngIf="removable">cancel</mat-icon>
            <!-- </div> -->
          </mat-chip>
        </mat-chip-list>
      </div>
      <!-- </mat-form-field> -->
    </div> <!-- complaints-filter-container -->

    <mat-tab-group class="complaint-list-tab" (selectedTabChange)="onTabChanges($event)"
      style="position: relative; bottom:2px; height: 92vh;">
      <mat-tab label="CCC Complaints">
        <div class="complaints-display-container" *ngIf="displayContainer">
          <div class="complaint-list">
            <mat-list-item *ngFor="let complaint of complaints; let i = index"
              (click)="clickEvent(complaint.ID, complaint.CASE_ID, complaint.FTC, i)">
              <div class="list-item-container" *ngIf="i + 1 != complaints.length"
                [ngClass]="{'case-selected': i + 1 == selectedCaseIndex}">
                <div mat-line class="item-head-container" style="display: flex;flex-direction: row;">
                  <p>
                    <span style="font-weight: bold;">{{complaint.REQUESTER_USER_NAME}} - </span>
                    <span style="font-size:14px;font-weight: 400;"> {{complaint.BRAND_NAME}} -</span> <span
                      [innerHTML]="safeHTML(complaint.ADVERTISEMENT_DESCRIPTION)"
                      class="ellipsis">{{complaint.ADVERTISEMENT_DESCRIPTION}} </span>
                  </p>
                </div>
                <div mat-line class="item-content-container">
                  <div class="case-container" fxLayout="row">
                    <span matSuffix style="position: relative;font-size: 11px;"> Case Id: {{complaint.CASE_ID}}</span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <mat-chip-list class="status-chip">
                    <mat-chip class="comp-status"
                      *ngIf="complaint.COMPLAINT_STATUS_NAME != 'Out of remit/Outside ASCI Purview'"
                      [ngClass]="{'theme-onhold' : complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' : complaint.COMPLAINT_STATUS_NAME == 'New', 'theme-inprogress' : complaint.COMPLAINT_STATUS_NAME == 'In Progress', 'theme-resolution' : complaint.COMPLAINT_STATUS_NAME == 'Resolution', 'theme-outofremit' : complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview' || complaint.COMPLAINT_STATUS_NAME == 'Sub-Judice', 'theme-invalid' : complaint.COMPLAINT_STATUS_NAME == 'Non-Issue', 'theme-closed' : complaint.COMPLAINT_STATUS_NAME == 'Closed'}">
                      {{complaint.COMPLAINT_STATUS_NAME}}
                    </mat-chip>
                    <mat-chip class="comp-status1"
                      *ngIf="complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview'"
                      [ngClass]="{'theme-onhold' : complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' : complaint.COMPLAINT_STATUS_NAME == 'New', 'theme-inprogress' : complaint.COMPLAINT_STATUS_NAME == 'In Progress', 'theme-resolution' : complaint.COMPLAINT_STATUS_NAME == 'Resolution', 'theme-outofremit' : complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview' || complaint.COMPLAINT_STATUS_NAME == 'Sub-Judice', 'theme-invalid' : complaint.COMPLAINT_STATUS_NAME == 'Non-Issue', 'theme-closed' : complaint.COMPLAINT_STATUS_NAME == 'Closed'}"
                      matTooltip="{{complaint.COMPLAINT_STATUS_NAME}}">
                      {{complaint.COMPLAINT_STATUS_NAME | slice:0:18}}..
                    </mat-chip>
                  </mat-chip-list>
                  <div class="name-container" fxLayout="row" style="padding-top: 4px;">
                    <mat-icon class="item-icon">perm_identity</mat-icon>
                    <span class="complainant-name" matSuffix
                      *ngIf="complaint.ASSIGNEE_USER_NAME != null && complaint.ASSIGNEE_USER_NAME != ''">
                      {{complaint.ASSIGNEE_USER_NAME}} </span>
                    <span class="complainant-name" matSuffix
                      *ngIf="complaint.ASSIGNEE_USER_NAME == null || complaint.ASSIGNEE_USER_NAME == ''"> Not assigned
                    </span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <div class="date-container" fxLayout="row">
                    <mat-icon class="item-icon1">calendar_today</mat-icon>
                    <span matSuffix
                      style="position: relative;top: 0px;left:-3px;font-size: 11px;">{{complaint.REGISTERED_DATE |
                      date:'dd/MM/yyyy'}}</span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <div class="requester-container" fxLayout="row">
                    <span matSuffix style="position: relative;top: 0px;left:3px;font-size: 11px;">{{complaint.COMPLAINT_TYPE_NAME}}</span>
                  </div>
                </div>
              </div>
              <div class="list-item-container" *ngIf="i + 1 === complaints.length"
                [ngClass]="{'case-selected': i + 1 == selectedCaseIndex}" #lastListData>
                <div mat-line class="item-head-container" style="display: flex;flex-direction: row;">
                  <p>
                    <span style="font-weight: bold;">{{complaint.REQUESTER_USER_NAME}} - </span>
                    <span style="font-size:14px;font-weight: 400;"> {{complaint.BRAND_NAME}} -</span> <span
                      [innerHTML]="safeHTML(complaint.ADVERTISEMENT_DESCRIPTION)"
                      class="ellipsis">{{complaint.ADVERTISEMENT_DESCRIPTION}} </span>
                  </p>
                </div>
                <div mat-line class="item-content-container">
                  <div class="case-container" fxLayout="row">
                    <span matSuffix style="position: relative;font-size: 11px;"> Case Id: {{complaint.CASE_ID}}</span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <mat-chip-list class="status-chip">
                    <mat-chip class="comp-status"
                      *ngIf="complaint.COMPLAINT_STATUS_NAME != 'Out of remit/Outside ASCI Purview'"
                      [ngClass]="{'theme-onhold' : complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' : complaint.COMPLAINT_STATUS_NAME == 'New', 'theme-inprogress' : complaint.COMPLAINT_STATUS_NAME == 'In Progress', 'theme-resolution' : complaint.COMPLAINT_STATUS_NAME == 'Resolution', 'theme-outofremit' : complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview' || complaint.COMPLAINT_STATUS_NAME == 'Sub-Judice', 'theme-invalid' : complaint.COMPLAINT_STATUS_NAME == 'Non-Issue', 'theme-closed' : complaint.COMPLAINT_STATUS_NAME == 'Closed'}">
                      {{complaint.COMPLAINT_STATUS_NAME}}
                    </mat-chip>
                    <mat-chip class="comp-status1"
                      *ngIf="complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview'"
                      [ngClass]="{'theme-onhold' : complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' : complaint.COMPLAINT_STATUS_NAME == 'New', 'theme-inprogress' : complaint.COMPLAINT_STATUS_NAME == 'In Progress', 'theme-resolution' : complaint.COMPLAINT_STATUS_NAME == 'Resolution', 'theme-outofremit' : complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview' || complaint.COMPLAINT_STATUS_NAME == 'Sub-Judice', 'theme-invalid' : complaint.COMPLAINT_STATUS_NAME == 'Non-Issue', 'theme-closed' : complaint.COMPLAINT_STATUS_NAME == 'Closed'}"
                      matTooltip="{{complaint.COMPLAINT_STATUS_NAME}}">
                      {{complaint.COMPLAINT_STATUS_NAME | slice:0:18}}..
                    </mat-chip>
                  </mat-chip-list>
                  <div class="name-container" fxLayout="row" style="padding-top: 4px;">
                    <mat-icon class="item-icon">perm_identity</mat-icon>
                    <span class="complainant-name" matSuffix
                      *ngIf="complaint.ASSIGNEE_USER_NAME != null && complaint.ASSIGNEE_USER_NAME != ''">
                      {{complaint.ASSIGNEE_USER_NAME}} </span>
                    <span class="complainant-name" matSuffix
                      *ngIf="complaint.ASSIGNEE_USER_NAME == null || complaint.ASSIGNEE_USER_NAME == ''"> Not assigned
                    </span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <div class="date-container" fxLayout="row">
                    <mat-icon class="item-icon1">calendar_today</mat-icon>
                    <span matSuffix style="position: relative;top: 0px;left:-3px;font-size: 11px;">
                      {{complaint.REGISTERED_DATE | date:'dd/MM/yyyy'}}</span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <div class="requester-container" fxLayout="row">
                    <span matSuffix style="position: relative;top: 0px;left:3px;font-size: 11px;">
                      {{complaint.COMPLAINT_TYPE_NAME}}</span>
                  </div>
                </div>
              </div>
            </mat-list-item>
          </div> <!-- complaint-list -->
        </div><!-- complaints-display-container -->

        <div class="filter-complaints-container" *ngIf="filterContainer">
          <div class="filter-complaint-list">
            <mat-list-item *ngFor="let complaint of filter_complaints;let i = index"
              (click)="clickEvent(complaint.ID, complaint.CASE_ID, complaint.FTC, i)">
              <div class="list-item-container" *ngIf="i + 1 != filter_complaints.length"
                [ngClass]="{'case-selected': i + 1 == selectedCaseIndex}">
                <div mat-line class="item-head-container" style="display: flex; flex-direction: row;">
                  <p>
                    <span style="font-weight: bold;">{{complaint.REQUESTER_USER_NAME}} - </span>
                    <span style="font-size:14px;font-weight: 400;">{{complaint.BRAND_NAME}} - </span> <span
                      [innerHTML]="safeHTML(complaint.ADVERTISEMENT_DESCRIPTION)"
                      class="ellipsis">{{complaint.ADVERTISEMENT_DESCRIPTION}} </span>
                  </p>
                </div>
                <div mat-line class="item-content-container">
                  <div class="case-container" fxLayout="row">
                    <span matSuffix style="position: relative;font-size: 11px;"> Case Id: {{complaint.CASE_ID}}</span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <mat-chip-list class="status-chip">
                    <mat-chip class="comp-status"
                      *ngIf="complaint.COMPLAINT_STATUS_NAME != 'Out of remit/Outside ASCI Purview'"
                      [ngClass]="{'theme-onhold' : complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' : complaint.COMPLAINT_STATUS_NAME == 'New', 'theme-inprogress' : complaint.COMPLAINT_STATUS_NAME == 'In Progress', 'theme-resolution' : complaint.COMPLAINT_STATUS_NAME == 'Resolution', 'theme-outofremit' : complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview' || complaint.COMPLAINT_STATUS_NAME == 'Sub-Judice', 'theme-invalid' : complaint.COMPLAINT_STATUS_NAME == 'Non-Issue', 'theme-closed' : complaint.COMPLAINT_STATUS_NAME == 'Closed'}">
                      {{complaint.COMPLAINT_STATUS_NAME}}
                    </mat-chip>
                    <mat-chip class="comp-status1"
                      *ngIf="complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview'"
                      [ngClass]="{'theme-onhold' : complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' : complaint.COMPLAINT_STATUS_NAME == 'New', 'theme-inprogress' : complaint.COMPLAINT_STATUS_NAME == 'In Progress', 'theme-resolution' : complaint.COMPLAINT_STATUS_NAME == 'Resolution', 'theme-outofremit' : complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview' || complaint.COMPLAINT_STATUS_NAME == 'Sub-Judice', 'theme-invalid' : complaint.COMPLAINT_STATUS_NAME == 'Non-Issue', 'theme-closed' : complaint.COMPLAINT_STATUS_NAME == 'Closed'}"
                      matTooltip="{{complaint.COMPLAINT_STATUS_NAME}}">
                      {{complaint.COMPLAINT_STATUS_NAME | slice:0:18}}..
                    </mat-chip>
                  </mat-chip-list>
                  <div class="name-container" fxLayout="row" style="padding-top: 4px;">
                    <mat-icon class="item-icon">perm_identity</mat-icon>
                    <span class="complainant-name" matSuffix
                      *ngIf="complaint.ASSIGNEE_USER_NAME != null && complaint.ASSIGNEE_USER_NAME != ''">
                      {{complaint.ASSIGNEE_USER_NAME}} </span>
                    <span class="complainant-name" matSuffix
                      *ngIf="complaint.ASSIGNEE_USER_NAME == null || complaint.ASSIGNEE_USER_NAME == ''"> Not assigned
                    </span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <div class="date-container" fxLayout="row">
                    <mat-icon class="item-icon1">calendar_today</mat-icon>
                    <span matSuffix
                      style="position: relative;top: 0px;left:-3px;font-size: 11px;">{{complaint.REGISTERED_DATE |
                      date:'dd/MM/yyyy'}}</span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <div class="requester-container" fxLayout="row">
                    <span matSuffix style="position: relative;top: 0px;left:3px;font-size: 11px;">{{complaint.COMPLAINT_TYPE_NAME}}</span>
                  </div>
                </div>
                <!-- <div mat-line class="item-content-container" style="display: flex; flex-direction: row;" fxFlex="row">
                  <mat-chip-list class="status-chip">
                    <mat-chip class="comp-status"
                      [ngStyle]="{'color':getColor(complaint.status),'background-color':getBGC(complaint.status),'border-color':getColor(complaint.status)}">
                      {{complaint.COMPLAINT_STATUS_NAME}}
                    </mat-chip>
                  </mat-chip-list>
                  <div class="name-container">
                    <mat-icon class="item-icon1" style="position: relative;">perm_identity</mat-icon>
                    <span class="complainant-name"> {{ complaint.FIRST_NAME }}, {{ complaint.LAST_NAME }} </span>
                  </div>
                  <div class="date-container">
                    <mat-icon class="item-icon">calendar_today</mat-icon>
                    <span class="demo-2" style="position: relative;top: 1px;left:3px">{{ complaint.UPDATED_DATE | date:'dd/MM/yyyy'}}
                    </span>
                  </div>
                </div> -->
                <div class="spinner-item" *ngIf="taskLoading">
                  <mat-progress-spinner [mode]="'indeterminate'" [diameter]="50">
                  </mat-progress-spinner>
                </div>
              </div>
              <div class="list-item-container" *ngIf="i + 1 === filter_complaints.length"
                [ngClass]="{'case-selected': i + 1 == selectedCaseIndex}" #lastListData>
                <div mat-line class="item-head-container" style="display: flex; flex-direction: row;">
                  <p>
                    <span style="font-weight: bold;">{{complaint.REQUESTER_USER_NAME}} - </span>
                    <span style="font-size:14px;font-weight: 400;">{{complaint.BRAND_NAME}} - </span> <span
                      [innerHTML]="safeHTML(complaint.ADVERTISEMENT_DESCRIPTION)"
                      class="ellipsis">{{complaint.ADVERTISEMENT_DESCRIPTION}} </span>
                  </p>
                </div>
                <div mat-line class="item-content-container">
                  <div class="case-container" fxLayout="row">
                    <span matSuffix style="position: relative;font-size: 11px;"> Case Id: {{complaint.CASE_ID}}</span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <mat-chip-list class="status-chip">
                    <mat-chip class="comp-status"
                      *ngIf="complaint.COMPLAINT_STATUS_NAME != 'Out of remit/Outside ASCI Purview'"
                      [ngClass]="{'theme-onhold' : complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' : complaint.COMPLAINT_STATUS_NAME == 'New', 'theme-inprogress' : complaint.COMPLAINT_STATUS_NAME == 'In Progress', 'theme-resolution' : complaint.COMPLAINT_STATUS_NAME == 'Resolution', 'theme-outofremit' : complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview' || complaint.COMPLAINT_STATUS_NAME == 'Sub-Judice', 'theme-invalid' : complaint.COMPLAINT_STATUS_NAME == 'Non-Issue', 'theme-closed' : complaint.COMPLAINT_STATUS_NAME == 'Closed'}">
                      {{complaint.COMPLAINT_STATUS_NAME}}
                    </mat-chip>
                    <mat-chip class="comp-status1"
                      *ngIf="complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview'"
                      [ngClass]="{'theme-onhold' : complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' : complaint.COMPLAINT_STATUS_NAME == 'New', 'theme-inprogress' : complaint.COMPLAINT_STATUS_NAME == 'In Progress', 'theme-resolution' : complaint.COMPLAINT_STATUS_NAME == 'Resolution', 'theme-outofremit' : complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview' || complaint.COMPLAINT_STATUS_NAME == 'Sub-Judice', 'theme-invalid' : complaint.COMPLAINT_STATUS_NAME == 'Non-Issue', 'theme-closed' : complaint.COMPLAINT_STATUS_NAME == 'Closed'}"
                      matTooltip="{{complaint.COMPLAINT_STATUS_NAME}}">
                      {{complaint.COMPLAINT_STATUS_NAME | slice:0:18}}..
                    </mat-chip>
                  </mat-chip-list>
                  <div class="name-container" fxLayout="row" style="padding-top: 4px;">
                    <mat-icon class="item-icon">perm_identity</mat-icon>
                    <span class="complainant-name" matSuffix
                      *ngIf="complaint.ASSIGNEE_USER_NAME != null && complaint.ASSIGNEE_USER_NAME != ''">
                      {{complaint.ASSIGNEE_USER_NAME}} </span>
                    <span class="complainant-name" matSuffix
                      *ngIf="complaint.ASSIGNEE_USER_NAME == null || complaint.ASSIGNEE_USER_NAME == ''"> Not assigned
                    </span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <div class="date-container" fxLayout="row">
                    <mat-icon class="item-icon1">calendar_today</mat-icon>
                    <span matSuffix
                      style="position: relative;top: 0px;left:-3px;font-size: 11px;">{{complaint.REGISTERED_DATE |
                      date:'dd/MM/yyyy'}}</span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <div class="requester-container" fxLayout="row">
                    <span matSuffix style="position: relative;top: 0px;left:3px;font-size: 11px;">{{complaint.COMPLAINT_TYPE_NAME}}</span>
                  </div>
                </div>
                <!-- <div mat-line class="item-content-container" style="display: flex; flex-direction: row;" fxFlex="row">
                  <mat-chip-list class="status-chip">
                    <mat-chip class="comp-status"
                      [ngStyle]="{'color':getColor(complaint.status),'background-color':getBGC(complaint.status),'border-color':getColor(complaint.status)}">
                      {{complaint.COMPLAINT_STATUS_NAME}}
                    </mat-chip>
                  </mat-chip-list>
                  <div class="name-container">
                    <mat-icon class="item-icon1" style="position: relative;">perm_identity</mat-icon>
                    <span class="complainant-name"> {{ complaint.FIRST_NAME }}, {{ complaint.LAST_NAME }} </span>
                  </div>
                  <div class="date-container">
                    <mat-icon class="item-icon">calendar_today</mat-icon>
                    <span class="demo-2" style="position: relative;top: 1px;left:3px">{{ complaint.UPDATED_DATE | date:'dd/MM/yyyy'}}
                    </span>
                  </div>
                </div> -->
                <div class="spinner-item" *ngIf="taskLoading">
                  <mat-progress-spinner [mode]="'indeterminate'" [diameter]="50">
                  </mat-progress-spinner>
                </div>
              </div>
            </mat-list-item>
          </div>
        </div>
      </mat-tab>
      <mat-tab label="FTC Complaints">
        <div class="complaints-display-container" *ngIf="displayContainer">
          <div class="complaint-list">
            <mat-list-item *ngFor="let complaint of FTCcomplaints; let i = index"
              (click)="clickEvent(complaint.ID, complaint.CASE_ID, complaint.FTC, i)">
              <div class="list-item-container" *ngIf="i + 1 != FTCcomplaints.length"
                [ngClass]="{'case-selected': i + 1 == selectedCaseIndex}">
                <div mat-line class="item-head-container" style="display: flex;flex-direction: row;">
                  <p>
                    <span style="font-weight: bold;">{{complaint.REQUESTER_USER_NAME}} - </span>
                    <span style="font-size:14px;font-weight: 400;">{{complaint.BRAND_NAME}} - </span> <span
                      [innerHTML]="safeHTML(complaint.ADVERTISEMENT_DESCRIPTION)"
                      class="ellipsis">{{complaint.ADVERTISEMENT_DESCRIPTION}} </span>
                  </p>
                </div>
                <div mat-line class="item-content-container">
                  <div class="case-container" fxLayout="row">
                    <span matSuffix style="position: relative;font-size: 11px;"> Case Id: {{complaint.CASE_ID}}</span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <mat-chip-list class="status-chip">
                    <mat-chip class="comp-status"
                      *ngIf="complaint.COMPLAINT_STATUS_NAME != 'Out of remit/Outside ASCI Purview'"
                      [ngClass]="{'theme-onhold' : complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' : complaint.COMPLAINT_STATUS_NAME == 'New', 'theme-inprogress' : complaint.COMPLAINT_STATUS_NAME == 'In Progress', 'theme-resolution' : complaint.COMPLAINT_STATUS_NAME == 'Resolution', 'theme-outofremit' : complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview' || complaint.COMPLAINT_STATUS_NAME == 'Sub-Judice', 'theme-invalid' : complaint.COMPLAINT_STATUS_NAME == 'Non-Issue', 'theme-closed' : complaint.COMPLAINT_STATUS_NAME == 'Closed'}">
                      {{complaint.COMPLAINT_STATUS_NAME}}
                    </mat-chip>
                    <mat-chip class="comp-status1"
                      *ngIf="complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview'"
                      [ngClass]="{'theme-onhold' : complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' : complaint.COMPLAINT_STATUS_NAME == 'New', 'theme-inprogress' : complaint.COMPLAINT_STATUS_NAME == 'In Progress', 'theme-resolution' : complaint.COMPLAINT_STATUS_NAME == 'Resolution', 'theme-outofremit' : complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview' || complaint.COMPLAINT_STATUS_NAME == 'Sub-Judice', 'theme-invalid' : complaint.COMPLAINT_STATUS_NAME == 'Non-Issue', 'theme-closed' : complaint.COMPLAINT_STATUS_NAME == 'Closed'}"
                      matTooltip="{{complaint.COMPLAINT_STATUS_NAME}}">
                      {{complaint.COMPLAINT_STATUS_NAME | slice:0:18}}..
                    </mat-chip>
                  </mat-chip-list>
                  <div class="name-container" fxLayout="row" style="padding-top: 4px;">
                    <mat-icon class="item-icon">perm_identity</mat-icon>
                    <span class="complainant-name" matSuffix
                      *ngIf="complaint.ASSIGNEE_USER_NAME != null && complaint.ASSIGNEE_USER_NAME != ''">
                      {{complaint.ASSIGNEE_USER_NAME}} </span>
                    <span class="complainant-name" matSuffix
                      *ngIf="complaint.ASSIGNEE_USER_NAME == null || complaint.ASSIGNEE_USER_NAME == ''"> Not assigned
                    </span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <div class="date-container" fxLayout="row">
                    <mat-icon class="item-icon1">calendar_today</mat-icon>
                    <span matSuffix
                      style="position: relative;top: 0px;left:-3px;font-size: 11px;">{{complaint.REGISTERED_DATE |
                      date:'dd/MM/yyyy'}}</span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <div class="requester-container" fxLayout="row">
                    <span matSuffix style="position: relative;top: 0px;left:3px;font-size: 11px;">{{complaint.COMPLAINT_TYPE_NAME}}</span>
                  </div>
                </div>
              </div>
              <div class="list-item-container" *ngIf="i + 1 === FTCcomplaints.length"
                [ngClass]="{'case-selected': i + 1 == selectedCaseIndex}" #lastListData>
                <div mat-line class="item-head-container" style="display: flex;flex-direction: row;">
                  <p>
                    <span style="font-weight: bold;">{{complaint.REQUESTER_USER_NAME}} - </span>
                    <span style="font-size:14px;font-weight: 400;">{{complaint.BRAND_NAME}} - </span> <span
                      [innerHTML]="safeHTML(complaint.ADVERTISEMENT_DESCRIPTION)"
                      class="ellipsis">{{complaint.ADVERTISEMENT_DESCRIPTION}} </span>
                  </p>
                </div>
                <div mat-line class="item-content-container">
                  <div class="case-container" fxLayout="row">
                    <span matSuffix style="position: relative;font-size: 11px;"> Case Id: {{complaint.CASE_ID}}</span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <mat-chip-list class="status-chip">
                    <mat-chip class="comp-status"
                      *ngIf="complaint.COMPLAINT_STATUS_NAME != 'Out of remit/Outside ASCI Purview'"
                      [ngClass]="{'theme-onhold' : complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' : complaint.COMPLAINT_STATUS_NAME == 'New', 'theme-inprogress' : complaint.COMPLAINT_STATUS_NAME == 'In Progress', 'theme-resolution' : complaint.COMPLAINT_STATUS_NAME == 'Resolution', 'theme-outofremit' : complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview' || complaint.COMPLAINT_STATUS_NAME == 'Sub-Judice', 'theme-invalid' : complaint.COMPLAINT_STATUS_NAME == 'Non-Issue', 'theme-closed' : complaint.COMPLAINT_STATUS_NAME == 'Closed'}">
                      {{complaint.COMPLAINT_STATUS_NAME}}
                    </mat-chip>
                    <mat-chip class="comp-status1"
                      *ngIf="complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview'"
                      [ngClass]="{'theme-onhold' : complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' : complaint.COMPLAINT_STATUS_NAME == 'New', 'theme-inprogress' : complaint.COMPLAINT_STATUS_NAME == 'In Progress', 'theme-resolution' : complaint.COMPLAINT_STATUS_NAME == 'Resolution', 'theme-outofremit' : complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview' || complaint.COMPLAINT_STATUS_NAME == 'Sub-Judice', 'theme-invalid' : complaint.COMPLAINT_STATUS_NAME == 'Non-Issue', 'theme-closed' : complaint.COMPLAINT_STATUS_NAME == 'Closed'}"
                      matTooltip="{{complaint.COMPLAINT_STATUS_NAME}}">
                      {{complaint.COMPLAINT_STATUS_NAME | slice:0:18}}..
                    </mat-chip>
                  </mat-chip-list>
                  <div class="name-container" fxLayout="row" style="padding-top: 4px;">
                    <mat-icon class="item-icon">perm_identity</mat-icon>
                    <span class="complainant-name" matSuffix
                      *ngIf="complaint.ASSIGNEE_USER_NAME != null && complaint.ASSIGNEE_USER_NAME != ''">
                      {{complaint.ASSIGNEE_USER_NAME}} </span>
                    <span class="complainant-name" matSuffix
                      *ngIf="complaint.ASSIGNEE_USER_NAME == null || complaint.ASSIGNEE_USER_NAME == ''"> Not assigned
                    </span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <div class="date-container" fxLayout="row">
                    <mat-icon class="item-icon1">calendar_today</mat-icon>
                    <span matSuffix
                      style="position: relative;top: 0px;left:-3px;font-size: 11px;">{{complaint.REGISTERED_DATE |
                      date:'dd/MM/yyyy'}}</span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <div class="requester-container" fxLayout="row">
                    <span matSuffix style="position: relative;top: 0px;left:3px;font-size: 11px;">{{complaint.COMPLAINT_TYPE_NAME}}</span>
                  </div>
                </div>
              </div>
            </mat-list-item>
          </div> <!-- complaint-list -->
        </div><!-- complaints-display-container -->

        <div class="filter-complaints-container" *ngIf="filterContainer">
          <div class="filter-complaint-list">
            <mat-list-item *ngFor="let complaint of filter_ftc_complaints;let i = index"
              (click)="clickEvent(complaint.ID, complaint.CASE_ID, complaint.FTC, i)">
              <div class="list-item-container" *ngIf="i + 1 != filter_ftc_complaints.length"
                [ngClass]="{'case-selected': i + 1 == selectedCaseIndex}">
                <div mat-line class="item-head-container" style="display: flex; flex-direction: row;">
                  <p>
                    <span style="font-weight: bold;">{{complaint.REQUESTER_USER_NAME}} - </span>
                    <span style="font-size:14px;font-weight: 400;">{{complaint.BRAND_NAME}} - </span> <span
                      [innerHTML]="safeHTML(complaint.ADVERTISEMENT_DESCRIPTION)"
                      class="ellipsis">{{complaint.ADVERTISEMENT_DESCRIPTION}} </span>
                  </p>
                </div>
                <div mat-line class="item-content-container">
                  <div class="case-container" fxLayout="row">
                    <span matSuffix style="position: relative;font-size: 11px;"> Case Id: {{complaint.CASE_ID}}</span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <mat-chip-list class="status-chip">
                    <mat-chip class="comp-status"
                      *ngIf="complaint.COMPLAINT_STATUS_NAME != 'Out of remit/Outside ASCI Purview'"
                      [ngClass]="{'theme-onhold' : complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' : complaint.COMPLAINT_STATUS_NAME == 'New', 'theme-inprogress' : complaint.COMPLAINT_STATUS_NAME == 'In Progress', 'theme-resolution' : complaint.COMPLAINT_STATUS_NAME == 'Resolution', 'theme-outofremit' : complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview' || complaint.COMPLAINT_STATUS_NAME == 'Sub-Judice', 'theme-invalid' : complaint.COMPLAINT_STATUS_NAME == 'Non-Issue', 'theme-closed' : complaint.COMPLAINT_STATUS_NAME == 'Closed'}">
                      {{complaint.COMPLAINT_STATUS_NAME}}
                    </mat-chip>
                    <mat-chip class="comp-status1"
                      *ngIf="complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview'"
                      [ngClass]="{'theme-onhold' : complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' : complaint.COMPLAINT_STATUS_NAME == 'New', 'theme-inprogress' : complaint.COMPLAINT_STATUS_NAME == 'In Progress', 'theme-resolution' : complaint.COMPLAINT_STATUS_NAME == 'Resolution', 'theme-outofremit' : complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview' || complaint.COMPLAINT_STATUS_NAME == 'Sub-Judice', 'theme-invalid' : complaint.COMPLAINT_STATUS_NAME == 'Non-Issue', 'theme-closed' : complaint.COMPLAINT_STATUS_NAME == 'Closed'}"
                      matTooltip="{{complaint.COMPLAINT_STATUS_NAME}}">
                      {{complaint.COMPLAINT_STATUS_NAME | slice:0:18}}..
                    </mat-chip>
                  </mat-chip-list>
                  <div class="name-container" fxLayout="row" style="padding-top: 4px;">
                    <mat-icon class="item-icon">perm_identity</mat-icon>
                    <span class="complainant-name" matSuffix
                      *ngIf="complaint.ASSIGNEE_USER_NAME != null && complaint.ASSIGNEE_USER_NAME != ''">
                      {{complaint.ASSIGNEE_USER_NAME}} </span>
                    <span class="complainant-name" matSuffix
                      *ngIf="complaint.ASSIGNEE_USER_NAME == null || complaint.ASSIGNEE_USER_NAME == ''"> Not assigned
                    </span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <div class="date-container" fxLayout="row">
                    <mat-icon class="item-icon1">calendar_today</mat-icon>
                    <span matSuffix
                      style="position: relative;top: 0px;left:-3px;font-size: 11px;">{{complaint.REGISTERED_DATE |
                      date:'dd/MM/yyyy'}}</span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <div class="requester-container" fxLayout="row">
                    <span matSuffix style="position: relative;top: 0px;left:3px;font-size: 11px;">{{complaint.COMPLAINT_TYPE_NAME}}</span>
                  </div>
                </div>
                <div class="spinner-item" *ngIf="taskLoading">
                  <mat-progress-spinner [mode]="'indeterminate'" [diameter]="50">
                  </mat-progress-spinner>
                </div>
              </div>
              <div class="list-item-container" *ngIf="i + 1 === filter_ftc_complaints.length"
                [ngClass]="{'case-selected': i + 1 == selectedCaseIndex}" #lastListData>
                <div mat-line class="item-head-container" style="display: flex; flex-direction: row;">
                  <p>
                    <span style="font-weight: bold;">{{complaint.REQUESTER_USER_NAME}} - </span>
                    <span style="font-size:14px;font-weight: 400;">{{complaint.BRAND_NAME}} - </span> <span
                      [innerHTML]="safeHTML(complaint.ADVERTISEMENT_DESCRIPTION)"
                      class="ellipsis">{{complaint.ADVERTISEMENT_DESCRIPTION}} </span>
                  </p>
                </div>
                <div mat-line class="item-content-container">
                  <div class="case-container" fxLayout="row">
                    <span matSuffix style="position: relative;font-size: 11px;"> Case Id: {{complaint.CASE_ID}}</span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <mat-chip-list class="status-chip">
                    <mat-chip class="comp-status"
                      *ngIf="complaint.COMPLAINT_STATUS_NAME != 'Out of remit/Outside ASCI Purview'"
                      [ngClass]="{'theme-onhold' : complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' : complaint.COMPLAINT_STATUS_NAME == 'New', 'theme-inprogress' : complaint.COMPLAINT_STATUS_NAME == 'In Progress', 'theme-resolution' : complaint.COMPLAINT_STATUS_NAME == 'Resolution', 'theme-outofremit' : complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview' || complaint.COMPLAINT_STATUS_NAME == 'Sub-Judice', 'theme-invalid' : complaint.COMPLAINT_STATUS_NAME == 'Non-Issue', 'theme-closed' : complaint.COMPLAINT_STATUS_NAME == 'Closed'}">
                      {{complaint.COMPLAINT_STATUS_NAME}}
                    </mat-chip>
                    <mat-chip class="comp-status1"
                      *ngIf="complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview'"
                      [ngClass]="{'theme-onhold' : complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' : complaint.COMPLAINT_STATUS_NAME == 'New', 'theme-inprogress' : complaint.COMPLAINT_STATUS_NAME == 'In Progress', 'theme-resolution' : complaint.COMPLAINT_STATUS_NAME == 'Resolution', 'theme-outofremit' : complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview' || complaint.COMPLAINT_STATUS_NAME == 'Sub-Judice', 'theme-invalid' : complaint.COMPLAINT_STATUS_NAME == 'Non-Issue', 'theme-closed' : complaint.COMPLAINT_STATUS_NAME == 'Closed'}"
                      matTooltip="{{complaint.COMPLAINT_STATUS_NAME}}">
                      {{complaint.COMPLAINT_STATUS_NAME | slice:0:18}}..
                    </mat-chip>
                  </mat-chip-list>
                  <div class="name-container" fxLayout="row" style="padding-top: 4px;">
                    <mat-icon class="item-icon">perm_identity</mat-icon>
                    <span class="complainant-name" matSuffix
                      *ngIf="complaint.ASSIGNEE_USER_NAME != null && complaint.ASSIGNEE_USER_NAME != ''">
                      {{complaint.ASSIGNEE_USER_NAME}} </span>
                    <span class="complainant-name" matSuffix
                      *ngIf="complaint.ASSIGNEE_USER_NAME == null || complaint.ASSIGNEE_USER_NAME == ''"> Not assigned
                    </span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <div class="date-container" fxLayout="row">
                    <mat-icon class="item-icon1">calendar_today</mat-icon>
                    <span matSuffix
                      style="position: relative;top: 0px;left:-3px;font-size: 11px;">{{complaint.REGISTERED_DATE |
                      date:'dd/MM/yyyy'}}</span>
                  </div>
                </div>
                <div mat-line class="item-content-container">
                  <div class="requester-container" fxLayout="row">
                    <span matSuffix style="position: relative;top: 0px;left:3px;font-size: 11px;">{{complaint.COMPLAINT_TYPE_NAME}}</span>
                  </div>
                </div>
                <div class="spinner-item" *ngIf="taskLoading">
                  <mat-progress-spinner [mode]="'indeterminate'" [diameter]="50">
                  </mat-progress-spinner>
                </div>
              </div>
            </mat-list-item>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div> <!-- manage -->

  <!-- <div class="tab" fxLayout="row"> -->
  <mat-tab-group [(selectedIndex)]="activeTab" (selectedTabChange)="onTabChanged($event)" class="right-screen"
    style="width: 75%;">
    <mat-tab>
      <ng-template mat-tab-label>
        <div class="tab-title-container" fxLayoutAlign="start center" fxLayoutGap="20%">
          <div class="title-text" fxlayout="column" fxLayoutGap="1px">
            <span style="color: rgb(155, 155, 155);font-weight: normal !important;">Case ID</span><br />
            <span style="float:left;color:black" [matTooltip]="caseId">{{caseId}}</span>
          </div>
          <!-- <div class="tab-close">
            <button mat-icon-button (click)="closeTab(index)">
              <mat-icon class="close" style="color:rgb(110, 110, 110);font-size: large;">close</mat-icon>
            </button>
          </div> -->
        </div>
      </ng-template>
      <!-- <div class="detail-screen" style="height: 100%; width: 100%;" *ngFor="let tab of tabs; let index = index">
        <app-complaint-detail [id]="tab"></app-complaint-detail>
      </div> -->
      <div class="detail-screen" style="height: 92vh; overflow: hidden;" *ngFor="let tab of tabs; let index = index">
        <app-complaint-detail [id]="tab.id" [ftc]="tab.ftc" (outFilter)="updateComplaintDetail($event)"
          (refreshComplaints)="refreshComplaints($event)">
        </app-complaint-detail>
      </div>
    </mat-tab>

    <mat-tab class="add-tab-label" style="min-width: 0px !important;">
      <ng-template mat-tab-label class="new-tab" *ngIf="!isTabChanged">
        <div class="new-tab" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="10px">
          <div class="plus-icon-container" style="height: 100% !important;width: 100% !important;">
            <img class="blue-icon" src="../../assets/images/blue_plus.svg">
            <img class="white-icon" src="../../assets/images/white_plus.svg">
          </div>
          <div>
            <span class="new-tab-text">New complaint</span>
          </div>
        </div>
      </ng-template>
      <ng-template mat-tab-label *ngIf="isTabChanged">
        <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="10px">
          <div>
            <span class="new-tab-text">New complaint</span>
          </div>
          <!-- <div>
            <img src="../../assets/images/blue_x.svg">
          </div> -->
        </div>
      </ng-template>
      <div class="register" style="height: 100%;width:100%; background-color: #F8F9F9;">
        <app-complaint-register [navigationState]="navState" (refreshEvent)="refreshComplaintList()">
        </app-complaint-register>
      </div>
    </mat-tab>
  </mat-tab-group>

  <div class="common-toolbar" fxLayout="row">
    <app-toolbar-options style="margin-right: 7px;"></app-toolbar-options>
  </div>
</div>