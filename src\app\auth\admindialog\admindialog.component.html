<div fxLayout="row">
    <mat-toolbar class="toolbar">
        <div fxFlex="35%" fxLayoutAlign="start">
            <h2 mat-dialog-title fxLayoutAlign="start" style="font-size: 16px;">
                <img src="../assets/images/edit-icon.svg">&nbsp;
                Edit user
            </h2>
        </div>
        <div fxFlex="60%"></div>
        <div fxFlex="5%" fxLayoutAlign="end">
            <mat-dialog-actions mat-dialog-close style="position: relative; top: -10px; left: 15px;">
                <button mat-icon-button class="search-btn" mat-dialog-close>
                    <mat-icon style="margin-bottom: 4px;">close</mat-icon>
                </button>
            </mat-dialog-actions>
        </div>
    </mat-toolbar>
</div>

<mat-divider></mat-divider>

<div class="edit-form">
    <form [formGroup]="addform">
        <div class="contents">
            <div class="names">
                <div>Title <span style="color: #ff0000;">*</span></div>
                <div fxFlex="15%"></div>
                <div> First name <span style="color: #ff0000;">*</span></div>
                <div fxFlex="25%"></div>
                <div>Last name <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" style="width: 110px;">
                    <mat-select formControlName="salutation">
                        <mat-option *ngFor="let title of titles" [value]="title.ID" style="color:black;"> {{title.SALUTATION_NAME}}</mat-option>
                    </mat-select>
                    <mat-error *ngIf="addform.get('salutation').touched && addform.controls['salutation'].errors?.required">
                        Salutation is required
                    </mat-error>   
                </mat-form-field>
                <mat-form-field appearance="outline" class="input-field">
                    <input matInput formControlName="first_name" style="font-style: normal;font-weight: normal;" autocomplete="off">
                    <mat-error class="error-msg" *ngIf="addform.controls['first_name'].errors?.required">
                        First name is required
                        </mat-error>
                        <mat-error class="error-msg" *ngIf="addform.controls['first_name'].errors?.pattern">
                        Only text is allowed
                        </mat-error>
                        <mat-error class="error-msg" style="width: max-content;" *ngIf="addform.controls['first_name'].errors?.minlength">
                        First name should be of min length 3 & max length 25
                        </mat-error>
                        <mat-error class="error-msg" style="width: max-content;" *ngIf="addform.controls['first_name'].errors?.maxlength">
                        First name should be of min length 3 & max length 25
                        </mat-error>
                </mat-form-field>
                <mat-form-field appearance="outline" class="input-field">
                    <input matInput formControlName="last_name" style="font-style: normal;font-weight: normal;" autocomplete="off">
                    <mat-error class="error-msg" *ngIf="addform.controls['last_name'].errors?.required">
                        Last Name is required
                        </mat-error>
                        <mat-error class="error-msg" *ngIf="addform.controls['last_name'].errors?.pattern">
                        Only text is allowed
                        </mat-error>
                        <mat-error class="error-msg" *ngIf="addform.controls['last_name'].errors?.maxlength">
                        Last Name should be of max length 25
                        </mat-error>
                </mat-form-field>
            </div>
            <div class="names1">
                <div>Phone number <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" class="input-field_phno">
                    <input matInput formControlName="contact" style="font-style: normal;font-weight: normal;" autocomplete="off">
                </mat-form-field>
            </div>
            <div class="names1">
                <div>Email address <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" class="input-field_phno">
                    <input matInput formControlName="email" style="font-style: normal;font-weight: normal;" autocomplete="off">
                </mat-form-field>
            </div>
            <div class="dept_role">
                <div> Department <span style="color: #ff0000;">*</span></div>
                <div>Role <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" class="input-field_dept">
                    <input matInput formControlName="department_name" [matAutocomplete]="autodept" style="font-style: normal;font-weight: normal;">
                    <mat-icon matSuffix style="cursor: pointer;">arrow_drop_down</mat-icon>
                    <mat-autocomplete #autodept="matAutocomplete" (optionSelected)="onDeptChange($event)">
                        <mat-option *ngFor="let stat of department_name;" [value]="stat.DEPARTMENT_TYPE_NAME" style="
                        font-style: normal;
                        font-weight: normal;">
                            {{stat.DEPARTMENT_TYPE_NAME}}
                        </mat-option>
                    </mat-autocomplete>
                    <mat-error class="error-msg" *ngIf="addform.controls['department_name'].errors?.required">
                        Please Choose Department
                    </mat-error> 
                </mat-form-field>
                <mat-form-field appearance="outline" class="input-field_lastname">
                    <input matInput formControlName="user_role" [matAutocomplete]="autorole" style="font-style: normal;font-weight: normal;">
                    <mat-icon matSuffix style="cursor: pointer;">arrow_drop_down</mat-icon>
                    <mat-autocomplete #autorole="matAutocomplete" (optionSelected)="onRoleChange($event)">
                        <mat-option *ngFor="let role of user_role;" [value]="role.ROLE_NAME" style="
                            font-style: normal;
                            font-weight: normal;">
                            {{role.ROLE_NAME}}
                        </mat-option>
                    </mat-autocomplete>
                    <mat-error class="error-msg" *ngIf="addform.controls['user_role'].errors?.required">
                        Please Choose Role
                    </mat-error>
                </mat-form-field>
            </div>
        </div>
        <div class="lastdivdr">
            <mat-divider></mat-divider>
        </div>
        <div fxLayout="row">
            <mat-toolbar class="toolbar2">
                <div fxFlex="35%" fxLayoutAlign="start">
                    <div class="search-div" *ngIf="userRoleId == 1 || (userRoleId == 2 && (roleID == 3 || roleID == 6))">
                        <button mat-button class="remove-btn" (click)='removeUser()'> 
                            <span class="bolder">Remove user</span> 
                        </button>
                    </div>
                </div>
                <div fxFlex="35%"></div>
                <div fxFlex="67%"></div>
                <div class="toolbar-btns" fxFlex="16%" fxLayoutAlign="end">
                    <div class="remove-div">
                        <button mat-flat-button class="cancel-btn" mat-dialog-close> 
                            <span class="bolder">Cancel</span> 
                        </button>
                    </div>
                    <div class="update-div">
                        <button mat-flat-button class="update-btn" (click)="updateUser(addform.value)" [disabled]="addform.invalid"> 
                            <span class="bolder">Update</span> 
                        </button>
                    </div>
                </div>
            </mat-toolbar>
        </div>
    </form>
</div>