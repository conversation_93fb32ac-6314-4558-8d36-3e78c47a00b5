import { ChangeDetectorR<PERSON>, Component, ContentChild, ElementRef, HostListener, OnInit, Renderer2, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { ThirdPartyService } from 'src/app/services/third-party.service';
import { UploadService } from 'src/app/services/upload.service';
import { colorObj } from 'src/app/shared/color-object';
import { AuthService } from '../../services/auth.service';
import { NotificationService } from '../../services/notification.service';
import { Router } from '@angular/router';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';
import { MatDialog } from '@angular/material/dialog';
import { environment } from 'src/environments/environment';
@Component({
  selector: 'app-my-profile',
  templateUrl: './my-profile.component.html',
  styleUrls: ['./my-profile.component.css']
})
export class MyProfileComponent implements OnInit {
  addform: FormGroup;
  changepswdform: FormGroup;
  value: any;
  mobilenopattern = /^((\\+91-?)|0)?[0-9]{10}$/;
  usernamepattern = /^[a-z A-Z]+$/;
  pagename: String;
  isuserprofile: boolean = false;
  isnamsfiles: boolean = false;
  ischangepassword: boolean = false;
  isverifyaccount: boolean = false;
  email: string ='';
  userData: any;
  departmentList: any[];
  roleList: any[];
  stateList: any[];
  districtList = [];
  titleList: any[];
  role;
  department;
  deptName;
  deptId: number;
  userInfo: any;
  userRoleId:number;
  stateName;
  districtName;
  title;
  stateId: number;
  distId: number;
  titleId: number;
  emailId: any;
  isMobileVerified: any;
  isEmailVerified: any;
  attribute: any;
  interval;
  expiryTime = 120;
  timeLeft: string;
  showTime:boolean = true;
  displayedColumns: string[] = ['FILE_NAME', 'CREATED_DATE'];
  isExpiryCompleted: boolean= true;
  passwordPattern = /^(?=.*?[A-Z])(?=.*?[a-z])(?!.* )(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,15}$/;
  isUploadProgress: boolean = false;
  selectedFile: any;
  namsFilePage: number = 1;
  dataSource = new MatTableDataSource([]);
  lastData: number = 0
  resArray: any[] = [];
  public innerWidth: any;
  pinPattern = /^[0-9]{6}$/;
  emailPattern = environment.emailPatterm;
  mobileView: boolean = false;
  roleName: any;
  roleId: any;
  confirmationMsg: any = {};

  constructor(private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private renderer: Renderer2,
    private notify:NotificationService,
    private uploadService: UploadService,
    private dialog: MatDialog,
    private namsService: ThirdPartyService) {
    this.addform = this.fb.group({
      salutation: [''],
      department: [''],
      company: [''],
      role: ['', [Validators.required]],
      fname: ['', [Validators.required,Validators.minLength(3), Validators.maxLength(25), Validators.pattern(this.usernamepattern)]],
      lname: ['', [Validators.required, Validators.maxLength(25),Validators.pattern(this.usernamepattern)]],
      emailId: ['', [Validators.required, Validators.pattern(this.emailPattern)]],
      phone: ['', [Validators.required, Validators.pattern(this.mobilenopattern)]],
      pin : ['',[Validators.required, Validators.pattern(this.pinPattern)]],
      organization:[''],
      profession:[''],
      state: [''],
      district: [''],
      otp: ['']
    })
    this.changepswdform = this.fb.group({
      old_pswd: ['', [Validators.required,Validators.pattern(this.passwordPattern)]],
      new_pswd: ['', [Validators.required,Validators.pattern(this.passwordPattern)]],
      confirm_pswd: ['', [Validators.required,Validators.pattern(this.passwordPattern)]],
    })
  }

  ngOnInit(): void {
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.userRoleId = this.userInfo.roleId;
    this.pagename = "My Profile";
    this.innerWidth = window.innerWidth;
    if(this.innerWidth <= 1250)
    this.mobileView = true;
    else
    this.mobileView = false;
    if(this.userRoleId == 9){
      this.pagename = 'NAMS';
    }
    this.departmentList = JSON.parse(window.localStorage.getItem('departmentType'));
    this.roleList = JSON.parse(window.localStorage.getItem('role'));
    this.stateList = JSON.parse(window.localStorage.getItem('stateType'));
    this.titleList = JSON.parse(window.localStorage.getItem('salutation'));
    if (this.authService.profileFlag) {
      this.isuserprofile = true;
    } else {
      this.ischangepassword = true;
        setTimeout(() => {
          var elem = this.renderer.selectRootElement('#oldPassword');
          elem.focus();
        }, 1000);
    }
    this.addform.controls['phone'].disable();
    this.addform.controls['role'].disable();
    this.addform.controls['company'].disable();
    this.getUserDetails();

    this.roleList = JSON.parse(window.localStorage.getItem('role'));
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.roleId = this.userInfo.roleId;
    if (!(this.userInfo.roleId == 7 || this.userInfo.roleId == 9)) {
      this.roleList.forEach(element => {
        if(this.userInfo.roleId == element.ID){
          this.roleName = element.ROLE_NAME[0];
        }
      });
    }
    else if (this.userInfo.roleId == 7 || this.userInfo.roleId == 9) {
      this.roleName = this.userInfo.firstName[0];
    }
    
  }

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if(this.innerWidth <= 1250)
    this.mobileView = true;
    else
    this.mobileView = false;
  }
  viewDashboard() {
    this.router.navigateByUrl("/home/<USER>")
  }
  viewNotifications() {
    this.router.navigateByUrl('mobile-notifications');
  }
  viewProfile(flag) {
    this.authService.profileFlag = flag;
    this.router.navigateByUrl('auth/my-profile');
  }
  logout() {
    this.confirmationMsg.title = 'Are you sure you want to logout ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.authService.logout().subscribe(
          (res) => {
            window.localStorage.clear();
            this.router.navigate(['auth/login']);
          },
          (err) => {
            window.localStorage.clear();
            this.router.navigate(['auth/login']);
          }
        );
      }
    });
  }


  selectState(event) {
    this.stateName = event.value;
    this.stateList.forEach(el => {
      if(this.stateName === el.STATE_TYPE_NAME){
        this.stateId = el.ID;
      }
    })
    let districtList = JSON.parse(window.localStorage.getItem('districtType'));
    this.districtList = districtList.filter(el => {
      return this.stateId == el.STATE_TYPE_ID;
    })
  }

  selectDistrict(event) {
    this.districtName = event.value;
    this.districtList.forEach(el => {
      if(this.districtName === el.DISTRICT_TYPE_NAME){
        this.distId = el.ID;
      }
    })
  }

  selectTitle(event) {
    this.title = event.value;
    this.titleList.forEach(el => {
      if(this.title === el.SALUTATION_NAME){
        this.titleId = el.ID;
      }
    })
  }

  getUserDetails() {
    this.authService.getMyProfile().subscribe(res => {
      this.isMobileVerified = res.data['MOBILE_VERIFIED'];
      this.isEmailVerified = res.data['EMAIL_VERIFIED'];
      for (let role of this.roleList) {
        if (role.ID == res.data['ROLE_ID']) {
          this.role = role.ROLE_NAME;
        }
      }

      this.titleId = res.data['SALUTATION_ID'];
      for (let title of this.titleList) {
        if (title.ID == res.data['SALUTATION_ID']) {
          this.title = title.SALUTATION_NAME;
        }
      }

      if (res.data['DEPARTMENT_TYPE_ID'] == null || res.data['DEPARTMENT_TYPE_ID'] == undefined) {
        res.data['DEPARTMENT_TYPE_ID'] = 0;
      }
      this.deptId = res.data['DEPARTMENT_TYPE_ID'];
      for (let dept of this.departmentList) {
        if (dept.ID == res.data['DEPARTMENT_TYPE_ID']) {
          this.department = dept.DEPARTMENT_TYPE_NAME;
        }
      }

      this.stateId = res.data['STATE_TYPE_ID'];
      for (let state of this.stateList) {
        if (state.ID == res.data['STATE_TYPE_ID']) {
          this.stateName = state.STATE_TYPE_NAME;
          let districtList = JSON.parse(window.localStorage.getItem('districtType'));
          this.districtList = districtList.filter(el => {
            return state.ID == el.STATE_TYPE_ID;
          })
        }
      }

      this.distId = res.data['DISTRICT_TYPE_ID'];
      for (let dist of this.districtList) {
        if (dist.ID == res.data['DISTRICT_TYPE_ID'] && dist.STATE_TYPE_ID == res.data['STATE_TYPE_ID']) {
          this.districtName = dist.DISTRICT_TYPE_NAME;
        }
      }

      this.addform.patchValue({
        salutation: this.title,
        fname: res.data['FIRST_NAME'],
        lname: res.data['LAST_NAME'],
        phone: res.data['MOBILE'],
        emailId: res.data['EMAIL_ID'],
        pin: res.data['PINCODE'],
        organization:res.data['ORGANIZATION_NAME'],
        profession:res.data['PROFESSION_NAME'],
        state: this.stateName,
        district: this.districtName,
        department: this.department,
        role: this.role,
        company: res.data['COMPANY_NAME']
      })
      this.email = res.data['EMAIL_ID'];
      this.userData = res.data;
    },err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status]: "error"),
        err.error.status
      )
    })
  }

  cancel() {
    this.isuserprofile = true;
    this.ischangepassword = false;
    this.isverifyaccount = false;
  }

  verifyEmail(val) {
    this.authService.getAttributeCode(val).subscribe(
      res => {
        this.notify.showNotification(
          res.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status]: "success"),
          res.status)
      },err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      }
    )
    this.otpExpiry();
    this.isverifyaccount = true;
    this.isuserprofile = false;
    this.attribute = val;
  }

  namsfiles() {
    this.isuserprofile = false;
    this.ischangepassword = false;
    this.isverifyaccount = false;
    this.isnamsfiles = true;
    if(this.isnamsfiles){
      this.getFiles();
    }
  }

  userprofile() {
    this.isuserprofile = true;
    this.ischangepassword = false;
    this.isverifyaccount = false;
    this.isnamsfiles = false;
  }

  changepassword() {
    this.isuserprofile = false;
    this.ischangepassword = true;
    setTimeout(() => {
      var elem = this.renderer.selectRootElement('#oldPassword');
      elem.focus();
    }, 1000);
    this.isverifyaccount = false;
    this.isnamsfiles = false;
    this.changepswdform.reset();
  }

  uploadFile(event: any){
    if (!this.uploadService.validateFileExtension(event)) {
      // this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        "File format is not supported!",
        "top",
        "warning",
        0
      );
      return;
    }
    this.selectedFile = event.target.files[0];
  }

  async uploadSignedFile(tempObj) {
    let progressInit = 0;
    await this.uploadService.uploadFilethroughSignedUrl(tempObj, progressInit => {
    }).catch((err) => {
      this.isUploadProgress = false;
      this.handleError(err);
    });
  }

  handleError(err: any) {
    this.notify.showNotification(
      err.error.message,
      "top",
      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
      err.error.status
    );
  }

  async saveDocumentToDB(tempObj) {
    await this.namsService.saveFileSourceToDB(tempObj).then((data) => {
      return data;
    })
      .catch((err) => {
        this.isUploadProgress = false;
        let body = {
          ID: tempObj['ID'],
          KEY: tempObj['KEY']
        }
        this.deleteFileFromS3(body)
        this.handleError(err);
      });
      this.resArray = [];
      this.namsFilePage = 1;
      this.getFiles();
  }

  deleteFileFromS3(obj:any) {
    this.uploadService.deleteObjectFromS3(obj).subscribe((res) => {
      return res;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
      return err;
    })
  }

  getFiles(){
    this.namsService.getNamsFileList(this.namsFilePage).subscribe(res => {
      this.resArray = this.resArray.concat(res.data);
      this.dataSource = new MatTableDataSource(this.resArray);
      this.lastData = res.data.length;
    },err => {
      this.handleError(err);
    })
  }

  onScrollDown(){
    this.namsFilePage++;
    this.getFiles();
  }

  async submit(){
    let tempObjforGetsigned = {
      FILE_NAME: this.selectedFile['name'],
      FILE_TYPE: this.selectedFile['type'],
      SECTION: 'sheets'
    }
    await this.namsService.getSignedUrl(tempObjforGetsigned).then(async (res) => {
      if (res && res['data'] && res['data']['SIGNED_URL']) {
        let tempObjForUpload = {
          url: res['data']['SIGNED_URL'],
          file: this.selectedFile
        }
        await this.uploadSignedFile(tempObjForUpload);
        let saveBody = {
          FILE_KEY : res['data']['PATH'],
          FILE_NAME:this.selectedFile['name'],
          FILE_TYPE : this.selectedFile['type'],
          SIZE : this.selectedFile['size']
        }
        await this.saveDocumentToDB(saveBody);
        this.isUploadProgress = false;
        // this.attachment.nativeElement.value = '';
        this.selectedFile = [];
        this.notify.showNotification(
          'Documents uploaded successfully',
          "top",
          (!!colorObj[200] ? colorObj[200] : "success"),
          200
        );
      }
    })
    .catch((err) => {
      this.isUploadProgress = false;
      this.handleError(err);
    });
  }

  removeFile(){
    this.selectedFile = [];
  }

  verifyOtp(val) {
    this.authService.verifyOTP(val,this.attribute).subscribe(res => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status]: "success"),
        res.status
      )
      this.isuserprofile = true;
      this.isverifyaccount = false;
      this.isEmailVerified = true;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status]: "warning"),
        err.error.status
      )
    })
  }

  otpExpiry() {
    this.expiryTime = 120;
    this.showTime =  true;
    this.isExpiryCompleted =  true;
    this.interval = setInterval(() => {
      if(this.expiryTime > 0) {
        this.expiryTime--;
        const minutes: number = Math.floor(this.expiryTime / 60);
        this.timeLeft = ('00' + minutes).slice(-2) + ':' + ('00' + Math.floor(this.expiryTime - minutes * 60)).slice(-2);
        return this.timeLeft;
      } else {
        clearInterval(this.interval)
        this.showTime =  false;
        this.isExpiryCompleted = false;
      }
    },1000)
  }

  filescolor() {
    if (this.isnamsfiles == true || this.isverifyaccount == true) {
      return 'white';
    }
    else {
      return '#000000';
    }
  }

  filesBGcolor() {
    if (this.isnamsfiles == true || this.isverifyaccount == true) {
      return '#000000';
    }
    else {
      return '#F8F9F9';
    }
  }

  filesIcon_black() {
    if (this.isnamsfiles == true || this.isverifyaccount == true) {
      return 'hidden';
    }
    else {
      return 'visible';
    }
  }

  filesIcon_white() {
    if (this.isnamsfiles == true || this.isverifyaccount == true) {
      return 'visible';
    }
    else {
      return 'hidden';
    }
  }

  profilecolor() {
    if (this.isuserprofile == true || this.isverifyaccount == true) {
      return 'white';
    }
    else {
      return '#000000';
    }
  }

  profileBGcolor() {
    if (this.isuserprofile == true || this.isverifyaccount == true) {
      return '#000000';
    }
    else {
      return '#F8F9F9';
    }

  }
  showTagIcon_black() {
    if (this.isuserprofile == true || this.isverifyaccount == true) {
      return 'hidden';
    }
    else {
      return 'visible';
    }

  }

  showTagIcon_white() {
    if (this.isuserprofile == true || this.isverifyaccount == true) {
      return 'visible';
    }
    else {
      return 'hidden';
    }
  }

  pswdcolor() {
    if (this.ischangepassword == true) {
      return 'white';
    }
    else {
      return '#000000';
    }
  }

  pswdBGcolor() {
    if (this.ischangepassword == true) {
      return '#000000';
    }
    else {
      return '#F8F9F9';
    }
  }

  pswdTagIcon_black() {
    if(this.ischangepassword == true)
    {
      return 'hidden';
    }
    else {
      return 'visible';
    }
  }

  pswdTagIcon_white() {
    if(this.ischangepassword == true)
    {
      return 'visible';
    }
    else {
      return 'hidden';
    }
  }

  update(model) {
    if(model.emailId != this.userData.EMAIL_ID){
      this.emailId = model.emailId;
    }
    else  {
      this.emailId ;
    }
    if (this.titleId != this.userData.SALUTATION_ID || model.fname != this.userData.FIRST_NAME || model.lname != this.userData.LAST_NAME || this.deptId != this.userData.DEPARTMENT_TYPE_ID ||
      this.stateId != this.userData.STATE_TYPE_ID || this.distId != this.userData.DISTRICT_TYPE_ID || model.emailId != this.userData.EMAIL_ID || model.pin != this.userData.PINCODE || model.organization != this.userData.ORGANIZATION_NAME || model.profession!= this.userData.PROFESSION_NAME) {
      this.authService.updateOwnProfile(model, this.emailId, this.deptId, this.stateId, this.distId, this.titleId).subscribe(res => {
        this.notify.showNotification(
          res.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status]: "success"),
          res.status
        )
        this.getUserDetails();
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status]: "warning"),
          err.error.status
        )
      })
    }
    this.isuserprofile = true;
    this.ischangepassword = false;
  }

  changePassword(model) {
    if (model.old_pswd != null && model.new_pswd != null && model.confirm_pswd != null) {
      if (this.changepswdform.controls.confirm_pswd.value === this.changepswdform.controls.new_pswd.value) {
        this.authService.changePassword(model).subscribe(res => {
          this.notify.showNotification(
            res.message,
            "top",
            (!!colorObj[res.status] ? colorObj[res.status]: "success"),
            res.status
          )
          this.changepswdform.reset();
          Object.keys(this.changepswdform.controls).forEach(key => {
            this.changepswdform.get(key).setErrors(null);
          });
        }, err => {
          this.notify.showNotification(
            "Old Password is Incorrect",
            "top",
            "error",
            0,
          )
        })
      } else {
        this.notify.showNotification(
          "Password does not match",
          "top",
          "warning",
          0
        )
      }
    }
  }

  onDeptChange(event){
    this.deptName = event.value;
    this.departmentList.forEach(el => {
      if(this.deptName === el.DEPARTMENT_TYPE_NAME){
        this.deptId = el.ID;
      }
    })
  }

}
