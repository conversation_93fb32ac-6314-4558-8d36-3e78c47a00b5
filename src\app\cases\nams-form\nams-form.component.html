<div class="comp-head">
  COMPLAINANT DETAILS
</div>
<div class="comp-head-mandatory">
  * labeled fields are mandatory
</div>
<form [formGroup]="step2Form" class="form">
  <div class="step2-consumer-container" style="padding-top: 21px;">

    <div class="row-container">
      <div class="input-container">
        <div class="text-container">
          <p>Advertiser's Company<span style="color: red;">*</span></p>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput type="text" formControlName="company" [matAutocomplete]="autoCompany" (blur)="companyInput()" (input)="companyChange()">
            <mat-autocomplete #autoCompany="matAutocomplete" (optionSelected)="onSelectionChange($event)">
              <mat-option *ngFor="let company of companyList;" [value]="company.COMPANY_NAME" style="
              font-style: normal;
              font-weight: normal;">
                {{company.COMPANY_NAME}}
              </mat-option>
            </mat-autocomplete>
            <mat-error class="error-msg" *ngIf="step2Form.controls['company'].errors?.required">
              Please choose advertiser's company
          </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="input-container">
        <div class="text-container">
          <p>Brand Name of Product/Service</p>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="brand" autocomplete="off">
          </mat-form-field>
        </div>
      </div>
      <div class="input-container" *ngIf="nams === 'tams' || nams === 'tams-reech'">
        <div class="text-container">
          <p>Advertiser <span style="color: red;">*</span></p>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="advertiser" autocomplete="off">
            <mat-error class="error-msg" *ngIf="step2Form.controls['advertiser'].errors?.required">
              Advertiser name is required
          </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="input-container" *ngIf="nams === 'reech'">
        <div class="text-container">
          <p>Product</p>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="product" autocomplete="off">
          </mat-form-field>
        </div>
      </div>
    </div> <!-- row container -->

    <div class="row-container">
      <div class="input-container" *ngIf="nams === 'tams' || nams === 'tams-reech'">
        <div class="text-container">
          Media <span style="color: red;">*</span>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="media" autocomplete="off" [readonly]="readonly">
            <mat-error class="error-msg" *ngIf="step2Form.controls['media'].errors?.required">
              Media is required
          </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="input-container" *ngIf="nams === 'tams' || nams === 'tams-reech'">
        <div class="text-container">
          Media Outlet <span style="color: red;">*</span>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="media_outlet" autocomplete="off">
            <mat-error class="error-msg" *ngIf="step2Form.controls['media_outlet'].errors?.required">
              Media Outlet is required
          </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="input-container" *ngIf="nams === 'reech'">
        <div class="text-container">
          Medium <span style="color: red;">*</span>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <mat-select formControlName="seen_medium" [disabled]="readonly">
              <mat-option *ngFor="let source of ads" [value]="source.ID">
                {{source.ADVERTISEMENT_SOURCE_NAME}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div class="input-container" *ngIf="nams === 'reech'">
        <div class="text-container">
          Platform <span style="color: red;">*</span>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <mat-select formControlName="platform" [disabled]="readonly">
              <mat-option *ngFor="let plat of platforms" [value]="plat.ID">
                {{plat.PLATFORM_NAME}}</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div class="input-container" *ngIf="nams === 'reech'">
        <div class="text-container">
          Publication link <span style="color: red;">*</span>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="publication_link" autocomplete="off">
            <mat-error class="error-msg" *ngIf="step2Form.controls['publication_link'].errors?.required">
              Publication link is required
          </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="input-container" *ngIf="nams === 'tams' || nams === 'tams-reech'">
        <div class="text-container">
          Date <span style="color: red;">*</span>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput [matDatepicker]="picker" formControlName="date" [max]="maxDate" autocomplete="off">
            <mat-datepicker-toggle matSuffix [for]="picker" style="position: relative;bottom: 3px;"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error class="error-msg" *ngIf="step2Form.controls['date'].errors?.required">
              Please Choose the date
          </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div> <!-- row container -->

    <div class="row-container">
      <div class="input-container" *ngIf="nams === 'reech'">
        <div class="text-container">
          Date <span style="color: red;">*</span>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput [matDatepicker]="picker" formControlName="date" [max]="maxDate" autocomplete="off">
            <mat-datepicker-toggle matSuffix [for]="picker" style="position: relative;bottom: 3px;"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error class="error-msg" *ngIf="step2Form.controls['date'].errors?.required">
              Please Choose the date
          </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="input-container">
        <div class="text-container">
          <p>Time</p>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="time" matTimepicker autocomplete="off">
            <span matSuffix><img src="../../../assets/images/Clock.png" style="position: relative; top: -7px;"
              matTimepicker></span>
          </mat-form-field>
        </div>
      </div>
      <div class="input-container">
        <div class="text-container">
          Product Category <span style="color: red;">*</span>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="product_category" required autocomplete="off">
            <mat-error class="error-msg" *ngIf="step2Form.controls['product_category'].errors?.required">
              Producy Category is required
          </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="input-container" *ngIf="nams === 'tams' || nams === 'tams-reech'">
        <div class="text-container">
          Edition
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="edition" autocomplete="off">
          </mat-form-field>
        </div>
      </div>
    </div>

    <div class="row-container">
      <div class="input-container" *ngIf="nams === 'reech'">
        <div class="text-container">
          Influencer name <span style="color: red;">*</span>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="influencer_name" autocomplete="off">
            <mat-error class="error-msg" *ngIf="step2Form.controls['influencer_name'].errors?.required">
              Influencer name is required
          </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="input-container" *ngIf="nams === 'reech'">
        <div class="text-container">
          Influencer profile URL
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="influencer_profile_URL" autocomplete="off">
          </mat-form-field>
        </div>
      </div>
    </div>
    <div class="row-container">
      <div class="input-container" *ngIf="nams === 'reech'">
        <div class="text-container">
          Influencer contact no.
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="influencer_contact_no" maxlength="10" autocomplete="off">
          </mat-form-field>
        </div>
      </div>
      <div class="input-container" *ngIf="nams === 'reech'">
        <div class="text-container">
          Influencer email address
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="influencer_email_address" autocomplete="off">
          </mat-form-field>
        </div>
      </div>
    </div>

    <div class="row-container">
      <div class="input-container" *ngIf="nams === 'tams' || nams === 'tams-reech'">
        <div class="text-container">
          Supplement
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="suppliment" autocomplete="off">
          </mat-form-field>
        </div>
      </div>
      <div class="input-container" *ngIf="nams === 'tams' || nams === 'tams-reech'">
        <div class="text-container">
          Ad language
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="ad_language" autocomplete="off">
          </mat-form-field>
        </div>
      </div>
      <div class="input-container" *ngIf="nams === 'tams' || nams === 'tams-reech'">
        <div class="text-container">
          Creative ID <span style="color: red;">*</span>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="creative_id" [readonly]="readonly">
          </mat-form-field>
        </div>
      </div>
    </div>

    <div class="row-container" *ngIf="nams === 'tams' || nams === 'tams-reech'">
      <div class="input-container">
        <div class="text-container">
          Duration
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <input matInput formControlName="duration" autocomplete="off">
          </mat-form-field>
        </div>
      </div>
    </div>

    <div formArrayName="advCompany" *ngIf="nams === 'reech'">
      <div class="outer-btn-container" *ngFor="let advCompanyControl of getControls1(); let in = index;"
        formGroupName="{{in}}">
        <div class="outer-row-container">
          <div fxLayout="row" style="padding: 1% 0% 2% 2%;">
            <div>
              <img src="../../assets/images/drop-arrow.png" style="margin-right: 5px">
              <mat-label style="color: #0088CB; font-size: 13px;">Advertisement company {{in+2}}</mat-label>
            </div>
            <div fxFlex="70%"></div>
            <div>
              <button mat-button matSuffix mat-icon-button aria-label="Clear" class="delete-red"
                (click)="removeCodes(in)">
                <img src="../assets/images/Trash-icon.svg" style="margin-top: -15px;">
              </button>
            </div>
          </div>
          <div class="inner-row-container">
            <div class="input-container">
              <div class="text-container">
                <p>Company name</p>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="adv-input-field">
                  <input matInput type="text" formControlName="advCompany" [matAutocomplete]="autoCompany">
                  <mat-autocomplete #autoCompany="matAutocomplete" (optionSelected)="onAdvCompSelectionChange($event, in)">
                    <mat-option *ngFor="let company of advCompanyControl.controls['advCompanyList'].value;" [value]="company.COMPANY_NAME" style="
                    font-style: normal;
                    font-weight: normal;">
                      {{company.COMPANY_NAME}}
                    </mat-option>
                  </mat-autocomplete>
                </mat-form-field>
              </div>
            </div>
            <div class="input-container">
              <div class="text-container">
                <p>Contact email address</p>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="adv-input-field">
                  <input matInput formControlName="advEmail" autocomplete="off">
                </mat-form-field>
              </div>
            </div>
          </div>
          <!-- <div class="input-container">
            <div class="text-container">
              <p>Brand <span style="color: red;">*</span></p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <input matInput formControlName="advBrand" autocomplete="off">
              </mat-form-field>
            </div>
          </div>
          <div class="input-container">
            <div class="text-container">
              <p>Product <span style="color: red;">*</span></p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <input matInput formControlName="advProduct" autocomplete="off">
              </mat-form-field>
            </div>
          </div> -->
        </div>
      </div>
      <div>
        <button mat-flat-button class="add-source-button" style="margin-left: 2%;"
        [disabled]="getControls1().length == 3" (click)="addAdvCompany()">
          + Add advertisement company
        </button>
      </div>
    </div>

    <div class="row-container">
      <div class="input-container">
        <div class="text-container">
          <p>Do you have a copy of the advertisement and/or other supporting documentation?</p>
        </div>
        <div class="control-container" fxLayout="row">
          <div class="upload-container">
            <button mat-button class="upload-btn" [class.spinner]="isUploadProgress" [disabled]="isUploadProgress"
              for="doc_file" (click)="fileInput.click()">
              <mat-icon style="font-size: large;">cloud_upload</mat-icon>&nbsp;Upload your file
              <input type="file" formControlName="doc_file" class="doc_file" id="doc_file" #fileInput hidden
              multiple="true" accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt,.jpeg,.png,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv"
               (change)="onFileSelected($event)" />
            </button>
          </div>
          <div fxFlex="30px"></div>
          <div class="url-container">
            <mat-form-field appearance="outline" [ngClass]="{'reech-link' : nams === 'reech', 'tams-link' : nams === 'tams' || nams === 'tams-reech' }">
              <span matPrefix>
                <span id="url-icon" class="glyphicon glyphicon-link"></span>&nbsp;&nbsp;
              </span>
              <input matInput type="url" formControlName="add_url" placeholder="Add url" autocomplete="off">
              <!-- <mat-error *ngIf="add_url.hasError('url') && !add_url.hasError('required')">
                      Please enter a valid URL
                  </mat-error> -->
            </mat-form-field>
          </div> <!-- url-container -->
        </div>
      </div>
      <div class="input-container" style="position:relative;" *ngIf="nams === 'tams' || nams === 'tams-reech'">
        <div class="text-container">
          <p>Translation hyperlink</p>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <span matPrefix>
              <span id="url-icon" class="glyphicon glyphicon-link"></span>&nbsp;&nbsp;
            </span>
            <input matInput type="url" formControlName="translation_hyperlink" autocomplete="off">
          </mat-form-field>
        </div>
      </div>
    </div> <!-- row container -->

    <div *ngIf="files_attached === 'Yes'">
      <div *ngFor="let videos of step2Form.controls['file_array'].value;let index = index" fxLayout="row"
        fxLayoutGap="5px" class="row-container filename-holder">
        <div fxFlex="95%" fxLayoutAlign="start" style="color: rgb(150, 148, 148);">
          <p class="adv-file-text">{{videos.ATTACHMENT_NAME}}</p>
        </div>
        <div fxFlex="5%" fxLayoutAlign="end" style="height: 33px;">
          <button mat-button mat-icon-button (click)="preview(videos.ATTACHMENT_SOURCE)">
            <img src="../../../assets/images/View.png" style="margin: 0px 10px 2px 10px;">
          </button>
        </div>
        <div fxFlex="5%" fxLayoutAlign="end" style="height: 33px;">
          <button mat-button mat-icon-button aria-label="Clear" (click)="removeVideoFile(index, videos.ATTACHMENT_SOURCE)">
            <img src="../../assets/images/close-red.png" style="margin: 0px 10px 2px 10px;">
          </button>
        </div>
        <!-- <div class="row-container filename-holder">
          <strong>{{videos.ATTACHMENT_NAME}}</strong>
        </div> -->
      </div>
      <section class="progress-bar-upload" *ngIf="fileProgress > 0">
        <mat-progress-bar [color]="'primary'" [value]="fileProgress">
        </mat-progress-bar>
      </section>
    </div>

    <div class="row-container" style="margin-top: 10px;" *ngIf="nams === 'tams' || nams === 'tams-reech'">
      <div class="input-container">
        <div class="text-container">
          <p>Transcription (max 2000 characters)</p>
        </div>
        <mat-form-field appearance="outline" class="textarea-field">
          <textarea matInput formControlName="transcription" rows="3" maxlength="2000"
            placeholder="Tell us about the transcription in brief..."
            style="width:800px !important; height: 150px; text-align:justify; text-justify: inter-word;" autocomplete="off"
            [(ngModel)]="transcriptionAlignment" (keyup)="keyUp($event)"></textarea>
        </mat-form-field>
      </div>
    </div> <!-- row container -->

    <div class="row-container" style="margin-top: 10px;">
      <div class="input-container">
        <div class="text-container">
          <p>Describe the Advertisement (max 5000 characters)<span style="color: red;">*</span></p>
        </div>
        <mat-form-field appearance="outline" class="textarea-field">
          <textarea matInput formControlName="description" rows="3" maxlength="5000"
            placeholder="Tell us about the advertisement in brief... &#13;&#10;Eg: The advertisement shows two kids playing in mud and dirtying themselves, mother of one says don't worry you can take a bath with this soap and it has 10times more power to protect against disease causing bacteria and viruses."
            style="width:800px !important; height: 150px; text-align:justify; text-justify: inter-word;" autocomplete="off"
            [(ngModel)]="descriptionAlignment" (keyup)="keyUp1($event)"></textarea>
            <mat-error class="error-msg" *ngIf="step2Form.controls['description'].errors?.required">
              Describe the Advertisement with max length 5000
            </mat-error>
        </mat-form-field>
      </div>
    </div> <!-- row container -->

    <div class="row-container" style="margin-top: 10px;">
      <div class="input-container">
        <div class="text-container">
          <p>Specify the Claims/Visual Frames you find objectionable (max 5000 characters) <span style="color: red;">*</span></p>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="textarea-field">
            <textarea matInput formControlName="compDesc" rows="3" maxlength="5000"
              placeholder="Your claims regarding complaints &#13;&#10;Eg: Claim 1: 10 times more power protect against disease causing bacteria and viruses. &#13;&#10;OR Visual 1: Representation of coronavirus like virus implying soap gives 10times more protection against covid."
              style="width:800px !important; height: 150px; text-align:justify; text-justify: inter-word" autocomplete="off"
              [(ngModel)]="claimsAlignment" (keyup)="keyUp2($event)"></textarea>
              <mat-error class="error-msg" *ngIf="step2Form.controls['compDesc'].errors?.required">
                Specify the claims with max length 5000
              </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>
    <!-- row container -->

    <!-- <div class="row-container">
      <div class="input-container">
        <div class="text-container">
          <p>Initial classification</p>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <mat-select formControlName="initial_class">
              <mat-option value="Promoting communal disharmony">Promoting communal disharmony</mat-option>
              <mat-option value="Misleading">Misleading</mat-option>
              <mat-option value="Being offensive">Being offensive</mat-option>
              <mat-option value="Offensive to religious beliefs">Offensive to religious beliefs</mat-option>
              <mat-option value="Graphics copyright">Graphics copyright</mat-option>
              <mat-option value="Offensive portray of goddess">Offensive portray of goddess</mat-option>
              <mat-option value="Indecent exposure">Indecent exposure</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div class="input-container">
        <div class="text-container">
          <p>Has the issue resolved</p>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="input-field">
            <mat-select formControlName="issue_resolved">
              <mat-option value="No">No, try to do it locally</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
    </div>  -->

    <!-- <div class="row-container" style="margin-top: 10px;">
      <div class="input-container">
        <div class="text-container">
          <p>What would the complainant like us to do to resolve the issue?</p>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="textarea-field">
            <textarea matInput formControlName="suggestion" rows="3"
              placeholder="Write suggestion.."></textarea>
             <mat-hint><strong>Don't disclose personal info</strong></mat-hint>
          </mat-form-field>
        </div>
      </div>
    </div>  -->
    <!-- row container -->

  </div> <!-- step2-consumer-container  -->

  <div class="divider-container">
    <mat-divider></mat-divider>
  </div>

  <div class="btn-container">
    <div class="next-container">
      <button mat-flat-button class="next-btn" ng-click="tabGroup.selectedIndex=4;" [disabled]="step2Form.invalid"
        (click)="step2Next('submit')">{{buttonName}}</button>
    </div>
    <div class="back-container">
      <button mat-stroked-button class="back-btn" (click)="back()" *ngIf="!backDisable">Back</button>
      <button mat-stroked-button class="back-btn" (click)="cancel()" *ngIf="backDisable">Cancel</button>
    </div>
  </div>
</form>
