import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, HostListener, Input, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { AddTaskComponent } from '../add-task/add-task.component';
import { MatDialog } from '@angular/material/dialog';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { TasksService } from 'src/app/services/tasks.service';
import { NotificationService } from 'src/app/services/notification.service';
import { DatePipe } from '@angular/common';
import { colorObj } from 'src/app/shared/color-object';
import { BehaviorSubject, Observable } from 'rxjs';
import { debounceTime, distinctUntilChanged, finalize } from 'rxjs/operators';
import { MY_FORMATS } from 'src/app/shared/calendarFormat';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter } from 'angular-calendar';
import { MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import moment from 'moment';
import { MatSort } from '@angular/material/sort';

@Component({
  selector: 'app-tasktable',
  templateUrl: './tasktable.component.html',
  styleUrls: ['./tasktable.component.css'],
  providers: [
    { provide: MAT_MOMENT_DATE_ADAPTER_OPTIONS, useValue: { useUtc: true } },
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
  ]
})

export class TasktableComponent implements OnInit, AfterViewInit {

  userInfo: any;
  userName: any;
  @Input() min: any;
  today;
  dataSource: any = [];
  dataSource1: any = [];
  pagename: String;
  loading: boolean = true;
  complaints = [];
  assigneeList = [];
  statusList = [];
  today_date;
  COMPLAINT_DESCRIPTION = new FormControl('');
  assigneeId: number = 0;
  SEARCH_KEY = new FormControl('');
  COMPLAINT_ID = new FormControl(null);
  ASSIGNEE_USER_NAME = new FormControl(null);
  ASSIGNEE_USER_ID = new FormControl(null);
  DUE_DATE = new FormControl(null);
  STATUS_ID = new FormControl(null);
  pageNumber: number = 0;
  private request$: Observable<any>;
  resArray: Array<any> = [];
  filter: boolean = false;
  filterData: any[];
  zeroTasks: boolean = false;
  lastRespSize: number = 0;

  private _taskData = new BehaviorSubject<any[]>([]);
  private taskDataStore: { $taskData: any[] } = { $taskData: [] };
  readonly $taskData = this._taskData.asObservable();

  displayedColumns: string[] = ['border', 'task', 'case', 'status', 'priority', 'assignee', 'due_date'];
  limit: number = 0;
  startIndex: number;
  lastIndex: number;
  totalCount: number;
  pageSize: number = 10;
  rangeLabel: string;
  filteredData: boolean = false;
  searchKey: { name: string; value: any; }[];
  searchArray: Array<any> = [];
  searchPageNumber: number;
  searchKeyword: any;
  filterArray: Array<any> = [];
  companyVal: any;
  assigneeVal: any;
  taskVal: any;
  dateVal: any;
  taskTableCount: any;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    public dialog: MatDialog,
    private router: Router,
    private taskTableService: TasksService,
    private notify: NotificationService,
    private fb: FormBuilder,
    private datePipe: DatePipe,
    private el: ElementRef<HTMLElement>,
    private ref: ChangeDetectorRef) { }

  @ViewChildren(MatPaginator) paginator = new QueryList<MatPaginator>();
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  ngOnInit(): void {
    this.pagename = "Tasks / Taskboard";
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.userName = this.userInfo.firstName;
    this.statusList = JSON.parse(window.localStorage.getItem('taskStatus'));
    if (this.statusList[0].ID == 0) {
      this.statusList[0].TASK_STATUS_NAME = "None";
    }
    this.today = new Date();
    this.today_date = this.datePipe.transform(this.today, 'yyyy-MM-dd');

    this.COMPLAINT_DESCRIPTION.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0) {
          this.complaintsNames(value);
        }
      }, err => {
      })

    this.SEARCH_KEY.valueChanges.pipe(debounceTime(200)).subscribe(search => {
      if (!this.SEARCH_KEY.value) {
        this.pageNumber = 0;
        this.getTaskTable();
      }
      else {
        this.searchPageNumber = 1;
        this.searchKeyword = search;
        this.searchKey = [
          { "name": "SEARCH_KEY", "value": search },
          { "name": "PAGE", "value": this.searchPageNumber },
          { "name": "limit", "value": 20 },
        ]
        this.dataSource.data = [];
        this.taskTableService.filterTask(this.searchKey).subscribe((taskData: any) => {
          this.searchArray = [];
          this.resArray = [];
          this.startIndex = null;
          this.lastIndex = null;
          this.pageSize = 10;
          this.loading = false;
          this.pageNumber++;
          if (taskData.data.length > 0) {
            this.totalCount = taskData.data[0].TOTAL_COUNT;
            this.taskTableCount = taskData.data[0].TOTAL_COUNT;
            if (taskData.data[0].TOTAL_COUNT < this.pageSize) {
              this.rangeLabel = 1 + ' - ' + taskData.data[0].TOTAL_COUNT;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
            this.searchArray = this.searchArray.concat(taskData.data[0].DATA);
            this.searchArray.forEach(element => {
              if (element.DUE_DATE == null) {
                element['due_date'] = '';
              } else {
                let dueDate = new Date(element.DUE_DATE);
                var month = dueDate.getMonth() + 1;
                var day = dueDate.getDate();
                var year = dueDate.getFullYear();
                element['due_date'] = '' + (day <= 9 ? '0' + day : day) + '/' + (month <= 9 ? '0' + month : month) + '/' + year;
              }
            })
            for (let item of this.searchArray) {
              item.DUE_DATE = this.datePipe.transform(item.DUE_DATE, 'yyyy-MM-dd');
            }
            this.zeroTasks = false;
            this.taskDataStore.$taskData = this.searchArray
            this.dataSource.data = this.taskDataStore.$taskData;
            this.dataSource = new MatTableDataSource<any>(this.taskDataStore.$taskData);
            this.dataSource.paginator = this.paginator.toArray()[0];
            this.dataSource.sort = this.sort;
          } else {
            this.zeroTasks = true;
          }
        }, err => {
          this.notify.showNotification(
            err.error.message,
            'top',
            !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
            err.error.status
          );
        });
      }
    });

    this.ASSIGNEE_USER_NAME.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0) {
          this.getAssigneeList(value);
        }
      }, err => {
      })
  }

  getAssigneeList(key) {
    this.taskTableService.getAssigneList(key).subscribe(res => {
      this.assigneeList = res.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  ngAfterContentChecked() {
    this.ref.detectChanges();
  }

  ngAfterViewInit() {
    this.getTaskTable();
    // if(this.totalCount < this.pageSize){
    //   this.rangeLabel = 1 +' - ' + this.totalCount;
    //   } else {
    //     this.rangeLabel = 1 +' - ' + this.pageSize;
    //   }
  }

  getTaskTable() {
    this.searchArray = [];
    this.filterArray = [];
    this.getTaskList()
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((taskData: any) => {
        this.loading = false;
        this.lastRespSize = taskData.data.length;
        if (this.lastRespSize > 0) {
          this.totalCount = taskData.data[0].TOTAL_COUNT;
          if (this.pageNumber == 1) {
            this.dataSource.data = [];
            if (this.pageSize > this.totalCount) {
              this.rangeLabel = 1 + ' - ' + this.totalCount;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.resArray = this.resArray.concat(taskData.data[0].DATA);
          this.resArray.forEach(element => {
            if (element.DUE_DATE == null) {
              element['due_date'] = '';
            } else {
              let dueDate = new Date(element.DUE_DATE);
              var month = dueDate.getMonth() + 1;
              var day = dueDate.getDate();
              var year = dueDate.getFullYear();
              element['due_date'] = '' + (day <= 9 ? '0' + day : day) + '/' + (month <= 9 ? '0' + month : month) + '/' + year;
            }
          })
          for (let item of this.resArray) {
            item.DUE_DATE = this.datePipe.transform(item.DUE_DATE, 'yyyy-MM-dd');
          }
          if (this.resArray.length == 0) {
            this.zeroTasks = true;
          } else {
            this.zeroTasks = false;
          }
          this.taskDataStore.$taskData = this.resArray;
          this.dataSource.data = this.taskDataStore.$taskData;
          this.dataSource = new MatTableDataSource<any>(this.taskDataStore.$taskData);
          this.dataSource.paginator = this.paginator.toArray()[0];
          this.dataSource.sort = this.sort;
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      });
  }

  changePage(event) {
    this.startIndex = (event.pageIndex * this.pageSize) + 1;
    this.lastIndex = this.startIndex + (this.pageSize - 1);
    if (this.lastIndex > this.totalCount) {
      this.lastIndex = this.totalCount;
    }

    this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
    if (this.resArray.length != 0) {
      this.filterArray = [];
      this.searchArray = [];
      this.getTaskTable();
    } else if (this.searchArray.length != 0) {
      this.resArray = [];
      this.filterArray = [];
      this.searchPageNumber++;
      this.searchKey = [
        { "name": "SEARCH_KEY", "value": this.searchKeyword },
        { "name": "PAGE", "value": this.searchPageNumber },
        { "name": "limit", "value": 20 },
      ]
      this.taskTableService.filterTask(this.searchKey).subscribe((taskData: any) => {
        this.loading = false;
        if (taskData.data.length > 0) {
          this.totalCount = taskData.data[0].TOTAL_COUNT;
          this.searchArray = this.searchArray.concat(taskData.data[0].DATA);
          this.searchArray.forEach(element => {
            if (element.DUE_DATE == null) {
              element['due_date'] = '';
            } else {
              let dueDate = new Date(element.DUE_DATE);
              var month = dueDate.getMonth() + 1;
              var day = dueDate.getDate();
              var year = dueDate.getFullYear();
              element['due_date'] = '' + (day <= 9 ? '0' + day : day) + '/' + (month <= 9 ? '0' + month : month) + '/' + year;
            }
          })
          for (let item of this.searchArray) {
            item.DUE_DATE = this.datePipe.transform(item.DUE_DATE, 'yyyy-MM-dd');
          }
          if (this.searchArray.length == 0) {
            this.zeroTasks = true;
          } else {
            this.zeroTasks = false;
          }
          this.taskDataStore.$taskData = this.searchArray
          this.dataSource.data = this.taskDataStore.$taskData;
          this.dataSource = new MatTableDataSource<any>(this.taskDataStore.$taskData);
          this.dataSource.paginator = this.paginator.toArray()[0];
          this.dataSource.sort = this.sort;
        }
      }, err => {
        this.notify.showNotification(
          err.error.message,
          'top',
          !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
          err.error.status
        );
      });
    } else {
      this.filter = true;
      this.resArray = [];
      this.searchArray = [];
      this.pageNumber++;
      this.filterData = [
        { "name": "COMPLAINT_ID", "value": this.COMPLAINT_ID.value },
        { "name": "ASSIGNEE_USER_ID", "value": this.ASSIGNEE_USER_ID.value },
        { "name": "TASK_STATUS_ID", "value": this.STATUS_ID.value },
        { "name": "DUE_DATE", "value": this.datePipe.transform(this.DUE_DATE.value, 'yyyy-MM-dd') },
        { "name": "PAGE", "value": this.pageNumber },
        { "name": "limit", "value": 20 },
      ]
      let fil = [];
      for (let i = 0; i < this.filterData.length; i++) {
        if (this.filterData[i].value != null) {
          fil.push(this.filterData[i])
        }
      }
      this.taskTableService.filterTask(fil).subscribe((filteredData: any) => {
        this.resArray = [];
        this.searchArray = [];
        if (filteredData.data.length > 0) {
          this.totalCount = filteredData.data[0].TOTAL_COUNT;
          this.filterArray = this.filterArray.concat(filteredData.data[0].DATA);
          this.filterArray.forEach(element => {
            if (element.DUE_DATE == null) {
              element['due_date'] = '';
            } else {
              let dueDate = new Date(element.DUE_DATE);
              var month = dueDate.getMonth() + 1;
              var day = dueDate.getDate();
              var year = dueDate.getFullYear();
              element['due_date'] = '' + (day <= 9 ? '0' + day : day) + '/' + (month <= 9 ? '0' + month : month) + '/' + year;
            }
          })
          for (let item of this.filterArray) {
            item.DUE_DATE = this.datePipe.transform(item.DUE_DATE, 'yyyy-MM-dd');
          }
          if (this.filterArray.length == 0) {
            this.zeroTasks = true;
          } else {
            this.zeroTasks = false;
          }
          this.dataSource.data = this.filterArray;
          this.dataSource = new MatTableDataSource<any>(this.filterArray);
          this.dataSource.paginator = this.paginator.toArray()[0];
          this.dataSource.sort = this.sort;
        }
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      });
    }
  }

  getTaskList() {
    this.limit = 20;
    this.pageNumber++;
    this.request$ = this.taskTableService.getTasktableData(this.pageNumber, this.limit);
    return this.request$;
  }

  private onFinalize(): void {
    this.request$ = null;
  }

  public onScrollDown(): void {
    if (this.lastRespSize == 10) {
      if (this.filter) {
        this.filter_comp(this.pageNumber);
      } else {
        this.getTaskTable();
      }
    }
  }


  addTask() {
    const dialogRef = this.dialog.open(AddTaskComponent, {
      width: '1500px',
      height: '380px',
      data: { name: "task-new" },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'refresh') {
          this.resArray = [];
          this.pageNumber = 0;
          this.getTaskTable();
        }
      }
    );
  }

  updateTask(row) {
    const dialogRef = this.dialog.open(AddTaskComponent, {
      width: '1500px',
      height: '600px',
      data: { row, name: "task-update" },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'refresh') {
          this.getTaskTableById(row.ID);
        } else if (data === 'delete') {
          this.taskDataStore.$taskData.forEach((t: any, i) => {
            if (t.ID === row.ID) {
              this.taskDataStore.$taskData.splice(i, 1);
            }
          });
          this.totalCount = this.totalCount - 1;
          if (this.totalCount !== 0) {
            if (this.pageSize > this.totalCount) {
              this.pageSize = this.totalCount;
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
            if (this.lastIndex > this.totalCount) {
              this.lastIndex = this.totalCount;
              this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
            }
          } else {
            this.zeroTasks = true;
          }
          this._taskData.next(Object.assign({}, this.taskDataStore).$taskData);
          this.$taskData.subscribe((res) => {
            if (this.filterArray.length !== 0) {
              this.filterArray = res;
            }
            if (this.resArray.length !== 0) {
              this.resArray = res;
            }
            if (this.searchArray.length !== 0) {
              this.searchArray = res;
            }
            this.dataSource.data = res;
            this.dataSource = new MatTableDataSource(res);
            this.dataSource.paginator = this.paginator.toArray()[0];
          })
        }
      }
    );
  }

  getTaskTableById(taskId) {
    this.taskTableService.getTaskDataByTaskId(taskId).subscribe(res => {
      let taskObj = res.data;
      this.taskDataStore.$taskData.forEach((t: any, i) => {
        if (t.ID === taskObj.ID) {
          this.taskDataStore.$taskData[i] = taskObj;
        }
      });
      this._taskData.next(Object.assign({}, this.taskDataStore).$taskData);
      this.updateTaskTableDate(taskObj.ID);
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  updateTaskTableDate(id) {
    this.$taskData.subscribe((res) => {
      this.resArray = res;
      this.dataSource.data = res;
      this.dataSource = new MatTableDataSource(res);
      this.dataSource.paginator = this.paginator.toArray()[0];
      this.dataSource.data.forEach(element => {
        if (id == element.ID) {
          element['due_date'] = moment(element.DUE_DATE).format('DD/MM/yyyy');
        }
      });
    });
  }

  onSelectionChange(event, ID) {
    let dat = {}
    this.assigneeList.forEach(element => {
      if (event.option.value === element.ASSIGNEE_USER_NAME) {
        this.assigneeId = element.ID;
        dat = { "ID": ID, "ASSIGNEE_USER_ID": this.assigneeId }
      }
    });
    this.taskTableService.updateTask(dat).subscribe(res => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      this.getTaskTable();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "warning"),
        err.error.status
      )
    })
  }

  changeValueStatus(currentValue, ID) {
    let dat = {}
    dat = { "ID": ID, "TASK_STATUS_ID": currentValue }
    this.taskTableService.updateTask(dat).subscribe(res => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      this.getTaskTable();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "warning"),
        err.error.status
      )
    })
  }

  complaintsNames(value) {
    this.taskTableService.getComplaintsList(value).subscribe((complaintList: any) => {
      this.complaints = complaintList.data;
      for (let complaint of this.complaints) {
        if (complaint.COMPLAINT_DESCRIPTION != null && complaint.COMPLAINT_DESCRIPTION != '') {
          complaint.COMPLAINT_DESCRIPTION = (complaint.COMPLAINT_DESCRIPTION).replace(/<[^>]+>/g, '');
        }
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    });
  }

  complaintSelected(event) {
    this.complaints.forEach(element => {
      if (event.option.value === element.CASE_ID) {
        this.COMPLAINT_ID.setValue(element.ID);
      }
    });
    this.filter_comp(1);
  }

  onSelectionAssignee(event) {
    this.assigneeList.forEach(element => {
      if (event.option.value === element.ASSIGNEE_USER_NAME) {
        this.ASSIGNEE_USER_ID.setValue(element.ID);
      }
    });
    this.filter_comp(1);
  }

  filter_comp(page) {
    this.startIndex = null;
    this.lastIndex = null;
    this.pageSize = 10;
    this.filter = true;
    this.pageNumber = page;
    this.filterData = [
      { "name": "COMPLAINT_ID", "value": this.COMPLAINT_ID.value },
      { "name": "ASSIGNEE_USER_ID", "value": this.ASSIGNEE_USER_ID.value },
      { "name": "TASK_STATUS_ID", "value": this.STATUS_ID.value },
      { "name": "DUE_DATE", "value": this.datePipe.transform(this.DUE_DATE.value, 'yyyy-MM-dd') },
      { "name": "PAGE", "value": this.pageNumber },
      { "name": "limit", "value": 20 },
    ]
    let fil = [];
    for (let i = 0; i < this.filterData.length; i++) {
      if (this.filterData[i].value != null) {
        fil.push(this.filterData[i])
      }
    }
    this.dataSource.data = [];
    this.taskTableService.filterTask(fil).subscribe((filteredData: any) => {
      this.resArray = [];
      this.searchArray = [];
      this.filterArray = [];
      this.loading = false;
      if (filteredData.data.length > 0) {
        this.totalCount = filteredData.data[0].TOTAL_COUNT;
        if (this.totalCount < this.pageSize) {
          this.rangeLabel = 1 + ' - ' + this.totalCount;
        } else {
          this.rangeLabel = 1 + ' - ' + this.pageSize;
        }
        this.filterArray = this.filterArray.concat(filteredData.data[0].DATA);
        this.filterArray.forEach(element => {
          if (element.DUE_DATE == null) {
            element['due_date'] = '';
          } else {
            let dueDate = new Date(element.DUE_DATE);
            var month = dueDate.getMonth() + 1;
            var day = dueDate.getDate();
            var year = dueDate.getFullYear();
            element['due_date'] = '' + (day <= 9 ? '0' + day : day) + '/' + (month <= 9 ? '0' + month : month) + '/' + year;
          }
        })
        for (let item of this.filterArray) {
          item.DUE_DATE = this.datePipe.transform(item.DUE_DATE, 'yyyy-MM-dd');
        }

        this.zeroTasks = false;
        this.taskDataStore.$taskData = this.filterArray;
        this.dataSource.data = this.taskDataStore.$taskData
        this.dataSource = new MatTableDataSource<any>(this.taskDataStore.$taskData);
        this.dataSource.paginator = this.paginator.toArray()[0];
        this.dataSource.sort = this.sort;
      }
      else {
        this.zeroTasks = true;
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  clearFilter() {
    this.dataSource.data = [];
    this.filterArray = [];
    this.searchArray = [];
    this.resArray = [];
    this.zeroTasks = false;
    this.filterData = [];
    this.filter = false;
    this.COMPLAINT_DESCRIPTION.reset();
    this.COMPLAINT_ID.reset();
    this.ASSIGNEE_USER_ID.reset();
    this.ASSIGNEE_USER_NAME.reset();
    this.DUE_DATE.reset();
    this.STATUS_ID.reset();
    this.pageNumber = 0;
    this.totalCount = null;
    this.getTaskTable();
  }

}
