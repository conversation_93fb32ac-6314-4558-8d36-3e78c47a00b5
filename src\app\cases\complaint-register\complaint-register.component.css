:host::ng-deep .mat-tab-label {
    min-width: 0 !important;
    padding: 5px !important;
    color: #000000;
    opacity: 1 !important;
    background: #FFFFFF;
    box-sizing: border-box;
}

:host::ng-deep.mat-tab-list {
    margin-left:32px;
}

:host ::ng-deep .detail-subtab , .mat-tab-link {
  opacity: 0.6 !important;
  background-color: white !important;
}

:host ::ng-deep .main-tab-group .mat-tab-label-active {
    background: #F8F9F9;
    opacity: 1!important;
    color: #000000 !important;
}

:host ::ng-deep .step-tab-group .mat-ink-bar {
    background-color: rgb(0, 195, 255) !important;
    height: 5px !important;
}

:host ::ng-deep .step-tab-group .mat-tab-label {
    background-color: #F8F9F9 !important;
}

:host ::ng-deep .mat-form-field-flex > .mat-form-field-infix {
    padding: .5em 0!important;
    position: relative;
    bottom: 4px;
}
:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-label { margin-top:-5px;margin-bottom: -5px; }
:host ::ng-deep label.ng-star-inserted { transform: translateY(-0.59375em) scale(.75) !important; }

.mat-warn {
    background-color:rgb(167, 55, 55);
    color: #fff;
}

.mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0em 139px 0em 2px;
}

.step-tab-group {
    height: 91vh;
}

.step1Form {
    height: 70%;
    overflow-y: hidden;
}
.step1-scroll {
    height: 82vh;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.step1-scroll::-webkit-scrollbar,
.step1-container::-webkit-scrollbar {
    display: none;
}
/* Hide scrollbar for IE, Edge and Firefox */
.step1-scroll,
.step1-container {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
.step1-container {
    background-color: white;
    /* width: 100%; */
    /* height: 515px; */
    height: 80vh;
    margin: 0px 0px 10px 10px;
    overflow-y: scroll;
    overflow-x: hidden;
    padding-bottom: 15px;
}
:host::ng-deep.mat-select-arrow {
    position: relative;
    top: 4px;
}
.comp-head {
    margin-left: 20px;
    color: #000000;
    font-style: normal;
    font-weight: 700;
    font-size: 13px;
    line-height: 16px;
    padding-top: 10px;
    padding-bottom: 10px;
}
.control-container .mat-form-field-infix {
    display: inherit;
}
.comp-head-mandatory {
    color: red;
    margin-left: 20px;
    position: relative;
    top:7px;
    font-style: normal;
    font-weight: normal;
    font-size: 11px;
    line-height: 13px;
}
.isSelected-span {
    display: flex;
    flex-direction: row;
}
.row-container {
    display: flex;
    flex-direction: row;
    /* gap:30px; */
    margin-left: 20px;
    /* margin-right: 30px; */
}
.row-container >div {
    margin-bottom: 8px;
    margin-right: 30px;

}
.outer-btn-container {
    margin-bottom: 40px;
    width: fit-content;
}
.outer-row-container {
    border: 1px solid rgb(171, 217, 255);
    border-radius: 10px;
    margin-left: 20px;
    margin-right: 20px;
    padding-right: 10px;
    padding-top: 10px;
    margin-bottom: 5px;
}
.inner-row-container {
    display: flex;
    flex-direction: row;
    gap:10px;
    margin-left: 20px;
}
.input-container {
    display: flex;
    flex-direction: column;
    gap:0px;
    /* width: 300px; */
}
.text-container {
    height: 20px;
    margin-bottom: 0px;
}
.control-container {
    /* height: 40px; */
    margin-top: 0px;
}
.textarea-field {
    width:510px !important;
}
/* .inner-textarea-field {
    width:390px !important;
} */
.input-field {
    width:240px !important;
}
/* .inner-input-field {
    width:130px !important;
} */
.divider-container {
    width: 100%;
    margin: 10px 20px 10px 20px;  /* TRBL */
}
.upload-container {
    position: relative;
    overflow: hidden;
    display: inline-block;
}
.upload-btn {
    background-color: rgb(190, 202, 212);
    color: rgb(88, 88, 88);
    width: 160px;
    height: 35px;
    /* display: inline-block; */
}
.upload-container input[type=file] {
    font-size: 100px;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
}
#url-icon {
    font-size: small;
    margin-bottom: 10px;
    /* vertical-align: middle; */
}
.edit-btns-container {
    float: right;
    display: flex;
    flex-direction: row;
    gap:0px;
}
.btn-container {
    display: flex;
    flex-direction: row;
    /* gap:10px; */
    margin-left: 20px;
}
.btn-container >div {
    margin-right: 10px;
}
.next-btn,
.submit-btn {
    width: 80px;
    border-radius: 15px;
    background-color: #0088CB;
    color: white;
}
.cancel-btn,
.back-btn {
    width: 80px;
    border-radius: 15px;
}
/* Hide scrollbar for Chrome, Safari and Opera */
.step2-container::-webkit-scrollbar {
    display: none;
}
/* Hide scrollbar for IE, Edge and Firefox */
.step2-container {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
.step2-container {
    /* width: 100%; */
    background-color: white;
    /* width: 100%; */
    /* height: 515px; */
    height: 80vh;
    margin: 0px 0px 10px 10px;
    overflow-y: scroll;
    overflow-x: hidden;
    padding-bottom: 15px;
}
.step2-company-container {
    height: auto;
    margin-bottom: 40px;
}
/* Hide scrollbar for Chrome, Safari and Opera */
.step-tab-group::-webkit-scrollbar,
.step3-container::-webkit-scrollbar {
    display: none;
}
/* Hide scrollbar for IE, Edge and Firefox */
.step-tab-group,
.step3-container {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
.step3-container {
    background-color: white;
    /* width: 100%; */
    /* height: 515px; */
    height: 80vh;
    margin: 0px 0px 10px 10px;
    overflow-y: scroll;
    overflow-x: hidden;
    padding-bottom: 10px;
}
.step3-content-container {
    width: 60%;
    /* width: 100%; */
    margin: 20px 20px;
}
.line-container {
    height: 35px;
}
.entry-container {
    line-height: 6px;
    padding: 10px 0px 1px 0px !important;
}
.line-text {
    font-size: 15px;
}
.attribute-span {
    color: rgb(182, 182, 182);
}
.value-label {
    float: right;
}
.line-divider {
    border-top-width: 1.5px;
}

/******************* SEARCH TAB  **************************/
.search-form-container {
    margin-top: 20px;
}
.input-icon {
    color:rgb(177, 177, 177);
    font-size: small;
}
.found-result-container {
    height: 250px;
}
.found-container {
    margin: 20px ;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
}
.found-button {
    border: 1px solid rgb(194, 194, 194);
    width: 30px;
    height: 30px;
    border-radius: 3px;
    display: flex;
    align-items: center;
}
.found-icon {
    color: rgb(187, 187, 187);
    font-size:xx-large;
    font-weight: 400;
    /* padding-right: 55px; */
}
.found-label {
    font-size:large;
    font-weight: 500;
}
.classfy-container {
    max-height: fit-content;
    width: 500px;
    line-height: 1px;
    position: relative;
    bottom:10px;
    margin-left: 50px;
}
.classfy-text {
    text-indent: 10px;
    padding:0px;
    margin: 0px;
    height: 25px;
}
.classfy-icon {
    color: rgb(163, 163, 163);
    font-size:large;
    margin-right: 10px;
    position: relative;
    top: 3px;
    /* padding-top: 20px !important; */
       /* OVERLAPS LINES */
    /* position:relative;
    top:1px; */
}
.chips-btn-container {
    position:relative;
    top: 5px;
    margin-left: 50px;
}
.relcomp-container {
    display: inline-block;
    /* gap: 1px; */
    margin-left: 10px;
}
.person-chips {
    border-radius: 20px;
    font-size: 10px;
    margin-right: 0px;
}
#person-chip1 {
    color: brown;
    background-color: rgb(255, 218, 218);
    z-index: 1;
    position: absolute;
    left:10px;
}
#person-chip2 {
    color: rgb(165, 163, 42);
    background-color: rgb(251, 255, 210);
    z-index: 2;
    position: absolute;
    left:40px;
}
#person-chip3 {
    color: rgb(123, 51, 165);
    background-color: rgb(241, 218, 255);
    z-index: 3;
    position: absolute;
    left:70px;
}
.add-btn {
    color: rgb(163, 163, 163);
    margin-left: 120px;
    border: 1px dashed rgb(163, 163, 163) ;
    border-radius: 20px;
}
.media-anchor {
  color: #0088CB;
  font-size: 12px;
  word-break: break-all;
  cursor: pointer;
}
:host ::ng-deep ul {
    margin-bottom: 0px !important;
}
:host ::ng-deep h1 {
    margin-bottom: 0px !important;
}
:host ::ng-deep h2 {
    margin-bottom: 0px !important;
}
:host ::ng-deep h3 {
    margin-bottom: 0px !important;
}
:host ::ng-deep h4 {
    margin-bottom: 0px !important;
}
:host ::ng-deep .ellipsis > p {
    margin-bottom: 0px !important;
}