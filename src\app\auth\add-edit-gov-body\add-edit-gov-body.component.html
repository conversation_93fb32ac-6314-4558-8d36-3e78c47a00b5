<div fxLayout="row">
    <mat-toolbar class="toolbar">
        <div fxFlex="35%" fxLayoutAlign="start">
            <h2 mat-dialog-title fxLayoutAlign="start" style="font-size: 16px;" *ngIf="isEdit">
                <img src="../assets/images/edit-icon.svg">&nbsp;
                Edit user
            </h2>
            <h2 mat-dialog-title fxLayoutAlign="start" style="font-size: 16px;" *ngIf="!isEdit">
                <mat-icon style="margin-top:4px">add</mat-icon>&nbsp;
                Add user
            </h2>
        </div>
        <div fxFlex="60%"></div>
        <div fxFlex="5%" fxLayoutAlign="end">
            <mat-dialog-actions mat-dialog-close style="position: relative; top: -10px; left: 15px;">
                <button mat-icon-button class="search-btn" mat-dialog-close>
                    <mat-icon style="margin-bottom: 4px;">close</mat-icon>
                </button>
            </mat-dialog-actions>
        </div>
    </mat-toolbar>
</div>

<mat-divider></mat-divider>

<div class="edit-form">
    <form [formGroup]="addform">
        <div class="contents">
            <div class="names">
                <div>Title <span style="color: #ff0000;">*</span></div>
                <div fxFlex="15%"></div>
                <div> First name <span style="color: #ff0000;">*</span></div>
                <div fxFlex="25%"></div>
                <div>Last name <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" style="width: 110px;">
                    <mat-select formControlName="salutation">
                        <mat-option *ngFor="let title of titles" [value]="title.ID" style="color:black;"> {{title.SALUTATION_NAME}}</mat-option>
                    </mat-select>
                    <mat-error *ngIf="addform.get('salutation').touched && addform.controls['salutation'].errors?.required">
                        Salutation is required
                    </mat-error>   
                </mat-form-field>
                <mat-form-field appearance="outline" class="input-field">
                    <input matInput formControlName="first_name" style="font-style: normal;font-weight: normal;" autocomplete="off">
                    <mat-error class="error-msg" *ngIf="addform.controls['first_name'].errors?.required">
                        First name is required
                    </mat-error>   
                    <mat-error class="pswd-error" *ngIf="addform.controls['first_name'].errors?.pattern || addform.controls['first_name'].errors?.minlength || addform.controls['first_name'].errors?.maxlength">
                        First Name should be of min length 3 & max length 25 with alphabets only
                    </mat-error> 
                </mat-form-field>
                <mat-form-field appearance="outline" class="input-field">
                    <input matInput formControlName="last_name" style="
                    font-style: normal;
                    font-weight: normal;" autocomplete="off">
                <mat-error class="error-msg" *ngIf="addform.controls['last_name'].errors?.required">
                    Last name is required
                </mat-error>   
                <mat-error class="pswd-error" *ngIf="addform.controls['last_name'].errors?.pattern || addform.controls['last_name'].errors?.maxlength">
                    Last Name should be of max length 25 with alphabets only
                </mat-error> 
                </mat-form-field>
            </div>
            <div class="names1">
                <div>Phone number <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" class="input-field_phno">
                    <input matInput formControlName="phone_number" style="
                    font-style: normal;
                    font-weight: normal;" autocomplete="off">
                    <mat-error class="error-msg" *ngIf="addform.controls['phone_number'].errors?.required">
                        Phone number is required
                    </mat-error>   
                    <mat-error class="pswd-error" *ngIf="addform.controls['phone_number'].errors?.pattern">
                        Enter the valid 10 digit mobile number
                    </mat-error> 
                </mat-form-field>
            </div>
            <div class="names1">
                <div>Email address <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" class="input-field_phno">
                    <input matInput formControlName="email" style="
                    font-style: normal;
                    font-weight: normal;" autocomplete="off">
                <mat-error class="error-msg" *ngIf="addform.controls['email'].errors?.required">
                    Email is required
                </mat-error>   
                <mat-error class="pswd-error" *ngIf="addform.controls['email'].errors?.pattern">
                    Enter the valid email
                </mat-error> 
                </mat-form-field>
            </div>
            <div class="names1">
                <div>Pincode <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" class="input-field_phno">
                    <input matInput formControlName="postal_code" style="
                    font-style: normal;
                    font-weight: normal;" autocomplete="off">
                        <mat-error class="error-msg" *ngIf="addform.controls['postal_code'].errors?.required">
                            Pincode is required
                        </mat-error>   
                        <mat-error class="pswd-error" *ngIf="addform.controls['postal_code'].errors?.pattern">
                            Enter 6 digit pincode
                        </mat-error> 
                </mat-form-field>
            </div>
            <div class="names1">
                <div>Government Body Name <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" class="input-field_phno">
                    <mat-select placeholder="Government body name" [(ngModel)]="selectedGovBody" formControlName="govt_body" (onChange)="getGovBodyId()">
                                   <mat-option *ngFor="let govBody of govDept" [value]="govBody.ID" style="color:black">
                                    {{govBody.GOVERNMENT_DEPARTEMENT_NAME}}
                                  </mat-option>
                    </mat-select>
                    <mat-error class="error-msg" *ngIf="addform.controls['govt_body'].errors?.required">
                        Government Body name is required
                    </mat-error>   
                </mat-form-field>
            </div>
            <div class="control-container" *ngIf="selectedGovBody == '10'">
                <mat-form-field appearance="outline" class="input-field_phno">
                    <input matInput formControlName="govt_body_name" style="
                    font-style: normal;
                    font-weight: normal;" autocomplete="off" placeholder="Specify the government body name" required>
                    <mat-error class="error-msg" *ngIf="addform.controls['govt_body_name'].errors?.required">
                        Please specify Government Body name
                    </mat-error>   
                </mat-form-field>
            </div>
            <div class="names1" *ngIf="!isEdit">
                <div>Password <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container" *ngIf="!isEdit">
                <mat-form-field appearance="outline" class="input-field_phno">
                    <input matInput formControlName="password" style="
                    font-style: normal;
                    font-weight: normal;" autocomplete="off">
                    <img matSuffix class="doc-btn flex" [cdkCopyToClipboard]="value">
                    <mat-error class="error-msg" *ngIf="addform.controls['password'].errors?.required">
                        Password is required
                    </mat-error>   
                    <mat-error class="pswd-error" *ngIf="addform.controls['password'].errors?.pattern">
                        Password should contain at least one uppercase letter, one lowercase letter, one number and one special character (#?!@$%^&*-) and should be of length 8-15 characters
                    </mat-error> 
                </mat-form-field>
            </div>
            <div class="gen-btn" *ngIf="!isEdit">
                <button mat-button class="generate-btn bolder" (click)="password()"> 
                    <span class="bolder">Generate password</span>
                </button>
            </div>
        </div>
        <div class="lastdivdr">
            <mat-divider></mat-divider>
        </div>
        <div fxLayout="row">
            <mat-toolbar class="toolbar2">
                <div fxFlex="35%" fxLayoutAlign="start">
                    <div class="search-div" *ngIf="isEdit">
                        <button mat-button class="remove-btn" (click)='removeUser()'> 
                            <span class="bolder">Remove user</span> 
                        </button>
                    </div>
                </div>
                <div fxFlex="35%"></div>
                <div fxFlex="67%"></div>
                <div class="toolbar-btns" fxFlex="16%" fxLayoutAlign="end">
                    <div class="remove-div">
                        <button mat-flat-button class="cancel-btn" mat-dialog-close> 
                            <span class="bolder">Cancel</span> 
                        </button>
                    </div>
                    <div class="update-div" *ngIf="!isEdit">
                        <button mat-flat-button class="update-btn" (click)="register(addform.value)" [disabled]="addform.invalid"> 
                            <span class="bolder">Register User</span> 
                        </button>
                    </div>
                    <div class="update-div" *ngIf="isEdit">
                        <button mat-flat-button class="update-btn" (click)="update(addform.value)" [disabled]="addform.invalid"> 
                            <span class="bolder">Update</span> 
                        </button>
                    </div>
                </div>
            </mat-toolbar>
        </div>
    </form>
</div>