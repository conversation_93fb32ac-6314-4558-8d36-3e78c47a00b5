.common-toolbar {
  border-bottom: 1px solid #D8DCDE;
  border-top: 0;
  border-left: none;
  border-right: none;
}
.heading-container {
  width: 60%;
}
.options-container {
  width: 40%;
}
.toolbar2 {
  background-color: rgb(245, 245, 245);
}
.toolbar2 >div {
  margin-left: 0.5%;
}
.header-search input[type=text] {
  box-sizing: border-box;
  margin-top: 3%;
  position: relative;
  padding-left: 30px;
  color: #92A2B1;
  height: 38px !important;
  width: 250px !important;
  border: 1px solid #CFD7DF!important;
  border-radius: 4px !important;
  border: 1px solid #CFD7DF!important;  
  border-radius: 12px !important;  
  background: url(../../../assets/images/search.png) no-repeat;
  background-position: 5%;
  background-color: white;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  z-index: 10;
}
.header-search ::placeholder {
  color: rgba(0, 0, 0, 0.4);
}
.add-btn {
  color: white;
  background-color: #0088CB;
  border-radius: 12px;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 38px;
  z-index: 10;
}
:host ::ng-deep .subtag-tabs .mat-tab-list .mat-tab-labels .mat-tab-label-active .mat-tab-label-content {
  font-weight: 600;
}
.card::-webkit-scrollbar {
  display: none;
}
.card {
  -ms-overflow-style: none;
  scrollbar-width: none; 
}
.card {
  background-color:rgb(245, 245, 245);
  box-shadow: none;
  border-width: 0px;
  height: fit-content;
  /* max-height: 80.5vh; */
  max-height: 68.0vh;
  position: relative;
  align-self: justify;
  width: 100%;
  overflow-y: scroll;
  overflow-x: hidden;
}
.subtag-table::-webkit-scrollbar {
  display: none;
}
.subtag-table {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.subtag-table {
  overflow-y: scroll;
  overflow-x: hidden;
  margin-left: 1%;
  width: 99%;
  height: min-content;
  position: relative;
  border-radius: 20px;
  border: 1px solid #D8DCDE;
  border-top: 0px;
}
.table-scroll::-webkit-scrollbar {
  display: none;
}
table th {
  position: -webkit-sticky; 
  position: sticky;
  top: 0;
  z-index: 1; 
  background: #fff; 
}
.table-scroll {
  -ms-overflow-style: none;
  scrollbar-width: none; 
}
.table-scroll {
  overflow-y: scroll;
  overflow-x: hidden;
  height: 63vh;
}
.text-message {
  display: flex;
  align-content: center;
  justify-content: center;
  font-size: large;
}
.mat-row .mat-cell {
  height: 40px !important;
  border-bottom: 1px solid transparent;
  border-top: 1px solid transparent;
  cursor: pointer;
}
:host ::ng-deep .mat-tab-list .mat-tab-labels .mat-tab-label-active {
  color:#0088CB;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  opacity: 1 !important;
}
:host ::ng-deep .mat-tab-group.mat-primary .mat-ink-bar {
  background-color: #0088CB !important;
}
:host ::ng-deep .admin-tabs .mat-tab-list .mat-tab-labels .mat-tab-label-active .mat-tab-label-content {
  font-weight: 600;
}
.mat-row:nth-child(2n+2) {
  background-color: #F5F5F7;
}
:host::ng-deep.mat-paginator-page-size-label {
  display: none;
}
:host ::ng-deep .mat-paginator-range-label {
  display: none;
}  
:host::ng-deep.mat-paginator-page-size-value {
  display: none;
}  
:host::ng-deep .mat-paginator-navigation-previous {
  color:#4DA1FF;
}
:host::ng-deep .mat-paginator-navigation-next {
  color:#4DA1FF;
}
:host::ng-deep .mat-paginator-range-actions {
  margin-right: 24px;
}
mat-paginator {
  width: 96.5%;
  margin-top: 21px;
  margin-left: 2%;
  border-radius: 20px;
  background: #FFFFFF;
  box-sizing: border-box;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
}
.label {
  position: relative;
  bottom: 38px;
  color: rgb(155, 155, 155);
  left: 45%;
}
.admin-table::-webkit-scrollbar {
  display: none;
}
.admin-table {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.admin-table {
  overflow-y: scroll;
  overflow-x: hidden;
  margin-left: 1%;
  width: 98.5%;
  height: min-content;
  position: relative;
  border-radius: 20px;
  border: 1px solid #D8DCDE;
  border-top: 0px;
}
.merge-btn {
  background: #FFFFFF;
  border: 1px solid #0088CB;
  box-sizing: border-box;
  border-radius: 12px;
  color: #0088CB;
  z-index: 2;
}
.merge-btn[disabled] {
  border: 1px solid #D8DCDE;
  opacity: 0.5;
}
@media only screen and (max-width: 1250px) {
  .common-toolbar-mobile {
    border-bottom: 1px solid #D8DCDE;
    border-top: 0;
    border-left: none;
    border-right: none;
  }
  .dashboard-admin-heading {
    padding-left: 0;
    padding-top: 0;
    font-weight:600;
    font-size:18px;
    line-height: 23.94px;
    color: #ED2F45;
    padding-left: 20px;
    padding-top: 6px;
  }
  .dashboard-container {
    width: 100%;
    height: 90%;
    background: #E5E5E5;
  }
  .dasboard-subheading {
    padding-right: 20px;
    padding-top: 0px;
    font-weight:400;
    font-size:14px;
    line-height: 18.62px;
    padding-left: 20px;
    padding-top: 3px;
  }
}