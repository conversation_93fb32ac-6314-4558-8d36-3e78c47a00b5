.dialogbox::-webkit-scrollbar {
  display: none;
}

.dialogbox {
  -ms-overflow-style: none;
  scrollbar-width: none;
  overflow-x: hidden;
  overflow-y: hidden;
}

.toolbar {
  height: 88px;
  padding-left: 15px;
  background-color: #F8F9F9;
  color: rgba(47, 57, 65, 0.6);
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
}

.input-container {
  margin-top: 5px;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

.spinner:before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  margin-left: -10px;
  border-radius: 50%;
  border: 2px solid #ccc;
  border-top-color: #000;
  animation: spinner .6s linear infinite;
}

.document-name {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 320px;
}

.doc-wrapper {
  margin: 12px;
}


.case-values {
  color: #92A2B1;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
}

.case-desc {
  color: #92A2B1;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  height: 35px;
}

.comp-values {
  color: #92A2B1;
  font-size: 14px;
}

.comp-values::placeholder {
  color: #92A2B1;
  float: none;
}

.comp_name {
  color: #92A2B1;
  float: none;
}

.compl_field {
  color: #92A2B1;
  margin-left: 10px;
  margin-top: -3px;
}

.edit-icon {
  cursor: pointer;
}

.save-btn {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  background: #0088CB;
  border-radius: 12px;
  color: #FFFFFF;
  padding-bottom: 1px;
}

.diasble-btn {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  background: #0088CB;
  border-radius: 12px;
  color: #FFFFFF;
  opacity: 0.4;
  cursor: none;
}

.delete-btn {
  color: solid rgba(47, 57, 65, 0.6);
  ;
  background: #F3F3F3;
  border: 1px solid rgba(47, 57, 65, 0.6);
}

.close-btn {
  color: solid rgba(47, 57, 65, 0.6);
  margin-top: 17px;
  background: #F3F3F3;
  border: 1px solid rgba(47, 57, 65, 0.6);
}

.content-head {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  color: #000000;
  margin-top: 15px;
}

.content-head1 {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  color: #000000;
  margin-top: 0px;
}

.select {
  width: 95px;
  height: 28px;
  border-radius: 2px;
  padding-left: 1px;
  background-color: #DFE1E6;
  font-style: normal;
  font-weight: 500;
  font-size: 11px;
  color: #42526E;
}

.assignnn {
  border: 1px solid gray;
  height: 28px;
  width: 158px;
  color: #000000;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  border: 1px solid #D8DCDE;
  box-sizing: border-box;
  border-radius: 2px;
}

.select-priority {
  width: 100px;
  height: 28px;
  padding-left: 0px;
  vertical-align: middle;
  align-items: center;
  border-radius: 2px;
  background-color: #DFE1E6;
  font-style: normal;
  font-weight: 500;
  font-size: 10px;
  color: #42526E;
}

.date-button {
  height: 28px;
  width: 158px;
  border: 1px solid #D8DCDE;
  box-sizing: border-box;
  border-radius: 2px;
}

.date {
  width: 70px;
  height: 33px;
  border: none;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  color: #000000;
}

::ng-deep.mat-tab-link,
::ng-deep.mat-tab-label,
::ng-deep.mat-tab-label-active {
  min-width: 103px !important;
  min-height: 41px !important;
}

:host ::ng-deep .detail-subtab .mat-tab-label,
.mat-tab-link {
  background: #F8F9F9 !important;
  border: 1px solid #D8DCDE;
  border-radius: 2px 0px 0px 0px;
  font-style: normal;
  font-weight: 600;
  font-size: 12px;
  text-align: center;
  color: #000000;
}

:host ::ng-deep .detail-subtab .mat-tab-label-active {
  background-color: #ffffff !important;
  font-style: normal;
  font-weight: 600;
  font-size: 12px;
  text-align: center;
  opacity: 1 !important;
  color: #000000;
}

:host ::ng-deep .detail-subtab .mat-tab-label .mat-tab-label-content {
  font-weight: 600;
}

:host ::ng-deep .detail-subtab .mat-ink-bar {
  display: none !important;
}

.detail-subtab {
  height: 225px;
}

.comments-tab {
  display: flex;
  overflow-x: hidden;
  overflow-y: scroll;
  /* padding-bottom: 200px; */
}

.comments-tab::-webkit-scrollbar {
  display: none;
}

.comments-tab {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.status-tab-container {
  margin: 20px 5px;
}

.Area-symbol {
  height: 40px;
  width: 20px;
  color: #FFFFFF;
  border-radius: 20px;
  border-color: transparent;
  background-color: #0088CB;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
}

.post-btn {
  margin-top: 15px;
  margin-left: -5px;
  border: 1px transparent;
  width: 35px;
  cursor: pointer;
}

.status_color1 {
  color: #0747A6;
}

.status_color2 {
  color: #006644;
}

.status_color3 {
  color: #42526E;
}

.status_color4 {
  color: #42526E;
}

.icon_color1 {
  color: #EA2D2D;
}

.icon_color2 {
  color: #F89E1B;
}

.icon_color3 {
  color: #2ED9FF;
}

.icon_color4 {
  color: #FF4242;
}

.add-btn {
  color: #5A6F84;
  background-color: #CFD7DF;
  border-radius: 12px;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  justify-content: center;
  align-items: center;
  margin-top: 2%;
}

textarea::placeholder {
  position: relative;
  top: -4px;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  color: rgba(47, 57, 65, 0.6);
}

/* Timeline-tab */
.time-container {
  padding-bottom: 20%;
  display: flex;
  margin-left: 3%;
  width: 95%;
  overflow-y: scroll;
  overflow-x: hidden;
}

.time-container::-webkit-scrollbar {
  display: none;
}

.time-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.timeline {
  width: 80%;
  margin-left: 10px;
  position: relative;
  padding-top: 20px;
}

.time-enents {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  color: #000000;
}

.timeline ul {
  list-style: none;
}

.timeline ul li {
  margin-bottom: 10px;
}

.timeline-content .date {
  font-size: 13px;
  font-weight: 200;
  letter-spacing: 2px;
}

@media only screen {

  .timeline:before {
    content: "";
    position: absolute;
    top: 0px;
    transform: translateX(-50%);
    width: 4px;
    height: 100%;
    background-color: #D8DCDE;
    border-radius: 0px 0px 2px 2px;
  }

  .timeline ul li {
    width: 100%;
    position: relative;
    transform: translateX(5px);
  }

  .timeline ul li .complaintreceived::after {
    content: "";
    position: absolute;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: #0088CB;
    border: 4px solid rgba(255, 255, 255, 0.726);
    ;
    top: 5px;
    transform: translate(-20%, -20%);
    left: -41.5px;
  }

  .timeline ul li .assignedto::after {
    content: "";
    position: absolute;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background-color: #04A585;
    border: 4px solid rgba(255, 255, 255, 0.726);
    transform: translate(-20%, -20%);
    left: -41.5px;
  }

  .timeline-content .date {
    position: absolute;
    top: -30px;
  }

  .timeline ul li:hover::before {
    background-color: aqua;
  }
}

/* Document Uploaded */
.file-container {
  background: #FFFFFF;
  border: 1px solid #D8DCDE;
  box-sizing: border-box;
  border-radius: 4px;
  width: max-content;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  color: #2F3941;
}

.documenttab {
  overflow-x: hidden;
  overflow-y: hidden;
  /* padding-bottom: 200px; */
}

.documenttab::-webkit-scrollbar {
  display: none;
}

.documenttab {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.header-right input {
  width: 450px;
  border-radius: 5px;
  border: none;
  background-color: #F8F9F9;
}

.commentfield {
  width: 100%;
}

.commentfield textarea {
  border: none;
  width: 70%;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  color: #000000;
  margin-top: 5px;
  margin-right: 5px;
}

.comment-edit textarea {
  border: 1px solid #D8DCDE;
  box-sizing: border-box;
  border-radius: 3px;
  width: 70%;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  color: #000000;
  margin-top: 5px;
  margin-right: 5px;
}

.search-input {
  color: #000000;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;

}

.header-right-complaint {
  border-radius: 5px;
  border: none;
  background-color: #F8F9F9;
  color: #000000;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
}

.comment-btn {
  font-style: normal;
  font-size: 12px;
  background: #c8c9ca;
  color: #000000;
  width: 25px;
  margin-top: 10px;
  margin-bottom: 10px;
  transform: scale(0.8);
}

.save-comment {
  color: #04A585;
  font-size: 18px;
  padding-top: 5px;
  cursor: pointer;
  margin-left: 10px;
}

.cancel-comment {
  color: #da1406;
  font-size: 18px;
  padding-top: 5px;
  margin-left: 10px;
  cursor: pointer;
}

.delete-menu {
  min-width: 112px;
  max-width: 280px;
  overflow: auto;
  max-height: calc(100vh - 48px);
  border-radius: 4px;
  outline: 0;
  min-height: 40px !important;
}

.delete-option-container {
  width: 160px;
}

.option-btn {
  line-height: 25%;
  height: 550%;
}

.option-text {
  color: #000000;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 200%;
  padding-left: 8px;
}

::ng-deep .mat-menu-panel {
  min-height: 0px !important;
}

/* .dialog-contents {
  height: 600px;
  overflow-x: hidden;
  overflow-y: scroll;
} */

.dialog-contents::-webkit-scrollbar {
  display: none;
}

.dialog-contents {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

:host ::ng-deep .compl_field .mat-form-field-infix {
  margin: -0.5px 0 -0.7em 0 !important;
  top: -0.20em;
}