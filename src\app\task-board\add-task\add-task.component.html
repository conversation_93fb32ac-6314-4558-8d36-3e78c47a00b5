<div mat-dialog-content class="dialogbox"
  style="box-shadow: rgba(0, 0, 0, 0.1);  border-radius: 4px; background: #FFFFFF; max-height: 550px !important;">
  <form [formGroup]="addform">

    <mat-toolbar class="toolbar">
      <div fxLayout="row" style="width: 100% !important;">
        <div class="input-container" fxLayout="column" fxLayoutGap="1px">
          <div fxLayoutAlign="start" fxLayout="row" fxLayoutGap="10px">
            <img src="../assets/images/edit-icon.svg" (click)="editbtn()" class="edit-icon">
            &nbsp;
            <h2 mat-dialog-title fxLayoutAlign="start" style="font-style: normal;font-weight: 600;font-size: 16px;">
              <div class="header-right" style="color: #000000;" *ngIf="!createTask">
                <span class="bolder"> Task -
                </span>
                <input [readonly]="true" class="search-input" v-model="text" formControlName="TASK_NAME"
                  *ngIf="shouldDisable">
                <mat-form-field style="height: 30px; width: 350px; margin-top: -15px;" *ngIf="!shouldDisable">
                  <input matInput type="text" #taskid class="search-input" v-model="text" formControlName="TASK_NAME" autocomplete="off">
                </mat-form-field>
              </div>
              <div class="header-right" *ngIf="createTask">
                <input placeholder="Create your task..." class="search-input" formControlName="TASK_NAME" autocomplete="off">
              </div>
            </h2>
          </div>

          <div class="case-values" id="multiselect-sample" class="control-section" fxLayout="row" fxLayoutGap="10px">
            <img class="casevalue-icon" src="../assets/images/case-icon.svg"> &nbsp;
            <span class="header-right-complaint">
              <div fxLayout="row" *ngIf="createTask && data.name == 'task-new'">
                <span class="case-values"> Complaint -</span> &nbsp;
                <mat-form-field [floatLabel]="'never'" class="compl_field" style="height: 30px; width: 500px;">
                  <input matInput name="search"
                    placeholder="Click here to select the complaint" class="comp_name" [matAutocomplete]="auto"
                    formControlName="COMPLAINT_DESCRIPTION">
                  <mat-autocomplete #auto="matAutocomplete" class="comp-values"
                    (optionSelected)="complaintSelected($event)">
                    <mat-option *ngFor="let complaint of complaints" [value]=complaint.CASE_ID matTooltip="{{complaint.CASE_ID}} - {{complaint.COMPLAINT_DESCRIPTION}}">
                      {{complaint.CASE_ID}} - {{complaint.COMPLAINT_DESCRIPTION}}
                    </mat-option>
                  </mat-autocomplete>
                </mat-form-field>
              </div>
              <div fxLayout="row" *ngIf="!createTask">
                <span class="case-values"> Complaint -</span> &nbsp;<span class="case-desc">
                  <mat-form-field class="compl_field" style="height: 30px; width: 500px;">
                    <input matInput [readonly]="true" formControlName="CASE_ID">
                  </mat-form-field>
                </span>
              </div>
              <div fxLayout="row" *ngIf="createTask && data.name == 'manage-new'">
                <span class="case-values"> Complaint -</span> &nbsp;<span class="case-desc">
                  <mat-form-field class="compl_field" style="height: 30px; width: 500px;">
                    <input matInput [readonly]="true" formControlName="CASE_ID">
                  </mat-form-field>
                </span>
              </div>
            </span>
          </div>
        </div>
        <span class="example-spacer" style="flex: 1 1 auto;"></span>
        <div class="toolbar-btns" fxLayoutAlign="end">
          <div fxLayoutGap="14px" fxLayout="row">
            <div class="save-div" *ngIf="createTask">
              <button mat-flat-button class="save-btn" (click)="saveTask(addform.value)" style="margin-top: 20px;"
                [ngClass]="{'diasble-btn': addform.invalid}"> 
                <span class="bolder"> Save task </span>
              </button>
            </div>
            <div class="save-div" *ngIf="!createTask">
              <button mat-flat-button class="save-btn" (click)="updateTask(addform.value)" style="margin-top: 20px;"
                [ngClass]="{'diasble-btn': !addform.dirty}"> 
                <span class="bolder"> Update task </span>
              </button>
            </div>
            <div class="delete-div" *ngIf="!createTask && delete_task">
              <button mat-icon-button [matMenuTriggerFor]="menu" class="delete-btn" style="margin-top: 19px;">
                <mat-icon style="margin-bottom: 3px;">more_horiz</mat-icon>
              </button>
              <mat-menu #menu="matMenu" xPosition="before" class="delete-menu">
                <div class="delete-option-container">
                  <button mat-menu-item class="option-btn" (click)="deleteTask()">
                    <img src="../../../assets/images/Trash-icon.svg" style="margin-right: 15px"><span
                      class="option-text">Delete task</span>
                  </button>
                </div>
              </mat-menu>
            </div>
            <div class="close-div">
                <button mat-icon-button [disabled]="isUploadProgress" class="close-btn" mat-dialog-close>
                  <mat-icon style="margin-bottom: 3px;">close</mat-icon>
                </button>
            </div>
          </div>
        </div>
      </div>
    </mat-toolbar>

    <mat-divider></mat-divider>

    <div [ngClass]="{'dialog-contents': !createTask}">
      <div fxLayout="column" style="margin-left: 15px;" *ngIf="!createTask">
        <div class="content-head">
          Description
        </div>
        <div class="content-container" style="margin-top: 5px;">
          <mat-form-field class="content-field" appearance="outline" style="width: 865px; background: #FFFFFF;">
            <textarea matInput formControlName="TASK_DESCRIPTION" style="font-size: 14px; height: 50px;" autocomplete="off"></textarea>
            <mat-error class="error-msg" *ngIf="addform.controls['TASK_DESCRIPTION'].errors?.required">
              Description is required.
              </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div fxLayout="column" style="margin-left: 15px;" *ngIf="createTask">
        <div class="content-head">
          Description
        </div>
        <div class="content-container" style="margin-top: 5px;">
          <mat-form-field class="content-field" appearance="outline" style="width: 865px; background: #FFFFFF;">
            <textarea matInput formControlName="TASK_DESCRIPTION" placeholder="Write a description to this task..."
              style="font-size: 14px; height: 50px;" autocomplete="off"></textarea>
              <mat-error class="error-msg" *ngIf="addform.controls['TASK_DESCRIPTION'].errors?.required">
                Description is required.
                </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="format-container" fxLayout="row" fxLayoutGap="40px">
        <div class="format-container" fxLayout="column" fxLayoutGap="7px" style="padding-left: 15px;">
          <div class="content-head1">
            <img src="../assets/images/sliders.svg">&nbsp;&nbsp;Status
          </div>
          <button mat-flat-button class="select">
            <mat-select formControlName="TASK_STATUS_ID"
              style=" position:relative; margin-top: -10px; margin-left: 2px;"
              (selectionChange)="changeValueStatus($event)">
              <mat-select-trigger
                [ngClass]="{'status_color1': status_var == 'In Progress', 'status_color2': status_var == 'Done', 'status_color3': status_var == 'New', 'status_color4': status_var == 'Hold'}">
                <span
                  [ngClass]="{'status_color1': status_var == 'In Progress', 'status_color2': status_var == 'Done', 'status_color3': status_var == 'New', 'status_color4': status_var == 'Hold'}">{{status_var}}</span>
              </mat-select-trigger>
              <mat-option *ngFor="let status of statusList" [value]="status.ID"
                [ngClass]="{'status_color1': status.ID == 2, 'status_color2': status.ID == 4, 'status_color3': status.ID == 1, 'status_color4': status.ID == 3}">
                <span>{{status.TASK_STATUS_NAME}}</span>
              </mat-option>
            </mat-select>
          </button>
        </div>

        <div class="format-container" fxLayout="column" fxLayoutGap="7px">
          <div class="content-head1">
            <img src="../assets/images/users.svg">&nbsp;&nbsp;Assignee
          </div>
          <button mat-flat-button class="assignnn">
            <span matPrefix>
              <img src="../assets/images/userassignee.svg"
                style="background-color:#92A2B1;width: 15px; height: 15px; border-radius: 9px;position: relative; top: -8px;margin: 2px -12px;">
            </span>
            <input matInput formControlName="ASSIGNED_TO" [matAutocomplete]="autoAssignee" style="margin-top: -13px; padding-left: 20px; text-align: start;" placeholder="Assignee">
            <mat-autocomplete #autoAssignee="matAutocomplete" (optionSelected)="onSelectionChange($event)">
              <mat-option *ngFor="let assignee of assigneeList" [value]="assignee.ASSIGNEE_USER_NAME">
                {{assignee.ASSIGNEE_USER_NAME}}
              </mat-option>
            </mat-autocomplete>
          </button>
        </div>

        <div class="format-container" fxLayout="column" fxLayoutGap="7px">
          <div class="content-head1">
            <img src="../assets/images/manage-icon.svg" style="width: 15px;">&nbsp;&nbsp;Priority
          </div>
          <button mat-button class="select-priority">
            <mat-select formControlName="PRIORITY_ID" (selectionChange)="changeValuePriority($event)"
              style="  position: relative; margin-top: -10px;">
              <mat-select-trigger
                [ngClass]="{'icon_color1': priority_var == 'High', 'icon_color2': priority_var == 'Medium', 'icon_color3': priority_var == 'Low', 'icon_color4': priority_var == 'Urgent'}">
                <mat-icon
                  [ngClass]="{'icon_color1': priority_var == 'High', 'icon_color2': priority_var == 'Medium', 'icon_color3': priority_var == 'Low', 'icon_color4': priority_var == 'Urgent'}"
                  style="font-size: 11px; margin-top: 14px;">fiber_manual_record</mat-icon>
                <span
                  [ngClass]="{'icon_color1': priority_var == 'High', 'icon_color2': priority_var == 'Medium', 'icon_color3': priority_var == 'Low', 'icon_color4': priority_var == 'Urgent'}"
                  style="position: relative; top: -2px;">{{priority_var}}</span>
              </mat-select-trigger>
              <mat-option *ngFor="let prior of priorityList" [value]="prior.ID"
                [ngClass]="{'icon_color1': prior.ID == 3, 'icon_color2': prior.ID == 2, 'icon_color3': prior.ID == 1, 'icon_color4': prior.ID == 4}"
                style="font-size:11px;padding-left: 10%">
                <mat-icon style="font-size: 11px;padding-top: 5%">fiber_manual_record</mat-icon>
                <span style="margin-left: -15%;">{{prior.PRIORITY_NAME}}</span>
              </mat-option>
            </mat-select>
          </button>
        </div>

        <div class="format-container" fxLayout="column" fxLayoutGap="7px">
          <div class="content-head1">
            <img src="../assets/images/calendar.svg" style="width: 15px;">&nbsp;&nbsp;Due date
          </div>
          <button mat-flat-button class="date-button">
            <input matInput [matDatepicker]="picker" class="date" formControlName="DUE_DATE"
              style="position: relative; top: -7px; left: -32px;" [min]="tomorrow" [readonly]="true">
            <!-- <mat-datepicker-toggle matSuffix [for]="picker" style="position: relative; top: -3px;left: 20px;">
            </mat-datepicker-toggle>  -->
            <span matSuffix><img src="../../assets/images/calendar.svg"
                style="position: relative; top: -9px; left:20px; width: 15px;" (click)="picker.open()"></span>
            <mat-datepicker #picker></mat-datepicker>
          </button>
        </div>
      </div>

      <div class="comp-tab-container" style="margin-top: 3%;" *ngIf="!createTask">
        <mat-tab-group animationDuration="0ms" class="detail-subtab" #tabGroup
          (selectedTabChange)="onTabChanged($event)">
          <mat-tab #tab label="Details">
            <div fxLayout="column" fxLayoutGap="4px" class="comments-tab">
              <div class="status-tab-container" fxLayout="row" fxLayoutGap="20px" *ngIf="!commentLoading">
                <div fxFlex="40px" class="Area-symbol"
                  style="display: flex; align-items: flex-end; justify-content: center;">
                  <p class="Area-icon">{{profile}}</p>
                </div>
                <div class="content-container">
                  <mat-form-field class="content-field" appearance="outline" style="width: 817px; background: #FFFFFF;">
                    <textarea matInput formControlName="comment_box" placeholder="Add a comment..."
                      style="font-size: 14px;color: #000000;" autocomplete="off"></textarea>
                  </mat-form-field>
                </div>
                <div><img src="../assets/images/send-icon.svg" class="post-btn"
                    (click)="sendMessage(addform.value['comment_box'])"></div>
              </div>

              <div *ngIf="!commentLoading">
                <div fxLayout="column" fxLayoutGap="15px">
                  <div *ngFor="let item of taskComments">
                    <div fxLayout="row" fxLayoutGap="25px" style="margin-left: 3px;">
                      <div fxFlex="4%" class="Area-symbol" [matTooltip]="item.CREATED_BY_USER_NAME"
                        matTooltipPosition="below"
                        style="display: flex; align-items: flex-end; justify-content: center; cursor: pointer;">
                        <p class="Area-icon">{{item.D_P}}</p>
                      </div>
                      <div class="content-container" fxFlex="90%">
                        <div>
                          <span
                            style="font-style: normal;font-weight: bold;font-size: 12px; color: #42526E;">{{item.CREATED_BY_USER_NAME}}
                            .
                          </span>&nbsp;
                          <span style="font-style: normal;font-weight: normal;font-size: 12px;color: #000000;">
                            {{item.UPDATED_DATE}}
                          </span>
                        </div>
                        <div class="commentfield" [ngClass]="{'comment-edit': commentID == item.ID}">
                          <textarea type="text" #cmtid formControlName="comment_text" [readonly]="commentID != item.ID"
                            v-model="text" value="{{item.COMMENTS}}"></textarea>
                          <span *ngIf="commentID == item.ID">
                            <mat-icon class="save-comment" (click)="updateCmt(item.ID, addform.value['comment_text'])">
                              done
                            </mat-icon>
                            <mat-icon class="cancel-comment" (click)="cancel()">close</mat-icon>
                          </span>
                        </div>
                      </div>
                    </div>
                    <div fxLayout="row" style="padding-left: 6%;" *ngIf="item.CREATED_BY_USER_ID == user_id">
                      <button mat-flat-button class="comment-btn" (click)="editCmt(item.ID)"> Edit </button>
                      <button mat-flat-button class="comment-btn" (click)="deleteCmt(item.ID)"> Delete </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <mat-card *ngIf="commentLoading"
              style="height:300px; display: flex; justify-content: center; align-items: center; background: rgb(238, 237, 237)">
              <mat-progress-spinner color="primary" mode="indeterminate" diameter="70">
              </mat-progress-spinner>
            </mat-card>
          </mat-tab>
          <mat-tab label="Timeline" #tab>
            <div class="time-container" *ngIf="!timelineLoading">
              <div *ngIf="!timeLineExist" style="margin-left: 25px;">
                No timeline created ....
              </div>
              <div class="timeline">
                <ul>
                  <li *ngFor="let item of taskTimeline">
                    <div mat-line>
                      <p *ngFor="let val of item.updates">
                        <span class="complaintreceived">
                          <span class="time-enents">{{val.label}} &nbsp;</span>
                          <span
                            style="font-style: normal;font-weight: normal;font-size: 14px;color: #000000;">{{val.value}}
                          </span>
                        </span>
                      </p>
                    </div>
                    <div>
                      <p style="font-style: normal;font-weight: normal;font-size: 12px;color: #555454;">{{item.date}}
                      </p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
            <mat-card *ngIf="timelineLoading"
              style="height:300px; display: flex; justify-content: center; align-items: center; background: rgb(238, 237, 237)">
              <mat-progress-spinner color="primary" mode="indeterminate" diameter="70">
              </mat-progress-spinner>
            </mat-card>
          </mat-tab>
          <mat-tab label="Documents" #tab>
            <div fxLayoutAlign="start" fxLayout="column" fxLayoutGap="20px" class="documenttab" *ngIf="!documentLoading">
              <button mat-button class="add-btn"  [class.spinner]="isUploadProgress" [disabled]="isUploadProgress" style="width: 150px;" (click)="fileInput.click()">
                <span class="bolder">
                  <img src="../assets/images/plus.svg" style="margin: 2px -5px; position: relative;top: -2px;">&nbsp;
                  &nbsp;Add Document
                </span>
              </button>
              <input style="display: none" #attachments type="file" (change)="onFileChange($event)" #fileInput
              accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt,.jpeg,.png,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv" multiple="true">
              <div  fxLayoutGap="8px" *ngIf="!documentLoading">
                <div *ngFor="let attachment of fileName; let index = index" fxLayout="column wrap"  fxLayoutGap="5px"
                  class="file-container doc-wrapper">
                  <div fxLayout="row wrap" fxLayoutGap="8px" >
                    <div fxLayout="row wrap" style="height: 42px; width: 40px; background: #3A3A3A;border-radius: 4px;position: relative;">
                      <img src="../assets/images/doc_video.svg" style="margin: 15px 14px;">
                    </div>
                    <div  fxLayout="row warp" style="vertical-align: middle; align-items: center;">
                      <div class="document-name">{{attachment.FILENAME}}</div>
                      <span style="position: relative;top:1px;font-size:11px">{{attachment.DATE| date: 'dd/MM/yyyy h:mm a'}}</span>
                      <div style="position: relative; margin-right: 20px; left: 10px;">
                        <button mat-button *ngIf="attachment.UPLOADED_BY == user_id" matSuffix mat-icon-button aria-label="Clear"
                          (click)="removeSelectedFile(attachment.ID, attachment.ATTACHMENT_SOURCE, index)">
                          <img src="../assets/images/Trash-icon.svg" style="border: 1px;">
                        </button>
                      </div>
                      <div style="margin-left: -15px;">
                        <button mat-icon-button [matMenuTriggerFor]="admin">
                          <mat-icon>more_vert</mat-icon>
                        </button>
                      </div>
                      <mat-menu #admin="matMenu" class="action-buttons">
                        <div class="admin-option-container">
                          <button mat-menu-item class="option-btn" (click)="preview(attachment.ATTACHMENT_SOURCE)">
                            <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                            <span class="option-text">Preview</span>
                          </button>
                          <mat-divider class="option-divider">
                          </mat-divider>
                          <button mat-menu-item class="option-btn" (click)="download(attachment.FILENAME, attachment.ATTACHMENT_SOURCE)">
                            <span class="option-text"><img src="../../../assets/images/Download (1).png"></span>
                            <span class="option-text">Download</span>
                          </button>
                        </div>
                      </mat-menu>
                    </div>
                  </div>
                  <div class="doc-progress-bar" *ngIf="filesProgress[index]?.progress > 0">
                    <mat-progress-bar [color]='primary' [mode]="determinate" [value]="filesProgress[index]?.progress">
                    </mat-progress-bar>
                  </div>
                </div>
              </div>
            </div>
            <mat-card *ngIf="documentLoading"
              style="height:300px; display: flex; justify-content: center; align-items: center; background: rgb(238, 237, 237)">
              <mat-progress-spinner color="primary" mode="indeterminate" diameter="70">
              </mat-progress-spinner>
            </mat-card>
          </mat-tab>
        </mat-tab-group>
      </div>
    </div>
  </form>
</div>
