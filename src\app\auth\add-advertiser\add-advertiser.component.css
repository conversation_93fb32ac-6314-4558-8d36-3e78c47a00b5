
::ng-deep .mat-form-field-flex > .mat-form-field-infix {padding: 5px 0px 0.4em 0px !important }
:host:ng-deep .mat-form-field-appearance-outline .mat-form-field-label { margin-top:-15px; }
:host:ng-deep label.ng-star-inserted { transform: translateY(-0.59375em) scale(.75) !important; }
:host:ng-deep .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    transform: translateY(-1.1em) scale(.75);
    width: 133.33333%;
}
:host ::ng-deep .mat-input-element {
    position: relative;
    bottom: 5px;
}
:host ::ng-deep .mat-select-value-text {
    position: relative;
    bottom: 2px;
}
.toolbar {
    height: 84px;
    padding-left: 15px;
    background-color: #F8F9F9;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #2F3941;
}
.contents::-webkit-scrollbar {
    display: none;
}
.contents {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.contents {
    margin-left: 5px;
    height: 405px;
    width: 580px;
    overflow-y: scroll;
    overflow-x: hidden;
}
.form {
    width: 580px;
    overflow-y: auto;
    overflow-x: hidden;
}
.search-btn {
    color: solid rgba(47, 57, 65, 0.6);
    background: #F3F3F3;
    border: 1px solid rgba(47, 57, 65, 0.6);
}
.names {
    padding-top: 10px;
    color: #2F3941;
}
.control-container {
    margin-top: 0px;
    flex-direction: row;
    display: flex;
    height: 54px;
    width: 540px;
}
.input-field {
    width:215px;
    margin-left: 7px;
}
.input-field_phno {
    width: 540px;
}
.namess1 {
    flex-direction: row;
    display: flex;
    grid-gap: 250px;
    color: #2F3941;
}
.input-field_dept {
    width:270px !important;
    height: 20px !important;
}
.copy {
    background: #CFD7DF;
    border-radius: 3px;
    width: 35px;
    height: 34px;
    margin-bottom: -9px;
    position: relative;
    background-image: url('../../../assets/images/copy.svg');
    background-repeat: no-repeat;
    background-position: center;
    bottom: 12px;
    left: 11px;
}
.salutation-error {
    width: 103px;
    margin-left: -8px;
}
.generate-btn {
    color: #5A6F84;
    background-color: #CFD7DF;
    border: 5px ;
    border-radius: 30px;
    margin-top: 20px;
}
.lastdivdr {
    padding-top: 1%;
    width: 560px;
}
.cancel-btn {
    color: #284255;
    background-color: white;
    border-radius: 14px;
    margin-right: 10px;
}
.register-btn {
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: white;
    background-color: #0088CB;
    border-radius: 12px;
}
.toolbar-btns2 {
    float: right;
    padding-top: 2%;
}
.mat-dialog-actions {
    margin-right: 15px;
    margin-bottom: 3px;
}
.angularformcss {
  width: 539px;
}
.error-msg {
    position: relative;
    margin-left: -10px;
}
.pswd-error {
   color: #ED2F45;
    position: relative;
    margin-left: -5px;
    line-height: 1.5;
}
