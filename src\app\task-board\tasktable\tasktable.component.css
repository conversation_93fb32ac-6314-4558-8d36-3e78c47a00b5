:host::ng-deep.mat-paginator-page-size-label {
  display: none;
}

:host ::ng-deep .mat-paginator-range-label {
  display: none;
}

:host::ng-deep.mat-paginator-page-size-value {
  display: none;
}

:host::ng-deep .mat-paginator-navigation-previous {
  color: #4DA1FF;
  margin-right: 43px;
}

:host::ng-deep .mat-paginator-navigation-next {
  color: #4DA1FF;
}

:host::ng-deep .mat-paginator-range-actions {
  margin-right: 40px;
}

mat-paginator {
  width: 94%;
  margin-top: 17px;
  margin-left: 5%;
  border-radius: 20px;
  background: #FFFFFF;
  box-sizing: border-box;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
}

.label {
  position: relative;
  bottom: 38px;
  color: rgb(155, 155, 155);
  left: 47%;
}

.common-toolbar {
  border-bottom: 1px solid #D8DCDE;
  border-top: 0;
  border-left: none;
  border-right: none;
}

.heading-container {
  width: 60%;
}

.options-container {
  width: 40%;
}

.toolbar {
  background-color: white;
  height: 9%;
  width: 100%;
}

.table-left-border {
  border-left: 1px solid #D8DCDE;
}

.mat-cell:last-child {
  border-bottom: 1px solid #D8DCDE;
}

.mat-column-case {
  width: 22%;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  color: #010101;
}

.mat-column-task {
  width: 20%;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  color: #010101;
  padding-right: 10px;
}

.mat-column-priority {
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  color: #010101
}

.mat-column-due_date {
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  color: #010101
}

.toolbar2 {
  background-color: rgb(245, 245, 245);
  width: 100%;
  position: relative;
  height: 55px;
}

.toolbar2-sub-container {
  width: 130%;
  padding: 0% 1%;
  height: 55px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.addtask-container {
  width: 25%;
  height: 55px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.filter {
  position: relative;
  padding-left: 28px;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  color: #010101;
}

:host::ng-deep .mat-select-placeholder {
  color: #010101;
}

:host::ng-deep input::placeholder {
  opacity: 1 !important;
  color: #010101 !important;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  padding-left: 7px;
}

.material-icons-outlined {
  font-size: 17px;
  margin-left: -8%;
  margin-right: 3%;
}

.date-button {
  height: 35px;
  border: 1px solid #D8DCDE;
  border-radius: 4px;
}

.date {
  width: 70px;
  height: 47px;
  color: #010101;
  border: none;
  margin-top: -8px;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
}

.seicon {
  top: 12px;
  position: relative;
  margin-right: 10px;
  margin-bottom: 22px;
}

.header-right input {
  width: 150px;
  border-radius: 5px;
  border: none;
  background-color: rgb(245, 245, 245);
}

.search-input {
  width: 150px;
  position: static;
  /* margin-top: 5px; */
  margin-left: -5px;
  padding-left: 5px;
  /* margin-bottom: 6px; */
  font-style: normal;
  font-weight: normal;
  font-size: 13px;
  color: #010101;
}

.other {
  background-color: #FFFFFF;
  position: relative;
}

.col {
  background-color: rgba(255, 198, 198, 0.2);
  position: relative;
}

.coll {
  border-left: 7px solid #FF2222;
}

.text-message {
  display: flex;
  align-content: center;
  justify-content: center;
  font-size: large;
  background-color: rgb(245, 245, 245);
  height: 82vh;
}

.mat-row .mat-cell {
  border-bottom: 1px solid transparent;
  border-top: 1px solid transparent;
  cursor: pointer;
  height: 70px !important;
}

.circle {
  font-size: 11px;
}

.prior {
  margin-left: -8%;
}

.assign {
  border: 1px solid #D8DCDE;
  box-sizing: border-box;
  border-radius: 2px;
  width: 158px;
  height: 30px;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  color: #2F3941;
}

.select {
  width: 102px;
  height: 30px;
  border: 1px;
  font-size: 12px;
  border-radius: 2px;
  padding-left: 5px;
  font-style: normal;
  font-weight: 500;
}

.rej {
  color: red;
}

.stat {
  margin-top: -12%;
  margin-right: -13%;
  color: none;
  vertical-align: middle;
  align-items: center;
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
}

.complaint-dropdown {
  border: 1px solid #D8DCDE;
  width: 283px;
  height: 35px;
  font-size: 13px;
  color: black;
  border-radius: 4px;
  margin-bottom: 2px;
}

.assignee {
  border: 1px solid #D8DCDE;
  width: 135px;
  height: 35px;
  font-size: 12px;
  color: #010101;
  border-radius: 4px;
  font-style: normal;
  font-weight: normal;
}

.vertical-line {
  width: 1px;
  background-color: rgb(212, 207, 207);
}

.task-table::-webkit-scrollbar {
  display: none;
}

.task-table {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.task-table {
  overflow-y: scroll;
  overflow-x: hidden;
  width: 100%;
  height: 65vh;
  position: relative;
  border-right: 0px solid rgb(245, 245, 245);
  border-bottom: 0px solid rgb(245, 245, 245);
  border-top: 0px;
}

table {
  border-bottom: 1px solid #D8DCDE;
  box-sizing: border-box;
  border-radius: 4px;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
  margin-left: 57px;
  border-collapse: initial;
}

.card-container {
  max-height: 82.2vh;
  width: 100%;
}

.card-container::-webkit-scrollbar {
  display: none;
}

.card-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.card {
  background-color: rgb(245, 245, 245);
  box-shadow: none;
  border-width: 0px;
  height: fit-content;
  max-height: 68.2vh;
  position: relative;
  align-self: justify;
  width: 100%;
  overflow-y: scroll;
  overflow-x: hidden;
}

.card::-webkit-scrollbar {
  display: none;
}

.card {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

td,
th {
  width: 1%;
  height: 40px !important;
}

.mat-header-cell {
  background-color: rgb(245, 245, 245);
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  color: #010101;
  height: 35px !important;
}

.add-button {
  border-radius: 12px;
  background-color: #CFD7DF;
  color: #5A6F84;
  width: auto;
  height: 37px;
  text-align: center;
  flex-direction: row;
  box-sizing: border-box;
  font-weight: 600;
  font-size: 14px;
  line-height: 19px;
  justify-content: center;
  align-items: center;
  padding: 0px 16px 0px 9px;
}

::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
  background-color: #aae7c8;
}

::ng-deep.mat-checkbox-indeterminate.mat-accent .mat-checkbox-background {
  background-color: #aae7c8;
}

input[type=checkbox]:checked+span {
  text-decoration: line-through
}

.ellipsis {
  width: 320px;
  height: 40px !important;
}

.ellipsis1 {
  overflow: hidden;
  display: inline-block;
  display: -webkit-inline-box;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  vertical-align: middle;
  position: relative;
  width: 200px;
  max-width: 95%;
  overflow-wrap: break-word;
}

.clear-button {
  background-color: rgb(245, 245, 245);
  border: none;
  margin-top: 5px;
}

/* :host::ng-deep .mat-form-field-underline {
    display: none;
} */
:host ::ng-deep .mat-form-field-flex>.mat-form-field-infix {
  bottom: 0px;
}

:host::ng-deep .mat-form-field {
  font-size: 12px;
  font-weight: normal;
  font-style: normal;
  color: #010101;
}

.compl_field {
  margin-top: -6px;
  margin-left: 5px;
}

.comp_name {
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  color: #010101;
}

.comp-values {
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  color: #010101;
}

.autocomplete-option {
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  color: #010101;
  height: 30px;
}

.search_field {
  width: 100px;
}

@media only screen and (max-width: 1250px) {
  .common-toolbar-mobile {
    border-bottom: 1px solid #D8DCDE;
    border-top: 0;
    border-left: none;
    border-right: none;
  }

  .dashboard-admin-heading {
    padding-left: 0;
    padding-top: 0;
    font-weight: 600;
    font-size: 18px;
    line-height: 23.94px;
    color: #ED2F45;
    padding-left: 20px;
    padding-top: 6px;
  }

  .dashboard-container {
    width: 100%;
    height: 90%;
    background: #E5E5E5;
  }

  .dasboard-subheading {
    padding-right: 20px;
    padding-top: 0px;
    font-weight: 400;
    font-size: 14px;
    line-height: 18.62px;
    padding-left: 20px;
    padding-top: 3px;
  }
}