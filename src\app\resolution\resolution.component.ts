import { Component, HostListener, Inject, OnInit, Renderer2, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormGroupDirective, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ComplaintsService } from '../services/complaints.service';
import { NotificationService } from '../services/notification.service';
import { colorObj } from '../shared/color-object';

@Component({
  selector: 'app-resolution',
  templateUrl: './resolution.component.html',
  styleUrls: ['./resolution.component.scss']
})

export class ResolutionComponent implements OnInit {

  @ViewChild(FormGroupDirective) formGroupDirective: FormGroupDirective;
  resolutionForm: FormGroup;
  id: any;
  records: any[];
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    public fb: FormBuilder,
    public dialog: MatDialog,
    private renderer: Renderer2,
    private cs: ComplaintsService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<ResolutionComponent>,
    private notify: NotificationService) {
    this.resolutionForm = fb.group({
      subject: ['', Validators.required],
      resolution: ['', Validators.required]
    })
  }

  ngAfterViewInit() {
    setTimeout(() => {
      var elem = this.renderer.selectRootElement('#subject');
      elem.focus();
    }, 1000);
  }

  ngOnInit(): void {
    this.getRecords();
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  getRecords() {
    this.cs.getResolutionsById(this.data).subscribe(res => {
      this.records = res.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    })
  }

  onSubmit(data) {
    this.id = this.data;
    this.cs.addResolution(this.id, data).subscribe(val => {
      this.getRecords();
      this.formGroupDirective.resetForm();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    })
  }

  cancel() {
    this.formGroupDirective.resetForm();
  }

}