import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { AuthGuardService } from "../services/auth-guard.service";
import { CasesComponent } from "./cases.component";
import { ComplaintConversationsComponent } from "./complaint-conversations/complaint-conversations.component";
import { ManageCasesComponent } from "./manage-cases/manage-cases.component";
import { ViewUploadDocumentComponent } from './view-upload-document/view-upload-document.component';
import { MobileHeaderComponent } from '../mobile-header/mobile-header.component';

const routes: Routes = [
  {
    path: '',
    component: CasesComponent,
    children: [
      {
        path: 'manage-cases',
        canActivate:[AuthGuardService],
        data: {
          role: [1,2,3,6]
        },
        component: ManageCasesComponent
      },
      {
        path: 'conversations',
        canActivate:[AuthGuardService],
        data: {
          role: [1,2,3,6]
        },
        component: ComplaintConversationsComponent
      },
      {
        path: 'upload-document',
        canActivate:[AuthGuardService],
        data: {
          role: [1,2,3,6]
        },
        component: ViewUploadDocumentComponent
      },
      {
        path: 'mobile-header', component: MobileHeaderComponent
      }
    ]
  }
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CaseRoutingModule { }

