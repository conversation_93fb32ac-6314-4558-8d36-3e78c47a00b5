import { Component, OnInit, <PERSON><PERSON>hild, HostListener, Renderer2 } from '@angular/core';
import { trigger, transition, style, animate, state } from '@angular/animations';
import { FormBuilder, FormGroup, FormControl, Validators } from '@angular/forms';
import { ComplaintsService } from '../../services/complaints.service';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { NotificationService } from '../../services/notification.service';
import { ChatbotComponent } from '../chatbot/chatbot.component';
import { environment } from 'src/environments/environment';
import { colorObj } from 'src/app/shared/color-object';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-login-register',
  templateUrl: './login-register.component.html',
  styleUrls: ['./login-register.component.css'],
  animations: [
    trigger('showHide', [
      state('show', style({
        opacity: 1,
        zIndex: 1,
      })),
      state('hide', style({
        opacity: 0,
        zIndex: 0,
      })),
      transition('show => hide', [
        animate('0.4s')
      ]),
      transition('hide => show', [
        animate('0.4s')
      ]),
    ]),
  ],
})
export class LoginRegisterComponent implements OnInit {
  @ViewChild(ChatbotComponent) chatbot;
  resetForm: FormGroup;
  loginForm: FormGroup;
  signupForm: FormGroup;
  forgotForm: FormGroup;
  trackComplaintForm: FormGroup;
  otpForm: FormGroup;
  forgotOtpForm: FormGroup;
  resendOtpUsername;
  trackOtpForm: FormGroup;
  selectedColor1;
  expiryTime = 120;
  showTime: boolean = true;
  passwordCheckbox: boolean = false;
  disabled: boolean = false;
  isRegisterDisabled: boolean = false;
  selectedRole = "";
  selectedGovBody = "";
  inputType;
  selectedTitle = "";
  forgotPasswordCode;
  forgotUserName;
  companies;
  signupOtpForm: FormGroup;
  companyList: any[];
  companyId: number;
  companyNameControl = new FormControl();
  error: string = "";
  cIDByOtp: string = "";
  cIDSignup: string = "";
  emailPattern = environment.emailPatterm;
  mobilenopattern = /^[0-9]{10}$/;
  pinPattern = /^[0-9]{6}$/;
  textPattern = /^[a-zA-Z\s]*$/;
  namePattern = /^[a-zA-Z]+$/;
  companyPattern = /^[0-9A-Za-z'&-.()]+(?:\s[0-9A-Za-z'&-.()]+)*$/;
  passwordPattern = /^(?=.*?[A-Z])(?=.*?[a-z])(?!.* )(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,15}$/;
  signUpUserName: string = '';
  trackComplaintSessionToken: string = '';
  roles: any[];
  govDept: any[];
  selectedCompany: any;
  selectedColor2: string;
  selectedColor3: string;
  titles: any[];
  confirmationMsg: any = {};
  interval;
  isExpiryCompleted: boolean = true;
  timeLeft: string;
  govBodyName: any;
  onTrack: boolean;

  constructor(private fb: FormBuilder,
    private cs: ComplaintsService,
    private router: Router,
    private authService: AuthService,
    private renderer: Renderer2,
    private notify: NotificationService,
    public dialog: MatDialog,
  ) {
    this.forgotForm = this.fb.group({
      email: ['', [Validators.required, Validators.pattern(this.emailPattern)]]
    })
    this.resetForm = this.fb.group({
      new_password: new FormControl('', [Validators.required, Validators.pattern(this.passwordPattern)]),
      confirm_password: new FormControl('', [Validators.required, Validators.pattern(this.passwordPattern)])
    });
    this.loginForm = this.fb.group({
      userName: ['', [Validators.required, Validators.pattern(this.emailPattern)]],
      password: ['', [Validators.required]],
    });
    this.signupForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(25), Validators.pattern(this.namePattern)]],
      lastName: ['', [Validators.required, Validators.maxLength(25), Validators.pattern(this.namePattern)]],
      phonenumber: ['', [Validators.required, Validators.pattern(this.mobilenopattern)]],
      email: ['', [Validators.required, Validators.pattern(this.emailPattern)]],
      password: ['', [Validators.required, Validators.pattern(this.passwordPattern)]],
      companyname: ['', [Validators.pattern(this.companyPattern)]],
      professionName: [''],
      companyID: [''],
      salutation: ['', [Validators.required]],
      role: ['', [Validators.required]],
      address: [''],
      pin: ['', [Validators.pattern(this.pinPattern)]],
      govBody: [''],
      govBodyName: [''],
    });
    this.trackComplaintForm = this.fb.group({
      phoneNumber: ['', [Validators.required, Validators.pattern(this.mobilenopattern)]]
    })
    this.forgotOtpForm = this.fb.group({
      val1: ['', Validators.required],
      val2: ['', Validators.required],
      val3: ['', Validators.required],
      val4: ['', Validators.required],
      val5: ['', Validators.required],
      val6: ['', Validators.required]
    })
    this.trackOtpForm = this.fb.group({
      val1: ['', Validators.required],
      val2: ['', Validators.required],
      val3: ['', Validators.required],
      val4: ['', Validators.required],
      val5: ['', Validators.required],
      val6: ['', Validators.required]
    })
    this.signupOtpForm = this.fb.group({
      val1: ['', Validators.required],
      val2: ['', Validators.required],
      val3: ['', Validators.required],
      val4: ['', Validators.required],
      val5: ['', Validators.required],
      val6: ['', Validators.required]
    })
  }
  showLoginContainer: boolean = true;
  showLoginContainer1: boolean = false;
  showRegisterContainer: boolean = false;
  showForgotPasswordContainer: boolean = false;
  showVerificationContainer: boolean = false;
  showForgotPasswordVerificationContainer: boolean = false;
  showTrackComplaintVerification: boolean = false;
  showResetPasswordContainer: boolean = false;
  showTrackComplaint: boolean = false;
  isSubmitDisabled: boolean = false;
  isShowTrack: boolean = true;
  isShowVerification: boolean = true;
  public innerWidth: any;
  isMobileScreen: boolean = false;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobileScreen = true;
      this.showTrackComplaint = true;
    }
    else {
      this.isMobileScreen = false;
      this.showTrackComplaint = false;
    }
  }

  ngOnInit(): void {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      setTimeout(() => {
        var elem = this.renderer.selectRootElement('#mobile_no');
        elem.focus();
      }, 1000);
      this.isMobileScreen = true;
      this.showTrackComplaint = true;
    }
    else {
      setTimeout(() => {
        var elem = this.renderer.selectRootElement('#email');
        elem.focus();
      }, 1000);
      this.isMobileScreen = false;
      this.showTrackComplaint = false;
    }
    localStorage.clear();
    this.getMasterData();
    this.inputType = 'password'
    this.companyNameControl.valueChanges.pipe(
      debounceTime(300),
      // filter((value) => value.length > 2),
      distinctUntilChanged()).subscribe(value => {
        if (value.length > 0) {
          this.getCompanyList(value);
        }
      }, err => {

      })
  }

  getCompanyList(value) {
    this.authService.getCompanies(value).subscribe(res => {
      this.companyList = res.data;
      // if(this.companyList.length == 0){
      //   this.companyNameControl.setValue('');
      // }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  hideShowPassword() {
    if (this.inputType === 'password') {
      this.inputType = 'text';
      this.passwordCheckbox = true;
    } else {
      this.inputType = 'password';
      this.passwordCheckbox = false;
    }
  }

  getMasterData() {
    this.cs.getMasterData().subscribe(res => {
      for (let md_key in res.data) {
        res.data[md_key].shift();
        window.localStorage.setItem(md_key, JSON.stringify(res.data[md_key]));
      }
      this.roles = JSON.parse(window.localStorage.getItem('role')).filter(element => {
        return (element.REGISTRATION_VISIBILITY == 1)
      });
      this.titles = JSON.parse(window.localStorage.getItem('salutation'));
      this.govDept = JSON.parse(window.localStorage.getItem('governmentDepartment'));
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  showHideLoginRegister(tab: String) {
    if (tab === 'login') {
      this.loginForm.reset();
      this.showLoginContainer = true;
      setTimeout(() => {
        var elem = this.renderer.selectRootElement('#email');
        elem.focus();
      }, 1000);
      this.showRegisterContainer = false;
      this.showForgotPasswordContainer = false;
      this.showVerificationContainer = false;
      this.showTrackComplaint = false;
      this.showForgotPasswordVerificationContainer = false;
      this.showResetPasswordContainer = false;
      this.showLoginContainer1 = false;
      this.chatbot.hiddenIntroMsg = false;
    }
    else if (tab === 'register') {
      this.showLoginContainer = false;
      this.showRegisterContainer = true;
      this.showTrackComplaint = false;
      this.showForgotPasswordContainer = false;
      this.showVerificationContainer = false;
      this.showResetPasswordContainer = false;
      this.showForgotPasswordVerificationContainer = false;
      this.showLoginContainer1 = false;
      this.showTrackComplaintVerification = false;
      this.chatbot.hiddenIntroMsg = true;
    }
    else if (tab === 'forgot') {
      this.showLoginContainer = false;
      this.showRegisterContainer = false;
      this.showTrackComplaint = false;
      this.showForgotPasswordVerificationContainer = false;
      this.showForgotPasswordContainer = true;
      setTimeout(() => {
        var elem = this.renderer.selectRootElement('#forgotEmail');
        elem.focus();
      }, 1000);
      this.showVerificationContainer = false;
      this.showResetPasswordContainer = false;
      this.showLoginContainer1 = false;
      this.showTrackComplaintVerification = false;
    }
    else if (tab === 'track') {
      this.showLoginContainer = false;
      this.showRegisterContainer = false;
      this.showForgotPasswordVerificationContainer = false;
      this.showForgotPasswordContainer = false;
      this.trackOtpForm.reset();
      this.trackComplaintForm.reset();
      this.showTrackComplaint = true;
      setTimeout(() => {
        var elem = this.renderer.selectRootElement('#trackNumber');
        elem.focus();
      }, 1000);
      this.showVerificationContainer = false;
      this.showResetPasswordContainer = false;
      this.showLoginContainer1 = false;
      this.showTrackComplaintVerification = false;
    }
    else if (tab === 'track-verify') {
      this.showLoginContainer = false;
      this.showTrackComplaintVerification = true;
      if (this.isMobileScreen == false) {
        setTimeout(() => {
          var elem = this.renderer.selectRootElement('#trackNumberOtp');
          elem.focus();
        }, 1000);
      } else {
        setTimeout(() => {
          var elem = this.renderer.selectRootElement('#trackNumberOtpMobile');
          elem.focus();
        }, 1000);
      }
      this.showRegisterContainer = false;
      this.showTrackComplaint = false;
      this.showForgotPasswordContainer = false;
      this.showVerificationContainer = false;
      this.showResetPasswordContainer = false;
      this.showForgotPasswordVerificationContainer = false;
      this.showLoginContainer1 = false;
    }
    else if (tab === 'change-number') {
      this.showLoginContainer = false;
      this.showTrackComplaintVerification = false;
      this.showRegisterContainer = false;
      this.showTrackComplaint = false;
      this.showForgotPasswordContainer = true;
      this.showVerificationContainer = false;
      this.showResetPasswordContainer = false;
      this.showForgotPasswordVerificationContainer = false;
      this.showLoginContainer1 = false;
      this.forgotOtpForm.reset();
    }
    else if (tab === 'track-change-number') {
      this.trackOtpForm.reset();
      clearInterval(this.interval);
      this.timeLeft = '';
      this.expiryTime = 120;
      this.showLoginContainer = false;
      this.showTrackComplaintVerification = false;
      this.showRegisterContainer = false;
      this.showTrackComplaint = true;
      setTimeout(() => {
        var elem = this.renderer.selectRootElement('#trackNumber');
        elem.focus();
      }, 1000);
      this.showForgotPasswordContainer = false;
      this.showVerificationContainer = false;
      this.showResetPasswordContainer = false;
      this.showForgotPasswordVerificationContainer = false;
      this.showLoginContainer1 = false;
    }
    else if (tab === 'next') {
      if (this.loginForm.controls['userName'].invalid) {
        this.notify.showNotification(
          "Enter the correct Email ID",
          "top",
          "warning",
          0
        );
      } else {
        this.showLoginContainer = false;
        this.showRegisterContainer = false;
        this.showTrackComplaint = false;
        this.showForgotPasswordContainer = false;
        this.showLoginContainer1 = true;
        setTimeout(() => {
          var elem = this.renderer.selectRootElement('#password');
          elem.focus();
        }, 1000);
        this.showVerificationContainer = false;
        this.showForgotPasswordVerificationContainer = false;
        this.showResetPasswordContainer = false;
        this.showTrackComplaintVerification = false;
      }
    }
    else if (tab === 'reset') {
      if (this.forgotForm.invalid) {
        this.notify.showNotification(
          "Please enter the correct Email id",
          "top",
          "warning",
          0
        );
        return;
      }
      this.forgotPassword(this.forgotForm.value['email']);
    }
    else if (tab === 'registerVerify') {
      if (this.signupForm.value['role'] == '5') {
        if (!this.companyNameControl.value) {
          this.notify.showNotification(
            "Choose the company",
            "top",
            "warning",
            0
          );
          return;
        }

      }
      if (this.signupForm.invalid) {
        if (this.signupForm.value['role'] == '4') {
          if (this.signupForm.value['govBody'] != '10') {
            this.signupForm.get('govBodyName').clearValidators();
          }
          this.companyId = null;
          this.signupForm.get('address').clearValidators();
          this.signupForm.get('pin').clearValidators();
          this.signupForm.get('companyname').clearValidators();
          if (this.signupForm.controls['email'].invalid || this.signupForm.controls['salutation'].invalid || this.signupForm.controls['firstName'].invalid || this.signupForm.controls['lastName'].invalid || this.signupForm.controls['govBody'].invalid || this.signupForm.controls['govBodyName'].invalid || this.signupForm.controls['pin'].invalid) {
            this.notify.showNotification(
              "Please fill all the fields correctly",
              "top",
              "warning",
              0
            );
            return;
          }
          if (this.signupForm.controls['password'].invalid) {
            this.notify.showNotification(
              "Password should contain atleast one uppercase,one lowercase,one special character, one digit and should be of length 8-15",
              "top",
              "warning",
              0
            );
            return;
          }
        }
        else if (this.signupForm.value['role'] == '5' || this.signupForm.value['role'] == '8') {
          if (this.signupForm.value['role'] == '8') {
            this.companyId = null;
          }
          this.signupForm.get('pin').clearValidators();
          this.signupForm.get('govBody').clearValidators();
          this.signupForm.get('govBodyName').clearValidators();
          if (this.signupForm.controls['email'].invalid || this.signupForm.controls['salutation'].invalid || this.signupForm.controls['firstName'].invalid || this.signupForm.controls['lastName'].invalid || this.signupForm.controls['companyname'].invalid || this.signupForm.controls['address'].invalid || this.signupForm.controls['pin'].invalid) {
            this.notify.showNotification(
              "Please fill all the fields correctly",
              "top",
              "warning",
              0
            );
            return;
          }
          if (this.signupForm.controls['password'].invalid) {
            this.notify.showNotification(
              "Password should contain atleast one uppercase,one lowercase,one special character, one digit and should be of length 8-15",
              "top",
              "warning",
              0
            );
            return;
          }
        }
        else if (this.signupForm.value['role'] == '7') {
          this.companyId = null;
          this.signupForm.get('govBody').clearValidators();
          this.signupForm.get('govBodyName').clearValidators();
          this.signupForm.get('address').clearValidators();
          this.signupForm.get('companyname').clearValidators();
          if (this.signupForm.controls['email'].invalid || this.signupForm.controls['salutation'].invalid || this.signupForm.controls['firstName'].invalid || this.signupForm.controls['lastName'].invalid || this.signupForm.controls['pin'].invalid) {
            this.notify.showNotification(
              "Please fill all the fields correctly",
              "top",
              "warning",
              0
            );
            return;
          }
          if (this.signupForm.controls['password'].invalid) {
            this.notify.showNotification(
              "Password should contain atleast one uppercase,one lowercase,one special character, one digit and should be of length 8-15",
              "top",
              "warning",
              0
            );
            return;
          }
        }
        else {
          this.notify.showNotification(
            "Please fill all the fields correctly",
            "top",
            "warning",
            0
          );
          return;
        }
      }
      this.confirmationMsg.title = 'Are you sure you want to move to next step ?';
      const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
        data: { title: this.confirmationMsg.title },
        disableClose: true
      });
      dialogRef.updatePosition({ right: `190px` });
      dialogRef.afterClosed().subscribe(dialogResult => {
        if (dialogResult && dialogResult.state) {
          this.signup(this.signupForm);
        }
      });
    }
  }

  forgotPassword(mail) {
    this.resendOtpUsername = mail;
    this.disabled = !this.disabled;
    this.authService.onForgot(mail).subscribe((res) => {
      this.disabled = !this.disabled
      this.notify.showNotification(
        res.body.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      this.interval = setInterval(() => {
        if (this.expiryTime > 0) {
          this.expiryTime--;
          const minutes: number = Math.floor(this.expiryTime / 60);
          this.timeLeft = ('00' + minutes).slice(-2) + ':' + ('00' + Math.floor(this.expiryTime - minutes * 60)).slice(-2);
        } else {
          clearInterval(this.interval)
          this.showTime = false;
          this.isExpiryCompleted = false;
        }
      }, 1000)
      this.showLoginContainer = false;
      this.showRegisterContainer = false;
      this.showForgotPasswordContainer = false;
      this.showForgotPasswordVerificationContainer = true;
      setTimeout(() => {
        var elem = this.renderer.selectRootElement('#forgotEmailOtp');
        elem.focus();
      }, 1000);
      this.showVerificationContainer = false;
      this.showResetPasswordContainer = false;
    },
      (err) => {
        this.disabled = !this.disabled
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "warning"),
          err.error.status
        )
      }
    )
  }

  trackNext(model) {
    this.signUpUserName = model.phoneNumber;
    // this.disabled = !this.disabled
    this.disabled = !this.disabled
    if (this.trackComplaintForm.valid) {
      this.authService.getOTPByNumber(model.phoneNumber).subscribe(res => {
        this.showTime = true;
        clearInterval(this.interval)
        this.timeLeft = '';
        this.isExpiryCompleted = true;
        this.expiryTime = 120;
        this.disabled = !this.disabled
        if (res.body.data.USER_CONFIRMED == 1) {
          this.interval = setInterval(() => {
            if (this.expiryTime > 0) {
              this.expiryTime--;
              const minutes: number = Math.floor(this.expiryTime / 60);
              this.timeLeft = ('00' + minutes).slice(-2) + ':' + ('00' + Math.floor(this.expiryTime - minutes * 60)).slice(-2);
            } else {
              clearInterval(this.interval)
              this.showTime = false;
              this.isExpiryCompleted = false;
            }
          }, 1000)
          this.notify.showNotification(
            res.body.message,
            "top",
            (!!colorObj[res.status] ? colorObj[res.status] : "success"),
            res.status
          );
          this.showTrackComplaintVerification = true;
          if (this.isMobileScreen == false) {
            setTimeout(() => {
              var elem = this.renderer.selectRootElement('#trackNumberOtp');
              elem.focus();
            }, 1000);
          } else {
            setTimeout(() => {
              var elem = this.renderer.selectRootElement('#trackNumberOtpMobile');
              elem.focus();
            }, 1000);
          }
          this.cIDByOtp = res.body.data.TOKEN_DATA.C_ID;
          this.trackComplaintSessionToken = res.body.data.TOKEN_DATA.session_token;
        } else if (res.body.data.USER_CONFIRMED == 0) {
          this.cIDSignup = res.body.data.C_ID;
          this.notify.showNotification(
            res.body.message,
            "top",
            (!!colorObj[res.status] ? colorObj[res.status] : "success"),
            res.status
          );
          this.interval = setInterval(() => {
            if (this.expiryTime > 0) {
              this.expiryTime--;
              const minutes: number = Math.floor(this.expiryTime / 60);
              this.timeLeft = ('00' + minutes).slice(-2) + ':' + ('00' + Math.floor(this.expiryTime - minutes * 60)).slice(-2);
            } else {
              clearInterval(this.interval)
              this.showTime = false;
              this.isExpiryCompleted = false;
            }
          }, 1000)
          this.signupOtpForm.reset();
          this.showRegisterContainer = false;
          this.showLoginContainer = false;
          this.showLoginContainer1 = false;
          this.showTrackComplaint = false;
          this.showTrackComplaintVerification = false;
          this.showForgotPasswordContainer = false;
          this.showVerificationContainer = true;
          if (this.isMobileScreen == false) {
            setTimeout(() => {
              var elem = this.renderer.selectRootElement('#signupOtp');
              elem.focus();
            }, 1000);
          } else {
            setTimeout(() => {
              var elem = this.renderer.selectRootElement('#signupOtpMobile');
              elem.focus();
            }, 1000);
          }
          this.showResetPasswordContainer = false;
          this.showForgotPasswordVerificationContainer = false;
        }
      }, err => {
        this.disabled = !this.disabled
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "warning"),
          err.error.status
        );
      })
    }
    else {
      this.disabled = !this.disabled
      this.notify.showNotification(
        "Please enter the valid mobile number",
        "top",
        "warning",
        0
      );
    }
  }

  resendOtpForTracking() {
    this.trackOtpForm.reset();
    this.authService.getOTPByNumber(this.signUpUserName).subscribe(res => {
      if (res.body.data.USER_CONFIRMED == 1) {
        this.showTime = true;
        this.timeLeft = '';
        this.isExpiryCompleted = true;
        this.expiryTime = 120;
        this.notify.showNotification(
          res.body.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status] : "success"),
          res.status
        );
        this.interval = setInterval(() => {
          if (this.expiryTime > 0) {
            this.expiryTime--;
            const minutes: number = Math.floor(this.expiryTime / 60);
            this.timeLeft = ('00' + minutes).slice(-2) + ':' + ('00' + Math.floor(this.expiryTime - minutes * 60)).slice(-2);
          } else {
            clearInterval(this.interval)
            this.showTime = false;
            this.isExpiryCompleted = false;
          }
        }, 1000)
        this.showTrackComplaintVerification = true;
        this.cIDByOtp = res.body.data.TOKEN_DATA.C_ID;
        this.trackComplaintSessionToken = res.body.data.TOKEN_DATA.session_token;
      } else {
        this.notify.showNotification(
          "User is not yet confirmed",
          "top",
          "warning",
          0
        );
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "warning"),
        err.error.status
      );
    })
  }

  openMobileLogin() {
    this.showTrackComplaint = true;
    this.showRegisterContainer = false;
  }

  onLogin(loginForm) {
    if (this.loginForm.controls['password'].invalid) {
      this.notify.showNotification(
        "Please enter the password",
        "top",
        "warning",
        0
      );
      return;
    }
    this.disabled = !this.disabled;
    let obj = {
      'USERNAME': loginForm.value.userName,
      'PASSWORD': loginForm.value.password
    }
    this.authService.loginByEmail(obj).subscribe((res) => {
      this.disabled = !this.disabled;
      if (res) {
        if (res.body.data.USER_CONFIRMED == 1) {
          window.localStorage.setItem(
            'userInfo',
            JSON.stringify({
              userId: res.body.data.USER_ID,
              C_ID: res.body.data.C_ID,
              firstName: res.body.data.FIRST_NAME,
              lastName: res.body.data.LAST_NAME,
              salutation: res.body.data.SALUTATION_ID,
              department: res.body.data.DEPARTMENT,
              gov_dept_id: res.body.data.GOVERNMENT_DEPARTMENT_ID,
              emailId: res.body.data.EMAIL_ID,
              companyId: res.body.data.COMPANY_ID,
              companyName: res.body.data.COMPANY_NAME,
              mobile: res.body.data.MOBILE,
              industryMemberRoleId: res.body.data.MEMBER_ROLE_ID,
              emailVerified: res.body.data.EMAIL_VERIFIED,
              mobileVerified: res.body.data.MOBILE_VERIFIED,
              roleId: res.body.data.ROLE_ID,
              access_token: res.body.data.TOKEN_DATA.access_token,
              id_token: res.body.data.TOKEN_DATA.id_token,
              refresh_token: res.body.data.TOKEN_DATA.refresh_token,
              expiresIn: res.body.data.TOKEN_DATA.expires_in
            })
          )
          if (res.body.data.ROLE_ID == 1 || res.body.data.ROLE_ID == 2 || res.body.data.ROLE_ID == 3 || res.body.data.ROLE_ID == 6) {
            this.router.navigate(['/home/<USER>']);
          }
          else if (res.body.data.ROLE_ID == 4 || res.body.data.ROLE_ID == 5 || res.body.data.ROLE_ID == 7 || res.body.data.ROLE_ID == 8) {
            this.router.navigate(['/home/<USER>']);
          }
          else if (res.body.data.ROLE_ID == 9) {
            this.router.navigate(['/auth/my-profile']);
          }
        } else if (res.body.data.USER_CONFIRMED == 0) {
          this.cIDSignup = res.body.data.C_ID;
          this.notify.showNotification(
            res.body.message,
            "top",
            (!!colorObj[res.status] ? colorObj[res.status] : "success"),
            res.status
          );
          this.interval = setInterval(() => {
            if (this.expiryTime > 0) {
              this.expiryTime--;
              const minutes: number = Math.floor(this.expiryTime / 60);
              this.timeLeft = ('00' + minutes).slice(-2) + ':' + ('00' + Math.floor(this.expiryTime - minutes * 60)).slice(-2);
            } else {
              clearInterval(this.interval)
              this.showTime = false;
              this.isExpiryCompleted = false;
            }
          }, 1000)
          this.signupOtpForm.reset();
          this.showRegisterContainer = false;
          this.showLoginContainer = false;
          this.showLoginContainer1 = false;
          this.showTrackComplaint = false;
          this.showTrackComplaintVerification = false;
          this.showForgotPasswordContainer = false;
          this.showVerificationContainer = true;
          if (this.isMobileScreen == false) {
            setTimeout(() => {
              var elem = this.renderer.selectRootElement('#signupOtp');
              elem.focus();
            }, 1000);
          } else {
            setTimeout(() => {
              var elem = this.renderer.selectRootElement('#signupOtpMobile');
              elem.focus();
            }, 1000);
          }
          this.showResetPasswordContainer = false;
          this.showForgotPasswordVerificationContainer = false;
        }
      }
    }, (err) => {
      this.disabled = !this.disabled;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    });
  }

  signup(model) {
    if (this.companyId == undefined || this.companyId == null) {
      this.companyId = 0;
      this.selectedCompany = this.companyNameControl.value;
    }
    if (this.companyId != 0) {
      this.selectedCompany = null;
    }
    if (model.value.govBody == 10) {
      this.govBodyName = model.value.govBodyName;
    } else {
      this.govBodyName = '';
    }
    let obj = {
      "EMAIL_ID": model.value.email,
      "FIRST_NAME": model.value.firstName,
      "LAST_NAME": model.value.lastName,
      "PASSWORD": model.value.password,
      "PHONE_NUMBER": model.value.phonenumber,
      "COMPANY_ID": this.companyId,
      "COMPANY_NAME": this.selectedCompany,
      "ORGANIZATION_NAME": model.value.companyname,
      "PROFESSION_NAME": model.value.professionName,
      "PINCODE": model.value.pin,
      "ADDRESS": model.value.address,
      "ROLE_ID": model.value.role,
      "SALUTATION_ID": model.value.salutation,
      "GOVERNMENT_DEPARTMENT_ID": model.value.govBody,
      "GOVERNMENT_DEPARTMENT_NAME": this.govBodyName
    }
    this.signUpUserName = model.value.email;
    this.resendOtpUsername = model.value.email;
    this.isRegisterDisabled = !this.isRegisterDisabled
    this.authService.signUp(obj).subscribe((res) => {
      this.isRegisterDisabled = !this.isRegisterDisabled
      if (res) {
        this.cIDSignup = res.body.data.C_ID;
        this.notify.showNotification(
          res.body.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status] : "success"),
          res.status
        );
        this.interval = setInterval(() => {
          if (this.expiryTime > 0) {
            this.expiryTime--;
            const minutes: number = Math.floor(this.expiryTime / 60);
            this.timeLeft = ('00' + minutes).slice(-2) + ':' + ('00' + Math.floor(this.expiryTime - minutes * 60)).slice(-2);
          } else {
            clearInterval(this.interval)
            this.showTime = false;
            this.isExpiryCompleted = false;
          }
        }, 1000)
        this.signupOtpForm.reset();
        this.showRegisterContainer = false;
        this.showLoginContainer = false;
        this.showLoginContainer1 = false;
        this.showTrackComplaint = false;
        this.showTrackComplaintVerification = false;
        this.showForgotPasswordContainer = false;
        this.showVerificationContainer = true;
        if (this.isMobileScreen == false) {
          setTimeout(() => {
            var elem = this.renderer.selectRootElement('#signupOtp');
            elem.focus();
          }, 1000);
        } else {
          setTimeout(() => {
            var elem = this.renderer.selectRootElement('#signupOtpMobile');
            elem.focus();
          }, 1000);
        }
        this.showResetPasswordContainer = false;
        this.showForgotPasswordVerificationContainer = false;
      }
    }, (err) => {
      this.isRegisterDisabled = !this.isRegisterDisabled
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  onInputEntry(event, nextInput) {
    let input = event.target;
    let length = input.value.length;
    let maxLength = input.attributes.maxlength.value;
    if (length >= maxLength) {
      nextInput.focus();
    }
  }

  resetPassword() {
    this.isSubmitDisabled = true;
    if (this.resetForm.controls['new_password'].invalid || this.resetForm.controls['confirm_password'].invalid) {
      this.notify.showNotification(
        "Password should contain atleast one uppercase,one lowercase,one special character, one digit and should be of length 8-15",
        "top",
        "warning",
        0
      );
      return;
    }
    if (this.resetForm.valid) {
      if (this.resetForm.value['new_password'] != this.resetForm.value['confirm_password']) {
        this.notify.showNotification(
          "Password donot match",
          "top",
          "warning",
          0
        );
        return;
      } else {
        this.error = "";
        this.disabled = !this.disabled;
        this.authService.onReset(this.forgotPasswordCode, this.resetForm.value['new_password'], this.resendOtpUsername).subscribe((res) => {
          this.disabled = !this.disabled;
          this.notify.showNotification(
            res.body.message,
            "top",
            (!!colorObj[res.status] ? colorObj[res.status] : "success"),
            res.status
          );
          this.loginForm.reset();
          this.showHideLoginRegister('login');
        }, (err) => {
          clearInterval(this.interval)
          this.timeLeft = '';
          this.disabled = !this.disabled;
          this.isExpiryCompleted = false;
          this.showForgotPasswordVerificationContainer = true;
          this.showResetPasswordContainer = false;
          this.forgotOtpForm.reset();
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "warning"),
            err.error.status
          );
        });
      }
    }
  }

  verifySignUpByOtp(model) {
    if (this.signupOtpForm.invalid) {
      this.notify.showNotification(
        "Please enter correct OTP",
        "top",
        "warning",
        0
      );
      return;
    }
    this.disabled = !this.disabled;
    let otp = model.val1 + model.val2 + model.val3 + model.val4 + model.val5 + model.val6;
    this.authService.signUpByOtp(otp, this.cIDSignup).subscribe(res => {
      if (res && !this.isMobileScreen) {
        this.showHideLoginRegister('login');
        this.signupForm.reset();
      }
      if (res && this.isMobileScreen) {
        this.showTrackComplaint = true;
        this.showVerificationContainer = false;
        this.signupForm.reset();
      }
      this.disabled = !this.disabled;
      this.notify.showNotification(
        res.body.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      );
    }, err => {
      this.signupOtpForm.reset();
      clearInterval(this.interval);
      this.isExpiryCompleted = false;
      this.timeLeft = '';
      this.disabled = !this.disabled;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  verifyForgotPWByOtp(model) {
    if (this.forgotOtpForm.invalid) {
      this.notify.showNotification(
        "Please enter the correct otp",
        "top",
        "warning",
        0
      );
      return;
    }
    this.forgotPasswordCode = model.val1 + model.val2 + model.val3 + model.val4 + model.val5 + model.val6;
    this.resetForm.reset();
    this.showResetPasswordContainer = true;
    setTimeout(() => {
      var elem = this.renderer.selectRootElement('#newPassword');
      elem.focus();
    }, 1000);
    this.showForgotPasswordVerificationContainer = false;
    this.showLoginContainer = false;
    this.showRegisterContainer = false;
    this.showTrackComplaint = false;
    this.showForgotPasswordContainer = false;
    this.showLoginContainer1 = false;
    this.showVerificationContainer = false;
    this.showTrackComplaintVerification = false;
  }

  next(model) {
    if (this.trackOtpForm.invalid) {
      this.notify.showNotification(
        "Please enter the correct otp",
        "top",
        "warning",
        0
      );
      return;
    }
    let otp = model.val1 + model.val2 + model.val3 + model.val4 + model.val5 + model.val6;
    this.disabled = !this.disabled;
    this.authService.loginByOTP(otp, this.cIDByOtp, this.trackComplaintSessionToken).subscribe(res => {
      this.disabled = !this.disabled;
      window.localStorage.setItem(
        'userInfo',
        JSON.stringify({
          userId: res.data.USER_ID,
          C_ID: res.data.C_ID,
          firstName: res.data.FIRST_NAME,
          lastName: res.data.LAST_NAME,
          salutation: res.data.SALUTATION_ID,
          department: res.data.DEPARTMENT,
          gov_dept_id: res.data.GOVERNMENT_DEPARTMENT_ID,
          emailId: res.data.EMAIL_ID,
          companyId: res.data.COMPANY_ID,
          companyName: res.data.COMPANY_NAME,
          mobile: res.data.MOBILE,
          industryMemberRoleId: res.data.MEMBER_ROLE_ID,
          emailVerified: res.data.EMAIL_VERIFIED,
          mobileVerified: res.data.MOBILE_VERIFIED,
          roleId: res.data.ROLE_ID,
          roleName: res.data.ROLE_NAME,
          access_token: res.data.TOKEN_DATA.access_token,
          id_token: res.data.TOKEN_DATA.id_token,
          refresh_token: res.data.TOKEN_DATA.refresh_token,
          expiresIn: res.data.TOKEN_DATA.expires_in
        })
      )
      if (res.data.ROLE_ID == 1 || res.data.ROLE_ID == 2 || res.data.ROLE_ID == 3 || res.data.ROLE_ID == 6) {
        this.router.navigate(['/home/<USER>']);
      }
      else if (res.data.ROLE_ID == 4 || res.data.ROLE_ID == 5 || res.data.ROLE_ID == 7 || res.data.ROLE_ID == 8) {
        this.router.navigate(['/home/<USER>']);
      }
      else if (res.data.ROLE_ID == 9) {
        this.router.navigate(['/auth/my-profile']);
      }
    }, err => {
      this.disabled = !this.disabled;
      clearInterval(this.interval)
      this.timeLeft = '';
      this.isExpiryCompleted = false;
      this.trackOtpForm.reset();
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  getRole(id) {
    if (id == 7) {
      this.signupForm.get('companyname').clearValidators();
      this.signupForm.get('companyname').updateValueAndValidity();
    }
  }

  onChange() {
    this.selectedColor1 = '#000000';
    this.signupForm.controls['firstName'].reset();
    this.signupForm.controls['lastName'].reset();
    this.signupForm.controls['salutation'].reset();
    this.signupForm.controls['phonenumber'].reset();
    this.signupForm.controls['email'].reset();
    this.signupForm.controls['password'].reset();
    this.signupForm.controls['govBody'].reset();
    this.signupForm.controls['companyname'].reset();
    this.signupForm.controls['pin'].reset();
    this.signupForm.controls['address'].reset();
  }

  // onChangeTitle(){
  //   this.selectedColor2 = '#000000' ;
  // }

  // onChangeGovBody(){
  //   this.selectedColor3 = '#000000' ;
  // }

  onSelectionChange(event) {
    this.companyList.forEach(element => {
      if (event.option.value === element.COMPANY_NAME) {
        this.companyId = element.ID;
        this.selectedCompany = element.COMPANY_NAME;
      }
    });
  }

  resendOtp(email) {
    this.showTime = true;
    this.isExpiryCompleted = true;
    this.expiryTime = 120;
    this.authService.resendOtp(email).subscribe(res => {
      this.notify.showNotification(
        res.body.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      );
      this.interval = setInterval(() => {
        if (this.expiryTime > 0) {
          this.expiryTime--;
          const minutes: number = Math.floor(this.expiryTime / 60);
          this.timeLeft = ('00' + minutes).slice(-2) + ':' + ('00' + Math.floor(this.expiryTime - minutes * 60)).slice(-2);
        } else {
          clearInterval(this.interval)
          this.showTime = false;
          this.isExpiryCompleted = false;
        }
      }, 1000)
    }, err => {
      clearInterval(this.interval)
      this.timeLeft = '';
      this.isExpiryCompleted = false;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

}