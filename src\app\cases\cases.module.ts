import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { MaterialModule } from "../material/material.module";
import { CommonModule } from "@angular/common";
import { ClipboardModule } from '@angular/cdk/clipboard';
import { SharedModule } from "../shared/shared.module";
import { FlexLayoutModule } from '@angular/flex-layout';
import { CaseRoutingModule } from "./cases-routing.module";
import { ManageCasesComponent } from "./manage-cases/manage-cases.component";
import { PopoverModule } from "ngx-smart-popover";
import { ComplaintDetailComponent } from "./complaint-detail/complaint-detail.component";
import { ComplaintRegisterComponent } from "./complaint-register/complaint-register.component";
import { IntraIndustryComponent } from "./intra-industry/intra-industry.component";
import { GeneralPublicComponent } from "./general-public/general-public.component";
import { MatExpansionModule } from '@angular/material/expansion';
import { ViewDocumentComponent } from "./view-document/view-document.component";
import { ViewMediaFileComponent } from './view-media-file/view-media-file.component';
import { NamsFormComponent } from './nams-form/nams-form.component';
import { RelatedComplaintsComponent } from './related-complaints/related-complaints.component';
import { MatTimepickerModule } from 'mat-timepicker';
import { ComplaintConversationsComponent } from './complaint-conversations/complaint-conversations.component';
import { ViewUploadDocumentComponent } from './view-upload-document/view-upload-document.component';
import { InternalMediaShareComponent } from './internal-media-share/internal-media-share.component';
import { QuillModule } from "ngx-quill";
import { TimelineSidebarComponent } from './timeline-sidebar/timeline-sidebar.component';
import { InfiniteScrollModule } from "ngx-infinite-scroll";

@NgModule({
  declarations: [
    ManageCasesComponent,
    ComplaintRegisterComponent,
    ComplaintDetailComponent,
    IntraIndustryComponent,
    GeneralPublicComponent,
    ViewDocumentComponent,
    ViewMediaFileComponent,
    NamsFormComponent,
    RelatedComplaintsComponent,
    ComplaintConversationsComponent,
    ViewUploadDocumentComponent,
    InternalMediaShareComponent,
    TimelineSidebarComponent
  ],
  imports: [
    CaseRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    MaterialModule,
    CommonModule,
    ClipboardModule,
    SharedModule,
    FlexLayoutModule,
    PopoverModule,
    InfiniteScrollModule,
    MatTimepickerModule,
    MatExpansionModule,
    QuillModule
  ],
  exports: [
    GeneralPublicComponent,
    IntraIndustryComponent
  ],
  entryComponents: [
    ViewDocumentComponent,
    ViewMediaFileComponent,
    RelatedComplaintsComponent,
    InternalMediaShareComponent
  ],
  providers: [],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class CaseModule { }