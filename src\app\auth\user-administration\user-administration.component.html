<div class="common-toolbar-mobile" style="width: 100%; height: auto;" *ngIf="isMobile">
  <app-mobile-header></app-mobile-header>
</div>
<span class="dashboard-admin-heading" *ngIf="isMobile">
  <p style="text-align: center;margin-top: 20px;margin-bottom: 3px;"> Welcome {{userName}}!</p>
</span>
<div class="dasboard-subheading" *ngIf="isMobile">
  <p style="text-align: center;">Dear ASCI Officers, please move to a larger device for seamlessly accessing all
      functionalities of the system</p>
</div>
<div class="dashboard-container" *ngIf="isMobile">
</div>

<app-icons *ngIf="!isMobile"></app-icons>
<div class="common-toolbar" fxLayout="row" style="width: 100%; height: 50px;" *ngIf="!isMobile">
  <div class="heading-container">
    <app-heading [pagename]="pagename"></app-heading>
  </div>
  <div class="options-container">
    <app-toolbar-options></app-toolbar-options>
  </div>
</div>

<app-mat-spinner-overlay overlay="true" *ngIf="loading && !isMobile">
</app-mat-spinner-overlay>

<mat-toolbar class="toolbar2" fxLayout="row" fxLayoutAlign="end end" *ngIf="!isMobile">
  <div class="header-search">
    <input type="text" name="search" placeholder="Search.." [formControl]="KEYWORD" (keyup)="applyFilter($event.target.value)" style="
        font-style: normal;
        font-weight: normal;
        font-size: 14px; color: #2F3941;" autocomplete="off">
  </div>
  <div>
    <button mat-button *ngIf="consumerUser" class="add-btn" (click)="addConsumerUser()"
      style="width: 140px; margin-top: 5px;">
      <mat-icon style="font-size: 16px; padding-top: 3px; margin-left: -5px;">add</mat-icon>
      <span class="bolder">Add New User</span>
    </button>
    <button mat-button *ngIf="generalPublicUser" class="add-btn" (click)="addGeneralPublicUser()"
      style="width: 140px; margin-top: 5px;">
      <mat-icon style="font-size: 16px; padding-top: 3px; margin-left: -5px;">add</mat-icon>
      <span class="bolder">Add New User</span>
    </button>
    <button mat-button *ngIf="govBodyUser" class="add-btn" (click)="addGovBodyUser()"
      style="width: 140px; margin-top: 5px;">
      <mat-icon style="font-size: 16px; padding-top: 3px; margin-left: -5px;">add</mat-icon>
      <span class="bolder">Add New User</span>
    </button>
  </div>
</mat-toolbar>
<mat-tab-group style="margin-left: 50px; margin-top: -50px;" class="admin-tabs" *ngIf="!isMobile"
  (selectedTabChange)="onTabChanged($event)">
  <mat-tab label="Consumer Organization" class="asci-member">
    <mat-card class="card" *ngIf="!loading">
      <div class="table-scroll">
        <table mat-table class="admin-table" [dataSource]="dataSource1" matSort #table>
          <div>
            <ng-container matColumnDef="first_name">
              <th mat-header-cell *matHeaderCellDef  style="width: 3%;"><b>First Name</b>
                <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!fname_asc && !fname_desc" (click)="sortConsumerComplaints('FIRST_NAME', 'ASC')">
                <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="fname_asc" (click)="sortConsumerComplaints('FIRST_NAME', 'DESC')">
                <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="fname_desc" (click)="sortConsumerComplaints('', '')">
              </th>
              <td mat-cell *matCellDef="let element" class="borderleft"> {{element.FIRST_NAME}} </td>
            </ng-container>

            <ng-container matColumnDef="last_name">
              <th mat-header-cell *matHeaderCellDef  style="width: 3%;"><b>Last Name</b>
                <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!lname_asc && !lname_desc" (click)="sortConsumerComplaints('LAST_NAME', 'ASC')">
                <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="lname_asc" (click)="sortConsumerComplaints('LAST_NAME', 'DESC')">
                <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="lname_desc" (click)="sortConsumerComplaints('', '')">
              </th>
              <td mat-cell *matCellDef="let element"> {{element.LAST_NAME}} </td>
            </ng-container>

            <ng-container matColumnDef="email">
              <th mat-header-cell *matHeaderCellDef  style="width: 2%;"><b>Email ID</b>
                <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!email_asc && !email_desc" (click)="sortConsumerComplaints('EMAIL_ID', 'ASC')">
                <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="email_asc" (click)="sortConsumerComplaints('EMAIL_ID', 'DESC')">
                <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="email_desc" (click)="sortConsumerComplaints('', '')">
              </th>
              <td mat-cell *matCellDef="let element"> {{element.EMAIL_ID}} </td>
            </ng-container>

            <ng-container matColumnDef="contact">
              <th mat-header-cell *matHeaderCellDef style="width: 2%;"><b>Contact no.</b></th>
              <td mat-cell *matCellDef="let element"> {{element.MOBILE}} </td>
            </ng-container>

            <ng-container matColumnDef="org_name">
              <th mat-header-cell *matHeaderCellDef style="width: 2%;"><b>Organisation Name</b></th>
              <td mat-cell *matCellDef="let element"> {{element.ORGANIZATION_NAME}} </td>
            </ng-container>

            <ng-container matColumnDef="action">
              <th mat-header-cell *matHeaderCellDef class="borderright"><b>Action</b></th>
              <td mat-cell *matCellDef="let element;let i = index">
                <div fxLayout="row" fxLayoutGap="10px">
                  <div fxLayout="column" *ngIf="element.status == 'Confirmed'" style="position: relative; top: 5px;">
                    <mat-slide-toggle (change)="onValChange($event, element.ID, element.C_ID)" [checked]="element.actionVal"
                      class="slider">
                    </mat-slide-toggle>
                    <p *ngIf="element.actionVal==true" style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(0, 0, 0, 0.6); margin-top: -2px;">Enabled</p>
                    <p *ngIf="element.actionVal==false" style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(0, 0, 0, 0.6); margin-top: -2px;">Disabled</p>
                  </div>
                  <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                    (click)="approve(element, 'Consumer')"
                    style="position: relative; top: 10px; display: flex; align-items: center;">
                    <img src="../../assets/images/Admin-Approve.png" width="15px">
                    <p style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(4, 165, 133, 0.6);">Approve</p>
                  </div>
                  <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                    (click)="rejectUser(element, 'Consumer')"
                    style="position: relative; top: 10px; left: 0px; display: flex; align-items: center">
                    <img src="../../assets/images/reject-red.png" width="15px">
                    <p style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(237, 47, 69, 0.6);">Reject</p>
                  </div>
                  <div fxLayout="column" fxLayoutGap="3px" (click)="editConsumerUser(element)"
                    style="position: relative; left: 5px; top: 10px;">
                    <img src="../assets/images/edit-icon.svg" width="13px">
                    <p style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(0, 0, 0, 0.6);">Edit</p>
                  </div>
                </div>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns1; sticky: true"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns1;">
            </tr>
          </div>
        </table>
      </div>
    </mat-card>
    <mat-paginator (page)="changePage($event)" [pageSize]="10" [pageIndex]="0" *ngIf="!noData"></mat-paginator> 
    <span class="label" *ngIf="!noData"> {{rangeLabel}} of {{totalCount}}</span> 
    <div class="text-message" *ngIf="noData">
      <span>
        <br>
        No Data available ....
        <br>
      </span>
    </div> 
  </mat-tab>
  <mat-tab label="General Public" class="asci-member">
    <mat-card class="card" *ngIf="!loading">
      <div class="table-scroll">
        <table mat-table class="admin-table" [dataSource]="dataSource2" matSort #table>
          <div>
            <ng-container matColumnDef="first_name">
              <th mat-header-cell *matHeaderCellDef  style="width: 3%;"><b>First Name</b>
                <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!fname_asc && !fname_desc" (click)="sortGeneralPublicComplaints('FIRST_NAME', 'ASC')">
                <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="fname_asc" (click)="sortGeneralPublicComplaints('FIRST_NAME', 'DESC')">
                <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="fname_desc" (click)="sortGeneralPublicComplaints('', '')">
              </th>
              <td mat-cell *matCellDef="let element" class="borderleft"> {{element.FIRST_NAME}} </td>
            </ng-container>

            <ng-container matColumnDef="last_name">
              <th mat-header-cell *matHeaderCellDef  style="width: 3%;"><b>Last Name</b>
                <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!lname_asc && !lname_desc" (click)="sortGeneralPublicComplaints('LAST_NAME', 'ASC')">
                <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="lname_asc" (click)="sortGeneralPublicComplaints('LAST_NAME', 'DESC')">
                <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="lname_desc" (click)="sortGeneralPublicComplaints('', '')">
              </th>
              <td mat-cell *matCellDef="let element"> {{element.LAST_NAME}} </td>
            </ng-container>

            <ng-container matColumnDef="email">
              <th mat-header-cell *matHeaderCellDef  style="width: 2%;"><b>Email ID</b>
                <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!email_asc && !email_desc" (click)="sortGeneralPublicComplaints('EMAIL_ID', 'ASC')">
                <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="email_asc" (click)="sortGeneralPublicComplaints('EMAIL_ID', 'DESC')">
                <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="email_desc" (click)="sortGeneralPublicComplaints('', '')">
              </th>
              <td mat-cell *matCellDef="let element"> {{element.EMAIL_ID}} </td>
            </ng-container>

            <ng-container matColumnDef="contact">
              <th mat-header-cell *matHeaderCellDef style="width: 2%;"><b>Contact no.</b></th>
              <td mat-cell *matCellDef="let element"> {{element.MOBILE}} </td>
            </ng-container>

            <ng-container matColumnDef="pincode">
              <th mat-header-cell *matHeaderCellDef style="width: 2%;"><b>Pin code</b></th>
              <td mat-cell *matCellDef="let element"> {{element.PINCODE}} </td>
            </ng-container>

            <ng-container matColumnDef="action">
              <th mat-header-cell *matHeaderCellDef class="borderright"><b>Action</b></th>
              <td mat-cell *matCellDef="let element;let i = index">
                <div fxLayout="row" fxLayoutGap="10px">
                  <div fxLayout="column" *ngIf="element.status == 'Confirmed'" style="position: relative; top: 5px;">
                    <mat-slide-toggle (change)="onValChange($event, element.ID, element.C_ID)" [checked]="element.actionVal"
                      class="slider">
                    </mat-slide-toggle>
                    <p *ngIf="element.actionVal==true" style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(0, 0, 0, 0.6); margin-top: -2px;">Enabled</p>
                    <p *ngIf="element.actionVal==false" style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(0, 0, 0, 0.6); margin-top: -2px;">Disabled</p>
                  </div>
                  <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                    (click)="approve(element, 'General')"
                    style="position: relative; top: 10px; display: flex; align-items: center;">
                    <img src="../../assets/images/Admin-Approve.png" width="15px">
                    <p style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(4, 165, 133, 0.6);">Approve</p>
                  </div>
                  <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                    (click)="rejectUser(element, 'General')"
                    style="position: relative; top: 10px; left: 0px; display: flex; align-items: center">
                    <img src="../../assets/images/reject-red.png" width="15px">
                    <p style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(237, 47, 69, 0.6);">Reject</p>
                  </div>
                  <div fxLayout="column" fxLayoutGap="3px" (click)="editGeneralPublicUser(element)"
                    style="position: relative; left: 5px; top: 10px;">
                    <img src="../assets/images/edit-icon.svg" width="13px">
                    <p style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(0, 0, 0, 0.6);">Edit</p>
                  </div>
                </div>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns2; sticky: true"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns2;">
            </tr>
          </div>
        </table>
      </div>
    </mat-card>
    <mat-paginator (page)="changePage($event)" [pageSize]="10" [pageIndex]="0" *ngIf="!noData"></mat-paginator> 
    <span class="label" *ngIf="!noData"> {{rangeLabel}} of {{totalCount}}</span> 
    <div class="text-message" *ngIf="noData">
      <span>
        <br>
        No Data available ....
        <br>
      </span>
    </div> 
  </mat-tab>
  <mat-tab label="Government Body" class="asci-member">
    <mat-card class="card" *ngIf="!loading">
      <div class="table-scroll">
        <table mat-table class="admin-table" [dataSource]="dataSource3" matSort #table>
          <div>
            <ng-container matColumnDef="first_name">
              <th mat-header-cell *matHeaderCellDef><b>First Name</b>
                <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!fname_asc && !fname_desc" (click)="sortGovBodyComplaints('FIRST_NAME', 'ASC')">
                <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="fname_asc" (click)="sortGovBodyComplaints('FIRST_NAME', 'DESC')">
                <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="fname_desc" (click)="sortGovBodyComplaints('', '')">
              </th>
              <td mat-cell *matCellDef="let element" class="borderleft"> {{element.FIRST_NAME}} </td>
            </ng-container>

            <ng-container matColumnDef="last_name">
              <th mat-header-cell *matHeaderCellDef><b>Last Name</b>
                <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!lname_asc && !lname_desc" (click)="sortGovBodyComplaints('LAST_NAME', 'ASC')">
                <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="lname_asc" (click)="sortGovBodyComplaints('LAST_NAME', 'DESC')">
                <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="lname_desc" (click)="sortGovBodyComplaints('', '')">
              </th>
              <td mat-cell *matCellDef="let element"> {{element.LAST_NAME}} </td>
            </ng-container>

            <ng-container matColumnDef="email">
              <th mat-header-cell *matHeaderCellDef><b>Email ID</b>
                <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!email_asc && !email_desc" (click)="sortGovBodyComplaints('EMAIL_ID', 'ASC')">
                <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="email_asc" (click)="sortGovBodyComplaints('EMAIL_ID', 'DESC')">
                <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="email_desc" (click)="sortGovBodyComplaints('', '')">
              </th>
              <td mat-cell *matCellDef="let element"> {{element.EMAIL_ID}} </td>
            </ng-container>

            <ng-container matColumnDef="contact">
              <th mat-header-cell *matHeaderCellDef><b>Contact no.</b></th>
              <td mat-cell *matCellDef="let element"> {{element.MOBILE}} </td>
            </ng-container>

            <ng-container matColumnDef="gov_body">
              <th mat-header-cell *matHeaderCellDef><b>Gov. body name</b></th>
              <td mat-cell *matCellDef="let element" style="width: 2%; padding-right: 30px"> {{element.GOVERNMENT_DEPARTMENT_NAME}} </td>
            </ng-container>

            <ng-container matColumnDef="action">
              <th mat-header-cell *matHeaderCellDef class="borderright"><b>Action</b></th>
              <td mat-cell *matCellDef="let element;let i = index">
                <div fxLayout="row" fxLayoutGap="10px">
                  <div fxLayout="column" *ngIf="element.status == 'Confirmed'" style="position: relative; top: 5px;">
                    <mat-slide-toggle (change)="onValChange($event, element.ID, element.C_ID)" [checked]="element.actionVal"
                      class="slider">
                    </mat-slide-toggle>
                    <p *ngIf="element.actionVal==true" style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(0, 0, 0, 0.6); margin-top: -2px;">Enabled</p>
                    <p *ngIf="element.actionVal==false" style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(0, 0, 0, 0.6); margin-top: -2px;">Disabled</p>
                  </div>
                  <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                    (click)="approve(element, 'GovBody')"
                    style="position: relative; top: 10px; display: flex; align-items: center;">
                    <img src="../../assets/images/Admin-Approve.png" width="15px">
                    <p style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(4, 165, 133, 0.6);">Approve</p>
                  </div>
                  <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                    (click)="rejectUser(element, 'GovBody')"
                    style="position: relative; top: 10px; left: 0px; display: flex; align-items: center">
                    <img src="../../assets/images/reject-red.png" width="15px">
                    <p style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(237, 47, 69, 0.6);">Reject</p>
                  </div>
                  <div fxLayout="column" fxLayoutGap="3px" (click)="editGovBodyUser(element)"
                    style="position: relative; left: 5px; top: 10px;">
                    <img src="../assets/images/edit-icon.svg" width="13px">
                    <p style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(0, 0, 0, 0.6);">Edit</p>
                  </div>
                </div>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns3; sticky: true"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns3;">
            </tr>
          </div>
        </table>
      </div>
    </mat-card>
    <mat-paginator (page)="changePage($event)" [pageSize]="10" [pageIndex]="0" *ngIf="!noData"></mat-paginator> 
    <span class="label" *ngIf="!noData"> {{rangeLabel}} of {{totalCount}}</span> 
    <div class="text-message" *ngIf="noData">
      <span>
        <br>
        No Data available ....
        <br>
      </span>
    </div> 
  </mat-tab>
</mat-tab-group>