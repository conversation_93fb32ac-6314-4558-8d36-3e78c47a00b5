import { Component, Inject, OnInit } from '@angular/core';
import { ComplaintsService } from '../services/complaints.service';
import { NotificationService } from '../services/notification.service';
import { colorObj } from '../shared/color-object';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-archived-details',
  templateUrl: './archived-details.component.html',
  styleUrls: ['./archived-details.component.scss']
})
export class ArchivedDetailsComponent implements OnInit {

  archived_details: any = [];
  recommendations = [];
  resolutions = [];
  loadingDetails: boolean = true;

  constructor(
    private cs: ComplaintsService,
    private notify: NotificationService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private sanitized: DomSanitizer
  ) { }

  ngOnInit(): void {
    this.getDetails(this.data.ID);
  }

  safeHTML(unsafe: string) {
    return this.sanitized.bypassSecurityTrustHtml(unsafe);
  }

  getDetails(id) {
    this.cs.getArchivedComplaintDetails(id).subscribe(res => {
      this.loadingDetails = false;
      this.archived_details = res.data;
      this.recommendations = this.archived_details.COMPLAINT_RECOMMENDATION;
      this.resolutions = this.archived_details.COMPLAINT_RESOLUTION;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

}