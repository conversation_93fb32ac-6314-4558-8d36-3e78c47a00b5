import { Component, HostListener, Inject, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ComplaintsService } from '../services/complaints.service';
import { NotificationService } from '../services/notification.service';
import { colorObj } from '../shared/color-object';

@Component({
  selector: 'app-recommendation-resolution-details',
  templateUrl: './recommendation-resolution-details.component.html',
  styleUrls: ['./recommendation-resolution-details.component.scss']
})

export class RecommendationResolutionDetailsComponent implements OnInit {

  records: any[];
  type: any;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(@Inject(MAT_DIALOG_DATA) public data: any,
    public dialog: MatDialog,
    private cs: ComplaintsService,
    private notify: NotificationService,
    public dialogRef: MatDialogRef<RecommendationResolutionDetailsComponent>
  ) { }

  ngOnInit(): void {
    this.type = this.data.type;
    if (this.type == 'recommendation') {
      this.cs.getRecommendationsById(this.data.id).subscribe(res => {
        this.records = res.data;
      }, err => {
        this.notify.showNotification(
          err.error.message,
          'top',
          !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
          err.error.status
        );
      })
    } else {
      this.cs.getResolutionsById(this.data.id).subscribe(res => {
        this.records = res.data;
      }, err => {
        this.notify.showNotification(
          err.error.message,
          'top',
          !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
          err.error.status
        );
      })
    }
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

}