import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-confirmation-popup',
  templateUrl: './confirmation-popup.component.html',
  styleUrls: ['./confirmation-popup.component.css']
})
export class ConfirmationPopupComponent implements OnInit {

  constructor(
    public dialogRef: MatDialogRef<ConfirmationPopupComponent>,
    @Inject(MAT_DIALOG_DATA) public message: any
  ) { }

  ngOnInit(): void {}

  onSubmit(): void {
    // Close the dialog, return true
    this.dialogRef.close({ 'state': true, 'id': this.message.id });
  }

  onClose(): void {
    // Close the dialog, return false
    this.dialogRef.close({ 'state': false, 'id': this.message.id });
  }

}