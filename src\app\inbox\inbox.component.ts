import { ChangeDetector<PERSON><PERSON>, Component, HostListener, <PERSON><PERSON>ni<PERSON>, Pi<PERSON> } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { SubscriptionLike } from 'rxjs';
import { environment } from 'src/environments/environment';
import { AddEditCompanyComponent } from '../auth/add-edit-company/add-edit-company.component';
import { ViewMediaFileComponent } from '../cases/view-media-file/view-media-file.component';
import { ConfirmationPopupComponent } from '../confirmation-popup/confirmation-popup.component';
import { ComplaintsService } from '../services/complaints.service';
import { NotificationService } from '../services/notification.service';
import { ThirdPartyService } from '../services/third-party.service';
import { colorObj } from '../shared/color-object';
import { saveAs as importedSaveAs } from 'file-saver';
import { SimilarComplaintDetailsComponent } from '../similar-complaint-details/similar-complaint-details.component';
import { <PERSON><PERSON>aniti<PERSON> } from '@angular/platform-browser';
import { AuthService } from '../services/auth.service';
import { ComplaintConversationsComponent } from '../cases/complaint-conversations/complaint-conversations.component';
import { MarkInvalidComponent } from '../mark-invalid/mark-invalid.component';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { group } from 'console';

@Component({
  selector: 'app-inbox',
  templateUrl: './inbox.component.html',
  styleUrls: ['./inbox.component.css'],
})
@Pipe({ name: 'safeHtml' })
export class InboxComponent implements OnInit {
  public msgvalue: any = [];
  pagename: String;
  userInfo: any;
  userName: any;
  setmsg_count;
  emailPattern = environment.emailPatterm;
  mobilenopattern = /^[0-9]{10}$/;
  whatsappComplaints: any[];
  mailComplaints: any[];
  normalComplaints: any[];
  chatbotComplaints: any[];
  sysGenComplaints: any[];
  complaintStats: any[];
  whatsappCount: number;
  mailCount: number;
  systemCount: number;
  chatbotCount: number;
  activePage;
  selectedTab: number = 0;
  searchPageNumber: number = 1;
  loading: boolean = false;
  lastData: number;
  similarCompLoading: boolean = false;
  chatbotComplaintId: number;
  sysGenComplaintId: number;
  whatsappComplaintId: number;
  whatsappId: number;
  mailComplaintId: number;
  chatbotDetails: any = [];
  whatsappDetail: any = [];
  mailDetail: any = [];
  sysGenDetail: any;
  conversationCompDetail: any = [];
  showURL;
  docURL;
  mobile: number;
  compSourceId: number;
  id: number;
  public bucketUrl = `${environment.BUCKET_URL}`;
  public classificationTag: string = '';
  complaint_attachment_data: string;
  similarityComplaintList: any[] = [];
  extraSimilarComplaints = 0;
  matchFound: boolean = true;
  listLoading: boolean = false;
  adSource: any[];
  confirmationMsg: any = {};
  idArray = [];
  detail_adsource: any;
  detail_platform: any;
  detail_channel: any;
  detail_addate: any;
  detail_link: any = '';
  docUrl: any;
  detail_place: any;
  noOfDocs: number = 0;
  adMedium: any;
  adDocs: any = [];
  url: string;
  imgURL: string;
  longText1: string;
  longText2: string;
  translation_hyper: any;
  detail_objection: any;
  registered: number = 0;
  ml_data: any;
  private subs: any;
  subscription: SubscriptionLike;
  pageToken: number;
  zeroComplaints: boolean = false;
  disabled: boolean = false;
  msg = [];
  chatbotBlueImage: number;
  parent_id: any;
  compDetails: any = [];
  whatsappForm: FormGroup;
  incompleteData: boolean = false;
  whatsappData: boolean = false;
  compSystemEdit: boolean = false;
  compChatbotEdit: boolean = false;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private router: Router,
    private cs: ComplaintsService,
    private namsService: ThirdPartyService,
    private notify: NotificationService,
    private cd: ChangeDetectorRef,
    public dialog: MatDialog,
    private sanitized: DomSanitizer,
    private authService: AuthService,
    private fb: FormBuilder
  ) { }

  safeHTML(unsafe: string) {
    return this.sanitized.bypassSecurityTrustHtml(unsafe);
  }

  ngOnInit(): void {
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.userName = this.userInfo.firstName;
    this.adSource = JSON.parse(
      window.localStorage.getItem('advertisementSource')
    );
    this.pagename = 'Inbox';
    this.getComplaintStats();
    this.getWhatsappComplaint(this.searchPageNumber);
    this.activePage = 'Inbox';
    this.cs.setActivePage(this.activePage);
  }

  async onTabChanged(event) {
    if (!!this.subscription) {
      this.subscription.unsubscribe();
    }
    this.selectedTab = event.index;
    this.searchPageNumber = 1;
    this.similarityComplaintList = [];
    this.similarCompLoading = false;
    this.lastData = 0;
    if (this.selectedTab == 0) {
      this.loading = true;
      await this.getWhatsappComplaint(this.searchPageNumber);
    } else if (this.selectedTab == 1 && this.mailCount != 0) {
      this.loading = true;
      await this.getMailComplaintList(this.searchPageNumber);
    } else if (this.selectedTab == 2) {
      this.loading = true;
      await this.getSysGenComplaints(this.searchPageNumber);
    } else if (this.selectedTab == 3) {
      this.loading = true;
      this.chatbotBlueImage = 1;
      await this.getChatbotComplaints(this.searchPageNumber);
    }
  }

  getWhatsappComplaint(pageNumber) {
    this.cs.getWhatsappComplaints(pageNumber).subscribe(
      (whatsappList) => {
        this.whatsappComplaints = whatsappList.data;
        this.loading = false;
        this.lastData = whatsappList.data.length;
        if (!!this.whatsappComplaints && this.whatsappComplaints.length > 0) {
          this.whatsappComplaintId = this.whatsappComplaints[0].ID;
          this.whatsappId = this.whatsappComplaints[0].WHATSAPP_ID;
          this.getWhatsappDetail(this.whatsappId);
        }
      },
      (err) => {
        this.notify.showNotification(
          err.error.message,
          'top',
          !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
          err.error.status
        );
      }
    );
  }

  getWhatsappDetail(id) {
    this.similarCompLoading = true;
    this.whatsappId = id;
    this.noOfDocs = 0;
    this.subscription = this.cs.getWhatsappComplaint(id).subscribe(
      (res: any) => {
        this.whatsappDetail = res.data;
        this.whatsappComplaintId = this.whatsappDetail['ID'];
        this.id = this.whatsappDetail['ID'];
        this.whatsappId = this.whatsappDetail['WHATSAPP_ID'];
        this.adDocs = this.whatsappDetail['ATTACHED_MEDIA'];
        this.mailDetail['ADVERTISEMENT_SOURCE_NAME'];
        if (!!this.whatsappDetail['ATTACHED_MEDIA']) {
          this.noOfDocs = this.whatsappDetail['ATTACHED_MEDIA'].length;
          if (this.adDocs.length > 0) {
            for (let doc of this.adDocs) {
              if (doc.SOURCE_URL.length > 25) {
                this.longText1 = '..';
              } else {
                this.longText1 = ' ';
              }
            }
          }
        }
        if (!this.whatsappDetail?.EMAIL_ID || !this.whatsappDetail?.MOBILE || !this.whatsappDetail?.COMPLAINT_DESCRIPTION || !this.whatsappDetail?.ADVERTISEMENT_DESCRIPTION || !this.whatsappDetail?.BRAND_NAME) {
          this.incompleteData = true;
          this.whatsappData = true;
          this.createWhatsappForm();
        } else {
          if (!this.whatsappForm) {
            this.whatsappForm = new FormGroup({});
          }
          this.whatsappForm.setValidators(null);
          this.whatsappForm.updateValueAndValidity();
        }
        // let file = [];
        // if (this.chatbotDetails['ATTACHMENT_SOURCE'] != null) {
        //   file.push(this.chatbotDetails['ATTACHMENT_SOURCE'].split("/"));
        //   this.showURL = file[0][file[0].length - 1];
        // }
        // if (!!this.chatbotDetails['ATTACHMENT_SOURCE']) {
        //   this.docURL = this.bucketUrl + this.chatbotDetails['ATTACHMENT_SOURCE'];
        // } else {
        //   this.docURL = '';
        // }
        // this.mobile = res.data.MOBILE;
        // this.compSourceId = this.chatbotDetails['COMPLAINT_SOURCE_ID'];
        // this.id = this.chatbotDetails['ID'];
        this.compSourceId = this.whatsappDetail['COMPLAINT_SOURCE_ID'];
        this.getClassificationTag(
          this.whatsappDetail['COMPLAINT_DESCRIPTION'],
          '',
          this.whatsappDetail['BRAND_NAME'],
          '',
          this.whatsappDetail['COMPANY_NAME'],
          this.whatsappDetail['COMPLAINT_SOURCE_ID'],
          this.whatsappDetail['ID']
        );
        // if (!this.whatsappDetail['COMPANY_ID']) {
        //   this.getCompanyName(this.whatsappDetail['COMPANY_NAME']);
        // }
      },
      (err) => {
        this.notify.showNotification(
          err.error.message,
          'top',
          !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
          err.error.status
        );
      }
    );
  }

  createWhatsappForm() {
    this.whatsappForm = new FormGroup({});
    if (!this.whatsappDetail?.BRAND_NAME) {
      this.whatsappForm.addControl('brand', new FormControl('', Validators.required))
    }
    if (!this.whatsappDetail?.EMAIL_ID) {
      this.whatsappForm.addControl('email', new FormControl('', [Validators.required, Validators.pattern(this.emailPattern)]))
    }
    if (!this.whatsappDetail?.MOBILE) {
      this.whatsappForm.addControl('mobile', new FormControl('', [Validators.required, Validators.pattern(this.mobilenopattern)]))
    }
    if (!this.whatsappDetail?.ADVERTISEMENT_DESCRIPTION) {
      this.whatsappForm.addControl('advDesc', new FormControl('', Validators.required))
    }
    if (!this.whatsappDetail?.COMPLAINT_DESCRIPTION) {
      this.whatsappForm.addControl('compDesc', new FormControl('', Validators.required))
    }
  }

  markInvalid() {
    const dialogRef = this.dialog.open(MarkInvalidComponent, {
      width: '500px',
      height: 'auto',
      data: this.id,
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
      }
    );
  }

  getMailComplaintList(page) {
    this.cs.getMailComplaints(page).subscribe(
      (mailList) => {
        this.mailComplaints = mailList.data.EMAILS;
        this.pageToken = mailList.data.PAGE_TOKEN;
        if (!!this.mailComplaints && this.mailComplaints.length > 0) {
          this.lastData = this.mailComplaints.length;
          this.mailComplaintId = this.mailComplaints[0].ID;
          this.getMailDetail(this.mailComplaintId);
        } else {
          this.loading = false;
        }
      },
      (err) => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          'top',
          !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
          err.error.status
        );
      }
    );
  }

  getMailDetail(id) {
    // this.similarCompLoading = true;
    this.mailComplaintId = id;
    this.subscription = this.cs.getMailComplaint(id).subscribe(
      (res: any) => {
        this.mailDetail = res.data;
        this.mobile = res.data.MOBILE;
        this.mailDetail['ADVERTISEMENT_SOURCE_NAME'];
        this.compSourceId = this.mailDetail['COMPLAINT_SOURCE_ID'];
        this.id = this.mailDetail['ID'];
        this.getClassificationTag(
          this.mailDetail['COMPLAINT_DESCRIPTION'],
          this.docURL,
          this.mailDetail['BRAND_NAME'],
          this.mailDetail['PRODUCT_NAME'],
          this.mailDetail['COMPANY_NAME'],
          this.mailDetail['COMPLAINT_SOURCE_ID'],
          this.mailDetail['ID']
        );
        // if (!this.mailDetail['COMPANY_ID']) {
        //   this.getCompanyName(this.mailDetail['COMPANY_NAME']);
        // }
      },
      (err) => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          'top',
          !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
          err.error.status
        );
      }
    );
  }

  displayDetails(data) {
    this.msgvalue = data;
  }

  getSysGenComplaints(pageNumber) {
    this.cs
      .getSysGenComplaints(
        null,
        null,
        null,
        null,
        null,
        pageNumber,
        0,
        this.registered
      )
      .subscribe(
        (sysGenList) => {
          this.sysGenComplaints = sysGenList.data;
          this.loading = false;
          this.lastData = sysGenList.data.length;
          if (!!this.sysGenComplaints && this.sysGenComplaints.length > 0) {
            this.sysGenComplaintId = this.sysGenComplaints[0].ID;
            this.getSysGenDetail(this.sysGenComplaintId);
          }
        },
        (err) => {
          this.notify.showNotification(
            err.error.message,
            'top',
            !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
            err.error.status
          );
        }
      );
  }

  getSysGenDetail(id) {
    this.similarCompLoading = true;
    this.sysGenComplaintId = id;
    this.noOfDocs = 0;
    this.detail_adsource = [];
    this.detail_platform = '';
    this.detail_channel = '';
    this.detail_addate = '';
    this.detail_link = '';
    this.longText1 = '';
    this.docURL = '';
    this.subscription = this.cs.getComplaint(id).subscribe(
      (res: any) => {
        this.sysGenDetail = res.data;
        if (this.sysGenDetail['EDITABLE'] == 1) {
          this.compSystemEdit = true;
        } else if (this.sysGenDetail['EDITABLE'] == 0) {
          this.compSystemEdit = false;
        }
        this.adMedium = res.data['ADVERTISEMENT_MEDIUM'];
        this.adDocs = res.data.ADVERTISEMENT_MEDIUM_DOCUMENT;
        if (this.adDocs.length > 0) {
          for (let doc of this.adDocs) {
            if (doc.ATTACHMENT_NAME.length > 20) {
              this.longText1 = '..';
            } else {
              this.longText1 = ' ';
            }
          }
        }
        this.compSourceId = this.sysGenDetail['COMPLAINT_SOURCE_ID'];
        this.id = this.sysGenDetail['ID'];
        if (this.sysGenDetail['ADVERTISEMENT_MEDIUM'].length != 0) {
          this.detail_adsource = this.sysGenDetail['ADVERTISEMENT_MEDIUM'];
          this.detail_platform =
            this.sysGenDetail['ADVERTISEMENT_MEDIUM'][0].PLATFORM_ID;
          this.detail_channel =
            this.sysGenDetail['ADVERTISEMENT_MEDIUM'][0].SOURCE_NAME;
          this.detail_addate =
            this.sysGenDetail['ADVERTISEMENT_MEDIUM'][0].DATE;
          this.detail_link =
            this.sysGenDetail['ADVERTISEMENT_MEDIUM'][0].SOURCE_URL;
          if (!!this.detail_link) {
            this.docUrl = this.bucketUrl + this.detail_link;
          } else {
            this.docURL = '';
          }
          this.detail_place =
            this.sysGenDetail['ADVERTISEMENT_MEDIUM'][0].SOURCE_PLACE;
          if (!!this.detail_link) {
            if (this.detail_link.length > 28) {
              this.longText1 = '..';
            } else {
              this.longText1 = ' ';
            }
          }
        }
        if (this.sysGenDetail.COMPLAINT_SOURCE_ID == 7) {
          this.translation_hyper = this.sysGenDetail.TRANSLATION_HYPERLINK;
          if (this.translation_hyper) {
            if (this.translation_hyper.length > 28) {
              this.longText2 = '..';
            } else {
              this.longText2 = '..';
            }
          }
        }
        if (this.sysGenDetail.CLAIMS.length != 0) {
          this.detail_objection = this.sysGenDetail.CLAIMS[0].KEY_OBJECTION;
        }
        for (
          let i = 0;
          i < res.data.ADVERTISEMENT_MEDIUM_DOCUMENT.length;
          i++
        ) {
          if (
            res.data.ADVERTISEMENT_MEDIUM_DOCUMENT[i].ATTACHMENT_SOURCE !== ''
          ) {
            this.noOfDocs += 1;
          }
        }

        this.getClassificationTag(
          this.sysGenDetail['COMPLAINT_DESCRIPTION'],
          this.docURL,
          this.sysGenDetail['BRAND_NAME'],
          this.sysGenDetail['PRODUCT_NAME'],
          this.sysGenDetail['COMPANY_ID'] == 0
            ? this.sysGenDetail['SUGGESTED_COMPANY_NAME']
            : this.sysGenDetail['COMPANY_NAME'],
          this.sysGenDetail['COMPLAINT_SOURCE_ID'],
          this.sysGenDetail['ID']
        );
        // if (!this.sysGenDetail['COMPANY_ID']) {
        //   this.getCompanyName(this.sysGenDetail['SUGGESTED_COMPANY_NAME']);
        // }
      },
      (err) => {
        this.notify.showNotification(
          err.error.message,
          'top',
          !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
          err.error.status
        );
      }
    );
  }

  getChatbotComplaints(pageNumber) {
    this.namsService.listChatbotList(pageNumber).subscribe(
      (chatbotList: any) => {
        this.chatbotComplaints = chatbotList.data;
        this.loading = false;
        this.lastData = chatbotList.data.length;
        if (!!this.chatbotComplaints && this.chatbotComplaints.length > 0) {
          this.chatbotComplaintId = this.chatbotComplaints[0].ID;
          this.zeroComplaints = false;
          this.getChatbotDetails(this.chatbotComplaintId);
        } else {
          this.zeroComplaints = true;
        }
      },
      (err) => {
        this.notify.showNotification(
          err.error.message,
          'top',
          !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
          err.error.status
        );
      }
    );
  }

  getChatbotDetails(id) {
    this.similarCompLoading = true;
    this.chatbotComplaintId = id;
    this.subscription = this.namsService.getChatbotDetails(id).subscribe(
      (res: any) => {
        this.chatbotDetails = res.data;
        if (this.chatbotDetails['EDITABLE'] == 1) {
          this.compChatbotEdit = true;
        } else if (this.chatbotDetails['EDITABLE'] == 0) {
          this.compChatbotEdit = false;
        }
        this.adDocs = res.data.ATTACHMENT_DOCUMENT;
        if (this.adDocs.length > 0) {
          for (let doc of this.adDocs) {
            if (doc.ATTACHMENT_SOURCE_NAME.length > 20) {
              this.longText1 = '..';
            } else {
              this.longText1 = ' ';
            }
          }
        }
        this.mobile = res.data.MOBILE;
        this.compSourceId = this.chatbotDetails['COMPLAINT_SOURCE_ID'];
        this.id = this.chatbotDetails['ID'];
        this.getClassificationTag(
          this.chatbotDetails['COMPLAINT_DESCRIPTION'],
          this.docURL,
          this.chatbotDetails['BRAND_NAME'],
          '',
          this.chatbotDetails['COMPANY_NAME'],
          this.chatbotDetails['COMPLAINT_SOURCE_ID'],
          this.chatbotDetails['ID']
        );
      },
      (err) => {
        this.notify.showNotification(
          err.error.message,
          'top',
          !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
          err.error.status
        );
      }
    );
  }

  getClassificationTag(
    desc,
    hyperLink,
    brand,
    product,
    company,
    compSourceId,
    id
  ) {
    this.subscription = this.cs.getComplaintClassification(desc, this.compSourceId, id).subscribe(
      (res) => {
        this.classificationTag = res['data']['ml_classification_name'];
        this.getSimilarityCheck(
          this.classificationTag,
          desc,
          hyperLink,
          brand,
          product,
          company,
          compSourceId,
          id
        );
      },
      (err) => {
        if (err.error.status == 401) {
          window.localStorage.clear();
          this.router.navigate(['auth/login']);
        }
        this.classificationTag = '-';
        this.getSimilarityCheck(
          '',
          desc,
          hyperLink,
          brand,
          product,
          company,
          compSourceId,
          id
        );
      }
    );
  }

  async getSimilarityCheck(
    classificationTag,
    desc,
    hyperLink,
    brand,
    product,
    company,
    compSourceId,
    id
  ) {
    let FunctionName = 'aw-cms-dev-lamfc-csc01';
    let request = {
      complaint_attachment_location: '',
      brand_name: brand,
      complaint_description: desc,
      ClassificationTag: classificationTag,
      s3_bucket_name: 'aw-cms-s3-files',
      product_name: product,
      company_name: company,
    };
    // let response = await this.cs.invokeLambda(FunctionName, request);
    this.subscription = this.cs
      .getSimilarityCheck(
        classificationTag,
        desc,
        hyperLink,
        brand,
        product,
        company,
        compSourceId,
        id,
        null
      )
      .subscribe(
        (res) => {
          this.similarCompLoading = false;
          this.similarityComplaintList = [];
          this.loading = false;
          if (
            Array.isArray(res.data.classification) &&
            res.data.classification[1] != undefined
          ) {
            this.msgvalue = res.data.classification[1];
            this.ml_data = res.data.classification[0];
            this.complaint_attachment_data =
              res.data.classification[0].complaint_attachment_data;
            this.similarityComplaintList = JSON.parse(
              JSON.stringify(res.data.classification)
            );
            this.similarityComplaintList.shift();
            if (
              Array.isArray(res.data.classification) &&
              res.data.classification.length > 2
            ) {
              this.extraSimilarComplaints = res.data.classification.length - 2;
            }
            if (
              this.msgvalue['complaint_identification'] === 'No Match found'
            ) {
              this.matchFound = false;
            } else {
              this.matchFound = true;
            }
          }
        },
        (err) => {
          this.similarCompLoading = false;
          this.loading = false;
          this.notify.showNotification(
            err.error.message,
            'top',
            !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
            err.error.status
          );
        }
      );
  }

  onComplaintChange(id) {
    this.subscription.unsubscribe();
    if (this.selectedTab == 0) {
      this.loading = true;
      this.incompleteData = false;
      this.whatsappData = false;
      this.getWhatsappDetail(id);
    } else if (this.selectedTab == 1) {
      this.loading = true;
      this.getMailDetail(id);
    } else if (this.selectedTab == 2) {
      this.loading = true;
      this.getSysGenDetail(id);
    } else if (this.selectedTab == 3) {
      this.loading = true;
      this.getChatbotDetails(id);
    }
  }

  async addNewComplaint(similarComp) {
    this.loading = true;
    await this.cs
      .addNewComplaint(
        similarComp,
        this.complaint_attachment_data,
        this.compSourceId,
        this.id
      )
      .toPromise()
      .then(
        async (res) => {
          this.notify.showNotification(
            res.message,
            'top',
            !!colorObj[res.status] ? colorObj[res.status] : 'success',
            res.status
          );
          this.similarCompLoading = true;
          this.searchPageNumber = 1;
          if (this.selectedTab == 0) {
            this.getWhatsappComplaint(this.searchPageNumber);
          } else if (this.selectedTab == 1) {
            this.getMailDetail(this.mailComplaintId);
          } else if (this.selectedTab == 2) {
            this.getSysGenComplaints(this.searchPageNumber);
          } else if (this.selectedTab == 3) {
            this.getChatbotComplaints(this.searchPageNumber);
          }
          this.getComplaintStats();
          this.loading = false;
        },
        (err) => {
          this.loading = false;
          this.notify.showNotification(
            err.error.message,
            'top',
            !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
            err.error.status
          );
        }
      );
  }

  onScrollDown() {
    this.listLoading = true;
    this.searchPageNumber++;
    if (this.selectedTab == 1) {
      this.getNextSet(this.pageToken);
    } else {
      this.getNextSet(this.searchPageNumber);
    }
  }

  getNextSet(pageNumber) {
    if (this.selectedTab == 0) {
      if (this.lastData == 10) {
        this.cs.getWhatsappComplaints(pageNumber).subscribe(
          (whatsappList) => {
            this.loading = false;
            this.lastData = whatsappList.data.length;
            this.whatsappComplaints = this.whatsappComplaints.concat(
              whatsappList.data
            );
            this.cd.detectChanges();
          },
          (err) => {
            this.notify.showNotification(
              err.error.message,
              'top',
              !!colorObj[err.error.status]
                ? colorObj[err.error.status]
                : 'error',
              err.error.status
            );
          }
        );
      }
    } else if (this.selectedTab == 1) {
      if (this.lastData == 10) {
        this.cs.getMailComplaints(pageNumber).subscribe(
          (mailList) => {
            this.loading = false;
            this.lastData = mailList.data.EMAILS.length;
            this.pageToken = mailList.data.PAGE_TOKEN;
            this.mailComplaints = this.mailComplaints.concat(
              mailList.data.EMAILS
            );
            this.cd.detectChanges();
          },
          (err) => {
            this.notify.showNotification(
              err.error.message,
              'top',
              !!colorObj[err.error.status]
                ? colorObj[err.error.status]
                : 'error',
              err.error.status
            );
          }
        );
      }
    } else if (this.selectedTab == 2) {
      if (this.lastData == 10) {
        this.cs
          .getSysGenComplaints(
            null,
            null,
            null,
            null,
            null,
            pageNumber,
            0,
            this.registered
          )
          .subscribe(
            (sysGenList) => {
              this.loading = false;
              this.lastData = sysGenList.data.length;
              this.sysGenComplaints = this.sysGenComplaints.concat(
                sysGenList.data
              );
              this.cd.detectChanges();
            },
            (err) => {
              this.notify.showNotification(
                err.error.message,
                'top',
                !!colorObj[err.error.status]
                  ? colorObj[err.error.status]
                  : 'error',
                err.error.status
              );
            }
          );
      }
    } else if (this.selectedTab == 3) {
      if (this.lastData == 10) {
        this.namsService.listChatbotList(pageNumber).subscribe(
          (res) => {
            this.listLoading = false;
            this.lastData = res.data.length;
            this.chatbotComplaints = this.chatbotComplaints.concat(res.data);
            this.cd.detectChanges();
          },
          (err) => {
            this.notify.showNotification(
              err.error.message,
              'top',
              !!colorObj[err.error.status]
                ? colorObj[err.error.status]
                : 'error',
              err.error.status
            );
          }
        );
      }
    }
  }

  showFilter(data) {
    this.msg = data;
    this.setmsg_count = 1;
    this.cs.setMsg(this.msg, this.setmsg_count);
    this.router.navigate(['/manage-cases']);
  }

  openConversationDialog() {
    let keyValue = "";
    if (this.selectedTab == 2) {
      keyValue = "inbox-system";
      this.conversationCompDetail = this.sysGenDetail;
    }
    else if (this.selectedTab == 3) {
      keyValue = "inbox-chatbot";
      this.conversationCompDetail = this.chatbotDetails;
    }
    const dialogRef = this.dialog.open(ComplaintConversationsComponent, {
      width: '1000px',
      height: 'auto',
      position: {
        top: '0px',
        left: '27vw'
      },
      data: { details: this.conversationCompDetail, from: keyValue },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
      }
    );
  }

  createComp() {
    if (this.selectedTab == 0) {
      if (this.whatsappData) {
        this.incompleteData = false;
        this.whatsappData = false;
        if (!this.whatsappDetail?.BRAND_NAME) {
          this.whatsappDetail['BRAND_NAME'] = this.whatsappForm.value.brand;
        }
        if (!this.whatsappDetail?.EMAIL_ID) {
          this.whatsappDetail['EMAIL_ID'] = this.whatsappForm.value.email;
        }
        if (!this.whatsappDetail?.MOBILE) {
          this.whatsappDetail['MOBILE'] = this.whatsappForm.value.mobile;
        }
        if (!this.whatsappDetail?.COMPLAINT_DESCRIPTION) {
          this.whatsappDetail['COMPLAINT_DESCRIPTION'] = this.whatsappForm.value.compDesc;
        }
        if (!this.whatsappDetail?.ADVERTISEMENT_DESCRIPTION) {
          this.whatsappDetail['ADVERTISEMENT_DESCRIPTION'] = this.whatsappForm.value.advDesc;
        }
      }
      this.cs.updateWhatsappComplaintId(this.whatsappId);
      this.cs.updateWhatsappComplaintObj(this.whatsappDetail);
      this.cs.updateStep('whatsapp');
      this.router.navigate(['/cases/manage-cases'], {
        state: { from: 'INBOX' },
      });
    } else if (this.selectedTab == 1) {
      this.cs.updateEmailComplaintId(this.mailComplaintId);
      this.cs.updateStep('email');
      this.router.navigate(['/cases/manage-cases'], {
        state: { from: 'INBOX' },
      });
    } else if (this.selectedTab == 2) {
      this.cs.updateSysComplaintId(this.sysGenComplaintId);
      this.cs.updateStep('sysGen');
      this.cs.updateBlankComplaintId = this.sysGenComplaintId;
      this.cs.updateComplaintType(this.sysGenDetail['COMPLAINT_TYPE_ID']);
      this.router.navigate(['/cases/manage-cases'], {
        state: { from: 'INBOX' },
      });
      // this.disabled = true;
      // this.cs.registerComplaint(this.sysGenComplaintId, this.ml_data).subscribe(
      //   (res) => {
      //     this.disabled =  false;
      //     this.notify.showNotification(
      //       res.message,
      //       'top',
      //       !!colorObj[res.status] ? colorObj[res.status] : 'success',
      //       res.status
      //     );
      //     this.searchPageNumber = 1;
      //     this.getSysGenComplaints(this.searchPageNumber);
      //   },
      //   (err) => {
      //     this.notify.showNotification(
      //       err.error.message,
      //       'top',
      //       !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
      //       err.error.status
      //     );
      //   }
      // );
    } else if (this.selectedTab == 3) {
      this.cs.updateChatbotComplaintId(this.chatbotComplaintId);
      this.cs.updateStep('chatbot');
      this.router.navigate(['/cases/manage-cases'], {
        state: { from: 'NAMS' },
      });
    }
    // this.cs.updateStep('inbox');
    // this.router.navigate(['/manage-cases']);
  }

  deleteComp() {
    this.confirmationMsg.title =
      'Are you sure you want to delete the complaint ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.idArray, title: this.confirmationMsg.title },
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe((dialogResult) => {
      if (dialogResult && dialogResult.state) {
        if (this.selectedTab == 0) {
          this.cs.deleteWhatsappComplaint(this.whatsappComplaintId).subscribe(
            (res) => {
              if (res) {
                this.notify.showNotification(
                  res.message,
                  'top',
                  !!colorObj[res.status] ? colorObj[res.status] : 'success',
                  res.status
                );
                this.searchPageNumber = 1;
                this.getWhatsappComplaint(this.searchPageNumber);
              }
            },
            (err) => {
              this.notify.showNotification(
                err.error.message,
                'top',
                !!colorObj[err.error.status]
                  ? colorObj[err.error.status]
                  : 'error',
                err.error.status
              );
            }
          );
        } else if (this.selectedTab == 2) {
          this.cs.deleteComplaint(this.sysGenComplaintId).subscribe(
            (res) => {
              if (res) {
                this.notify.showNotification(
                  res.message,
                  'top',
                  !!colorObj[res.status] ? colorObj[res.status] : 'success',
                  res.status
                );
                this.searchPageNumber = 1;
                this.getSysGenComplaints(this.searchPageNumber);
              }
            },
            (err) => {
              this.notify.showNotification(
                err.error.message,
                'top',
                !!colorObj[err.error.status]
                  ? colorObj[err.error.status]
                  : 'error',
                err.error.status
              );
            }
          );
        } else if (this.selectedTab == 3) {
          this.idArray.push(this.chatbotComplaintId.toString());
          this.namsService.deleteChatbotComplaint(this.idArray).subscribe(
            (res) => {
              if (res) {
                this.notify.showNotification(
                  res.message,
                  'top',
                  !!colorObj[res.status] ? colorObj[res.status] : 'success',
                  res.status
                );
                this.idArray = [];
                this.getChatbotComplaints(1);
              }
            },
            (err) => {
              this.notify.showNotification(
                err.error.message,
                'top',
                !!colorObj[err.error.status]
                  ? colorObj[err.error.status]
                  : 'error',
                err.error.status
              );
            }
          );
        }
        this.getComplaintStats();
      }
    });
  }

  searchComp() {
    this.router.navigate(['/manage-cases']);
  }

  complaintDetails(case_id) {
    this.parent_id = "";
    this.cs.getSimilarComplaints(this.parent_id, case_id).subscribe(res => {
      this.compDetails = res.data;
      if (this.compDetails.length != 0) {
        this.openSimilarComplaintDetail(case_id)
      }
      else {
        this.notify.showNotification(
          res.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status] : "warning"),
          res.status
        )
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    })
  }

  openSimilarComplaintDetail(caseId) {
    const dialogRef = this.dialog.open(SimilarComplaintDetailsComponent, {
      width: '1500px',
      height: '550px',
      data: { CASE_ID: caseId },
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe((data) => { });
  }

  openNewTab() {
    const dialogRef = this.dialog.open(ViewMediaFileComponent, {
      width: '476px',
      height: 'auto',
      data: { adMedium: this.adMedium, adDocs: this.adDocs },
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe((data) => { });
  }

  previewLink(source) {
    if (source.indexOf('https') == -1 && source.indexOf('http') == -1) {
      window.open(this.bucketUrl + source, '_blank');
    } else {
      window.open(source, '_blank');
    }
  }

  preview(source) {
    this.imgURL = this.bucketUrl + source;
    window.open(this.imgURL, 'window 1', '');
  }

  download(doc, source) {
    this.url = this.bucketUrl + source;
    this.cs
      .downloadFile(this.url)
      .subscribe((data) => {
        importedSaveAs(data, doc)
      }, err => {
        this.notify.showNotification(
          err.error.message,
          'top',
          !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
          err.error.status
        );
      });
  }

  downloadWhatsappDoc(url) {
    this.cs
      .downloadFile(this.url)
      .subscribe((data) => {
        importedSaveAs(data)
      }, err => {
        this.notify.showNotification(
          err.error.message,
          'top',
          !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
          err.error.status
        );
      });
  }

  addCompany(row, section) {
    let name = 'sysGenCompany';
    if (this.selectedTab == 3) {
      name = 'chatbot';
    } else if (this.selectedTab == 0) {
      name = 'whatsapp';
    } else if (this.selectedTab == 1) {
      name = 'email';
    }
    const dialogRef = this.dialog.open(AddEditCompanyComponent, {
      width: '610px',
      data: { row, name: name },
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe((data) => {
      if (data === 'refresh') {
        if (this.selectedTab == 0) {
          this.getWhatsappDetail(this.whatsappId);
        } else if (this.selectedTab == 1) {
          this.getMailDetail(this.mailComplaintId);
        } else if (this.selectedTab == 2) {
          this.getSysGenDetail(this.sysGenComplaintId);
        } else if (this.selectedTab == 3) {
          this.getChatbotDetails(this.chatbotComplaintId);
        }
      }
    });
  }

  getCompanyName(companyValue) {
    if (!!companyValue) {
      this.authService.getCompanies(companyValue).subscribe(res => {
        if (res.data.length > 0) {
          if (this.selectedTab == 0) {
            this.whatsappDetail['COMPANY_ID'] = res.data[0].ID;
            this.whatsappDetail['COMPANY_NAME'] = res.data[0].COMPANY_NAME;
          } else if (this.selectedTab == 1) {
            this.mailDetail['COMPANY_ID'] = res.data[0].ID;
            this.mailDetail['COMPANY_NAME'] = res.data[0].COMPANY_NAME;
          } else if (this.selectedTab == 2) {
            this.sysGenDetail['COMPANY_ID'] = res.data[0].ID;
            this.sysGenDetail['COMPANY_NAME'] = res.data[0].COMPANY_NAME
          }
        }
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
  }

  checkUrl(url) {
    if (!!url) {
      if (url.indexOf('https://') == -1 && url.indexOf('http://') == -1) {
        return true;
      } else {
        return false;
      }
    }
  }

  getComplaintStats() {
    this.cs.getInboxComplaintStats().subscribe((complaintsStatus: any) => {
      this.complaintStats = complaintsStatus.data;
      for (let item of this.complaintStats) {
        this.whatsappCount = item.WHATSAPP_COMPLAINTS;
        this.mailCount = item.EMAIL_COMPLAINTS;
        this.systemCount = item.SYSTEM_COMPLAINTS;
        this.chatbotCount = item.CHATBOT_COMPLAINTS;
      }
      if (this.mailCount == 0) {
        this.mailComplaints = [];
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  checkValidForm(cond) {
    if (this.whatsappForm.valid) {
      this.incompleteData = false;
    } else {
      this.incompleteData = true;
    }
  }

  editSystemChange(event) {
    let cond = event.checked == true ? 1 : 0;
    if (cond == 1) {
      this.compSystemEdit = true;
    }
    else {
      this.compSystemEdit = false;
    }
    this.cs.editSystemComplaint(cond, this.sysGenComplaintId).subscribe(res => {
      this.notify.showNotification(
        res.message,
        'top',
        !!colorObj[res.status] ? colorObj[res.status] : 'success',
        res.status
      );
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  editChatbotChange(event) {
    let cond = event.checked == true ? 1 : 0;
    if (cond == 1) {
      this.compChatbotEdit = true;
    }
    else {
      this.compChatbotEdit = false;
    }
    this.cs.editChatbotComplaint(cond, this.chatbotComplaintId).subscribe(res => {
      this.notify.showNotification(
        res.message,
        'top',
        !!colorObj[res.status] ? colorObj[res.status] : 'success',
        res.status
      );
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

}