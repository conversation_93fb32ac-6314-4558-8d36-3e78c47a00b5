import { SelectionModel } from '@angular/cdk/collections';
import { AfterViewInit, Component, ElementRef, HostListener, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource, _MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { element } from 'protractor';
import { BehaviorSubject, Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';
import { AuthService } from 'src/app/services/auth.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';
import { AddAdvertiserComponent } from '../add-advertiser/add-advertiser.component';
import { AddEditConsumerComponent } from '../add-edit-consumer/add-edit-consumer.component';
import { AddEditGeneralPublicComponent } from '../add-edit-general-public/add-edit-general-public.component';
import { AddEditGovBodyComponent } from '../add-edit-gov-body/add-edit-gov-body.component';
import { AddUserComponent } from '../add-user/add-user.component';
import { AdmindialogComponent } from '../admindialog/admindialog.component';
import { CreateCompanyComponent } from '../create-company/create-company.component';
import { EditAdvertiserComponent } from '../edit-advertiser/edit-advertiser.component';

@Component({
  selector: 'app-user-administration',
  templateUrl: './user-administration.component.html',
  styleUrls: ['./user-administration.component.css']
})
export class UserAdministrationComponent implements OnInit, AfterViewInit {

  pagename: String;
  userInfo: any;
  userName: any;
  consumerRespArray: Array<any> = [];
  generalPublicRespArray: Array<any> = [];
  govBodyRespArray: Array<any> = [];
  dataSource1: any = [];
  dataSource2: any = [];
  dataSource3: any = [];
  checked = true;
  isAdmin: boolean;
  noData: boolean = false;
  loading: boolean = true;
  confirmationMsg: any = {};
  selectedTab;
  KEYWORD = new FormControl(null);
  consumerPageNumber: number = 0;
  generalPublicPageNumber: number = 0;
  govBodyPageNumber: number = 0;
  private request$: Observable<any>;
  consumerUser: boolean;
  isChecked: boolean;
  generalPublicUser: boolean;
  govBodyUser: boolean;

  private _consumerUserData = new BehaviorSubject<any[]>([]);
  private ConsumerUserDataStore: { $consumerUserData: any[] } = { $consumerUserData: [] };
  readonly $consumerUserData = this._consumerUserData.asObservable();

  private _generalPublicUserData = new BehaviorSubject<any[]>([]);
  private GeneralPublicUserDataStore: { $generalPublicUserData: any[] } = { $generalPublicUserData: [] };
  readonly $generalPublicUserData = this._generalPublicUserData.asObservable();

  private _govBodyUserData = new BehaviorSubject<any[]>([]);
  private GovBodyUserDataStore: { $govBodyUserData: any[] } = { $govBodyUserData: [] };
  readonly $govBodyUserData = this._govBodyUserData.asObservable();

  totalCount: any;
  startIndex: number;
  pageSize: number = 10;
  lastIndex: number;
  rangeLabel: string;
  limit: number = 0;
  lastBtn: any;
  consumerFilterArray: any[];
  consumerSortedArray: any[];
  generalPublicFilterArray: any[];
  generalPublicSortedArray: any[];
  govBodyFilterArray: any[];
  govBodySortedArray: any[];
  consumerFilter: boolean = false;
  generalPublicFilter: boolean = false;
  govBodyFilter: boolean = false;
  fname_asc: boolean = false;
  fname_desc: boolean = false;
  lname_asc: boolean = false;
  lname_desc: boolean = false;
  email_asc: boolean = false;
  email_desc: boolean = false;
  dept_asc: boolean = false;
  dept_desc: boolean = false;
  role_asc: boolean = false;
  role_desc: boolean = false;
  status_asc: boolean = false;
  status_desc: boolean = false;
  extcompany_asc: boolean = false;
  extcompany_desc: boolean = false;
  sortingKey: any = '';
  sortingOrder: any = '';
  dialogRef: any;
  currentIndex: any;
  adminIndex: any;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }
  }

  constructor(public dialog: MatDialog,
    private router: Router,
    private authService: AuthService,
    private notify: NotificationService,
    private el: ElementRef<HTMLElement>) { }

  @ViewChildren(MatPaginator) paginator = new QueryList<MatPaginator>();
  selection = new SelectionModel<any>(true, []);
  displayedColumns1: string[] = ['first_name', 'last_name', 'email', 'contact', 'org_name', 'action'];
  displayedColumns2: string[] = ['first_name', 'last_name', 'email', 'contact', 'pincode', 'action'];
  displayedColumns3: string[] = ['first_name', 'last_name', 'email', 'contact', 'gov_body', 'action'];

  ngOnInit(): void {
    this.pagename = "Administration : User Management";
    this.selectedTab = 0;
    this.consumerUser = true;
    this.generalPublicUser = false;
    this.govBodyUser = false;
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.userName = this.userInfo.firstName;
  }

  ngAfterViewInit() {
    if (this.selectedTab == 0) {
      this.KEYWORD.setValue('');
      this.getConsumerOrganisationUsers();
    } else if (this.selectedTab == 1) {
      this.KEYWORD.setValue('');
      this.getGeneralPublicUsers();
    } else if (this.selectedTab == 2) {
      this.KEYWORD.setValue('');
      this.getGovBodyUsers();
    }
  }


  changePage(event) {
    this.startIndex = (event.pageIndex * this.pageSize) + 1;
    this.lastIndex = this.startIndex + (this.pageSize - 1);
    if (this.lastIndex > this.totalCount) {
      this.lastIndex = this.totalCount;
    }
    this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
    if (this.selectedTab == 0) {
      if (this.consumerRespArray.length != 0) {
        this.getConsumerOrganisationUsers();
      }
      else if (this.consumerFilter == true) {
        this.getConsumerOrganisationUsersList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            if (res.data.length > 0) {
              this.consumerRespArray = [];
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.consumerPageNumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              if (this.consumerFilterArray.length != 0 && this.consumerSortedArray.length == 0) {
                this.consumerFilterArray = this.consumerFilterArray.concat(res.data[0].DATA);
                this.ConsumerUserDataStore.$consumerUserData = this.consumerFilterArray;
              }
              else if (this.consumerFilterArray.length == 0 && this.consumerSortedArray.length != 0) {
                this.consumerSortedArray = this.consumerSortedArray.concat(res.data[0].DATA);
                this.ConsumerUserDataStore.$consumerUserData = this.consumerSortedArray;
              }
              this.dataSource1.data = this.ConsumerUserDataStore.$consumerUserData;
              this.dataSource1 = new MatTableDataSource<any>(this.ConsumerUserDataStore.$consumerUserData);
              this._consumerUserData.next(Object.assign({}, this.ConsumerUserDataStore).$consumerUserData);
              this.dataSource1.paginator = this.paginator.toArray()[0];
              this.dataSource1.data.forEach(element => {
                if (element['USER_CONFIRMED'] == 1) {
                  element['status'] = 'Confirmed';
                } else {
                  element['status'] = 'Unconfirmed';
                }
                if (element['USER_ENABLED'] == 1) {
                  element['actionVal'] = true;
                } else {
                  element['actionVal'] = false;
                }
              });
            }
          }, err => {
            this.loading = false;
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
      }
      else {
        this.getConsumerOrganisationUsersList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            if (res.data.length > 0) {
              this.consumerRespArray = [];
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.consumerPageNumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              this.consumerSortedArray = this.consumerSortedArray.concat(res.data[0].DATA);
              this.ConsumerUserDataStore.$consumerUserData = this.consumerSortedArray;
              this.dataSource1.data = this.ConsumerUserDataStore.$consumerUserData
              this.dataSource1 = new MatTableDataSource<any>(this.ConsumerUserDataStore.$consumerUserData);
              this._consumerUserData.next(Object.assign({}, this.ConsumerUserDataStore).$consumerUserData);
              this.dataSource1.paginator = this.paginator.toArray()[0];
              this.dataSource1.data.forEach(element => {
                if (element['USER_CONFIRMED'] == 1) {
                  element['status'] = 'Confirmed';
                } else {
                  element['status'] = 'Unconfirmed';
                }
                if (element['USER_ENABLED'] == 1) {
                  element['actionVal'] = true;
                } else {
                  element['actionVal'] = false;
                }
              });
            }
          }, err => {
            this.loading = false;
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
      }
    }
    else if (this.selectedTab == 1) {
      if (this.generalPublicRespArray.length != 0) {
        this.getGeneralPublicUsers();
      }
      else if (this.generalPublicFilter == true) {
        this.getGeneralPublicUsersList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            if (res.data.length > 0) {
              this.generalPublicRespArray = [];
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.generalPublicPageNumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              if (this.generalPublicFilterArray.length != 0 && this.generalPublicSortedArray.length == 0) {
                this.generalPublicFilterArray = this.generalPublicFilterArray.concat(res.data[0].DATA);
                this.GeneralPublicUserDataStore.$generalPublicUserData = this.generalPublicFilterArray;
              }
              else if (this.generalPublicFilterArray.length == 0 && this.generalPublicSortedArray.length != 0) {
                this.generalPublicSortedArray = this.generalPublicSortedArray.concat(res.data[0].DATA);
                this.GeneralPublicUserDataStore.$generalPublicUserData = this.generalPublicSortedArray;
              }
              this.dataSource2.data = this.GeneralPublicUserDataStore.$generalPublicUserData;
              this.dataSource2 = new MatTableDataSource<any>(this.GeneralPublicUserDataStore.$generalPublicUserData);
              this._generalPublicUserData.next(Object.assign({}, this.GeneralPublicUserDataStore).$generalPublicUserData);
              this.dataSource2.paginator = this.paginator.toArray()[1];
              this.dataSource2.data.forEach(element => {
                if (element['USER_CONFIRMED'] == 1) {
                  element['status'] = 'Confirmed';
                } else {
                  element['status'] = 'Unconfirmed';
                }
                if (element['USER_ENABLED'] == 1) {
                  element['actionVal'] = true;
                } else {
                  element['actionVal'] = false;
                }
              });
            }
          }, err => {
            this.loading = false;
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
      }
      else {
        this.getGeneralPublicUsersList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            if (res.data.length > 0) {
              this.generalPublicRespArray = [];
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.generalPublicPageNumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              this.generalPublicSortedArray = this.generalPublicSortedArray.concat(res.data[0].DATA);
              this.GeneralPublicUserDataStore.$generalPublicUserData = this.generalPublicSortedArray;
              this.dataSource2.data = this.GeneralPublicUserDataStore.$generalPublicUserData;
              this.dataSource2 = new MatTableDataSource<any>(this.GeneralPublicUserDataStore.$generalPublicUserData);
              this._generalPublicUserData.next(Object.assign({}, this.GeneralPublicUserDataStore).$generalPublicUserData);
              this.dataSource2.paginator = this.paginator.toArray()[1];
              this.dataSource2.data.forEach(element => {
                if (element['USER_CONFIRMED'] == 1) {
                  element['status'] = 'Confirmed';
                } else {
                  element['status'] = 'Unconfirmed';
                }
                if (element['USER_ENABLED'] == 1) {
                  element['actionVal'] = true;
                } else {
                  element['actionVal'] = false;
                }
              });
            }
          }, err => {
            this.loading = false;
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
      }
    }
    else if (this.selectedTab == 2) {
      if (this.govBodyRespArray.length != 0) {
        this.getGovBodyUsers();
      }
      else if (this.govBodyFilter == true) {
        this.getGovBodyUsersList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            if (res.data.length > 0) {
              this.govBodyRespArray = [];
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.govBodyPageNumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              if (this.govBodyFilterArray.length != 0 && this.govBodySortedArray.length == 0) {
                this.govBodyFilterArray = this.govBodyFilterArray.concat(res.data[0].DATA);
                this.GovBodyUserDataStore.$govBodyUserData = this.govBodyFilterArray;
              }
              else if (this.govBodyFilterArray.length == 0 && this.govBodySortedArray.length != 0) {
                this.govBodySortedArray = this.govBodySortedArray.concat(res.data[0].DATA);
                this.GovBodyUserDataStore.$govBodyUserData = this.govBodySortedArray;
              }
              this.dataSource3.data = this.GovBodyUserDataStore.$govBodyUserData;
              this.dataSource3 = new MatTableDataSource<any>(this.GovBodyUserDataStore.$govBodyUserData);
              this._govBodyUserData.next(Object.assign({}, this.GovBodyUserDataStore).$govBodyUserData);
              this.dataSource3.paginator = this.paginator.toArray()[2];
              this.dataSource3.data.forEach(element => {
                if (element['USER_CONFIRMED'] == 1) {
                  element['status'] = 'Confirmed';
                } else {
                  element['status'] = 'Unconfirmed';
                }
                if (element['USER_ENABLED'] == 1) {
                  element['actionVal'] = true;
                } else {
                  element['actionVal'] = false;
                }
              });
            }
          }, err => {
            this.loading = false;
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
      }
      else {
        this.getGovBodyUsersList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            if (res.data.length > 0) {
              this.govBodyRespArray = [];
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.govBodyPageNumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              this.govBodySortedArray = this.govBodySortedArray.concat(res.data[0].DATA);
              this.GovBodyUserDataStore.$govBodyUserData = this.govBodySortedArray;
              this.dataSource3.data = this.GovBodyUserDataStore.$govBodyUserData;
              this.dataSource3 = new MatTableDataSource<any>(this.GovBodyUserDataStore.$govBodyUserData);
              this._govBodyUserData.next(Object.assign({}, this.GovBodyUserDataStore).$govBodyUserData);
              this.dataSource3.paginator = this.paginator.toArray()[2];
              this.dataSource3.data.forEach(element => {
                if (element['USER_CONFIRMED'] == 1) {
                  element['status'] = 'Confirmed';
                } else {
                  element['status'] = 'Unconfirmed';
                }
                if (element['USER_ENABLED'] == 1) {
                  element['actionVal'] = true;
                } else {
                  element['actionVal'] = false;
                }
              });
            }
          }, err => {
            this.loading = false;
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
      }
    }
  }

  private onFinalize(): void {
    this.request$ = null;
  }

  getConsumerOrganisationUsersList(value) {
    this.consumerPageNumber++;
    this.limit = 20;
    this.request$ = this.authService.getConsumerOrganisationUsersList(this.consumerPageNumber, value, this.limit, this.sortingKey, this.sortingOrder);
    return this.request$;
  }

  getConsumerOrganisationUsers() {
    this.getConsumerOrganisationUsersList(this.KEYWORD.value)
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((res: any) => {
        this.loading = false;
        if (res.data.length > 0) {
          this.totalCount = res.data[0].TOTAL_COUNT;
          if (this.consumerPageNumber == 1) {
            if (this.pageSize > this.totalCount) {
              this.rangeLabel = 1 + ' - ' + this.totalCount;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.consumerRespArray = this.consumerRespArray.concat(res.data[0].DATA);
          if (this.consumerRespArray.length == 0) {
            this.noData = true;
          } else {
            this.noData = false;
          }
          this.ConsumerUserDataStore.$consumerUserData = this.consumerRespArray;
          this.dataSource1.data = this.ConsumerUserDataStore.$consumerUserData;
          this.dataSource1 = new MatTableDataSource<any>(this.ConsumerUserDataStore.$consumerUserData);
          this._consumerUserData.next(Object.assign({}, this.ConsumerUserDataStore).$consumerUserData);
          this.dataSource1.paginator = this.paginator.toArray()[0];
          this.dataSource1.data.forEach(element => {
            if (element['USER_CONFIRMED'] == 1) {
              element['status'] = 'Confirmed';
            } else {
              element['status'] = 'Unconfirmed';
            }
            if (element['USER_ENABLED'] == 1) {
              element['actionVal'] = true;
            } else {
              element['actionVal'] = false;
            }
          });
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      })
  }

  getGeneralPublicUsersList(value) {
    this.generalPublicPageNumber++;
    this.limit = 20;
    this.request$ = this.authService.getGeneralPublicUsersList(this.generalPublicPageNumber, value, this.limit, this.sortingKey, this.sortingOrder);
    return this.request$;
  }

  getGeneralPublicUsers() {
    this.getGeneralPublicUsersList(this.KEYWORD.value)
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((res: any) => {
        this.loading = false;
        if (res.data.length > 0) {
          this.totalCount = res.data[0].TOTAL_COUNT;
          if (this.generalPublicPageNumber == 1) {
            if (this.pageSize > this.totalCount) {
              this.rangeLabel = 1 + ' - ' + this.totalCount;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.generalPublicRespArray = this.generalPublicRespArray.concat(res.data[0].DATA);
          if (this.generalPublicRespArray.length == 0) {
            this.noData = true;
          } else {
            this.noData = false;
          }
          this.GeneralPublicUserDataStore.$generalPublicUserData = this.generalPublicRespArray;
          this.dataSource2.data = this.GeneralPublicUserDataStore.$generalPublicUserData;
          this.dataSource2 = new MatTableDataSource<any>(this.GeneralPublicUserDataStore.$generalPublicUserData);
          this._generalPublicUserData.next(Object.assign({}, this.GeneralPublicUserDataStore).$generalPublicUserData);
          this.dataSource2.paginator = this.paginator.toArray()[1];
          this.dataSource2.data.forEach(element => {
            if (element['USER_CONFIRMED'] == 1) {
              element['status'] = 'Confirmed';
            } else {
              element['status'] = 'Unconfirmed';
            }
            if (element['USER_ENABLED'] == 1) {
              element['actionVal'] = true;
            } else {
              element['actionVal'] = false;
            }
          });
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      })
  }

  getGovBodyUsersList(value) {
    this.govBodyPageNumber++;
    this.limit = 20;
    this.request$ = this.authService.getGovBodyUsersList(this.govBodyPageNumber, value, this.limit, this.sortingKey, this.sortingOrder);
    return this.request$;
  }

  getGovBodyUsers() {
    this.getGovBodyUsersList(this.KEYWORD.value)
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((res: any) => {
        this.loading = false;
        if (res.data.length > 0) {
          this.totalCount = res.data[0].TOTAL_COUNT;
          if (this.govBodyPageNumber == 1) {
            if (this.pageSize > this.totalCount) {
              this.rangeLabel = 1 + ' - ' + this.totalCount;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.govBodyRespArray = this.govBodyRespArray.concat(res.data[0].DATA);
          if (this.govBodyRespArray.length == 0) {
            this.noData = true;
          } else {
            this.noData = false;
          }
          this.GovBodyUserDataStore.$govBodyUserData = this.govBodyRespArray;
          this.dataSource3.data = this.GovBodyUserDataStore.$govBodyUserData;
          this.dataSource3 = new MatTableDataSource<any>(this.GovBodyUserDataStore.$govBodyUserData);
          this._govBodyUserData.next(Object.assign({}, this.GovBodyUserDataStore).$govBodyUserData);
          this.dataSource3.paginator = this.paginator.toArray()[2];
          this.dataSource3.data.forEach(element => {
            if (element['USER_CONFIRMED'] == 1) {
              element['status'] = 'Confirmed';
            } else {
              element['status'] = 'Unconfirmed';
            }
            if (element['USER_ENABLED'] == 1) {
              element['actionVal'] = true;
            } else {
              element['actionVal'] = false;
            }
          });
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      })
  }

  rejectUser(row, key) {
    this.confirmationMsg.title = 'Are you sure you want to reject the user ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: row.C_ID, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.authService
          .deleteUser(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
               if (key == "Consumer") {
                this.ConsumerUserDataStore.$consumerUserData.forEach((t: any, i) => {
                  if (t.ID === row.ID) {
                    this.ConsumerUserDataStore.$consumerUserData.splice(i, 1);
                  }
                });
                this._consumerUserData.next(Object.assign({}, this.ConsumerUserDataStore).$consumerUserData);
                this.$consumerUserData.subscribe((res) => {
                  this.consumerRespArray = res;
                  this.dataSource1 = new MatTableDataSource(res);
                  this.dataSource1.paginator = this.paginator.toArray()[0];
                })
                this.totalCount = this.totalCount - 1;
                if (this.pageSize > this.totalCount) {
                  this.pageSize = this.totalCount;
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
                if (this.lastIndex > this.totalCount) {
                  this.lastIndex = this.totalCount;
                  this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                }
              }
              else if (key == "General") {
                this.GeneralPublicUserDataStore.$generalPublicUserData.forEach((t: any, i) => {
                  if (t.ID === row.ID) {
                    this.GeneralPublicUserDataStore.$generalPublicUserData.splice(i, 1);
                  }
                });
                this._generalPublicUserData.next(Object.assign({}, this.GeneralPublicUserDataStore).$generalPublicUserData);
                this.$generalPublicUserData.subscribe((res) => {
                  this.generalPublicRespArray = res;
                  this.dataSource2 = new MatTableDataSource(res);
                  this.dataSource2.paginator = this.paginator.toArray()[1];
                })
                this.totalCount = this.totalCount - 1;
                if (this.pageSize > this.totalCount) {
                  this.pageSize = this.totalCount;
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
                if (this.lastIndex > this.totalCount) {
                  this.lastIndex = this.totalCount;
                  this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                }
              }
              else if (key == "GovBody") {
                this.GovBodyUserDataStore.$govBodyUserData.forEach((t: any, i) => {
                  if (t.ID === row.ID) {
                    this.GovBodyUserDataStore.$govBodyUserData.splice(i, 1);
                  }
                });
                this._govBodyUserData.next(Object.assign({}, this.GovBodyUserDataStore).$govBodyUserData);
                this.$govBodyUserData.subscribe((res) => {
                  this.govBodyRespArray = res;
                  this.dataSource3 = new MatTableDataSource(res);
                  this.dataSource3.paginator = this.paginator.toArray()[2];
                })
                this.totalCount = this.totalCount - 1;
                if (this.pageSize > this.totalCount) {
                  this.pageSize = this.totalCount;
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
                if (this.lastIndex > this.totalCount) {
                  this.lastIndex = this.totalCount;
                  this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                }
              }
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  onValChange(value, id, C_ID) {
    let action = 'enable';
    if (value.checked == false) {
      action = 'disable';
    }
    this.authService.enableDisableUser(C_ID, action).subscribe(res => {
      if (this.selectedTab == 0) {
        this.getConsumerUserById(id);
      } else if (this.selectedTab == 1) {
        this.getGeneralPublicUserById(id);
      } else if (this.selectedTab == 2) {
        this.getGovBodyUserById(id);
      }
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "warning"),
        err.error.status
      )
      // value.checked = !value.checked;
    })
  }

  getConsumerUserById(userId) {
    this.authService.getConsumerMemberById(userId).subscribe(res => {
      let userObj = res.data[0];
      this.ConsumerUserDataStore.$consumerUserData.forEach((t: any, i) => {
        if (t.ID === userObj.ID) {
          this.ConsumerUserDataStore.$consumerUserData[i] = userObj;
        }
      });
      this._consumerUserData.next(Object.assign({}, this.ConsumerUserDataStore).$consumerUserData);
      this.updateConsumerUsersTableData(userObj.ID);
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  getGeneralPublicUserById(userId) {
    this.authService.getGeneralPublicMemberById(userId).subscribe(res => {
      let userObj = res.data[0];
      this.GeneralPublicUserDataStore.$generalPublicUserData.forEach((t: any, i) => {
        if (t.ID === userObj.ID) {
          this.GeneralPublicUserDataStore.$generalPublicUserData[i] = userObj;
        }
      });
      this._generalPublicUserData.next(Object.assign({}, this.GeneralPublicUserDataStore).$generalPublicUserData);
      this.updateGeneralPublicUsersTableData(userObj.ID);
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  getGovBodyUserById(userId) {
    this.authService.getGovBodyMemberById(userId).subscribe(res => {
      let userObj = res.data[0];
      this.GovBodyUserDataStore.$govBodyUserData.forEach((t: any, i) => {
        if (t.ID === userObj.ID) {
          this.GovBodyUserDataStore.$govBodyUserData[i] = userObj;
        }
      });
      this._govBodyUserData.next(Object.assign({}, this.GovBodyUserDataStore).$govBodyUserData);
      this.updateGovBodyUsersTableData(userObj.ID);
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  updateConsumerUsersTableData(userId) {
    this.$consumerUserData.subscribe((res) => {
      this.consumerRespArray = res;
      this.dataSource1.data = res;
      this.dataSource1 = new MatTableDataSource(res);
      this.dataSource1.paginator = this.paginator.toArray()[0];
      this.dataSource1.data.forEach(element => {
        if (userId == element.ID) {
          if (element['USER_CONFIRMED'] == 1) {
            element['status'] = 'Confirmed';
          } else {
            element['status'] = 'Unconfirmed';
          }
          if (element['USER_ENABLED'] == 1) {
            element['actionVal'] = true;
          } else {
            element['actionVal'] = false;
          }
        }
      });
    })
  }

  updateGeneralPublicUsersTableData(userId) {
    this.$generalPublicUserData.subscribe((res) => {
      this.generalPublicRespArray = res;
      this.dataSource2.data = res;
      this.dataSource2 = new MatTableDataSource(res);
      this.dataSource2.paginator = this.paginator.toArray()[1];
      this.dataSource2.data.forEach(element => {
        if (userId == element.ID) {
          if (element['USER_CONFIRMED'] == 1) {
            element['status'] = 'Confirmed';
          } else {
            element['status'] = 'Unconfirmed';
          }
          if (element['USER_ENABLED'] == 1) {
            element['actionVal'] = true;
          } else {
            element['actionVal'] = false;
          }
        }
      });
    })
  }

  updateGovBodyUsersTableData(userId) {
    this.$govBodyUserData.subscribe((res) => {
      this.govBodyRespArray = res;
      this.dataSource3.data = res;
      this.dataSource3 = new MatTableDataSource(res);
      this.dataSource3.paginator = this.paginator.toArray()[2];
      this.dataSource3.data.forEach(element => {
        if (userId == element.ID) {
          if (element['USER_CONFIRMED'] == 1) {
            element['status'] = 'Confirmed';
          } else {
            element['status'] = 'Unconfirmed';
          }
          if (element['USER_ENABLED'] == 1) {
            element['actionVal'] = true;
          } else {
            element['actionVal'] = false;
          }
        }
      });
    })
  }



  addConsumerUser() {
    const dialogRef = this.dialog.open(AddEditConsumerComponent, {
      width: '610px',
      data: { isEdit: false },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'refresh') {
          this.consumerPageNumber = 0;
          this.consumerRespArray = [];
          this.dataSource1.data = '';
          this.rangeLabel = 1 + ' - ' + this.pageSize;
          this.getConsumerOrganisationUsers();
        }
      }
    );
  }

  editConsumerUser(row) {
    const dialogRef = this.dialog.open(AddEditConsumerComponent, {
      width: '610px',
      data: { isEdit: true, row },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'u-refresh') {
          this.getConsumerUserById(row.ID);
          dialogRef.close('refresh');
        }
        if (data === 'd-refresh') {
          this.removeConsumerUserFromStore(row.ID)
          dialogRef.close('refresh');
        }
      }
    );
  }

  removeConsumerUserFromStore(userId) {
    this.ConsumerUserDataStore.$consumerUserData.forEach((t: any, i) => {
      if (t.ID === userId) {
        this.ConsumerUserDataStore.$consumerUserData.splice(i, 1);
      }
    });
    this.totalCount = this.totalCount - 1;
    if (this.totalCount !== 0) {
      if (this.pageSize > this.totalCount) {
        this.pageSize = this.totalCount;
        this.rangeLabel = 1 + ' - ' + this.pageSize;
      }
      if (this.lastIndex > this.totalCount) {
        this.lastIndex = this.totalCount;
        this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
      }
    } else {
      this.noData = true;
    }
    this._consumerUserData.next(Object.assign({}, this.ConsumerUserDataStore).$consumerUserData);
    this.dataSource1 = new MatTableDataSource(this.ConsumerUserDataStore.$consumerUserData);
    this.dataSource1.paginator = this.paginator.toArray()[0];
  }

  removeGovBodyUserFromStore(userId) {
    this.GovBodyUserDataStore.$govBodyUserData.forEach((t: any, i) => {
      if (t.ID === userId) {
        this.GovBodyUserDataStore.$govBodyUserData.splice(i, 1);
      }
    });
    this.totalCount = this.totalCount - 1;
    if (this.totalCount !== 0) {
      if (this.pageSize > this.totalCount) {
        this.pageSize = this.totalCount;
        this.rangeLabel = 1 + ' - ' + this.pageSize;
      }
      if (this.lastIndex > this.totalCount) {
        this.lastIndex = this.totalCount;
        this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
      }
    } else {
      this.noData = true;
    }
    this._govBodyUserData.next(Object.assign({}, this.GovBodyUserDataStore).$govBodyUserData);
    this.dataSource3 = new MatTableDataSource(this.GovBodyUserDataStore.$govBodyUserData);
    this.dataSource3.paginator = this.paginator.toArray()[2];
  }

  addGeneralPublicUser() {
    const dialogRef = this.dialog.open(AddEditGeneralPublicComponent, {
      width: '610px',
      data: { isEdit: false },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'refresh') {
          this.generalPublicPageNumber = 0;
          this.generalPublicRespArray = [];
          this.dataSource2.data = '';
          this.rangeLabel = 1 + ' - ' + this.pageSize;
          this.getGeneralPublicUsers();
        }
      }
    );
  }

  editGeneralPublicUser(row) {
    const dialogRef = this.dialog.open(AddEditGeneralPublicComponent, {
      width: '610px',
      data: { isEdit: true, row },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'u-refresh') {
          this.getGeneralPublicUserById(row.ID);
          dialogRef.close('refresh');
        }
        if (data === 'd-refresh') {
          this.removeGeneralPublicUserFromStore(row.ID)
          dialogRef.close('refresh');
        }
      }
    );
  }

  removeGeneralPublicUserFromStore(userId) {
    this.GeneralPublicUserDataStore.$generalPublicUserData.forEach((t: any, i) => {
      if (t.ID === userId) {
        this.GeneralPublicUserDataStore.$generalPublicUserData.splice(i, 1);
      }
    });
    this.totalCount = this.totalCount - 1;
    if (this.totalCount !== 0) {
      if (this.pageSize > this.totalCount) {
        this.pageSize = this.totalCount;
        this.rangeLabel = 1 + ' - ' + this.pageSize;
      }
      if (this.lastIndex > this.totalCount) {
        this.lastIndex = this.totalCount;
        this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
      }
    } else {
      this.noData = true;
    }
    this._generalPublicUserData.next(Object.assign({}, this.GeneralPublicUserDataStore).$generalPublicUserData);
    this.dataSource2 = new MatTableDataSource(this.GeneralPublicUserDataStore.$generalPublicUserData);
    this.dataSource2.paginator = this.paginator.toArray()[1];
  }

  addGovBodyUser() {
    const dialogRef = this.dialog.open(AddEditGovBodyComponent, {
      width: '610px',
      data: { isEdit: false },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'refresh') {
          this.govBodyPageNumber = 0;
          this.govBodyRespArray = [];
          this.dataSource3.data = '';
          this.rangeLabel = 1 + ' - ' + this.pageSize;
          this.getGovBodyUsers();
        }
      }
    );
  }

  editGovBodyUser(row) {
    const dialogRef = this.dialog.open(AddEditGovBodyComponent, {
      width: '610px',
      data: { isEdit: true, row },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'u-refresh') {
          this.getGovBodyUserById(row.ID);
          dialogRef.close('refresh');
        }
        if (data === 'd-refresh') {
          this.removeGovBodyUserFromStore(row.ID)
          dialogRef.close('refresh');
        }
      }
    );
  }

  applyFilter(filterValue: string) {
  if (this.selectedTab == 0) {
      this.startIndex = null;
      this.lastIndex = null;
      this.pageSize = 10;
      this.totalCount = null;
      if (filterValue.length == 0) {
        this.consumerPageNumber = 0;
        this.noData = false;
        this.consumerFilterArray = [];
        this.dataSource1.data = '';
        this.KEYWORD.setValue('');
        this.consumerFilter = false;
        this.getConsumerOrganisationUsers();
      } else {
        this.consumerFilter = true;
        this.consumerRespArray = [];
        this.consumerPageNumber = 0;
        this.noData = false;
        this.KEYWORD.setValue(filterValue)
        this.filterConsumerUsers();
      }
    }
    else if (this.selectedTab == 1) {
      this.startIndex = null;
      this.lastIndex = null;
      this.pageSize = 10;
      this.totalCount = null;
      if (filterValue.length == 0) {
        this.generalPublicPageNumber = 0;
        this.noData = false;
        this.generalPublicFilterArray = [];
        this.dataSource2.data = '';
        this.KEYWORD.setValue('');
        this.generalPublicFilter = false;
        this.getGeneralPublicUsers();
      } else {
        this.generalPublicFilter = true;
        this.generalPublicRespArray = [];
        this.generalPublicPageNumber = 0;
        this.noData = false;
        this.KEYWORD.setValue(filterValue)
        this.filterGeneralPublicUsers();
      }
    }
    else if (this.selectedTab == 2) {
      this.startIndex = null;
      this.lastIndex = null;
      this.pageSize = 10;
      this.totalCount = null;
      if (filterValue.length == 0) {
        this.govBodyPageNumber = 0;
        this.noData = false;
        this.govBodyFilterArray = [];
        this.dataSource3.data = '';
        this.KEYWORD.setValue('');
        this.govBodyFilter = false;
        this.getGovBodyUsers();
      } else {
        this.govBodyFilter = true;
        this.govBodyRespArray = [];
        this.govBodyPageNumber = 0;
        this.noData = false;
        this.KEYWORD.setValue(filterValue)
        this.filterGovBodyUsers();
      }
    }
  }

  filterGovBodyUsers() {
    this.noData = false;
    this.dataSource3.data = '';
    this.getGovBodyUsersList(this.KEYWORD.value)
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((res: any) => {
        this.govBodyFilterArray = [];
        this.govBodyRespArray = [];
        this.govBodySortedArray = [];
        if (res.data.length > 0) {
          this.totalCount = res.data[0].TOTAL_COUNT;
          if (this.govBodyPageNumber == 1) {
            if (res.data[0].TOTAL_COUNT < this.pageSize) {
              this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.noData = false;
          this.govBodyFilterArray = this.govBodyFilterArray.concat(res.data[0].DATA);
          this.GovBodyUserDataStore.$govBodyUserData = this.govBodyFilterArray;
          this.dataSource3.data = this.GovBodyUserDataStore.$govBodyUserData
          this.dataSource3 = new MatTableDataSource<any>(this.GovBodyUserDataStore.$govBodyUserData);
          this.dataSource3.paginator = this.paginator.toArray()[2];
          this._govBodyUserData.next(Object.assign({}, this.GovBodyUserDataStore).$govBodyUserData);
          this.dataSource3.data.forEach(element => {
            if (element['USER_CONFIRMED'] == 1) {
              element['status'] = 'Confirmed';
            } else {
              element['status'] = 'Unconfirmed';
            }
            if (element['USER_ENABLED'] == 1) {
              element['actionVal'] = true;
            } else {
              element['actionVal'] = false;
            }
          });
        } else {
          this.noData = true;
          this.govBodyFilterArray = [];
          this.dataSource3.data = '';
        }
      })
  }

  filterGeneralPublicUsers() {
    this.noData = false;
    this.dataSource2.data = '';
    this.getGeneralPublicUsersList(this.KEYWORD.value)
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((res: any) => {
        this.generalPublicRespArray = [];
        this.generalPublicSortedArray = [];
        this.generalPublicFilterArray = [];
        if (res.data.length > 0) {
          this.totalCount = res.data[0].TOTAL_COUNT;
          if (this.generalPublicPageNumber == 1) {
            if (res.data[0].TOTAL_COUNT < this.pageSize) {
              this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.noData = false;
          this.generalPublicFilterArray = this.generalPublicFilterArray.concat(res.data[0].DATA);
          this.GeneralPublicUserDataStore.$generalPublicUserData = this.generalPublicFilterArray;
          this.dataSource2.data = this.GeneralPublicUserDataStore.$generalPublicUserData
          this.dataSource2= new MatTableDataSource<any>(this.GeneralPublicUserDataStore.$generalPublicUserData);
          this.dataSource2.paginator = this.paginator.toArray()[1];
          this._generalPublicUserData.next(Object.assign({}, this.GeneralPublicUserDataStore).$generalPublicUserData);
          this.dataSource2.data.forEach(element => {
            if (element['USER_CONFIRMED'] == 1) {
              element['status'] = 'Confirmed';
            } else {
              element['status'] = 'Unconfirmed';
            }
            if (element['USER_ENABLED'] == 1) {
              element['actionVal'] = true;
            } else {
              element['actionVal'] = false;
            }
          });
        } else {
          this.noData = true;
          this.generalPublicFilterArray = [];
          this.dataSource2.data = '';
        }
      })
  }

  filterConsumerUsers() {
    this.noData = false;
    this.dataSource1.data = '';
    this.getConsumerOrganisationUsersList(this.KEYWORD.value)
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((res: any) => {
        this.consumerRespArray = [];
        this.consumerSortedArray = [];
        this.consumerFilterArray = [];
        if (res.data.length > 0) {
          this.totalCount = res.data[0].TOTAL_COUNT;
          if (this.consumerPageNumber == 1) {
            if (res.data[0].TOTAL_COUNT < this.pageSize) {
              this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.noData = false;
          this.consumerFilterArray = this.consumerFilterArray.concat(res.data[0].DATA);
          this.ConsumerUserDataStore.$consumerUserData = this.consumerFilterArray;
          this.dataSource1.data = this.ConsumerUserDataStore.$consumerUserData
          this.dataSource1 = new MatTableDataSource<any>(this.ConsumerUserDataStore.$consumerUserData);
          this.dataSource1.paginator = this.paginator.toArray()[0];
          this._consumerUserData.next(Object.assign({}, this.ConsumerUserDataStore).$consumerUserData);
          this.dataSource1.data.forEach(element => {
            if (element['USER_CONFIRMED'] == 1) {
              element['status'] = 'Confirmed';
            } else {
              element['status'] = 'Unconfirmed';
            }
            if (element['USER_ENABLED'] == 1) {
              element['actionVal'] = true;
            } else {
              element['actionVal'] = false;
            }
          });
        } else {
          this.noData = true;
          this.consumerFilterArray = [];
          this.dataSource1.data = '';
        }
      })
  }

  approve(row, key) {
    this.confirmationMsg.title = 'Are you sure you want to approve the user ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: row.C_ID, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.authService
          .approveUser(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
            if (key == "Consumer") {
                this.getConsumerUserById(row.ID);
              }
              else if (key == "General") {
                this.getGeneralPublicUserById(row.ID);
              }
              else if (key == "GovBody") {
                this.getGovBodyUserById(row.ID);
              }
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  onTabChanged(event) {
    this.selectedTab = event.index;
    if (this.selectedTab == 0) {
      this.rangeLabel = '';
      this.totalCount = '';
      this.KEYWORD.setValue('');
      this.consumerSortedArray = [];
      this.consumerRespArray = [];
      this.fname_asc = false;
      this.fname_desc = false;
      this.lname_asc = false;
      this.lname_desc = false;
      this.email_asc = false;
      this.email_desc = false;
      this.consumerUser = true;
      this.generalPublicUser = false;
      this.govBodyUser = false;
      this.loading = true;
      this.consumerPageNumber = 0;
      this.dataSource1.data = '';
      this.dataSource1 = new MatTableDataSource(this.dataSource1.data);
      this.dataSource1.paginator = this.paginator.toArray()[0];
      this.sortingKey = '';
      this.sortingOrder = '';
      this.getConsumerOrganisationUsers();
    }
    else if (this.selectedTab == 1) {
      this.rangeLabel = '';
      this.totalCount = '';
      this.KEYWORD.setValue('');
      this.generalPublicRespArray = [];
      this.generalPublicSortedArray = [];
      this.fname_asc = false;
      this.fname_desc = false;
      this.lname_asc = false;
      this.lname_desc = false;
      this.email_asc = false;
      this.email_desc = false;
      this.consumerUser = false;
      this.generalPublicUser = true;
      this.govBodyUser = false;
      this.loading = true;
      this.generalPublicPageNumber = 0;
      this.dataSource2.data = '';
      this.dataSource2 = new MatTableDataSource(this.dataSource2.data);
      this.dataSource2.paginator = this.paginator.toArray()[1];
      this.sortingKey = '';
      this.sortingOrder = '';
      this.getGeneralPublicUsers();
    }
    else if (this.selectedTab == 2) {
      this.rangeLabel = '';
      this.totalCount = '';
      this.KEYWORD.setValue('');
      this.govBodyFilterArray = [];
      this.govBodyRespArray = [];
      this.fname_asc = false;
      this.fname_desc = false;
      this.lname_asc = false;
      this.lname_desc = false;
      this.email_asc = false;
      this.email_desc = false;
      this.consumerUser = false;
      this.generalPublicUser = false;
      this.govBodyUser = true;
      this.loading = true;
      this.govBodyPageNumber = 0;
      this.dataSource3.data = '';
      this.dataSource3 = new MatTableDataSource(this.dataSource3.data);
      this.dataSource3.paginator = this.paginator.toArray()[2];
      this.sortingKey = '';
      this.sortingOrder = '';
      this.getGovBodyUsers();
    }
  }

  sortConsumerComplaints(key, order) {
    this.sortingKey = key;
    this.sortingOrder = order;
    this.consumerPageNumber = 0;
    if (order != '') {
      if (order == 'ASC') {
        if (key == 'FIRST_NAME') {
          this.fname_asc = true;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'LAST_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = true;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'EMAIL_ID') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = true;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
      }
      else if (order == 'DESC') {
        if (key == 'FIRST_NAME') {
          this.fname_asc = false;
          this.fname_desc = true;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'LAST_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = true;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'EMAIL_ID') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = true;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
      }
      this.noData = false;
      this.dataSource1.data = '';
      if (this.consumerFilter == true) {
        this.filterConsumerUsers();
      }
      else {
        this.noData = false;
        this.dataSource1.data = '';
        this.getConsumerOrganisationUsersList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            this.consumerRespArray = [];
            this.consumerFilterArray = [];
            this.consumerSortedArray = [];
            if (res.data.length > 0) {
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.consumerPageNumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              this.noData = false;
              this.consumerSortedArray = this.consumerSortedArray.concat(res.data[0].DATA);
              this.ConsumerUserDataStore.$consumerUserData = this.consumerSortedArray;
              this.dataSource1.data = this.ConsumerUserDataStore.$consumerUserData;
              this.dataSource1 = new MatTableDataSource<any>(this.ConsumerUserDataStore.$consumerUserData);
              this.dataSource1.paginator = this.paginator.toArray()[0];
              this._consumerUserData.next(Object.assign({}, this.ConsumerUserDataStore).$consumerUserData);
              this.dataSource1.data.forEach(element => {
                if (element['USER_CONFIRMED'] == 1) {
                  element['status'] = 'Confirmed';
                } else {
                  element['status'] = 'Unconfirmed';
                }
                if (element['USER_ENABLED'] == 1) {
                  element['actionVal'] = true;
                } else {
                  element['actionVal'] = false;
                }
              });
            }
          })
      }
    }
    else if (order == '') {
      this.fname_asc = false;
      this.fname_desc = false;
      this.lname_asc = false;
      this.lname_desc = false;
      this.email_asc = false;
      this.email_desc = false;
      if (this.consumerFilter == true) {
        this.filterConsumerUsers();
      }
      else {
        this.dataSource1.data = [];
        this.consumerRespArray = [];
        this.noData = false;
        this.consumerPageNumber = 0;
        this.getConsumerOrganisationUsers();
      }
    }
  }

  sortGeneralPublicComplaints(key, order) {
    this.sortingKey = key;
    this.sortingOrder = order;
    this.generalPublicPageNumber = 0;
    if (order != '') {
      if (order == 'ASC') {
        if (key == 'FIRST_NAME') {
          this.fname_asc = true;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'LAST_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = true;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'EMAIL_ID') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = true;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
      }
      else if (order == 'DESC') {
        if (key == 'FIRST_NAME') {
          this.fname_asc = false;
          this.fname_desc = true;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'LAST_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = true;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'EMAIL_ID') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = true;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
      }
      this.noData = false;
      this.dataSource2.data = '';
      if (this.generalPublicFilter == true) {
        this.filterGeneralPublicUsers();
      }
      else {
        this.noData = false;
        this.dataSource2.data = '';
        this.getGeneralPublicUsersList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            this.generalPublicRespArray = [];
            this.generalPublicFilterArray = [];
            this.generalPublicSortedArray = [];
            if (res.data.length > 0) {
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.generalPublicPageNumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              this.noData = false;
              this.generalPublicSortedArray = this.generalPublicSortedArray.concat(res.data[0].DATA);
              this.GeneralPublicUserDataStore.$generalPublicUserData = this.generalPublicSortedArray;
              this.dataSource2.data = this.GeneralPublicUserDataStore.$generalPublicUserData;
              this.dataSource2 = new MatTableDataSource<any>(this.GeneralPublicUserDataStore.$generalPublicUserData);
              this.dataSource2.paginator = this.paginator.toArray()[1];
              this._generalPublicUserData.next(Object.assign({}, this.GeneralPublicUserDataStore).$generalPublicUserData);
              this.dataSource2.data.forEach(element => {
                if (element['USER_CONFIRMED'] == 1) {
                  element['status'] = 'Confirmed';
                } else {
                  element['status'] = 'Unconfirmed';
                }
                if (element['USER_ENABLED'] == 1) {
                  element['actionVal'] = true;
                } else {
                  element['actionVal'] = false;
                }
              });
            }
          })
      }
    }
    else if (order == '') {
      this.fname_asc = false;
      this.fname_desc = false;
      this.lname_asc = false;
      this.lname_desc = false;
      this.email_asc = false;
      this.email_desc = false;
      if (this.generalPublicFilter == true) {
        this.filterGeneralPublicUsers();
      }
      else {
        this.dataSource2.data = [];
        this.generalPublicRespArray = [];
        this.noData = false;
        this.generalPublicPageNumber = 0;
        this.getGeneralPublicUsers();
      }
    }
  }

  sortGovBodyComplaints(key, order) {
    this.sortingKey = key;
    this.sortingOrder = order;
    this.govBodyPageNumber = 0;
    if (order != '') {
      if (order == 'ASC') {
        if (key == 'FIRST_NAME') {
          this.fname_asc = true;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'LAST_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = true;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'EMAIL_ID') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = true;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
      }
      else if (order == 'DESC') {
        if (key == 'FIRST_NAME') {
          this.fname_asc = false;
          this.fname_desc = true;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'LAST_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = true;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'EMAIL_ID') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = true;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
      }
      this.noData = false;
      this.dataSource3.data = '';
      if (this.govBodyFilter == true) {
        this.filterGovBodyUsers();
      }
      else {
        this.noData = false;
        this.dataSource3.data = '';
        this.getGovBodyUsersList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            this.govBodyRespArray = [];
            this.govBodyFilterArray = [];
            this.govBodySortedArray = [];
            if (res.data.length > 0) {
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.govBodyPageNumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              this.noData = false;
              this.govBodySortedArray = this.govBodySortedArray.concat(res.data[0].DATA);
              this.GovBodyUserDataStore.$govBodyUserData = this.govBodySortedArray;
              this.dataSource3.data = this.GovBodyUserDataStore.$govBodyUserData;
              this.dataSource3 = new MatTableDataSource<any>(this.GovBodyUserDataStore.$govBodyUserData);
              this.dataSource3.paginator = this.paginator.toArray()[2];
              this._govBodyUserData.next(Object.assign({}, this.GovBodyUserDataStore).$govBodyUserData);
              this.dataSource3.data.forEach(element => {
                if (element['USER_CONFIRMED'] == 1) {
                  element['status'] = 'Confirmed';
                } else {
                  element['status'] = 'Unconfirmed';
                }
                if (element['USER_ENABLED'] == 1) {
                  element['actionVal'] = true;
                } else {
                  element['actionVal'] = false;
                }
              });
            }
          })
      }
    }
    else if (order == '') {
      this.fname_asc = false;
      this.fname_desc = false;
      this.lname_asc = false;
      this.lname_desc = false;
      this.email_asc = false;
      this.email_desc = false;
      if (this.govBodyFilter == true) {
        this.filterGovBodyUsers();
      }
      else {
        this.dataSource3.data = [];
        this.govBodyRespArray = [];
        this.noData = false;
        this.govBodyPageNumber = 0;
        this.getGovBodyUsers();
      }
    }
  }
}