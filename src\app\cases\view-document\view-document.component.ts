import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { environment } from 'src/environments/environment';
import { saveAs as importedSaveAs } from "file-saver";
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';

@Component({
  selector: 'app-view-document',
  templateUrl: './view-document.component.html',
  styleUrls: ['./view-document.component.scss']
})
export class ViewDocumentComponent implements OnInit {

  claimsDocuments: any = [];
  imgURL: any;
  public bucketUrl = `${environment.BUCKET_URL}`;
  url: string;

  constructor(
    private cs: ComplaintsService,
    private dialogRef: MatDialogRef<ViewDocumentComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private notify: NotificationService
  ) { }

  ngOnInit(): void {
    this.getClaimsDocuments();
  }

  getClaimsDocuments() {
    this.cs.getClaimsDocuments(this.data).subscribe((document: any) => {
      this.claimsDocuments = document.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    });
  }

  download(doc, source) {
    this.url = this.bucketUrl + source;
    this.cs.downloadFile(this.url).subscribe(data => {
      importedSaveAs(data, doc)
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    });
  }

  preview(doc, source) {
    this.imgURL = this.bucketUrl + source;
    window.open(this.imgURL, 'window 1', '');
  }

}