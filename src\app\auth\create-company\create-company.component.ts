import { Component, HostListener, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { AuthService } from 'src/app/services/auth.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';
import { CompanyCreatedComponent } from '../company-created/company-created.component';

@Component({
  selector: 'app-create-company',
  templateUrl: './create-company.component.html',
  styleUrls: ['./create-company.component.scss']
})

export class CreateCompanyComponent implements OnInit {
  createcompany: FormGroup;
  user_id: number;
  companyList: any[];
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(private fb: FormBuilder,
    private dialogRef: MatDialogRef<CreateCompanyComponent>,
    public dialog: MatDialog,
    private authService: AuthService,
    private notify: NotificationService,
    @Inject(MAT_DIALOG_DATA) public data: any) {
    this.createcompany = this.fb.group({
      cname: ['', Validators.required],
      member_name: [''],
      member_email: [''],
      member_contact: [''],
      caddress: [''],
      about_company: ['']
    })
  }

  ngOnInit(): void {
    this.createcompany.get('cname')!.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0) {
          this.getCompanyList(value);
        }
      }, err => {
      })
    this.createcompany.controls['cname'].setValue(this.data.row.NEW_COMPANY_NAME);
    this.createcompany.controls['member_name'].setValue(this.data.row.FIRST_NAME + ' ' + this.data.row.LAST_NAME);
    this.createcompany.controls['member_email'].setValue(this.data.row.EMAIL_ID);
    this.createcompany.controls['member_contact'].setValue(this.data.row.MOBILE);
    this.user_id = this.data.row.ID;
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  getCompanyList(value) {
    this.authService.getCompanies(value).subscribe(res => {
      this.companyList = res.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  onSelectionChange(event) {
    this.createcompany.patchValue({ 'cname': event.option.value })
    this.companyList.forEach(element => {
      if (event.option.value === element.COMPANY_NAME) {
        this.createcompany.controls['cname'].setValue(element.COMPANY_NAME);
      }
    });
  }

  confirmCompany(model) {
    let obj = {
      "COMPANY_NAME": model.cname,
      "ADDRESS": model.caddress,
      "COMPANY_DESCRIPTION": model.about_company,
      "EMAIL_ID": model.member_email,
      "MOBILE": model.member_contact,
      "REG_FROM": "user",
      "USER_ID": this.user_id
    }
    this.authService.createCompanyByAdmin(obj).subscribe(res => {
      const dialogRef = this.dialog.open(CompanyCreatedComponent, {
        width: '349px',
        disableClose: true
      });
      dialogRef.afterClosed().subscribe(
        data => {
          if (data === 'refresh') {
            this.dialogRef.close('refreshed');
          }
        }
      );
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

}