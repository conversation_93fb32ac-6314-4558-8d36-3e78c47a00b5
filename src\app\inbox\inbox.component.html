<div class="common-toolbar-mobile" style="width: 100%; height: auto;" *ngIf="isMobile">
  <app-mobile-header></app-mobile-header>
</div>
<span class="dashboard-admin-heading" *ngIf="isMobile">
  <p style="text-align: center;margin-top: 20px;margin-bottom: 3px;"> Welcome {{userName}}!</p>
</span>
<div class="dasboard-subheading" *ngIf="isMobile">
  <p style="text-align: center;">Dear ASCI Officers, please move to a larger device for seamlessly accessing all functionalities of the system</p>
</div>
<div class="dashboard-container" *ngIf="isMobile">
</div>

<app-icons *ngIf="!isMobile"></app-icons>
<div class="inbox-page-container" fxLayout="column" fxLayoutGap="10px" *ngIf="!isMobile">
  <div class="common-toolbar" fxLayout="row" style="width: 100%;height: 50px;">
    <div class="heading-container">
      <app-heading [pagename]="pagename"></app-heading>
    </div>
    <div class="options-container">
      <app-toolbar-options></app-toolbar-options>
    </div>
  </div>

  <div class="inbox-body" fxLayout="row">
    <div class="tab-div">
      <mat-tab-group class="tab-group" [(selectedIndex)]="selectedTab" (selectedTabChange)="onTabChanged($event)">
        <mat-tab class="whatsapp-tab">
          <div class="tab-head">
            <ng-template mat-tab-label class="whatsapp-ng-temp">
              <div class="tab-icon">
                <i class="fa fa-whatsapp" style="font-size: x-large; color:#25D366;"></i>
                <span class="count-badge" *ngIf="selectedTab != 0 && whatsappCount != 0" style="top: 0px; left: 33px;">{{whatsappCount}}</span>
              </div>
            </ng-template>
          </div>

          <mat-nav-list>
            <div class="complaint-list" infiniteScroll [infiniteScrollDistance]="2" [infiniteScrollThrottle]="300" 
              (scrolled)="onScrollDown()" [scrollWindow]="false">
              <mat-list-item class="wpsg-list-container" (click)='onComplaintChange(wpmsg.WHATSAPP_ID)'
                *ngFor="let wpmsg of whatsappComplaints">
                <div class="wpsg-item-container" fxLayout="column" fxLayoutGap="5px" style="width: 310px;">
                  <div class="wpsg-head-container">
                    <p style="font-weight: 550;" matTooltip="{{wpmsg.PROFILE_NAME}} - {{wpmsg.WHATSAPP_ID}}">
                      {{wpmsg.PROFILE_NAME}} - {{wpmsg.WHATSAPP_ID}}</p>
                  </div>
                  <div fxLayout="column" fxLayoutGap="1px">
                    <div class="wpsg-msg-container">
                      <p class="wpsg-msg-text">{{wpmsg.LAST_MESSAGE}}</p>
                    </div>
                  </div>
                  <!-- <div fxLayout="column" fxLayoutGap="1px">
                    <div class="wpsg-msg-container">
                      <p class="wpsg-msg-text" [matTooltip]="wpmsg.LAST_MESSAGE">{{wpmsg.LAST_MESSAGE | slice:0:100}}
                        {{wpmsg.LAST_MESSAGE != null ?
                        (wpmsg.LAST_MESSAGE.length > 101 ? '..' : '') : ''}}</p>
                    </div>
                  </div> -->
                  <div>
                    <p class="wpsg-date-container">{{wpmsg.DATE | date:'dd MMM yyyy'}}</p>
                  </div>
                </div>
                <mat-divider [inset]="true" class="list-divider"></mat-divider>
              </mat-list-item>
            </div>
          </mat-nav-list>
        </mat-tab>

        <mat-tab class="mail-tab">
          <div class="tab-head">
            <ng-template mat-tab-label class="mail-ng-temp">
              <img src="../../assets/images/mail_blue.png">
              <span class="count-badge" *ngIf="selectedTab != 1 && mailCount != 0" style="top: 0px; left: 37px;">{{mailCount}}</span>
            </ng-template>
          </div>

          <mat-nav-list>
            <div class="complaint-list" infiniteScroll [infiniteScrollDistance]="2" [infiniteScrollThrottle]="300" 
              (scrolled)="onScrollDown()" [scrollWindow]="false">
              <mat-list-item class="mail-list-container" (click)="onComplaintChange(mail.ID)"
                *ngFor="let mail of mailComplaints">
                <div class="mail-item-container" fxLayout="column" style="width: 310px;">
                  <div class="mail-head-container">
                    <p style="font-weight: 550;" matTooltip="{{mail.FROM}}">{{mail.FROM |
                      slice:0:35}}{{mail.FROM.length>36? '..':''}}</p>
                  </div>
                  <div fxLayout="column" fxLayoutGap="1px" style="margin-top: 5px;">
                    <p class="mail-subject-text" matTooltip="{{mail.SUBJECT}}"> {{mail.SUBJECT}} </p>
                    <!-- <div class="mail-msg-container">
                      <p class="mail-msg-text">{{mail.msg}} </p>
                    </div> -->
                  </div>
                  <div>
                    <p class="mail-date-container">{{mail.DATE | date:'dd MMM yyyy'}}</p>
                  </div>
                </div>
                <mat-divider [inset]="true" class="list-divider"></mat-divider>
              </mat-list-item>
            </div>
          </mat-nav-list>
        </mat-tab>

        <mat-tab class="system-tab">
          <div class="tab-head">
            <ng-template mat-tab-label class="system-ng-temp">
              <img src="../../assets/images/system_comps.png">
              <span class="count-badge" *ngIf="selectedTab != 2 && systemCount != 0" style="top: -1px; left: 36px;">{{systemCount}}</span>
            </ng-template>
          </div>

          <mat-nav-list>
            <div class="complaint-list" infiniteScroll [infiniteScrollDistance]="2" [infiniteScrollThrottle]="300" 
              (scrolled)="onScrollDown()" [scrollWindow]="false">
              <mat-list-item class="mail-list-container" *ngFor="let complaint of sysGenComplaints"
                (click)="onComplaintChange(complaint.ID)">
                <div class="list-item-container" fxLayout="column">
                  <div fxLayout="row" class="comp-row1">
                    <div fxLayout="row" fxLayoutGap="8px" class="item-head-container"
                      style="display: flex;flex-direction: row;margin-left: 3px;">
                      <p>
                        <span style="font-weight: 550;width: max-content !important;">{{complaint.REQUESTER_USER_NAME}} -
                        </span>
                        <span style="font-size:14px;font-weight: 400;"> {{complaint.BRAND_NAME}} - </span>
                        <span [innerHTML]="safeHTML(complaint.ADVERTISEMENT_DESCRIPTION)" class="ellipsis" style="font-size: 14px">{{complaint.ADVERTISEMENT_DESCRIPTION}}</span>
                      </p>
                    </div>
                    <div class="ftc-container">
                      <div class="ccc-tag" *ngIf="complaint.FTC == '0'">CCC</div>
                      <div class="ftc-tag" *ngIf="complaint.FTC == '1'">FTC</div>
                    </div>
                  </div>
                  <div fxLayout="row" fxLayoutGap="9px" class="item-content-container"
                    style="display: flex;align-items:center;margin-bottom: 8px;margin-left: 2px;">
                    <mat-chip-list class="status-chip flex">
                      <mat-chip class="comp-status"
                        *ngIf="complaint.COMPLAINT_STATUS_NAME != 'Out of remit/Outside ASCI Purview'"
                        [ngClass]="{'theme-onhold' :complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' :complaint.COMPLAINT_STATUS_NAME == 'New'}">
                        {{complaint.COMPLAINT_STATUS_NAME}}
                      </mat-chip>
                      <mat-chip class="comp-status1"
                        *ngIf="complaint.COMPLAINT_STATUS_NAME == 'Out of remit/Outside ASCI Purview'"
                        [ngClass]="{'theme-onhold' :complaint.COMPLAINT_STATUS_NAME == 'On Hold', 'theme-new' :complaint.COMPLAINT_STATUS_NAME == 'New'}"
                        matTooltip="{{complaint.COMPLAINT_STATUS_NAME}}">
                        {{complaint.COMPLAINT_STATUS_NAME | slice:0:18}}..
                      </mat-chip>
                    </mat-chip-list>
                    <div class="name-container flex" fxLayout="row" fxLayoutGap="0px" style="position: relative; top: -1px;">
                      <div class="flex" style="padding-top: 1px;">
                        <mat-icon class="item-icon flex">perm_identity</mat-icon>
                      </div>
                      <div>
                        <span class="complainant-name assignee" style="position: relative; left: -3px; font-size: 11px;" matSuffix
                          *ngIf="complaint.ASSIGNEE_USER_NAME != null && complaint.ASSIGNEE_USER_NAME != ''"> {{
                          complaint.ASSIGNEE_USER_NAME }} </span>
                        <span class="complainant-name assignee" style="position: relative; left: -3px; font-size: 11px;" matSuffix
                          *ngIf="complaint.ASSIGNEE_USER_NAME == null || complaint.ASSIGNEE_USER_NAME == ''"> Not
                          assigned </span>
                      </div>
                    </div>
                  </div>
                  <div mat-line class="item-content-container" fxLayout="row" fxLayoutGap="9px">
                    <div class="date-container" fxLayout="row">
                      <div class="flex">
                        <mat-icon class="item-icon1  flex">calendar_today</mat-icon>
                      </div>
                      <div class="flex">
                        <span class="flex" matSuffix style="font-size: 11px; position: relative;top: 1px; left: -3px;">
                          {{ complaint.UPDATED_DATE | date:'dd MMM yyyy'}}
                        </span>
                      </div>
                    </div>
                    <div class="requester-container  flex" style="font-size: 11px;">
                      <span matSuffix>
                        {{complaint.REQUESTER_USER_ROLE}}
                      </span>
                    </div>
                  </div>
                </div>
              </mat-list-item>
            </div>
          </mat-nav-list>
        </mat-tab>

        <mat-tab class="chatbot-tab">
          <div class="tab-head">
            <ng-template mat-tab-label class="chatbot-ng-temp" *ngIf="!chatbotBlueImage">
              <img src="../../assets/images/Chatbot_grey.svg">
              <span class="count-badge" style="top: 0px; left: 46px;" *ngIf="chatbotCount != 0">{{chatbotCount}}</span>
            </ng-template>
            <ng-template mat-tab-label class="chatbot-ng-temp" *ngIf="chatbotBlueImage">
              <img src="../../assets/images/Chatbot_blue.svg">
              <span class="count-badge" *ngIf="selectedTab != 3 && chatbotCount != 0" style="top: 0px; left: 46px;">{{chatbotCount}}</span>
            </ng-template>
          </div>

          <mat-nav-list class="chatbot-list">
            <div class="complaint-list" infiniteScroll [infiniteScrollDistance]="2" [infiniteScrollThrottle]="300" 
              (scrolled)="onScrollDown()" [scrollWindow]="false">
              <mat-list-item class="chatbot-list-container" *ngFor="let chatbot of chatbotComplaints">
                <div class="chatbot-item-container" fxLayout="row" fxLayoutGap="8px">
                  <div fxLayout="column" (click)="onComplaintChange(chatbot.ID)" style="width: 310px;">
                    <div class="chatbot-head-container" fxLayout="row">
                      <div *ngFor="let source of adSource">
                        <div class="medium-container" *ngIf="source.ID == chatbot.ADVERTISEMENT_SOURCE_ID">
                          <span class="medium-list">{{source.ADVERTISEMENT_SOURCE_NAME}}</span>
                        </div>
                      </div>
                      <div class="brand-container">
                        <div class="brand-text" matTooltip="{{chatbot.BRAND_NAME}}">
                          {{chatbot.BRAND_NAME}}
                        </div>
                      </div>
                    </div>
                    <div fxLayout="column" fxLayoutGap="1px">
                      <div class="nams-msg-container">
                        <p class="nams-msg-text" matTooltip="{{chatbot.COMPLAINT_DESCRIPTION}}">
                          {{chatbot.COMPLAINT_DESCRIPTION}} </p>
                      </div>
                    </div>
                    <div fxLayoutAlign="end end">
                      <p class="nams-date-container" style="right: 232px;">{{chatbot.CREATED_DATE | date:'dd MMM yyyy'}}
                      </p>
                    </div>
                  </div>
                </div>
                <mat-divider [inset]="true" class="list-divider"></mat-divider>
              </mat-list-item>
            </div>
          </mat-nav-list>
        </mat-tab>
      </mat-tab-group>
    </div>
    <div class="complaint-container">
      <div class="details-content-container" fxLayout="column" fxLayoutGap="2%">
        <div fxLayoutGap="1%" style="width: 100%;">
          <div class="mat-card-container" fxLayout="column" fxLayoutGap="1%">
            <div class="mat-card-scroll" fxLayout="column" fxLayoutGap="3%">
              <div class="details-content-container" fxLayout="column" fxLayoutGap="2%">
                <div class="comp-head-container" fxLayout="row" fxLayoutGap="2%" style="padding-top: 7px;"
                  *ngIf="selectedTab == 0">
                  <div class="right-tab-icon">
                    <i class="fa fa-whatsapp" style="font-size: x-large; color:#25D366;"></i>
                  </div>
                  <div class="comp-head" style="padding-top: 9px;">Whatsapp complaints</div>
                  <div class="top-divider-container">
                    <mat-divider class="classfy-head-divider"></mat-divider>
                  </div>
                </div>
                <div class="comp-head-container" fxLayout="row" fxLayoutGap="2%" style="padding-top: 7px;"
                  *ngIf="selectedTab == 1">
                  <div class="right-tab-icon">
                    <img src="../../assets/images/mail_blue.png">
                  </div>
                  <div class="comp-head" style="padding-top: 9px;">Email complaints</div>
                  <div class="top-divider-container">
                    <mat-divider class="classfy-head-divider"></mat-divider>
                  </div>
                </div>
                <div class="comp-head-container" fxLayout="row" fxLayoutGap="2%" style="padding-top: 7px;"
                  *ngIf="selectedTab == 2">
                  <div class="right-tab-icon">
                    <img src="../../assets/images/system_comps.png">
                  </div>
                  <div class="comp-head" style="padding-top: 9px;">System generated complaints</div>
                  <div class="top-divider-container">
                    <mat-divider class="classfy-head-divider"></mat-divider>
                  </div>
                </div>
                <div class="comp-head-container" fxLayout="row" fxLayoutGap="2%" style="padding-top: 7px;"
                  *ngIf="selectedTab == 3">
                  <div class="right-tab-icon">
                    <img src="../../assets/images/Chatbot_blue.svg" style="width: 31px;">
                  </div>
                  <div class="comp-head" style="padding-top: 9px;">Complaints from Chatbot</div>
                  <div class="top-divider-container">
                    <mat-divider class="classfy-head-divider"></mat-divider>
                  </div>
                </div>
                <mat-progress-spinner color="primary" mode="indeterminate" diameter="70" *ngIf="loading" style="position: absolute;right: 35%;top: 50%;">
                </mat-progress-spinner>
                <div class="complaint-detail-container" fxLayout="column" fxLayoutGap="15px" [ngStyle]="{'opacity': loading ? '0.6' : '1'}">
                <div fxLayout="row">
                  <div fxLayout="column" *ngIf="selectedTab == 0 && whatsappComplaints?.length > 0" fxFlex="30%">
                    <span style="color: #000000; font-weight: 550; font-size: 14px;"> {{whatsappDetail?.PROFILE_NAME}} - {{whatsappDetail?.WHATSAPP_ID}} </span>
                    <span style="color: #999999;">Tracking Id: {{whatsappDetail?.ID}}</span>
                  </div>
                  <div fxLayout="column" *ngIf="selectedTab == 1  && mailComplaints?.length > 0 && mailCount != 0" fxFlex="30%">
                    <span style="color: #000000; font-weight: 550; font-size: 14px;"> {{mailDetail?.PROFILE_NAME}} - {{mailDetail?.MOBILE}} </span>
                    <!-- <span style="color: #999999;">Tracking Id: {{mailDetail?.ID}}</span> -->
                  </div>
                  <div *ngIf="selectedTab == 2 && sysGenComplaints?.length > 0" fxLayout="column" fxFlex="30%">
                    <div style="color: #000000; font-weight: 550; font-size: 14px;">
                      {{sysGenDetail?.REQUESTER_USER_NAME}} - {{sysGenDetail?.MOBILE}}</div>
                    <div style="color: #999999;">Tracking Id: {{sysGenDetail?.ID}}</div>
                  </div>
                  <div *ngIf="selectedTab == 3 && chatbotComplaints?.length > 0" fxLayout="column" fxFlex="30%">
                    <span style="color: #000000; font-weight: 550; font-size: 14px;"> {{chatbotDetails?.FIRST_NAME}}
                      {{chatbotDetails?.LAST_NAME}} - {{chatbotDetails?.MOBILE}}</span>
                    <span style="color: #999999;">Tracking Id: {{chatbotDetails?.ID}}</span>
                  </div> 
                  <div fxLayout="row" fxLayoutAlign="end end" style="margin-right: 16px; align-items: center;width: 100%;"
                    *ngIf="(selectedTab == 0 && whatsappComplaints?.length > 0) || (selectedTab == 1 && mailComplaints?.length > 0 && mailCount != 0)
                    || (selectedTab == 2 && sysGenComplaints?.length > 0) || (selectedTab == 3 && chatbotComplaints?.length > 0)">
                    <div class="buttons">
                      <span *ngIf="selectedTab == 2 && sysGenComplaints?.length > 0">
                        <button class="invalid-button" style="display: flex;">
                          Editable <mat-slide-toggle *ngIf="selectedTab == 2" [checked]="compSystemEdit" (change)="editSystemChange($event)" class="slider">
                          </mat-slide-toggle>
                        </button>
                      </span>
                      <span *ngIf="selectedTab == 3 && chatbotComplaints?.length > 0">
                        <button class="invalid-button" style="display: flex;">
                          Editable <mat-slide-toggle *ngIf="selectedTab == 3" [checked]="compChatbotEdit" (change)="editChatbotChange($event)" class="slider">
                          </mat-slide-toggle>
                        </button>
                      </span>
                      <span *ngIf="(selectedTab == 2 && sysGenComplaints?.length > 0) || (selectedTab == 3 && chatbotComplaints?.length > 0)">
                        <button class="theme-blue-button-admin" (click)="openConversationDialog()" style="border: none;padding: 0px 10px;margin-left: 18px">Conversation</button>
                      </span>
                      <span *ngIf="selectedTab == 0 && whatsappComplaints?.length > 0">
                        <button class="invalid-button" (click)="markInvalid()" style="padding: 0px 10px;">Mark invalid</button>
                      </span>
                      <button class="theme-blue-button-admin" (click)='createComp()'
                        [disabled]="(selectedTab == 0 && (!whatsappDetail?.ID || !whatsappDetail?.COMPANY_ID || similarityComplaintList.length == 0 || (incompleteData && whatsappForm.invalid)))
                          || (selectedTab == 1 && (!mailDetail?.COMPANY_ID || !mailDetail?.COMPANY_NAME || !mailDetail?.EMAIL_ID || !mailDetail?.MOBILE || !mailDetail?.COMPLAINT_DESCRIPTION || !!mailDetail?.CASE_ID || similarityComplaintList.length == 0))
                          || (selectedTab == 2 && (!sysGenDetail?.COMPANY_ID || !sysGenDetail?.MOBILE || !sysGenDetail?.COMPLAINT_DESCRIPTION || similarityComplaintList.length == 0 || compSystemEdit))
                          || (selectedTab == 3 && (!chatbotDetails?.COMPANY_ID || !chatbotDetails?.COMPANY_NAME || !chatbotDetails?.MOBILE || !chatbotDetails?.COMPLAINT_DESCRIPTION || similarityComplaintList.length == 0 || compChatbotEdit)) || disabled"
                        style="font-weight: 100; border: none; padding: 14px; width: max-content;margin-left: 18px">
                        <span class="bolder">Create new complaint</span>
                      </button>
                      <button class="theme-red-button"
                        style="font-weight: 100; border: none; padding: 6px 15px; margin-left: 18px; display: inline;"
                        (click)="deleteComp()" *ngIf="selectedTab == 0 || selectedTab == 2 || selectedTab == 3"
                        [disabled]="selectedTab == 0 && whatsappDetail?.ID === ''">
                        <span class="bolder">Delete</span>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="scroll-items">
                  <form [formGroup]="whatsappForm" *ngIf="whatsappForm">
                  <div class="contents-scroll" *ngIf="selectedTab == 0 && whatsappComplaints?.length > 0">
                    <div class="details-container" fxLayout="column" fxLayoutGap="1%">
                      <div class="classfy-head">
                        <div class="comp-head" style="font-weight: 550;">COMPLAINT DETAILS</div>
                        <div class="divider-container" style="width: 92%; margin-left: 71px;">
                          <mat-divider class="classfy-head-divider" [inset]="true"></mat-divider>
                        </div>
                      </div>

                      <div fxLayout="row" *ngIf="whatsappComplaints?.length > 0">
                        <div class="detail-left-container" fxLayout="column" fxLayoutGap="2px">
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Case ID : </span>
                              <span class="detail-value">{{whatsappDetail?.CASE_ID}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Company name : </span>
                              <span class="detail-value">{{whatsappDetail?.COMPANY_ID == 0 || whatsappDetail?.COMPANY_ID == null ? whatsappDetail?.SUGGESTED_COMPANY_NAME : whatsappDetail?.COMPANY_NAME}}</span>
                              <span class="company-btn" *ngIf="!whatsappDetail?.COMPANY_ID || whatsappDetail?.COMPANY_ID == 0">
                                <button (click)="addCompany(whatsappDetail)"
                                  class="not-exist-btn">
                                  <span class="not-exist">Not exist</span>
                                </button>
                              </span>
                            </p>
                          </div>
                          <div mat-line>
                            <div fxLayout="row" style="height: 34px;">
                              <div style="width: 89px;">
                                <span class="detail-attribute">Brand name : </span>
                              </div>
                              <div *ngIf="whatsappDetail?.BRAND_NAME; else brand_input">
                                <span class="detail-value">{{whatsappDetail?.BRAND_NAME}}</span>
                              </div>
                              <ng-template #brand_input>
                                <mat-form-field appearance="outline" class="input-box">
                                  <input matInput autocomplete="off" formControlName="brand" (input)="checkValidForm('brand')">
                                  <!-- <mat-error class="error-msg" *ngIf="step2Form.controls['brand'].errors?.required">
                                    Brand Name is required
                                  </mat-error> -->
                                </mat-form-field>
                              </ng-template>
                            </div>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Product name : </span>
                              <span class="detail-value">{{whatsappDetail?.PRODUCT_NAME}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <div fxLayout="row" style="height: 34px;">
                              <div style="width: 66px;">
                                <span class="detail-attribute">Email ID : </span>
                              </div>
                              <div *ngIf="whatsappDetail?.EMAIL_ID; else email_input">
                                <span class="detail-value">{{whatsappDetail?.EMAIL_ID}}</span>
                              </div>
                              <ng-template #email_input>
                                <mat-form-field appearance="outline" class="input-box">
                                  <input matInput autocomplete="off" placeholder="<EMAIL>" formControlName="email" (input)="checkValidForm('email')">
                                </mat-form-field>
                              </ng-template>
                            </div>
                          </div>
                          <div mat-line>
                            <div fxLayout="row" style="height: 34px;">
                              <div style="width: 53px;">
                                <span class="detail-attribute">Mobile : </span>
                              </div>
                              <div *ngIf="whatsappDetail?.MOBILE; else mobile_input">
                                <span class="detail-value">{{whatsappDetail?.MOBILE}}</span>
                              </div>
                              <ng-template #mobile_input>
                                <mat-form-field appearance="outline" class="input-box" style="right:3px">
                                  <input matInput autocomplete="off" formControlName="mobile" (input)="checkValidForm('mobile')">
                                  <!-- <mat-error class="error-msg" *ngIf="step2Form.controls['brand'].errors?.required">
                                    Brand Name is required
                                  </mat-error> -->
                                </mat-form-field>
                              </ng-template>
                            </div>
                            <!-- <p>
                              <span class="detail-attribute">Mobile : </span>
                              <span class="detail-value">{{whatsappDetail?.MOBILE}}</span>
                            </p> -->
                          </div>
                        </div>
                      </div>
                      <div mat-line *ngIf="whatsappComplaints?.length > 0">
                        <p>
                          <span class="detail-attribute">Media source : </span>
                          <span class="detail-value">{{whatsappDetail?.ADVERTISEMENT_SOURCE_NAME}}</span>
                        </p>
                      </div>
                      <div mat-line *ngIf="whatsappComplaints?.length > 0">
                        <p>
                          <span class="detail-attribute">Channel/Paper/Platform : </span>
                          <span class="detail-value">{{whatsappDetail?.CHANNEL_PAPER_PLATFORM_NAME}}</span>
                        </p>
                      </div>
                      <div mat-line *ngIf="whatsappComplaints?.length > 0">
                        <p>
                          <span class="detail-attribute">Advertisement Seen Date : </span>
                          <span class="detail-value">{{whatsappDetail?.ADVERTISEMENT_SEEN_DATE}}</span>
                        </p>
                      </div>
                      <div mat-line *ngIf="whatsappComplaints?.length > 0">
                        <p>
                          <span class="detail-attribute">Advertisement Seen Time : </span>
                          <span class="detail-value">{{whatsappDetail?.ADVERTISEMENT_SEEN_TIME}}</span>
                        </p>
                      </div>
                      <div mat-line *ngIf="whatsappComplaints?.length > 0">
                        <p>
                          <span class="detail-attribute">Advertisement Description : </span>
                        </p>
                      </div>
                      <div mat-line class="comp-msg-container" *ngIf="whatsappComplaints?.length > 0">
                        <p class="detail-value" *ngIf="whatsappDetail?.ADVERTISEMENT_DESCRIPTION; else ad_input">{{whatsappDetail?.ADVERTISEMENT_DESCRIPTION}}</p>
                        <ng-template #ad_input>
                          <mat-form-field appearance="outline" class="input-field">
                            <textarea row="5" matInput formControlName="advDesc" autocomplete="off" style="padding:1em 0 1em 0;" (input)="checkValidForm('advDesc')"></textarea>
                          </mat-form-field>
                        </ng-template>
                      </div>
                      <div mat-line *ngIf="whatsappComplaints?.length > 0">
                        <p>
                          <span class="detail-attribute">Complaint description :</span>
                        </p>
                      </div>
                      <div mat-line class="comp-msg-container" *ngIf="whatsappComplaints?.length > 0">
                        <p class="detail-value" *ngIf="whatsappDetail?.COMPLAINT_DESCRIPTION; else desc_input">{{whatsappDetail?.COMPLAINT_DESCRIPTION}}</p>
                        <ng-template #desc_input>
                          <mat-form-field appearance="outline" class="input-field">
                            <textarea row="5" matInput formControlName="compDesc" autocomplete="off" style="padding:1em 0 1em 0;" (input)="checkValidForm('compDesc')"></textarea>
                          </mat-form-field>
                        </ng-template>
                      </div>
                    </div>
                  </div>
                  </form>

                  <div class="details-container" fxLayout="column" fxLayoutGap="2%" *ngIf="selectedTab == 0  && whatsappComplaints?.length > 0">
                    <div class="classfy-head">
                      <div class="comp-head" style="font-weight: 550;">ATTACHMENTS / DOCUMENTS</div>
                      <div class="divider-container" style="width: 86%; margin-left: 132px;">
                        <mat-divider class="classfy-head-divider" [inset]="true"></mat-divider>
                      </div>
                    </div>
                    <div *ngIf="noOfDocs == 0">
                      No documents
                    </div>
                    <!-- <div class="attachment-box" *ngIf="(sysGenDetail?.COMPLAINT_SOURCE_ID != 7 && (noOfDocs == 0 && (detail_link != '' && detail_link != null))) || (sysGenDetail?.COMPLAINT_SOURCE_ID == 7 && (noOfDocs == 0 && (detail_link != '' && detail_link != null)))">
                      <div class="media-container" style="width:96%" fxLayout="row" fxLayoutGap="4%">
                        <div>
                          <img src="../assets/images/Link-blue.png" style="margin-top: 8px;"/>
                        </div>
                        <div fxFlex="75%" matTooltip="{{detail_link}}">
                          <a (click)="previewLink(detail_link)" class="media-anchor1">
                            {{detail_link}}
                          </a>
                        </div>
                        <div>
                          <a (click)="previewLink(detail_link)">
                            <img src="../assets/images/visit.png" style="margin-top: 10px;"/>
                          </a>
                        </div>
                      </div>
                    </div> -->
                    <div *ngIf="noOfDocs != 0 && adDocs.length != 0" fxLayout="row wrap">
                      <div class="attachment-box" *ngFor="let doc of adDocs">
                        <div class="media-container" style="width:100%" fxLayout="row" fxLayoutGap="4%">
                          <div>
                            <img src="../assets/images/Link-blue.png" style="margin-top: 8px;"/>
                          </div>
                          <!-- <div *ngIf="(doc.TYPE_OF_DOCUMENT).split('/')[0] == 'image'">
                            <img src="../../assets/images/img.png" style="margin-top: 5px;" />
                          </div>
                          <div *ngIf="(doc.TYPE_OF_DOCUMENT).split('/')[0] == 'video'">
                            <img src="../../assets/images/vid.png" style="margin-top: 5px;" />
                          </div>
                          <div *ngIf="(doc.TYPE_OF_DOCUMENT).split('/')[0] == 'application' || (doc.TYPE_OF_DOCUMENT).split('/')[0] == 'text' || doc.TYPE_OF_DOCUMENT == 'pdf'">
                            <img src="../../assets/images/docs.png" style="margin-top: 5px;" />
                          </div>
                          <div *ngIf="(doc.TYPE_OF_DOCUMENT).split('/')[0] == 'audio'">
                            <img src="../../assets/images/audio.png" style="margin-top: 5px;" />
                          </div> -->
                          <div matTooltip="{{bucketUrl + doc.SOURCE_URL}}" fxFlex="65%">
                            <div style="margin-top: 5px;">
                              <span *ngIf="checkUrl(doc.SOURCE_URL)">{{bucketUrl + doc.SOURCE_URL | slice: 0:25}}{{longText1}}</span>
                              <span *ngIf="!checkUrl(doc.SOURCE_URL)">{{doc.SOURCE_URL | slice: 0:25}}{{longText1}}</span>
                            </div>
                          </div>
                          <!-- <div style="cursor: pointer;">
                            <img src="../../assets/images/Down.png" (click)="download(doc.SOURCE_URL, doc.ATTACHMENT_SOURCE)"
                              style="margin-top: 5px;">
                          </div> -->
                          <div style="cursor: pointer;">
                            <a (click)="previewLink(doc.SOURCE_URL)">
                              <img src="../assets/images/visit.png" style="margin-top: 10px;"/>
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="contents-scroll" *ngIf="selectedTab == 1 && mailComplaints?.length > 0 && mailCount != 0">
                    <div class="details-container" fxLayout="column" fxLayoutGap="1%">
                      <div class="classfy-head">
                        <div class="comp-head" style="font-weight: 550;">COMPLAINT DETAILS</div>
                        <div class="divider-container" style="width: 92%; margin-left: 71px;">
                          <mat-divider class="classfy-head-divider" [inset]="true"></mat-divider>
                        </div>
                      </div>
                      <div fxLayout="row" *ngIf="mailComplaints?.length > 0">
                        <div class="detail-left-container" fxLayout="column" fxLayoutGap="2px">
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Case ID : </span>
                              <span class="detail-value">{{!!mailDetail?.CASE_ID ? mailDetail?.CASE_ID : ''}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Company name : </span>
                              <span class="detail-value">{{mailDetail?.COMPANY_ID == 0 ? mailDetail?.SUGGESTED_COMPANY_NAME : mailDetail?.COMPANY_NAME}}</span>
                              <span class="company-btn" *ngIf="!mailDetail?.COMPANY_ID || mailDetail?.COMPANY_ID == 0">
                                <button (click)="addCompany(mailDetail)"
                                  class="not-exist-btn">
                                  <span class="not-exist">Not exist</span>
                                </button>
                              </span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Brand name : </span>
                              <span class="detail-value">{{mailDetail?.BRAND_NAME}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Product name : </span>
                              <span class="detail-value">{{mailDetail?.PRODUCT_NAME}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Email ID : </span>
                              <span class="detail-value">{{mailDetail?.EMAIL_ID}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Mobile : </span>
                              <span class="detail-value">{{mailDetail?.MOBILE}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Media source : </span>
                              <span class="detail-value">{{mailDetail?.ADVERTISEMENT_SOURCE_NAME}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Channel/Paper/Platform : </span>
                              <span class="detail-value">{{mailDetail?.CHANNEL_PAPER_PLATFORM_NAME}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Advertisement Seen Date : </span>
                              <span class="detail-value">{{mailDetail?.ADVERTISEMENT_SEEN_DATE}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Advertisement Seen Time : </span>
                              <span class="detail-value">{{mailDetail?.ADVERTISEMENT_SEEN_TIME}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Advertisement Description : </span>
                              <span class="detail-value">{{mailDetail?.ADVERTISEMENT_DESCRIPTION}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Complaint description :</span>
                            </p>
                          </div>
                          <div mat-line class="comp-msg-container">
                            <p class="detail-value">{{mailDetail?.COMPLAINT_DESCRIPTION}}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="contents-scroll" *ngIf="selectedTab == 2 && sysGenComplaints?.length > 0">
                    <div class="details-container" fxLayout="column" fxLayoutGap="1%">
                      <div class="classfy-head">
                        <div class="comp-head" style="font-weight: 550;">COMPLAINT DETAILS</div>
                        <div class="divider-container" style="width: 92%; margin-left: 71px;">
                          <mat-divider class="classfy-head-divider" [inset]="true"></mat-divider>
                        </div>
                      </div>
                      <div fxLayout="row" fxLayoutGap="99px" *ngIf="sysGenComplaints.length > 0">
                        <div mat-line style="width:288px">
                          <p>
                            <span class="detail-attribute">Case ID : </span>
                            {{sysGenDetail?.CASE_ID}}
                          </p>
                        </div>
                        <div mat-line>
                          <p>
                            <span class="detail-attribute">Tracking ID : </span>
                            {{sysGenDetail?.ID}}
                          </p>
                        </div>
                      </div>
                      <div fxLayout="row" fxLayoutGap="100px" *ngIf="sysGenComplaints.length > 0">
                        <div mat-line style="width:288px">
                          <p>
                            <span class="detail-attribute">Company : </span>
                            {{sysGenDetail?.COMPANY_ID == 0 ? sysGenDetail?.SUGGESTED_COMPANY_NAME :
                            sysGenDetail?.COMPANY_NAME}}
                            <span class="company-btn">
                              <button (click)="addCompany(sysGenDetail)" *ngIf="!sysGenDetail?.COMPANY_ID || sysGenDetail?.COMPANY_ID == 0"
                                class="not-exist-btn">
                                <span class="not-exist">Not exist</span>
                              </button>
                            </span>
                          </p>
                        </div>
                        <div mat-line *ngIf="sysGenDetail?.BRAND_NAME">
                          <p>
                            <span class="detail-attribute">Brand : </span>
                            {{sysGenDetail?.BRAND_NAME}}
                          </p>
                        </div>
                      </div>
                      <div fxLayout="row" fxLayoutGap="100px" *ngIf="sysGenComplaints.length > 0">
                        <div mat-line *ngIf="sysGenDetail?.PRODUCT_NAME" style="width:288px">
                          <p>
                            <span class="detail-attribute">Product name : </span>
                            {{sysGenDetail?.PRODUCT_NAME}}
                          </p>
                        </div>
                        <div mat-line *ngIf="sysGenDetail?.PRODUCT_CATEGORY">
                          <p>
                            <span class="detail-attribute">Product category : </span>
                            {{sysGenDetail?.PRODUCT_CATEGORY}}
                          </p>
                        </div>
                      </div>
                      <div fxLayout="row" fxLayoutGap="100px" *ngIf="sysGenComplaints.length > 0">
                        <div mat-line style="width:288px">
                          <p>
                            <span class="detail-attribute">Contact : </span>
                            {{sysGenDetail?.MOBILE}}
                          </p>
                        </div>
                        <div mat-line>
                          <p>
                            <span class="detail-attribute">Email Id : </span>
                            {{sysGenDetail?.EMAIL_ID}}
                          </p>
                        </div>
                      </div>
                      <div fxLayout="row" fxLayoutGap="100px" *ngFor="let source of detail_adsource;let i=index;">
                        <div mat-line style="width: 288px;" *ngIf="source.ADVERTISEMENT_SOURCE_ID != 0">
                          <p>
                            <span class="detail-attribute">Media source {{i+1}} : </span>
                            <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 1">Television</span>
                            <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 2">Radio</span>
                            <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3">Digital Media</span>
                            <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 4">Hoardings</span>
                            <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 5">Print</span>
                            <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 6">Promotional Material</span>
                            <span *ngIf="source.ADVERTISEMENT_SOURCE_ID== 7">Packaging</span>
                            <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 8">SMS</span>
                            <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 9">Others</span>
                          </p>
                        </div>
                        <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3" style="margin-left: 100px;">
                          <p>
                            <span class="detail-attribute">Platform name : </span>
                            <span *ngIf="source.PLATFORM_ID == 1">Facebook</span>
                            <span *ngIf="source.PLATFORM_ID  == 2">Instagram</span>
                            <span *ngIf="source.PLATFORM_ID  == 3">YouTube</span>
                            <span *ngIf="source.PLATFORM_ID  == 4">Twitter</span>
                            <span *ngIf="source.PLATFORM_ID  == 5">LinkedIn</span>
                            <span *ngIf="source.PLATFORM_ID  == 6">Website</span>
                            <span *ngIf="source.PLATFORM_ID  == 7">Google Ad</span>
                            <span *ngIf="source.PLATFORM_ID  == 8">Mobile App</span>
                            <span *ngIf="source.PLATFORM_ID  == 9">Others - {{source.SOURCE_PLACE}}</span>
                          </p>
                        </div>
                        <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID== 1 || source.ADVERTISEMENT_SOURCE_ID == 2"
                          style="margin-left: 100px;">
                          <p>
                            <span class="detail-attribute">Channel name : </span>
                            {{source.SOURCE_NAME}}
                          </p>
                        </div>
                        <div mat-line
                          *ngIf="source.ADVERTISEMENT_SOURCE_ID == 4 || source.ADVERTISEMENT_SOURCE_ID == 6 "
                          style="margin-left: 100px;">
                          <p>
                            <span class="detail-attribute">Place : </span>
                            {{source.SOURCE_PLACE}}

                          </p>
                        </div>
                        <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 5" style="margin-left: 100px;">
                          <p>
                            <span class="detail-attribute">Print source : </span>
                            {{source.SOURCE_NAME}}
                          </p>
                        </div>
                        <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 7" style="margin-left: 100px;">
                          <p>
                            <span class="detail-attribute">MFD/PKD Date : </span>
                            {{source.DATE| date:'dd/MM/yyyy'}}
                          </p>
                        </div>
                        <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 8" style="margin-left: 100px;">
                          <p>
                            <span class="detail-attribute">Sender : </span>
                            {{source.SOURCE_NAME}}
                          </p>
                        </div>
                        <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 9" style="margin-left: 100px;">
                          <p>
                            <span class="detail-attribute">Source : </span>
                            {{source.SOURCE_NAME}}
                          </p>
                        </div>
                        <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID != 0 && !source.ADVERTISEMENT_SOURCE_ID"
                          style="margin-left: 100px;">
                          <p>
                            <span class="detail-attribute">Source : </span>
                          </p>
                        </div>
                      </div>
                      <div fxLayout="row" fxLayoutGap="100px" *ngIf="sysGenComplaints.length > 0">
                        <div mat-line style="width: 288px;">
                          <p>
                            <span class="detail-attribute">Created Date : </span>
                            {{sysGenDetail?.CREATED_DATE |date:'dd MMM yyyy'}}
                          </p>
                        </div>
                        <div mat-line>
                          <p>
                            <span class="detail-attribute">Time : </span>
                            {{sysGenDetail?.CREATED_DATE | date:'shortTime'}}
                          </p>
                        </div>
                      </div>
                      <div fxLayout="row" *ngIf="sysGenComplaints.length > 0">
                        <div mat-line style="width: 234px;">
                          <p>
                            <span class="detail-attribute">Complaint via : </span>
                            {{sysGenDetail?.COMPLAINT_SOURCE_NAME}}
                          </p>
                        </div>
                        <div mat-line *ngIf="sysGenDetail?.COMPLAINT_SOURCE_ID != 7">
                          <p *ngIf="noOfDocs != 0" style="margin-left: 153px;">
                            <span class="detail-attribute">Media file : </span>
                            <a style="font-size: 13px; cursor: pointer;" (click)="openNewTab()"
                              class="media-anchor">{{noOfDocs}} {{noOfDocs > 1 ? 'files': 'file'}} <img
                                src="../../assets/images/media_download.svg"
                                style="position: relative;left:8px;bottom:1px"></a>
                          </p>
                        </div>
                        <div mat-line [hidden]="hiddenMore" *ngIf="sysGenDetail?.COMPLAINT_SOURCE_ID == 7">
                          <p style="margin-left: 153px;">
                            <span class="detail-attribute">Creative Id : </span>
                            {{sysGenDetail?.CREATIVE_ID}}
                          </p>
                        </div>
                      </div>
                      <div fxLayout="row" fxLayoutGap="100px" *ngIf="sysGenDetail?.COMPLAINT_SOURCE_ID == 7 && sysGenComplaints.length > 0">
                        <div mat-line style="width:288px">
                          <p>
                            <span class="detail-attribute">Media outlet : </span>
                            {{sysGenDetail?.MEDIA_OUTLET}}
                          </p>
                        </div>
                        <div mat-line>
                          <p>
                            <span class="detail-attribute">Media : </span>
                            {{sysGenDetail?.MEDIA}}
                          </p>
                        </div>
                      </div>
                      <div fxLayout="row" *ngIf="sysGenDetail?.COMPLAINT_SOURCE_ID == 7 && sysGenComplaints.length > 0">
                        <div mat-line style="width:288px" *ngIf="sysGenDetail?.SUPPLIMENT != null">
                          <p>
                            <span class="detail-attribute">Supplement : </span>
                            {{suppliment}}
                          </p>
                        </div>
                        <div mat-line *ngIf="sysGenDetail?.EDITION!= null">
                          <p style="margin-left: 101px;">
                            <span class="detail-attribute">Edition : </span>
                            {{sysGenDetail?.EDITION}}
                          </p>
                        </div>
                      </div>
                      <div fxLayout="row" *ngIf="sysGenDetail?.COMPLAINT_SOURCE_ID == 7 && sysGenComplaints.length > 0">
                        <div mat-line style="width:288px">
                          <p>
                            <span class="detail-attribute">Ad language : </span>
                            {{sysGenDetail?.AD_LANGUAGE}}
                          </p>
                        </div>
                        <div mat-line>
                          <p matTooltip="{{sysGenDetail?.PRODUCT_CATEGORY}}" style="margin-left: 101px;">
                            <span class="detail-attribute">Product category : </span>
                            {{sysGenDetail?.PRODUCT_CATEGORY | slice:0:20}}{{sysGenDetail?.PRODUCT_CATEGORY.length>21?
                            '..':''}}
                          </p>
                        </div>
                      </div>
                      <div fxLayout="row" fxLayoutGap="100px" *ngIf="sysGenDetail?.COMPLAINT_SOURCE_ID == 7 && sysGenComplaints.length > 0">
                        <div mat-line style="width:288px">
                          <p *ngIf="noOfDocs != 0">
                            <span class="detail-attribute">Media file : </span>
                            <a style="font-size: 13px; cursor: pointer;" (click)="openNewTab()"
                              class="media-anchor">{{noOfDocs}} {{noOfDocs > 1 ? 'files': 'file'}} <img
                                src="../../assets/images/media_download.svg"
                                style="position: relative;left:8px;bottom:1px"></a>
                          </p>
                          <p *ngIf="noOfDocs == 0 && detail_link != ''">
                            <span class="detail-attribute">Media file : </span>
                            <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(detail_link)"
                              class="media-anchor" matTooltip="{{detail_link}}">{{detail_link | slice:0:
                              28}}{{longText1}}</a>
                          </p>
                        </div>
                        <div mat-line [hidden]="hiddenMore">
                          <p>
                            <span class="detail-attribute">Translation hyperlink : </span>
                            <a style="font-size: 13px; cursor: pointer;"
                              (click)="previewLink(sysGenDetail?.TRANSLATION_HYPERLINK)" class="media-anchor"
                              matTooltip="{{sysGenDetail?.TRANSLATION_HYPERLINK}}">{{sysGenDetail?.TRANSLATION_HYPERLINK
                              | slice:0: 24}}{{longText2}}</a>
                          </p>
                        </div>
                      </div>
                      <div fxLayout="row" fxLayoutGap="100px" *ngIf="sysGenComplaints.length > 0">
                        <div mat-line *ngIf="sysGenDetail?.ADVERTISEMENT_DESCRIPTION && sysGenComplaints.length">
                          <p>
                            <span class="detail-attribute">Advertisement Description :</span>
                          </p>
                        </div>
                        <div mat-line *ngIf="sysGenDetail?.COMPLAINT_SOURCE_ID != 7">
                          <p *ngIf="detail_link">
                            <span class="detail-attribute" style="margin-left: 115px;">Media URL : </span>
                            <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(detail_link)"
                              class="media-anchor" matTooltip="{{detail_link}}">{{detail_link | slice:0:
                              28}}{{longText1}}</a>
                          </p>
                        </div>
                      </div>
                      <div mat-line class="comp-msg-container" *ngIf="sysGenDetail?.ADVERTISEMENT_DESCRIPTION && sysGenComplaints.length > 0">
                        <p class="comp-msg" [innerHTML]="safeHTML(sysGenDetail?.ADVERTISEMENT_DESCRIPTION)">
                          {{sysGenDetail?.ADVERTISEMENT_DESCRIPTION}}
                        </p>
                      </div>
                      <div mat-line *ngIf="sysGenComplaints.length > 0">
                        <p>
                          <span class="detail-attribute">Objectionable frames :</span>
                        </p>
                      </div>
                      <div mat-line class="comp-msg-container" *ngIf="sysGenComplaints.length > 0">
                        <p class="comp-msg" [innerHTML]="safeHTML(sysGenDetail?.COMPLAINT_DESCRIPTION)">
                          {{sysGenDetail?.COMPLAINT_DESCRIPTION}}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="details-container" fxLayout="column" fxLayoutGap="2%" *ngIf="selectedTab == 2 && sysGenComplaints?.length > 0">
                    <div class="classfy-head">
                      <div class="comp-head" style="font-weight: 550;">ATTACHMENTS / DOCUMENTS</div>
                      <div class="divider-container" style="width: 86%; margin-left: 132px;">
                        <mat-divider class="classfy-head-divider" [inset]="true"></mat-divider>
                      </div>
                    </div>
                    <div *ngIf="noOfDocs == 0 && (detail_link == '' || detail_link == null)">
                      No documents
                    </div>
                    <div class="attachment-box" *ngIf="(sysGenDetail?.COMPLAINT_SOURCE_ID != 7 && (noOfDocs == 0 && (detail_link != '' && detail_link != null))) || (sysGenDetail?.COMPLAINT_SOURCE_ID == 7 && (noOfDocs == 0 && (detail_link != '' && detail_link != null)))">
                      <div class="media-container" style="width:96%" fxLayout="row" fxLayoutGap="4%">
                        <div>
                          <img src="../assets/images/Link-blue.png" style="margin-top: 8px;"/>
                        </div>
                        <div fxFlex="75%" matTooltip="{{detail_link}}">
                          <a (click)="previewLink(detail_link)" class="media-anchor1">
                            {{detail_link}}
                          </a>
                        </div>
                        <div>
                          <a (click)="previewLink(detail_link)">
                            <img src="../assets/images/visit.png" style="margin-top: 10px;"/>
                          </a>
                        </div>
                      </div>
                    </div>
                    <div *ngIf="(sysGenDetail?.COMPLAINT_SOURCE_ID != 7 && noOfDocs != 0) || sysGenDetail?.COMPLAINT_SOURCE_ID == 7 && noOfDocs != 0" fxLayout="row wrap">
                      <div class="attachment-box" *ngFor="let doc of adDocs">
                        <div class="media-container" style="width:100%" fxLayout="row" fxLayoutGap="4%">
                          <div *ngIf="doc.TYPE_OF_DOCUMENT">
                          <div *ngIf="(doc.TYPE_OF_DOCUMENT).split('/')[0] == 'image'">
                            <img src="../../assets/images/img.png" style="margin-top: 5px;" />
                          </div>
                          <div *ngIf="(doc.TYPE_OF_DOCUMENT).split('/')[0] == 'video'">
                            <img src="../../assets/images/vid.png" style="margin-top: 5px;" />
                          </div>
                          <div *ngIf="(doc.TYPE_OF_DOCUMENT).split('/')[0] == 'application' || (doc.TYPE_OF_DOCUMENT).split('/')[0] == 'text' || doc.TYPE_OF_DOCUMENT == 'pdf'">
                            <img src="../../assets/images/docs.png" style="margin-top: 5px;" />
                          </div>
                          <div *ngIf="(doc.TYPE_OF_DOCUMENT).split('/')[0] == 'audio'">
                            <img src="../../assets/images/audio.png" style="margin-top: 5px;" />
                          </div>
                          </div>
                          <div matTooltip="{{doc.ATTACHMENT_NAME}}" fxFlex="65%">
                            <div style="margin-top: 5px;">{{doc.ATTACHMENT_NAME | slice: 0:20}}{{longText1}}
                            </div>
                          </div>
                          <div style="cursor: pointer;">
                            <img src="../../assets/images/Down.png" (click)="download(doc.ATTACHMENT_NAME, doc.ATTACHMENT_SOURCE)"
                              style="margin-top: 5px;">
                          </div>
                          <div style="cursor: pointer;">
                            <img src="../../assets/images/eye.png" (click)="preview(doc.ATTACHMENT_SOURCE)" style="margin-top: 7px;">
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="contents-scroll" *ngIf="selectedTab == 3 && chatbotComplaints?.length > 0">
                    <div class="details-container" fxLayout="column" fxLayoutGap="1%">
                      <div class="classfy-head">
                        <div class="comp-head" style="font-weight: 550;">COMPLAINT DETAILS</div>
                        <div class="divider-container" style="width: 92%; margin-left: 71px;">
                          <mat-divider class="classfy-head-divider" [inset]="true"></mat-divider>
                        </div>
                      </div>
                      <div fxLayout="row" *ngIf="chatbotComplaints?.length > 0">
                        <div class="detail-left-container" fxLayout="column" fxLayoutGap="2px">
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Tracking ID : </span>
                              <span class="detail-value">{{chatbotDetails?.ID}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Company : </span>
                              <button (click)="addCompany(chatbotDetails)"
                                *ngIf="!zeroComplaints && (!chatbotDetails.COMPANY_ID || chatbotDetails.COMPANY_ID == 0)"
                                class="not-exist-btn">
                                <span class="not-exist">Not Exist</span>
                              </button>
                              <span class="detail-value" *ngIf="chatbotDetails.COMPANY_ID != 0">
                                {{chatbotDetails?.COMPANY_ID == 0 ? chatbotDetails?.SUGGESTED_COMPANY_NAME :
                                  chatbotDetails?.COMPANY_NAME}}
                              </span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Complainant Name : </span>
                              <span class="detail-value">{{chatbotDetails?.FIRST_NAME}}
                                {{chatbotDetails?.LAST_NAME}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Contact : </span>
                              <span class="detail-value">{{chatbotDetails?.MOBILE}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Medium : </span>
                              <span *ngFor="let source of adSource">
                                <span *ngIf="source.ID == chatbotDetails.ADVERTISEMENT_SOURCE_ID">
                                  <span class="detail-value">{{source.ADVERTISEMENT_SOURCE_NAME}}</span>
                                </span>
                              </span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Brand : </span>
                              <span class="detail-value">{{chatbotDetails?.BRAND_NAME}}</span>
                            </p>
                          </div>
                          <!-- <div mat-line fxLayout="row">
                            <div class="media-attribute" *ngIf="chatbotDetails.ATTACHMENT_SOURCE != ''">
                              <p class="detail-attribute">
                                <span style="word-wrap: normal;">
                                  Media File :&nbsp;
                                </span>
                              </p>
                            </div>
                            <div class="media-container" *ngIf="chatbotDetails.ATTACHMENT_SOURCE != ''">
                              <a (click)="previewLink(docURL)" class="media-anchor">
                                {{showURL}}
                              </a>
                            </div>
                          </div> -->
                          <div mat-line fxLayout="row">
                            <div class="media-attribute" *ngIf="chatbotDetails.SOURCE_URL != '' && chatbotDetails.SOURCE_URL != null">
                              <p class="detail-attribute">
                                <span style="word-wrap: normal;">
                                  Source URL :&nbsp;
                                </span>
                              </p>
                            </div>
                            <div class="media-container" *ngIf="chatbotDetails.SOURCE_URL != '' && chatbotDetails.SOURCE_URL != null">
                              <a (click)="previewLink(chatbotDetails.SOURCE_URL)" class="media-anchor">
                                {{chatbotDetails?.SOURCE_URL}}
                              </a>
                            </div>
                          </div>
                        </div>
                        <div class="detail-right-container" fxLayout="column" fxLayoutGap="2px">
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Email ID : </span>
                              <span class="detail-value">{{chatbotDetails?.EMAIL_ID}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Profession : </span>
                              <span class="detail-value">{{chatbotDetails?.PROFESSION_NAME}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Organization Name : </span>
                              <span class="detail-value">{{chatbotDetails?.ORGANIZATION_NAME}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute">Postal Code : </span>
                              <span class="detail-value">{{chatbotDetails?.PINCODE}}</span>
                            </p>
                          </div>
                          <div mat-line>
                            <p>
                              <span class="detail-attribute"> Medium Date : </span>
                              <span class="detail-value">{{chatbotDetails?.DATE | date:'dd-MM-yyyy'}}</span>
                            </p>
                          </div>
                        </div>
                      </div>
                      <div mat-line *ngIf="chatbotComplaints?.length > 0">
                        <p>
                          <span class="detail-attribute">Complaint Description : </span>
                          <span class="detail-value">{{chatbotDetails?.COMPLAINT_DESCRIPTION}}</span>
                        </p>
                      </div>
                      <div mat-line *ngIf="chatbotDetails.ADVERTISEMENT_DESCRIPTION != '' && chatbotDetails.ADVERTISEMENT_DESCRIPTION != null && chatbotComplaints?.length > 0">
                        <p>
                          <span class="detail-attribute">Advertisement Description : </span>
                          <span class="detail-value">{{chatbotDetails?.ADVERTISEMENT_DESCRIPTION}}</span>
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="details-container" fxLayout="column" fxLayoutGap="2%" *ngIf="selectedTab == 3 && chatbotComplaints?.length > 0">
                    <div class="classfy-head">
                      <div class="comp-head" style="font-weight: 550;">ATTACHMENTS / DOCUMENTS</div>
                      <div class="divider-container" style="width: 86%; margin-left: 132px;">
                        <mat-divider class="classfy-head-divider" [inset]="true"></mat-divider>
                      </div>
                    </div>
                    <div *ngIf="adDocs.length == 0">
                      No documents
                    </div>
                    <div *ngIf="adDocs.length != 0" fxLayout="row wrap">
                      <div class="attachment-box" *ngFor="let doc of adDocs">
                        <div class="media-container" style="width:100%" fxLayout="row" fxLayoutGap="4%">
                          <div *ngIf="doc.ATTACHMENT_SOURCE_TYPE">
                            <div *ngIf="(doc.ATTACHMENT_SOURCE_TYPE).split('/')[0] == 'image'">
                              <img src="../../assets/images/img.png" style="margin-top: 5px;" />
                            </div>
                            <div *ngIf="(doc.ATTACHMENT_SOURCE_TYPE).split('/')[0] == 'video'">
                              <img src="../../assets/images/vid.png" style="margin-top: 5px;" />
                            </div>
                            <div *ngIf="(doc.ATTACHMENT_SOURCE_TYPE).split('/')[0] == 'application' || (doc.ATTACHMENT_SOURCE_TYPE).split('/')[0] == 'text' || doc.ATTACHMENT_SOURCE_TYPE == 'pdf'">
                              <img src="../../assets/images/docs.png" style="margin-top: 5px;" />
                            </div>
                            <div *ngIf="(doc.ATTACHMENT_SOURCE_TYPE).split('/')[0] == 'audio'">
                              <img src="../../assets/images/audio.png" style="margin-top: 5px;" />
                            </div>
                          </div>
                          <div matTooltip="{{doc.ATTACHMENT_SOURCE_NAME}}" fxFlex="65%">
                            <div style="margin-top: 5px;">{{doc.ATTACHMENT_SOURCE_NAME | slice: 0:20}}{{longText1}}</div>
                          </div>
                          <div style="cursor: pointer;">
                            <img src="../../assets/images/Down.png" (click)="download(doc.ATTACHMENT_SOURCE_NAME, doc.ATTACHMENT_SOURCE)"
                              style="margin-top: 5px;">
                          </div>
                          <div style="cursor: pointer;">
                            <img src="../../assets/images/eye.png" (click)="preview(doc.ATTACHMENT_SOURCE)" style="margin-top: 7px;">
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="classify-content-container" fxLayout="column" fxLayoutGap="2%"
                    *ngIf="(selectedTab == 0 && whatsappComplaints?.length > 0) || (selectedTab == 1 && mailComplaints?.length > 0 && mailCount != 0) || (selectedTab == 2 && sysGenComplaints?.length > 0) || (selectedTab == 3 && chatbotComplaints?.length > 0)">
                    <div class="classfy-head">
                      <div class="comp-head" style="font-weight: 550;">COMPLAINTS CLASSIFICATION</div>
                      <div class="divider-container" style="width: 85%; margin-left: 138px;">
                        <mat-divider class="classfy-head-divider" [inset]="true"></mat-divider>
                      </div>
                    </div>
                    <div class="classification-container">
                      <mat-chip-list>
                        <mat-chip class="comp-chips" for="message">
                          {{!classificationTag ? '-' : classificationTag}}
                        </mat-chip>
                      </mat-chip-list>
                    </div>
                  </div>

                  <div class="classify-content-container" fxLayout="column" fxLayoutGap="2px"
                    *ngIf="(selectedTab == 0 && whatsappComplaints?.length > 0) || (selectedTab == 1 && !mailDetail['CASE_ID'] && mailComplaints?.length > 0 && mailCount != 0) || (selectedTab == 2 && sysGenComplaints?.length > 0) || (selectedTab == 3 && chatbotComplaints?.length > 0)">
                    <div class="classfy-head">
                      <div class="comp-head" style="font-weight: 550;">SIMILAR COMPLAINTS</div>
                      <div class="divider-container" style="width: 91%;margin-left: 79px;">
                        <mat-divider class="classfy-head-divider" [inset]="true"></mat-divider>
                      </div>
                    </div>
                    <mat-card *ngIf="similarCompLoading"
                      style="display: flex; justify-content: center; align-items: center; background: white;">
                      <mat-progress-spinner color="primary" mode="indeterminate" diameter="70">
                      </mat-progress-spinner>
                    </mat-card>
                    <div *ngIf="!similarCompLoading">
                      <div *ngFor="let similarComp of similarityComplaintList; let in = index;">
                        <div class="related-name-container" fxLayout="row" fxLayoutGap="2%" style="margin-bottom: 15px;">
                          <h4 for="message">
                            <img src="../assets/images/arrow_circle-icon.svg" />
                            <span class="related-name"
                              *ngIf="!matchFound">{{similarComp?.complaint_identification}}</span>
                            <span class="related-name" *ngIf="matchFound">{{similarComp?.prediction}} -
                              {{similarComp?.similarity_score}}% Matching</span>
                          </h4>
                          <span *ngIf="matchFound"><img src="../../assets/images/eye.svg" style="height: 18px; cursor: pointer;" (click)="complaintDetails(similarComp?.complaint_identification)"></span>
                        </div>
                        <div mat-line class="related-detail-container" *ngIf="!matchFound">
                          No related complaints found
                        </div>
                        <div mat-line class="related-detail-container" *ngIf="matchFound">
                          <p>
                            <span class="related-attribute" style="padding-right: 37px;">Case ID : </span>
                            <span class="related-value">{{similarComp?.complaint_identification}} &nbsp;&nbsp;</span>
                            <span class="related-value" *ngIf="similarComp?.old_complaint_identification != 'None'">({{similarComp?.old_complaint_identification}})</span>
                          </p>
                        </div>
                        <div mat-line class="related-detail-container" *ngIf="matchFound">
                          <p>
                            <span class="related-attribute" style="padding-right: 26px;">Company : </span>
                            <span class="related-value">{{similarComp?.company_name}}</span>
                          </p>
                        </div>
                        <div mat-line class="related-detail-container" *ngIf="matchFound">
                          <p>
                            <span class="related-attribute" style="padding-right: 47px;">Brand : </span>
                            <span class="related-value">{{similarComp?.brand_name}}</span>
                          </p>
                        </div>
                        <div mat-line class="related-detail-container" *ngIf="matchFound">
                          <p>
                            <span class="related-attribute" style="padding-right: 37px;">Product : </span>
                            <span class="related-value">{{similarComp?.product_name}}</span>
                          </p>
                        </div>
                        <div mat-line class="related-detail-container" *ngIf="matchFound">
                          <p>
                            <span class="related-attribute" style="padding-right: 15px;">Description :
                            </span>
                            <span class="related-value"
                              [matTooltip]="similarComp?.user_description">{{similarComp?.user_description
                              | slice:0:150}}</span>
                          </p>
                        </div>
                        <button mat-button class="add-btn" (click)="addNewComplaint(similarComp)" *ngIf="matchFound"
                          [disabled]="(selectedTab == 0 && (!whatsappDetail?.ID || !whatsappDetail?.COMPANY_ID || !whatsappDetail?.EMAIL_ID || !whatsappDetail?.MOBILE || !whatsappDetail?.COMPLAINT_DESCRIPTION || !whatsappDetail?.ADVERTISEMENT_DESCRIPTION || !whatsappDetail?.BRAND_NAME || similarityComplaintList.length == 0))
                          || (selectedTab == 1 && (!mailDetail?.COMPANY_ID || !mailDetail?.COMPANY_NAME || !mailDetail?.EMAIL_ID || !mailDetail?.MOBILE || !mailDetail?.COMPLAINT_DESCRIPTION || !!mailDetail?.CASE_ID || similarityComplaintList.length == 0))
                          || (selectedTab == 2 && (!sysGenDetail?.COMPANY_ID || !sysGenDetail?.MOBILE || !sysGenDetail?.COMPLAINT_DESCRIPTION || similarityComplaintList.length == 0))
                          || (selectedTab == 3 && (!chatbotDetails?.COMPANY_ID || !chatbotDetails?.COMPANY_NAME || !chatbotDetails?.MOBILE || !chatbotDetails?.COMPLAINT_DESCRIPTION || similarityComplaintList.length == 0))">
                          <mat-icon style="font-size: 12px;font-weight: bold; padding-top: 5px;">add</mat-icon><span style="font-size: 12px;">Add to this complaint</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>