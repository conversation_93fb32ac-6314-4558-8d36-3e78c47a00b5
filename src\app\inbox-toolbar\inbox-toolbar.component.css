::ng-deep .mat-form-field-flex .mat-form-field-infix { 
    /* background-color: rgb(255, 102, 0); */
    padding: 1px 2px !important;
    /* padding-bottom: 0.1em; */
    width: 200px !important;     /* WIDTH OF MAT-FORM-FIELD*/ 
    height: 1px !important;
    max-height: 1px !important;
}
::ng-deep .mat-form-field-appearance-outline {
    height: 1px !important; 
    border-radius: 13px !important;
}
::ng-deep .mat-form-field-prefix,   /*  CENTERIZE SEARCHBAR ICONS  */
::ng-deep .mat-form-field-suffix {
		align-self: flex-end !important;
	}
/* ::ng-deep .mat-form-field-appearance-outline .mat-form-field-label {
    border: 1px solid rgb(8, 56, 42);
    height: 1px !important; 
} */
/* ::ng-deep .mat-form-field-label-wrapper { top: -1.5em; } */
/* ::ng-deep .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    transform: translateY(-1.1em) scale(.75);
    width: 133.33333%;
} */


:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline .mat-form-field-outline-start ,
:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline .mat-form-field-outline-end {
  border-radius: 30px  !important;
  border: 1px solid rgb(179, 179, 179);
}



.toolbar{
    background-color: white;
    height: 60px; 
    width: auto;
    margin-left: 40px;
}
.inbox-head{
    padding-left:20px;
}
.toolbar-spacer{
    flex: 1 1 auto;
}
.toolbar-btns{
    /* float:right; */
    display: flex;
    flex-direction: row;
    grid-gap:20px;
    padding-right: 10px;
}
.search-btn{
    color: rgb(151, 151, 151);
    border:1px solid rgb(189, 189, 189);
    border-radius: 13px;
}
.separator {
    /* background-color: blue; */

    color: rgb(184, 184, 184);
    /* vertical-align: text-top !important;
    
    align-self: flex-end !important; */
    padding-bottom: 15px;
    /* margin-top: 0vh !important;
    padding-bottom: 10px ;  */
}
.searchbar-btn{
    height: min-content !important;
    width: max-content !important;
    margin-bottom: 5px !important;
    /* padding-bottom: 10px; */
    /* vertical-align: super !important; */
}
.input-field{
    display: flex;
    align-items: center;
    margin-top: 30px;
}
.search-icons{
    /* background-color: rgb(37, 220, 20); */
    color: rgb(184, 184, 184);
    font-size: larger;
}
.search-input{
    /* background-color: crimson; */
    font-size: medium;
    width: 190px;
    height: 20px;
    align-self: flex-end !important;
    margin-bottom: 5px !important;
    padding: 0px 0px 2px 5px;
}
.bell-btn{
    color: crimson;
    border:1px solid rgb(189, 189, 189);
    border-radius: 13px;
}
.admin-btn{
    color: rgb(255, 255, 255);
    background-color: rgb(51, 134, 212);
    border-radius: 15px;
    /* border: 1px solid crimson; */
    /* border-radius: 30px; */
}
.admin-option-container{
    /* height: 90px;
    width: 200px; */
    height: 10%;
    width: 100%; 
}
.option-btn{
    line-height: 15px;
    height: 30px;
}
.option-text{
    line-height: 15px;
}


.input-container{
    border: 1px solid #CFD7DF;
    /* box-sizing: border-box; */
    height: 40px;
    width: 275px;
    /* border-radius: 12px; */
    border-radius: 15px;
}
.input-search{
    /* font-size: 10px; */
    font-size: 15px;
    height: 35px;
    border: none;
    /* border-radius: 12px; */
    padding: 5px 10px;
}
.toolbar-search-icon{
    /* vertical-align: middle; */
    font-weight: 600;
}   
