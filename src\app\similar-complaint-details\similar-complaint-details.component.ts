import { Component, HostListener, Inject, OnInit, Pi<PERSON> } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { environment } from 'src/environments/environment';
import { ComplaintsService } from '../services/complaints.service';
import { saveAs as importedSaveAs } from "file-saver";
import { ViewMediaFileComponent } from '../cases/view-media-file/view-media-file.component';
import { DomSanitizer } from '@angular/platform-browser';
import { NotificationService } from '../services/notification.service';
import { colorObj } from '../shared/color-object';

@Component({
  selector: 'app-similar-complaint-details',
  templateUrl: './similar-complaint-details.component.html',
  styleUrls: ['./similar-complaint-details.component.scss']
})
@Pipe({ name: 'safeHtml' })
export class SimilarComplaintDetailsComponent implements OnInit {

  compDetails: any = [];
  company_details: any;
  detail_adsource: any;
  detail_platform: any;
  detail_channel: any;
  detail_addate: any;
  detail_link: any;
  detail_place: any;
  longText1: string;
  longText2: string;
  longText4: string;
  longText5: string;
  public bucketUrl = `${environment.BUCKET_URL}`;
  docUrl;
  media_outlet: any;
  media: any;
  super_category: any;
  edition: any;
  suppliment: any;
  ad_language: any;
  creative_id: any;
  translation_hyper: any;
  influencer_name: any;
  engagements: any;
  publication_url: any;
  profile_url: any;
  influencer_contact: any;
  influencer_email: any;
  complaint_source_id: number;
  seen_date: any;
  transcription: any;
  duration: any;
  detail_advert: string;
  detail_complaint: string;
  noOfDocs = 0;
  adDocs: any;
  adMedium: any;
  imgURL: string;
  url: string;
  parent_id: any;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private cs: ComplaintsService,
    private matDialog: MatDialog,
    private dialogRef: MatDialogRef<SimilarComplaintDetailsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private sanitized: DomSanitizer,
    private notify: NotificationService
  ) { }

  safeHTML(unsafe: string) {
    return this.sanitized.bypassSecurityTrustHtml(unsafe);
  }

  ngOnInit(): void {
    this.complaintDetails(this.data.CASE_ID);
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  complaintDetails(case_id) {
    this.parent_id = "";
    this.cs.getSimilarComplaints(this.parent_id, case_id).subscribe(res => {
      this.compDetails = res.data;
      if (this.compDetails.length != 0) {
        this.complaint_source_id = this.compDetails.COMPLAINT_SOURCE_ID;
        this.seen_date = this.compDetails.DATE;
        this.transcription = this.compDetails.TRANSCRIPTION;
        this.duration = this.compDetails.DURATION;
        this.detail_advert = this.compDetails.ADVERTISEMENT_DESCRIPTION;
        this.adDocs = this.compDetails.ADVERTISEMENT_MEDIUM_DOCUMENT;
        this.adMedium = res.data.ADVERTISEMENT_MEDIUM;
        for (let i = 0; i < this.compDetails.ADVERTISEMENT_MEDIUM_DOCUMENT.length; i++) {
          if (this.compDetails.ADVERTISEMENT_MEDIUM_DOCUMENT[i].ATTACHMENT_SOURCE !== '') {
            this.noOfDocs += 1;
          }
        }
        if (this.detail_link) {
          if (this.detail_advert.length > 10) {
            this.longText1 = ' ...'
          }
        }
        this.detail_complaint = this.compDetails.COMPLAINT_DESCRIPTION;
        if (this.compDetails.ADVERTISEMENT_MEDIUM.length != 0) {
          this.detail_adsource = this.compDetails.ADVERTISEMENT_MEDIUM;
          this.detail_platform = this.compDetails.ADVERTISEMENT_MEDIUM[0].PLATFORM_ID;
          this.detail_channel = this.compDetails.ADVERTISEMENT_MEDIUM[0].SOURCE_NAME;
          this.detail_addate = this.compDetails.ADVERTISEMENT_MEDIUM[0].DATE;
          this.detail_link = this.compDetails.ADVERTISEMENT_MEDIUM[0].SOURCE_URL;
          if (this.detail_link) {
            if (this.detail_link.length > 28) {
              this.longText4 = '..';
            } else {
              this.longText4 = ' ';
            }
          }
          this.docUrl = this.bucketUrl + this.detail_link;
          this.detail_place = this.compDetails.ADVERTISEMENT_MEDIUM[0].SOURCE_PLACE;
        }
        if (this.compDetails.COMPLAINT_SOURCE_ID == 7) {
          this.media_outlet = this.compDetails.MEDIA_OUTLET;
          this.media = this.compDetails.MEDIA;
          this.edition = this.compDetails.EDITION;
          this.super_category = this.compDetails.PRODUCT_CATEGORY;
          if (this.super_category) {
            if (this.super_category.length > 10) {
              this.longText2 = '...'
            }
          }
          this.ad_language = this.compDetails.AD_LANGUAGE;
          this.suppliment = this.compDetails.SUPPLIMENT;
          this.creative_id = this.compDetails.CREATIVE_ID;
          this.translation_hyper = this.compDetails.TRANSLATION_HYPERLINK;
          if (this.translation_hyper) {
            if (this.translation_hyper.length > 28) {
              this.longText5 = '..';
            } else {
              this.longText5 = '..';
            }
          }
        }
        if (this.compDetails.COMPLAINT_SOURCE_ID == 8) {
          this.influencer_name = this.compDetails.INFLUENCER_NAME;
          this.engagements = this.compDetails.ENGAGEMENTS;
          this.publication_url = this.compDetails.PUBLICATION_URL;
          this.profile_url = this.compDetails.PROFILE_URL;
          this.influencer_contact = this.compDetails.INFLUENCER_MOBILE;
          this.influencer_email = this.compDetails.INFLUENCER_EMAIL;
        }
        if (this.compDetails.COMPANY_INFO.length != 0) {
          this.company_details = this.compDetails.COMPANY_INFO;
        }
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    })
  }

  download(doc, source) {
    this.url = this.bucketUrl + source;
    this.cs.downloadFile(this.url).subscribe(data => {
      importedSaveAs(data, doc)
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    });
  }

  preview(source) {
    this.imgURL = this.bucketUrl + source;
    window.open(this.imgURL, 'window 1', '');
  }

  previewLink(source) {
    if (source.indexOf("https") == -1) {
      window.open("https://" + source, "_blank");
    } else {
      window.open(source, "_blank");
    }
  }

  openNewTab() {
    const dialogRef = this.matDialog.open(ViewMediaFileComponent, {
      width: '476px',
      height: 'auto',
      data: { adMedium: this.adMedium, adDocs: this.adDocs },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
      }
    );
  }

}