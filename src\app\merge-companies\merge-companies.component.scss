:host ::ng-deep .mat-form-field-flex > .mat-form-field-infix { padding: 0px 0px 0.4em 0px !important; }
.merge-box {
    background: #FFFFFF;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 3px;
    height: auto;
}
.contact-info {
    background: #FFFFFF;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 3px;
    width: 90%;
    padding-left: 15px;
    padding-top: 10px;
    margin-bottom: 15px;
}
.mat-expansion-panel:not([class*='mat-elevation-z']) {
    box-shadow: none;
}
 .mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:hover:not([aria-disabled=true]){
    background: none;
}
.mat-expansion-panel {
    position: relative;
    bottom: 49px;
    right: 188px;
    overflow: visible;
    background: none;
}
:host ::ng-deep .mat-expansion-panel-body {
    padding: 0;
}
:host ::ng-deep .mat-expansion-panel-header {
    width: 35px;
    position: relative;
    left: 376px;
}
.form {
    width: 50%;
}
.merge {
    width: 50%;
    margin-top: 15px;
}
.divider {
    position: relative;
    width: 35%;
    top: 14px;
    left: 5px;
}
.remove-box {
    background: #FFFFFF;
    border: 1px solid #ED2F45;
    box-sizing: border-box;
    border-radius: 4px;
    width: 35px;
    height: 31px;
    margin-top: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}
.company-info {
    padding: 7px 0px 9px 22px;
}
.content {
    overflow-y: scroll;
    overflow-x: hidden;
    height: 460px;
}
.content::-webkit-scrollbar {
    display: none;
}
.content {
    -ms-overflow-style: none;
    scrollbar-width: none;
}