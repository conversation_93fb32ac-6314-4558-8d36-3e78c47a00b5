<div fxLayout="row">
    <mat-toolbar class="toolbar">
        <div fxFlex="35%" fxLayoutAlign="start" *ngIf="!editSubTag">
            <h2 mat-dialog-title fxLayoutAlign="start" style="font-size: 16px;">
                <mat-icon style="position: relative; top: 7px; font-size: 16px;">add</mat-icon>Add new sub tag
            </h2>
        </div>
        <div fxFlex="35%" fxLayoutAlign="start" *ngIf="editSubTag">
            <h2 mat-dialog-title fxLayoutAlign="start" style="font-size: 16px;">
                <img src="../assets/images/edit-icon.svg">&nbsp; Edit sub tag
            </h2>
        </div>
        <div fxFlex="60%"></div>
        <div fxFlex="5%" fxLayoutAlign="end">
                <button mat-icon-button class="search-btn" mat-dialog-close>
                    <mat-icon style="margin-bottom: 4px;">close</mat-icon>
                </button>
        </div>
    </mat-toolbar>
</div>

<mat-divider></mat-divider>

<div class="form">
    <form [formGroup]="addform">
        <div class="contents">
            <div class="names1" style="margin-top: 5px;">
                <div>Sub tag name</div>
            </div>
            <div class="phone_number">
                <mat-form-field appearance="outline" class="input-field_subtag">
                    <input matInput type="text" id="subTag" formControlName="SUB_TAG_NAME" style="
                    font-style: normal;
                    font-weight: normal; height: 20px;" autocomplete="off">
                    <mat-error class="error-msg" *ngIf="addform.controls['SUB_TAG_NAME'].errors?.required">
                        Sub tag is required
                    </mat-error> 
                </mat-form-field>
            </div>
        </div>
        <div class="lastdivdr">
            <mat-divider></mat-divider>
        </div>
        <div class="toolbar-btns2">
            <div>
                <mat-dialog-actions>
                    <div fxFlex="45%" fxLayoutAlign="start">
                        <button mat-flat-button fxLayoutAlign="start" *ngIf="editSubTag" class="remove-btn" [disabled]="addform.invalid"
                            (click)="removeTag()">
                            <span>Remove Tag</span>
                        </button>
                    </div>
                    <div fxFlex="5%"></div>
                    <div class="toolbar-btns" fxFlex="47%" fxLayoutAlign="end">
                        <button mat-flat-button class="cancel-btn" mat-dialog-close>
                            <span>Cancel</span>
                        </button>
                        <button mat-flat-button class="save-btn" [disabled]="addform.invalid"
                            (click)="save_updateSubtag(addform.value)">
                            <span *ngIf="!editSubTag">Add Tag</span>
                            <span *ngIf="editSubTag">Update details</span>
                        </button>
                    </div>
                    <div fxFlex="3%"></div>
                </mat-dialog-actions>
            </div>
        </div>
    </form>
</div>