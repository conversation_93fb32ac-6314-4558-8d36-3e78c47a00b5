import { SelectionModel } from '@angular/cdk/collections';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { Component, HostListener, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatChipInputEvent } from '@angular/material/chips';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { QuillConfiguration } from '../model/quill-configuration';
import { ComplaintsService } from '../services/complaints.service';
import { NotificationService } from '../services/notification.service';
import { UploadService } from '../services/upload.service';
import { colorObj } from '../shared/color-object';

@Component({
  selector: 'app-nams-email',
  templateUrl: './nams-email.component.html',
  styleUrls: ['./nams-email.component.scss']
})
export class NamsEmailComponent implements OnInit {

  quillConfiguration = QuillConfiguration;
  namsEmailForm: FormGroup;
  emailPattern = environment.emailPatterm;
  userData: any;
  complaint_id;
  emailList = [];
  emailIdList = [];
  emailCcList = [];
  emailIdCcList = [];
  emailBccList = [];
  emailIdBccList = [];
  public separatorKeysCodes = [ENTER, COMMA];
  removable = true;
  noEmails: boolean = false;
  templateButtonName = "Use Template";
  isTemplate: boolean = false;
  isEditTemplate: boolean;
  emailTemplates = [];
  selectedTemplate = [];
  selectTemplate: boolean = false;
  disableSend: boolean;
  usersList = [];
  uploadFile: boolean = false;
  isAttachFile: boolean = true;
  docFileList: any[] = [];
  isUploadProgress: boolean = false;
  dat = {};
  selectedFiles = [];
  uploadFlag: boolean = false;
  selection = new SelectionModel<any>(true, []);
  template_mail;
  internalEmails: any[];
  advAndCompEmails = [];
  complainantFiles = [];
  advertiserFiles = [];
  internalFiles = [];
  public bucketUrl = `${environment.BUCKET_URL}`;
  imgURL: string;
  attachId = [];
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private dialogRef: MatDialogRef<NamsEmailComponent>,
    private fb: FormBuilder,
    private cs: ComplaintsService,
    private notify: NotificationService,
    private uploadService: UploadService,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.namsEmailForm = this.fb.group({
      TO_EMAIL_ID: [[], [Validators.required, Validators.pattern(this.emailPattern)]],
      CC_EMAIL_ID: [[], [Validators.pattern(this.emailPattern)]],
      BCC_EMAIL_ID: [[], [Validators.pattern(this.emailPattern)]],
      TITLE_CASEID: ['', [Validators.required]],
      TITLE: ['', [Validators.required]],
      BODY: ['', [Validators.required]]
    })
  }

  ngOnInit(): void {
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
    this.userData = JSON.parse(window.localStorage.getItem('userInfo'));
    this.namsEmailForm.controls['TITLE_CASEID'].setValue('Case ID: ' + this.data.CASE_ID + ' - ');
    if (this.data.name == 'toAdvertiser') {
      this.usersList = this.data.toSender;
      if (this.data.mail) {
        this.emailList = this.data.mail;
      }
      for (let i = 0; i < this.usersList.length; i++) {
        if (this.data.mail == this.usersList[i].EMAIL_ID) {
          this.emailIdList.push(this.usersList[i].USER_ID);
        }
      }
    } else if (this.data.name == 'toComplainant') {
      this.usersList = this.data.complainants;
      if (this.data.mail) {
        this.emailList.push(this.data.mail);
      }
      for (let i = 0; i < this.usersList.length; i++) {
        if (this.data.mail == this.usersList[i].EMAIL_ID) {
          this.emailIdList.push(this.usersList[i].USER_ID);
        }
      }
    } else if (this.data.name == 'toAllAdvertisers') {
      this.usersList = this.data.toSender;
      this.emailList = this.data.mail;
      for (let j = 0; j < this.emailList.length; j++) {
        for (let i = 0; i < this.usersList.length; i++) {
          if (this.emailList[j] == this.usersList[i].EMAIL_ID) {
            this.emailIdList.push(this.usersList[i].USER_ID);
          }
        }
      }
    } else if (this.data.name == 'toCompanyMail') {
      this.usersList = this.data.toSender;
      if (this.data.mail) {
        this.emailList = this.data.mail;
      }
      for (let i = 0; i < this.usersList.length; i++) {
        if (this.data.mail == this.usersList[i].EMAIL_ID) {
          this.emailIdList.push(this.usersList[i].USER_ID);
        }
      }
    } else if (this.data.name == 'toAllCompMails') {
      this.usersList = this.data.toSender;
      this.emailList = this.data.mail;
      for (let j = 0; j < this.emailList.length; j++) {
        for (let i = 0; i < this.usersList.length; i++) {
          if (this.emailList[j] == this.usersList[i].EMAIL_ID) {
            this.emailIdList.push(this.usersList[i].USER_ID);
          }
        }
      }
    } else if (this.data.name == 'intra') {
      this.usersList = this.data.users;
      if (this.data.mail) {
        this.emailList = this.data.mail;
      }
      for (let i = 0; i < this.usersList.length; i++) {
        if (this.data.mail == this.usersList[i].EMAIL_ID) {
          this.emailIdList.push(this.usersList[i].ID);
        }
      }
    } else if (this.data.name == 'toIntraAssignee') {
      this.usersList = this.data.users;
      if (this.data.mail) {
        this.emailList = this.data.mail;
      }
      for (let i = 0; i < this.usersList.length; i++) {
        if (this.data.mail == this.usersList[i].EMAIL_ID) {
          this.emailIdList.push(this.usersList[i].USER_ID);
        }
      }
    } else if (this.data.name == 'toInternal') {
      this.usersList = this.data.toSender;
      if (this.data.mail) {
        this.emailList = this.data.mail;
      }
      for (let i = 0; i < this.usersList.length; i++) {
        if (this.data.mail == this.usersList[i].EMAIL_ID) {
          this.emailIdList.push(this.usersList[i].ID);
        }
      }
    } else if (this.data.name == 'toNewEmail') {
      this.usersList = this.data.toSender;
      if (this.data.mail) {
        this.emailList = this.data.mail;
      }
    }
    if (this.data.call_from == 'system') {
      this.complaint_id = this.data.ID;
    }
    else if (this.data.call_from == 'chatbot') {
      this.complaint_id = this.data.CHATBOT_ID;
    }
    let output = document.getElementById('output');
    let buttons = document.getElementsByClassName('tool--btn');
    for (let btn = 0; btn < buttons.length; btn++) {
      buttons[btn].addEventListener('click', () => {
        let cmd = buttons[btn].getAttribute('data-command');
        if (cmd === 'createlink') {
          let url = prompt("Enter the link here: ", "http:\/\/");
          document.execCommand(cmd, false, url);
        } else {
          document.execCommand(cmd, false, null);
        }
      })
    }

    this.namsEmailForm.get('TO_EMAIL_ID')!.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0 && (this.userData.roleId == 1 || this.userData.roleId == 2 || this.userData.roleId == 3 || this.userData.roleId == 5 || this.userData.roleId == 6)) {
          this.getAssigneeList(value);
        }
      }, err => {
      })

    this.namsEmailForm.get('CC_EMAIL_ID')!.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0 && (this.userData.roleId == 1 || this.userData.roleId == 2 || this.userData.roleId == 3 || this.userData.roleId == 5 || this.userData.roleId == 6)) {
          this.getAssigneeList(value);
        }
      }, err => {
      })

    this.namsEmailForm.get('BCC_EMAIL_ID')!.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0 && (this.userData.roleId == 1 || this.userData.roleId == 2 || this.userData.roleId == 3 || this.userData.roleId == 5 || this.userData.roleId == 6)) {
          this.getAssigneeList(value);
        }
      }, err => {
      })

    if (this.userData.roleId == 1 || this.userData.roleId == 2 || this.userData.roleId == 3 || this.userData.roleId == 5 || this.userData.roleId == 6) {
      this.getAdvAndComp();
    }
  }

  getAssigneeList(email) {
    this.cs.getAssigneeList('', email).subscribe(res => {
      this.internalEmails = res.data;
      if (this.internalEmails.length == 0) {
        this.internalEmails = this.advAndCompEmails;
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  getAdvAndComp() {
    this.cs.getAdvertiser(this.data.COMP_ID).subscribe((advcompanymails: any) => {
      let advCompArray = [];
      advCompArray = advcompanymails.data;
      if (advCompArray.length != 0) {
        if (advCompArray[0].ADVERTISER_CONTACT.length != 0) {
          for (let item of advCompArray[0].ADVERTISER_CONTACT) {
            if (item.EMAIL_ID != null && item.EMAIL_ID != "") {
              this.advAndCompEmails.push({ EMAIL_ID: item.EMAIL_ID });
            }
          }
        }
        if (advCompArray[0].COMPANY_CONTACT.length != 0) {
          for (let item of advCompArray[0].COMPANY_CONTACT) {
            if (item.EMAIL_ID != null && item.EMAIL_ID != "") {
              this.advAndCompEmails.push({ EMAIL_ID: item.EMAIL_ID });
            }
          }
        }
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    })
  }

  onSelectionChange(event: MatAutocompleteSelectedEvent): void {
    // for (let j = 0; j < this.emailList.length; j++) {
    // if (event.option.value != this.emailList[j]) {
    this.emailList.push(event.option.value);
    this.internalEmails.forEach(element => {
      if (event.option.value === element.EMAIL_ID) {
        this.emailIdList.push(element.ID);
      }
    });
    // }
    // }
    this.removeDuplicateEmails(this.emailList);
  }

  onSelectionChangeCc(event: MatAutocompleteSelectedEvent): void {
    // for (let j = 0; j < this.emailCcList.length; j++) {
    // if (event.option.value != this.emailCcList[j]) {
    this.emailCcList.push(event.option.value);
    this.internalEmails.forEach(element => {
      if (event.option.value === element.EMAIL_ID) {
        this.emailIdCcList.push(element.ID);
      }
    });
    // }
    // }
    this.removeDuplicateCcEmails(this.emailCcList);
  }

  onSelectionChangeBcc(event: MatAutocompleteSelectedEvent): void {
    // for (let j = 0; j < this.emailBccList.length; j++) {
    // if (event.option.value != this.emailBccList[j]) {
    this.emailBccList.push(event.option.value);
    this.internalEmails.forEach(element => {
      if (event.option.value === element.EMAIL_ID) {
        this.emailIdBccList.push(element.ID);
      }
    });
    // }
    // }
    this.removeDuplicateBccEmails(this.emailBccList);
  }

  getAllTemplates(id) {
    this.cs.getEmailTemplates(id).subscribe((templates: any) => {
      this.emailTemplates = templates.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    })
  }

  openTemplate(id) {
    this.selectedTemplate = [];
    this.selectTemplate = true;
    this.isAttachFile = true;
    this.isTemplate = false;
    this.cs.getEmailTemplates(id).subscribe((template: any) => {
      this.selectedTemplate = template.data;
      this.namsEmailForm.patchValue({
        'TITLE': this.selectedTemplate[0]['SUBJECT'],
        'BODY': this.selectedTemplate[0]['BODY']
      })
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    })
  }

  viewTemplate(key) {
    if (key == 'select') {
      this.isTemplate = !this.isTemplate;
    }
    else if (key == 'clear') {
      this.isTemplate = false;
    }
    this.selectTemplate = false;
    this.uploadFile = false;
    this.isAttachFile = true;
    this.namsEmailForm.controls['TITLE'].setValue('');
    this.namsEmailForm.controls['BODY'].setValue('');
    if (this.isTemplate == true) {
      this.getAllTemplates('');
    }
  }

  changeTemplate() {
    this.isTemplate = true;
    this.selectTemplate = false;
    this.uploadFile = false;
    this.isAttachFile = false;
    this.getAllTemplates('');
    this.namsEmailForm.controls['TITLE'].setValue('');
    this.namsEmailForm.controls['BODY'].setValue('');
  }

  sendEmail(form) {
    if (!form.BODY) {
      this.template_mail = document.getElementById('template_mail').innerHTML;
      if (!!this.template_mail) {
        form.BODY = this.template_mail;
      }
    }
    form.TO_EMAIL_ID = this.emailList;
    form.CC_EMAIL_ID = this.emailCcList;
    form.BCC_EMAIL_ID = this.emailBccList;
    form.BODY = "<pre><font face='Arial'>" + form.BODY + "</font></pre>";
    if (this.emailList.length != 0) {
      this.cs.sendEmails(form, this.data.ID, this.data.CHATBOT_ID, this.emailIdList, this.emailIdCcList, this.emailIdBccList, this.selectedFiles, this.data.call_from).subscribe(res => {
        this.notify.showNotification(
          res.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status] : "success"),
          res.status
        )
        this.dialogRef.close({ 'state': 'refresh', 'name': this.data.name });
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      })
      this.selectedFiles = [];
    } else {
      this.notify.showNotification(
        "Please make sure that you have added atleast one valid Email ID",
        "top",
        "warning",
        0
      )
      return;
    }
  }

  addEmail(event: MatChipInputEvent) {
    const input = event.input;
    const value = (event.value || '').trim();
    if (value) {
      if (this.namsEmailForm.get('TO_EMAIL_ID').invalid) {
        // this.notify.showNotification(
        //   "Invalid Email ID",
        //   "top",
        //   "warning",
        //   0
        // );
      }
      else if (this.namsEmailForm.controls['TO_EMAIL_ID'].valid) {
        if (this.userData.roleId == 1 || this.userData.roleId == 2 || this.userData.roleId == 3 || this.userData.roleId == 6) {
          if (this.usersList.length != 0) {
            for (let i = 0; i < this.usersList.length; i++) {
              if (value == this.usersList[i].EMAIL_ID) {
                this.emailList.push(value);
                this.emailIdList.push(this.usersList[i].USER_ID);
              }
              else {
                this.emailList.push(value);
                this.emailIdList.push("");
                break;
              }
            }
          }
          else {
            this.emailList.push(value);
            this.emailIdList.push("");
          }
        }
        else if (this.userData.roleId == 4 || this.userData.roleId == 5 || this.userData.roleId == 7 || this.userData.roleId == 8) {
          for (let j = 0; j < this.emailList.length; j++) {
            if (value != this.emailList[j]) {
              if (this.usersList.length != 0) {
                for (let i = 0; i < this.usersList.length; i++) {
                  if (value == this.usersList[i].EMAIL_ID) {
                    this.emailList.push(value);
                    this.emailIdList.push(this.usersList[i].USER_ID);
                  }
                }
              }
            }
          }
        }
        this.removeDuplicateEmails(this.emailList);
      }
    }
    if (input) {
      input.value = '';
    }
  }

  addCcEmail(event: MatChipInputEvent) {
    const input = event.input;
    const value = (event.value || '').trim();
    if (value) {
      if (this.namsEmailForm.get('CC_EMAIL_ID').invalid) {
        // this.notify.showNotification(
        //   "Invalid Email ID",
        //   "top",
        //   "warning",
        //   0
        // );
      }
      else if (this.namsEmailForm.controls['CC_EMAIL_ID'].valid) {
        if (this.userData.roleId == 1 || this.userData.roleId == 2 || this.userData.roleId == 3 || this.userData.roleId == 6) {
          if (this.usersList.length != 0) {
            for (let i = 0; i < this.usersList.length; i++) {
              if (value == this.usersList[i].EMAIL_ID) {
                this.emailCcList.push(value);
                this.emailIdCcList.push(this.usersList[i].USER_ID);
              }
              else {
                this.emailCcList.push(value);
                this.emailIdCcList.push("");
                break;
              }
            }
          }
          else {
            this.emailCcList.push(value);
            this.emailIdCcList.push("");
          }
        }
        else if (this.userData.roleId == 4 || this.userData.roleId == 5 || this.userData.roleId == 7 || this.userData.roleId == 8) {
          for (let j = 0; j < this.emailCcList.length; j++) {
            if (value != this.emailCcList[j]) {
              if (this.usersList.length != 0) {
                for (let i = 0; i < this.usersList.length; i++) {
                  if (value == this.usersList[i].EMAIL_ID) {
                    this.emailCcList.push(value);
                    this.emailIdCcList.push(this.usersList[i].USER_ID);
                  }
                }
              }
            }
          }
        }
        this.removeDuplicateCcEmails(this.emailCcList);
      }
    }
    if (input) {
      input.value = '';
    }
  }

  addBccEmail(event: MatChipInputEvent) {
    const input = event.input;
    const value = (event.value || '').trim();
    if (value) {
      if (this.namsEmailForm.get('BCC_EMAIL_ID').invalid) {
        // this.notify.showNotification(
        //   "Invalid Email ID",
        //   "top",
        //   "warning",
        //   0
        // );
      }
      else if (this.namsEmailForm.controls['BCC_EMAIL_ID'].valid) {
        if (this.userData.roleId == 1 || this.userData.roleId == 2 || this.userData.roleId == 3 || this.userData.roleId == 6) {
          if (this.usersList.length != 0) {
            for (let i = 0; i < this.usersList.length; i++) {
              if (value == this.usersList[i].EMAIL_ID) {
                this.emailBccList.push(value);
                this.emailIdBccList.push(this.usersList[i].USER_ID);
              }
              else {
                this.emailBccList.push(value);
                this.emailIdBccList.push("");
                break;
              }
            }
          }
          else {
            this.emailBccList.push(value);
            this.emailIdBccList.push("");
          }
        }
        else if (this.userData.roleId == 4 || this.userData.roleId == 5 || this.userData.roleId == 7 || this.userData.roleId == 8) {
          for (let j = 0; j < this.emailBccList.length; j++) {
            if (value != this.emailBccList[j]) {
              if (this.usersList.length != 0) {
                for (let i = 0; i < this.usersList.length; i++) {
                  if (value == this.usersList[i].EMAIL_ID) {
                    this.emailBccList.push(value);
                    this.emailIdBccList.push(this.usersList[i].USER_ID);
                  }
                }
              }
            }
          }
        }
        this.removeDuplicateBccEmails(this.emailBccList);
      }
    }
    if (input) {
      input.value = '';
    }
  }

  async removeDuplicateEmails(data) {
    let unique = [];
    data.forEach(element => {
      if (!unique.includes(element)) {
        unique.push(element);
      }
    })
    this.emailList = unique;
  }

  async removeDuplicateCcEmails(data) {
    let unique = [];
    data.forEach(element => {
      if (!unique.includes(element)) {
        unique.push(element);
      }
    })
    this.emailCcList = unique;
  }

  async removeDuplicateBccEmails(data) {
    let unique = [];
    data.forEach(element => {
      if (!unique.includes(element)) {
        unique.push(element);
      }
    })
    this.emailBccList = unique;
  }

  removeEmail(item): void {
    const index = this.emailList.indexOf(item);
    if (index >= 0) {
      this.emailList.splice(index, 1);
      this.emailIdList.splice(index, 1);
    }
    if (this.emailList.length == 0) {
      this.noEmails = true;
    }
  }

  removeCcEmail(item): void {
    const index = this.emailCcList.indexOf(item);
    if (index >= 0) {
      this.emailCcList.splice(index, 1);
      this.emailIdCcList.splice(index, 1);
    }
    if (this.emailCcList.length == 0) {
      this.noEmails = true;
    }
  }

  removeBccEmail(item): void {
    const index = this.emailBccList.indexOf(item);
    if (index >= 0) {
      this.emailBccList.splice(index, 1);
      this.emailIdBccList.splice(index, 1);
    }
    if (this.emailBccList.length == 0) {
      this.noEmails = true;
    }
  }

  uploadAttachment() {
    this.uploadFile = true;
    this.isTemplate = false;
    this.isAttachFile = false;
    this.getDocuments(this.complaint_id);
    if (!this.namsEmailForm.value['BODY']) {
      this.template_mail = document.getElementById('template_mail').innerHTML;
      this.template_mail = this.template_mail.replace(/<[^>]+>/g, '');
      if (!!this.template_mail) {
        this.namsEmailForm.controls['BODY'].setValue(this.template_mail);
      }
    }
  }

  async onFileChangeInternal(event: any) {
    // ToDo: check here 
    if (!this.uploadService.validateFileExtension(event)) {
      this.notify.showNotification(
        "File format is not supported!",
        "top",
        "warning",
        0
      );
      return;
    }
    let sizeOfAllFiles = this.uploadService.getTotalFileSize(this.internalFiles) + this.uploadService.getTotalFileSize(event.target.files);
    // let totalFileSelected = this.internalFiles.length + event.target.files.length;
    if (sizeOfAllFiles <= 36700160) {
      this.isUploadProgress = true;
      for (let i = 0; i <= event.target.files.length - 1; i++) {
        var selectedFile = event.target.files[i];
        this.internalFiles.push(selectedFile);
        let tempObjforGetsigned = {
          id: this.complaint_id,
          section: 'internal',
          filename: selectedFile['name'],
          type: selectedFile['type']
        }
        await this.uploadService.getSignedUrl(tempObjforGetsigned).then(async (res) => {
          if (res && res['data'] && res['data']['SIGNED_URL']) {
            let tempObjForUpload = {
              url: res['data']['SIGNED_URL'],
              file: selectedFile
            }
            await this.uploadSignedFile(tempObjForUpload);
            let saveBody = {
              ID: this.complaint_id,
              KEY: res['data']['PATH'],
              SOURCE_NAME: selectedFile['name'],
              TYPE: selectedFile['type'],
              TAB: 'internal'
            }
            await this.saveDocumentToDB(saveBody);
          }
        })
          .catch((err) => {
            this.isUploadProgress = false;
            this.internalFiles.splice(i, 1);
            this.handleError(err);
          });
      }
      this.isUploadProgress = false;
      this.notify.showNotification(
        'Documents uploaded successfully',
        "top",
        (!!colorObj[200] ? colorObj[200] : "success"),
        200
      );
    } else {
      this.notify.showNotification(
        "Max size 35MB allowed",
        "top",
        "warning",
        0
      );
    }
  }

  async onFileChangeAdvertiser(event: any) {
    // ToDo: check here 
    if (!this.uploadService.validateFileExtension(event)) {
      this.notify.showNotification(
        "File format is not supported!",
        "top",
        "warning",
        0
      );
      return;
    }
    let sizeOfAllFiles = this.uploadService.getTotalFileSize(this.advertiserFiles) + this.uploadService.getTotalFileSize(event.target.files);
    // let totalFileSelected = this.advertiserFiles.length + event.target.files.length;
    if (sizeOfAllFiles <= 36700160) {
      this.isUploadProgress = true;
      for (let i = 0; i <= event.target.files.length - 1; i++) {
        var selectedFile = event.target.files[i];
        this.advertiserFiles.push(selectedFile);
        let tempObjforGetsigned = {
          id: this.data.ID,
          section: 'company_member',
          filename: selectedFile['name'],
          type: selectedFile['type']
        }
        await this.uploadService.getSignedUrl(tempObjforGetsigned).then(async (res) => {
          if (res && res['data'] && res['data']['SIGNED_URL']) {
            let tempObjForUpload = {
              url: res['data']['SIGNED_URL'],
              file: selectedFile
            }
            await this.uploadSignedFile(tempObjForUpload);
            let saveBody = {
              ID: this.data.ID,
              KEY: res['data']['PATH'],
              SOURCE_NAME: selectedFile['name'],
              TYPE: selectedFile['type'],
              TAB: 'advertiser'
            }
            await this.saveDocumentToDB(saveBody);
          }
        })
          .catch((err) => {
            this.isUploadProgress = false;
            this.advertiserFiles.splice(i, 1);
            this.handleError(err);
          });
      }
      this.isUploadProgress = false;
      this.notify.showNotification(
        'Documents uploaded successfully',
        "top",
        (!!colorObj[200] ? colorObj[200] : "success"),
        200
      );
    } else {
      this.notify.showNotification(
        "Max size 35MB allowed",
        "top",
        "warning",
        0
      );
    }
  }

  async saveDocumentToDB(tempObj) {
    await this.cs.saveFileSourceToDB(tempObj).then((data) => {
      return data;
    })
      .catch((err) => {
        this.isUploadProgress = false;
        let body = {
          ID: tempObj['ID'],
          KEY: tempObj['KEY']
        }
        this.deleteFileFromS3(body)
        this.handleError(err);
      });
    this.getDocuments(this.complaint_id);
  }

  deleteFileFromS3(obj: any) {
    this.uploadService.deleteObjectFromS3(obj).subscribe((res) => {
      return res;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
      return err;
    })
  }

  getFileName(file) {
    if (file['name']) {
      return file['name'];
    } else if (file['ATTACHMENT_SOURCE']) {
      return file['ATTACHMENT_SOURCE'].substring(file['ATTACHMENT_SOURCE'].lastIndexOf("/") + 1);
    }
  }

  getDocuments(id) {
    this.cs.getDocumentsByComplaint(id).subscribe(res => {
      this.docFileList = res.data;
      this.complainantFiles = this.docFileList['COMPLAINANT'];
      this.advertiserFiles = this.docFileList['ADVERTISER'];
      this.internalFiles = this.docFileList['INTERNAL'];
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    });
  }

  async uploadSignedFile(tempObj) {
    let progressInit = 0;
    await this.uploadService.uploadFilethroughSignedUrl(tempObj, progressInit => {
    }).catch((err) => {
      this.isUploadProgress = false;
      this.handleError(err);
    });
  }

  handleError(err: any) {
    this.notify.showNotification(
      err.error.message,
      "top",
      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
      err.error.status
    );
  }

  cancelUpload() {
    this.isTemplate = false;
    this.uploadFile = false;
    this.isAttachFile = true;
    if (this.uploadFlag == false) {
      this.selectedFiles = [];
    }
  }

  selectDoc(value, file, id) {
    if (value == true) {
      this.dat = { "id": file.ID, "filename": file.ATTACHMENT_SOURCE_NAME, "path": file.ATTACHMENT_SOURCE }
      this.selectedFiles.push(this.dat);
    }
    else if (value == false) {
      for (let i = 0; i < this.selectedFiles.length; i++) {
        if (this.selectedFiles[i].id == id) {
          this.selectedFiles.splice(i, 1);
        }
      }
    }
  }

  uploadDocs() {
    this.attachId = [];
    this.selectedFiles.forEach(x => {
      this.attachId.push(x['id']);
    })
    this.uploadFlag = true;
    this.isTemplate = false;
    this.uploadFile = false;
    this.isAttachFile = true;
    this.removeDuplicates(this.selectedFiles)
  }

  async removeDuplicates(data) {
    let unique = [];
    data.forEach(element => {
      if (!unique.includes(element)) {
        unique.push(element);
      }
    })
    this.selectedFiles = unique;
  }

  saveTemplate(form) {
    this.cs.createEmailTemplate(form).subscribe(res => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      // this.dialogRef.close('refresh');
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  preview(source) {
    this.imgURL = this.bucketUrl + source;
    window.open(this.imgURL, 'window 1', '');
  }

  removeUpload(doc) {
    for (let i = 0; i < this.selectedFiles.length; i++) {
      if (this.selectedFiles[i].id == doc.id) {
        this.selectedFiles.splice(i, 1);
      }
    }
    this.attachId = [];
    this.selectedFiles.forEach(x => {
      this.attachId.push(x['id']);
    })
  }

  viewAcademyDetails() {
    window.open('https://www.ascionline.in/academy/courses-2/', "_blank");
  }

}