/********************************  COMPLAINT LIST  *********************************/
:host::ng-deep .mat-tab-label-active {
    background-color: #F8F9F9 !important;
    opacity: 1 !important;
}

:host::ng-deep .mat-tab-label {
    opacity: 1 !important;
    /* margin-top: 7px;
    margin-bottom:10px; */
}

:host ::ng-deep .mat-toolbar-single-row {
    padding: 0px !important;
    border-radius: 12px;
}

.manage {
    width: 24%;
    margin-left: 60px;
    padding-left: 5px;
    height: 100%;
}

.head-container {
    display: flex;
    flex-direction: row;
    gap: 10px;
    text-align: center;
    height: 50px;
    /* padding-top: 3px; */
    padding: 3px 10px 0 0;
}

.comp-head,
.filter-container,
.search-container {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-box-container {
    height: 34px;
    min-width: 100% !important;
    border: 1px solid rgba(18, 131, 188, 0.2);
    border-radius: 12px;
    box-sizing: border-box;
    box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.08);
}

.search-box {
    width: 19vw;
    max-width: 100% !important;
    height: 34px;
    font-size: 12px;
    font-weight: 700;
    border: none;
    border-radius: 12px 0px 0px 12px;
    border-left: 1px solid rgba(18, 131, 188, 0.2);
    border-top: 1px solid rgba(18, 131, 188, 0.2);
    border-bottom: 1px solid rgba(18, 131, 188, 0.2);
    padding-left: 10px;
}

.filter-complaint-list {
    overflow-y: scroll;
    overflow-x: hidden;
    height: 74vh;
}

.comp-head {
    display: flex;
    align-items: center;
    justify-content: center;
    /* text-align: center; */
    /* display: table-cell;
    vertical-align: middle; */
    width: 84px;
}

.head-text {
    font-weight: 500;
    text-align: center;
    margin-left: 10px;
    /* max-height: 40px !important; */
    width: 83px !important;
    padding: 20px 0px 1px 0px;
}

.head-spacer {
    flex: 1 1 auto;
}

.search-btn,
.filter-btn {
    height: 40px;
    width: 40px;
    transform: scale(0.9);
    border: 0px;
    border-radius: 15px;
    border: 1px solid #0088CB;
    display: flex;
    align-items: center;
    justify-content: center;
}

.filter-btn {
    border: 1px solid #F89E1B;
    ;
}

.overdue-container {
    color: crimson;
}

/* .complaint{
    height:350px;
} */
.item-head-container {
    display: flex !important;
    flex-direction: row !important;
    /* padding-bottom: 0px !important;
    margin-bottom: 0px !important; */
}

.list-item-container {
    width: 98%;
    height: auto !important;
    padding-bottom: 0px !important;
    background-color: #F8F9F9;
    padding: 8px;
    border-radius: 4px;
    margin: 2px 4px;
}

.list-item-container:hover {
    background-color: rgb(233, 233, 233);
    cursor: pointer;
}

.item-content-container {
    display: flex;
    flex-direction: row;
    gap: 10px;
    /* padding-top: 0% !important;
    margin-top: 0px !important; */
}

.name-container,
.date-container {
    font-size: 12px;
    color: #2F3941;
    line-height: 13px;
    font-style: normal;
    font-weight: normal;
    /* margin-left: 10%; */
    /* display: inline; */
}

.date-container {
    position: relative;
    top: 13px;
}

.requester-container {
    position: relative;
    left: 91px;
    bottom: 15px;
}

.case-container {
    position: relative;
    left: 0px;
    bottom: 9px;
}

.today-list-container {
    margin-top: 10px;
}

.complaints-display-container::-webkit-scrollbar {
    display: none;
}

.complaints-display-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.complaints-display-container {
    overflow-y: scroll;
    height: 80vh;
    position: relative;
    bottom: 3px;
    margin-top: 5px;
}

.filter-complaints-container::-webkit-scrollbar {
    display: none;
}

.filter-complaints-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.filter-complaints-container {
    overflow-y: scroll;
    height: 74vh;
    position: relative;
    bottom: 3px;
    margin-top: 5px;
}

.complaint-list {
    overflow-y: scroll;
    overflow-x: hidden;
    height: 80vh;
    width: 100%;
}

.item-icon {
    margin-right: -13px;
    font-size: 12px;
    padding-top: 3px;
}

.item-icon1 {
    margin-right: -7%;
    font-size: 11px;
}

.status-chip {
    height: 3px !important;
}

::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-thumb {
    background: #ccc
}

.comp-status {
    height: auto;
    min-height: 22px;
    width: auto;
    max-width: 130px;
    font-size: smaller;
    font-weight: bolder;
    border-width: 1px;
    border-style: solid;
    border-radius: 30px;
    padding: 4px 8px 4px 8px;
    overflow: hidden;
    display: inline-block;
    display: -webkit-inline-box;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    white-space: normal;
    -webkit-box-orient: vertical;
    /*    T R B L */
}

.comp-status1 {
    /* height: auto !important; */
    height: auto;
    min-height: 22px;
    width: auto;
    max-width: 130px;
    font-size: smaller;
    border-width: 1px;
    border-style: solid;
    border-radius: 30px;
    padding: 4px 8px 4px 8px;
    overflow: hidden;
    display: inline-block;
    display: -webkit-inline-box;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    white-space: normal;
    -webkit-box-orient: vertical;
    /*    T R B L */
}

:host ::ng-deep ul {
    margin-bottom: 0px !important;
}

:host ::ng-deep h1 {
    margin-bottom: 0px !important;
}

:host ::ng-deep h2 {
    margin-bottom: 0px !important;
}

:host ::ng-deep h3 {
    margin-bottom: 0px !important;
}

:host ::ng-deep h4 {
    margin-bottom: 0px !important;
}

:host ::ng-deep .ellipsis p {
    margin-bottom: 0px !important;
}

.ellipsis {
    overflow: hidden;
    width: 275px;
    display: inline-block;
    display: -webkit-inline-box;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    vertical-align: middle;
    position: relative;
}

.complainant-name {
    position: relative;
    top: 3px;
    left: 2px;
    font-size: 11px;
}

/* .complaint-display-head{
    margin: 5px 0px 5px 15px;
} */

/****************************  CASE ID TAB ***************************************/
.tab-title-container {
    font-size: small;
    /* background-color: greenyellow; */
    height: 40px;
    width: auto;
}

:host::ng-deep .mat-tab-group.mat-tab-group.mat-primary .mat-ink-bar {
    background-color: none;
    border-bottom: none;
    height: 0px;
}

:host ::ng-deep .right-screen.mat-tab-header,
.mat-tab-nav-bar {
    border-bottom: 0px solid #D8DCDE;
    border-bottom-width: 0px;
}

:host ::ng-deep .complaint-list-tab .mat-tab-header {
    height: 66px;
    padding-top: 8px;
    margin-left: 4px;
}

/*  REMOVE MAT-TAB INK BAR */
/* :host ::ng-deep .mat-ink-bar {
    display: none !important;
} */
.status {
    width: 60px;
}

/* .search {
    margin-left:60px;
} */
.one {
    background-color: #e0f7fa;
    width: 60px;
}

.toolbar-btns {
    display: flex;
    /* flex-direction: row; */
    grid-gap: 10px;
    margin-left: -12%;
    /* position: static; */
    padding-top: 5px;
    /* margin-left: 5%; */
}

.tab {
    width: 100%;
    height: 100%;
}

/* .detail-screen{ */
/* width: 100%; */
/* height: 50%; */
/* position: relative; */
/* overflow-y: hidden; */
/* } */

/***~~~~~~~~~~~~~~~~~~~~~~           FILTER POPOVER            ~~~~~~~~~~~~~~~~~~~~***/

/* ::ng-deep .mat-form-field-flex > .mat-form-field-infix {
    padding: 0.4em 0px !important;
    width: 230px !important;
}
::ng-deep .mat-form-field-appearance-outline .mat-form-field-label { height: 10px !important;border-radius: 2px; }
::ng-deep label.ng-star-inserted { transform: translateY(-0.59375em) scale(.75) !important; } */
/* ::ng-deep .mat-select {
    width: 230px !important;
} */
/* ::ng-deep .mat-form-field-flex > .mat-form-field-infix  .mat-select { width: 260px !important;min-width: 230px !important; } */
/* ::ng-deep .mat-form-field-appearance-outline .mat-select { height: 10px !important;min-width: 330px !important; } */

.filter-head-container {
    display: flex;
    flex-direction: row;
    gap: 10px;
    text-align: center;
    height: fit-content;
    /* padding-top: 3px; */
    /* padding: 3px 10px 0 0; */
}

.head-container>p {
    color: #92A2B1;
    /* margin-bottom: 10%; */
}

.text-container>span {
    vertical-align: middle;
}

#label-icon {
    font-size: medium;
    padding-right: 5px;
    /* padding-bottom: 0px !important; */
}

.apply-btn {
    color: white;
    background: #0088CB;
    border-radius: 12px;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    padding: 7px 20px;
}

::ng-deep .mat-form-field-appearance-outline :hover {
    outline: none !important;
}

:host>>>.popover {
    width: 311px;
    height: auto;
    /* max-width: 280px; */
    /* width:23%; */
    /* max-width: 100%; */
    /* margin-left: 20px; */
    border-radius: 3px;
    margin: 10px 0 0 20px;
    padding: 0px;
}

.form-group {
    padding: 0px 10px;
}

::ng-deep .filter-field .mat-form-field-outline-start {
    border-radius: 2px 0 0 2px !important;
    /* min-width: 28px !important; */
}

::ng-deep .filter-field .mat-form-field-outline-end {
    border-radius: 0 2px 2px 0 !important;
}

::ng-deep .filter-field .mat-form-field-outline {
    max-height: 30px !important;
    min-height: 30px !important;
    height: 30px;
    /* margin: 0.4em 0px !important; */
    /* margin: 8px 0px !important; */
    /* margin: 5px 0px 1px !important; */
    margin: 0px !important;
}

.filter-head {
    color: #92A2B1;
    height: 20px;
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    line-height: 16px;
}

.input-container {
    padding-bottom: 12px;
}

.filter-label,
.company-input {
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    /* font-size: 14px; */
    line-height: 16px;
    /* line-height: 19px; */
    /* vertical-align:super; */
}

.tag-select {
    vertical-align: middle;
    padding-top: 0px;
}

#label-icon,
.select-icon,
.input-icon,
.filter-label {
    color: #2F3941;
}

.filter-img-icon {
    /* width: 6px; */
    /* height: 8px; */
    /* height: 13px; */
    /* width: 11px; */
    vertical-align: baseline;
}

.filter-select {
    height: 30px;
    border: 1px solid #D8DCDE;
    box-sizing: border-box;
    border-radius: 2px;
    font-size: 12px;
    line-height: 16px;
    padding: 6px 14px;
}

.company-input {
    color: #2F3941;
    /* padding: 1px 0px 1px; */
    height: 30px;
    border: 1px solid #D8DCDE;
    box-sizing: border-box;
    border-radius: 2px;
    padding: 6px 14px;
}

.brand-search-icon {
    display: block;
    margin: auto !important;
    width: 20px;
    height: 20px;
}

/* :host ::ng-deep .tag-select .mat-select-arrow {
    visibility: hidden;
    width: 0;
    height: 0;
} */
.search-tags {
    width: 100%;
    height: 50px;
}

.filter-tag-container {
    /* background-color: rgb(255, 189, 8); */
    width: 100%;
    border: 1px solid #D8DCDE;
    border-bottom: none;
    border-left: none;
    border-right: none;
    padding: 5px 10px;
    margin-bottom: 8px;
}

.filter-chip-container {
    border: 1px solid #D8DCDE;
    background-color: rgb(255, 189, 8);
    border-left: 0;
    border-right: 0;
}

.filter-chips {
    color: #2F3941;
    background-color: #E9EBED;
    border: 1px solid #D8DCDE;
    /* font-size: 10px; */
    font-size: 13px;
    font-weight: normal;
    /* line-height: 13px; */
}

.chip-close-icon {
    color: #92A2B1 !important;
    background-color: #E9EBED !important;
}

#chip-tag-icon {
    visibility: hidden;
    font-size: medium;
    position: absolute;
    left: 10px;
    top: 8px;
    /* vertical-align: middle; */
}

.chip-icon-container {
    margin-right: 18px;
}

:host ::ng-deep .complaint-list-tab .mat-tab-label,
.mat-tab-label-active {
    min-width: 0 !important;
    /* padding: 19px!important;
    margin: 3px!important; */
    font-size: 13px;
    font-weight: 600 !important;
    border-radius: 4px;
}

:host ::ng-deep .complaint-list-tab .mat-tab-label .mat-tab-label-content {
    font-weight: 600;
}

.complaint-list-tab {
    border-top: 1px solid #DCDCDC;
}

.common-toolbar {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    z-index: 10;
    border-top: 0;
    border-left: none;
    border-right: none;
    position: absolute;
    right: 0%;
    top: 1%;
}

.options-container {
    width: 100%;
    border: 1px dashed rgb(255, 255, 255);
}

.spinner-item {
    display: grid;
    place-items: center;
    margin-top: 10px;
}

.add-tab-label {
    min-width: 0px !important;
}

/* ::ng-deep .add-tab-label .mat-tab-label{
    min-width: 0px !important;
    color: black;

} */
.new-tab:hover,
.new-tab:hover .new-tab-text {
    background-color: #0088CB;
    color: #ffffff !important;
}

.blue-icon,
.white-icon {
    position: absolute;
    top: 16px;
    left: 32px;
}

.blue-icon {
    visibility: visible;
}

.white-icon {
    visibility: hidden;
}

.new-tab:hover .blue-icon {
    visibility: hidden !important;
}

.new-tab:hover .white-icon {
    visibility: visible !important;
}

.new-tab {
    color: #0088CB;
    height: 100%;
    border-radius: 4px;
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    line-height: 16px;
    padding: 10px 15px 10px 20px;
}

.new-tab-text {
    color: #0088CB;
    /* height: 100%;
    border-radius: 4px; */
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    line-height: 16px;
}

.flex {
    display: flex;
    justify-content: center;
    align-items: center;
}

.case-selected {
    background-color: rgb(233, 233, 233);
}

@media only screen and (max-width: 1250px) {
    .common-toolbar-mobile {
        border-bottom: 1px solid #D8DCDE;
        border-top: 0;
        border-left: none;
        border-right: none;
    }

    .dashboard-admin-heading {
        padding-left: 0;
        padding-top: 0;
        font-weight: 600;
        font-size: 18px;
        line-height: 23.94px;
        color: #ED2F45;
        padding-left: 20px;
        padding-top: 6px;
    }

    .dashboard-container {
        width: 100%;
        height: 90%;
        background: #E5E5E5;
    }

    .dasboard-subheading {
        padding-right: 20px;
        padding-top: 0px;
        font-weight: 400;
        font-size: 14px;
        line-height: 18.62px;
        padding-left: 20px;
        padding-top: 3px;
    }
}