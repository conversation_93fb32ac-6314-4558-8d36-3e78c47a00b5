.common-toolbar {
    border-bottom: 1px solid #D8DCDE;
    border-top: 0;
    border-left: none;
    border-right: none;
}
.heading-container {
    width: 60%;
}
.options-container {
    width:40%;
}
.inbox-page-container {
    background-color: #f1efef;
    height: 97vh;
}
.inbox-body::-webkit-scrollbar {
    display: none;
}
.inbox-body {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.inbox-body {
    height: auto;
    overflow-x: hidden;
}
.right-tab-icon {
    background: #FFFFFF;
    box-shadow: 0px 1px 3px rgb(0 0 0 / 8%);
    border-radius: 4px;
    width: 44px;
    height: 36px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.tab-div {
    width: 28%;
    height: 89vh;
    background-color: #f1efef;
    margin-left: 60px;
}
.tab-group {
    height: 89vh;
    overflow-y: hidden;
    padding: 0px 7px;
}
:host ::ng-deep .mat-ink-bar {
    display: none !important;
}
:host ::ng-deep .mat-tab-header {
    background-color: #ffffff;
    border: 1px solid #D8DCDE;
    box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.08);
    width: 100%;
}
:host ::ng-deep.mat-tab-label {
    min-width: 0!important;
    padding: 15px!important;
    color:rgb(92, 92, 92);
    opacity: 0.4 !important;
}
:host ::ng-deep .mat-tab-label-active {
    opacity: 1 !important;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    background-color: #B9DCEE !important;
}
mat-tab-label {
    max-width: 20px;
}
mat-nav-list::-webkit-scrollbar {
    display: none;
}
.buttons {
    display: flex;
    justify-content: flex-end;
}
mat-nav-list {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
mat-nav-list {
    height: 81vh;
    background-color: #FFFFFF;
    overflow-y: scroll;
    overflow-x: hidden;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    box-shadow: 4px 0px 8px rgba(0, 0, 0, 0.1);
    padding-top: 0px;
}
.mail-list-container {
    background-color: #fafafa;
    border-radius: 5px;
    height: 140px !important;
    margin-bottom: 5px;
}
.mail-list-container:hover {
    background-color: #f0f0f0;
}
.mail-item-container {
    height: 120px !important;
}
.wpsg-head-container,
.mail-head-container,
.sms-head-container {
    font-size: 16px;
    line-height: 21px;
    color: #000000;
    height: 20px;
    padding: 5% 0px 10% 0px;
}
.mail-subject-text {
    font-size: small;
    font-weight: 500;
    padding-bottom: 2px;
    word-break: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    display: -webkit-inline-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}
.wpsg-msg-container,
.mail-msg-container {
    height:40px;
    width: 100%;
    word-wrap: break-word;
    word-break: normal;
    overflow: hidden;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 19px;
    color: #2F3941;
}
@supports (height: max-content !important) {          /*Support added*/
    .wpsg-list-container,
    .sms-list-container {
        height: max-content !important;
        padding: 1%;
    }
    .wpsg-item-container,
    .sms-item-container {
        height: max-content !important;
    }
    .mail-list-container {
        height: max-content !important;
    }
    .wpsg-head-container,
    .mail-head-container,
    .sms-head-container {
        font-size: 16px;
        line-height: 21px;
        color: #000000;
        height: 20px;
        padding: 5% 0px 10% 0px;
    }
}
.wpsg-msg-text,
.mail-msg-text,
.sms-msg-text {
    max-height: 40px;
    line-height: 20px;
    font-size: 14px;
    color: #000000;
}
.wpsg-date-container,
.mail-date-container,
.sms-date-container {
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 19px;
    color: rgba(0, 0, 0, 0.4);
    margin: 2px 0px 3px 0px;
}
.sms-text-icon {
    font-size:smaller;
}
.complaint-container {
    width: 72% !important;
    height: 88vh;
}
.comp-matcard::-webkit-scrollbar {
    display: none;
}
.comp-matcard {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.comp-matcard {
    background-color: #FFFFFF;
    height: 88vh;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #D8DCDE;
    overflow-y: scroll;
    overflow-x: hidden;
}
.comp-divider-container {
    position:absolute;
    top: 23px;
    width: 82%;
    margin-left: 125px;
}
.comp-divider {
    position: relative;
    width: 100%;
}
.detail-attribute {
    color: rgba(0, 0, 0, 0.4);
    font-size: 14px;
    line-height: 19px;
}
.comp-msg-container {
    width: 90%;
    /* word-wrap: break-word;
    word-break: normal;
    overflow: hidden; */
    text-align: justify;
    font-size: 14px;
    line-height: 19px;
}
/* .comp-msg-container p {
    text-overflow: ellipsis !important;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
} */
.detail-value {
    color: #2F3941;
    line-height: 19px;
    font-size: 14px;
    color: #000000
}
.divider-container {
    position:absolute;
    top: 7px;
    width: 80%;
    margin-left: 170px;
}
.classfy-head-divider {
    position: relative;
}
.classification-container {
    display: inline;
}
.comp-chips {
    color: #4DA1FF;
    background-color: #E9F5FF;
    width: auto;
    font-size: 12px;
    line-height: 16px;
    font-style: normal;
    font-weight: normal;
    border: 1px solid #4DA1FF;
    border-radius: 5px;
}
.classfy-container {
    max-height: fit-content;
}
.classfy-text {
    color: #2F3941;
    text-indent: 10px;
    height: 25px;
    font-size: 14px;
}
.related-name {
    font-weight: bold;
    color: #2AB2FF;
    font-size: 14px;
    margin-left: 5px;
}
.classfy-icon {
    color: rgb(207, 205, 205);
    font-size:medium;
    margin-right: 10px;
    vertical-align:text-top;
}
.chips-btn-container {
    margin-left: 1%;
    position:relative;
}
.relcomp-container {
    margin-left: 10px;
}
.person-chips {
    font-size: 10px;
    line-height: 13px;
    display:flex;
    align-items: center;
    border-radius: 50px;
}
#person-chip1 {
    color: #EB8F8F;;
    background-color: #EAD1D1;
    z-index: 1;
}
#person-chip2 {
    color: #E1CE68;
    background-color: #EAE6D1;
    z-index: 2;
}
#person-chip3 {
    color: #8B97A3;
    background-color: #CFD7DF;
    z-index: 3;
}
.add-btn {
    color: rgb(163, 163, 163);
    font-weight: normal;
    padding-left: 5px;
    padding-top: 2px;
    word-spacing: 1px;
    line-height: 15px;
    border: 1px dashed rgb(163, 163, 163);
    border-radius: 20px;
    height: 34px;
    margin: 7px 0px 20px 20px;
}
.footer-content-container {
    width: 96% !important;
}
.footer-btn {
    color: #5A6F84;
    border-radius: 12px;
}
.footer-right-btn {
    float:right;
}
.delete-icon {
    font-size: large;
    vertical-align: baseline !important;
    margin-right: 10px;
}
.chatbot-list {
    height: 80.5vh;
}
.complaint-list {
    background-color: rgb(255, 255, 255);
    overflow: scroll;
    height: 100%;
    padding: 3px;
}
.chatbot-list-container {
    height: 130px !important;
    padding: 1%;
}
.chatbot-item-container {
    height: 130px !important;
    width: 308px !important;
}
.chatbot-head-container {
    padding: 5.5% 0px;
    color: #000000;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
    display: flex;
    align-items: center;
}
.medium-container {
    background: #0088CB;
    width: max-content;
    height: 16px;
    padding: 1px 7px;
    border-radius: 20px;
    display: flex;
    align-items: center;
}
.medium-list {
    color: #FFFFFF;
    font-style: normal;
    font-weight: 600;
    font-size: 10px;
    line-height: 13px;
    text-transform: uppercase;
}
.brand-container {
    color: #000000;
    height: 19px;
    width: calc(100%) !important;
    word-wrap: break-word !important;
    word-break: normal;
    overflow: hidden;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
    vertical-align: middle;
}
.brand-text {
    color: #000000;
    width: calc(100%);
    font-size: 16px;
    font-weight: 550;
    display: inline-block;
    display: -webkit-inline-box;
    text-overflow: ellipsis !important;
    -webkit-line-clamp: 1;
    white-space: normal;
    -webkit-box-orient: vertical;
    vertical-align: middle;
    position: relative;
    overflow: hidden;
    padding-bottom: 5.5px !important;
    padding-left: 7px;
}
.nams-msg-container {
    width: calc(100%) !important;
    word-wrap: break-word !important;
    word-break: normal;
    overflow: hidden;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 19px;
    color: #2F3941;
}
.nams-msg-text {
    font-size: 14px;
    color: #707070;
    text-overflow: ellipsis !important;
    width: calc(100%);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 19px;
}
.nams-date-container {
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 19px;
    color: #000000;
    padding: 2px 0px 3px 0px;
    position: relative;
    right:207px;
}
@supports (height: max-content !important) {
    .chatbot-item-container {
            height: max-content !important;
            width: 308px !important;
    }
    .chatbot-head-container {
            padding: 5.5% 0px;
            color: #000000;
            font-style: normal;
            font-weight: 600;
            font-size: 14px;
            line-height: 19px;
    }
}
.item-content-container {
    display: flex;
    flex-direction: row;
    gap: 10px;
}
.status-chip {
    height: 3px !important;
}
.comp-status {
    height: auto;
    min-height: 22px;
    width: auto;
    max-width: 130px;
    font-size: smaller;
    font-weight: bolder;
    border-width: 1px;
    border-style: solid;
    border-radius: 30px;
    padding: 4px 8px 4px 8px;
    overflow: hidden;
    display: inline-block;
    display: -webkit-inline-box;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    white-space: normal;
    -webkit-box-orient: vertical;
}
.comp-status1 {
    height: auto;
    min-height: 22px;
    width: auto;
    max-width: 130px;
    font-size: smaller;
    border-width: 1px;
    border-style: solid;
    border-radius: 30px;
    padding: 0px 7px 2px 7px;
    overflow: hidden;
    display: inline-block;
    display: -webkit-inline-box;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    white-space: normal;
    -webkit-box-orient: vertical;
}
.date-container {
    font-size: 12px;
    color: #2F3941;
    line-height: 13px;
    font-style: normal;
    font-weight: normal;
}
.item-icon {
    font-size: 12px;
}
.item-icon1 {
    font-size:11px;
}
.item-head-container {
    width: 88% !important;
}
.list-item-container {
    width: 100%;
    height: 100%;
    padding: 8px 0px;
    border-radius: 8px;
}
:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0em 0 0.5em 0;
}
.input-box {
    position: relative;
    bottom:9px;
    width: 40%;
}
:host ::ng-deep ul {
    margin-bottom: 0px !important;
}
:host ::ng-deep h1 {
    margin-bottom: 0px !important;
}
:host ::ng-deep h2 {
    margin-bottom: 0px !important;
}
:host ::ng-deep h3 {
    margin-bottom: 0px !important;
}
:host ::ng-deep h4 {
    margin-bottom: 0px !important;
}
:host ::ng-deep .ellipsis p {
    margin-bottom: 0px !important;
}
.ellipsis {
    overflow: hidden;
    width: 120px;
    display: inline-block;
    display: -webkit-inline-box;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    vertical-align: middle;
    position: relative;
    max-height: 1.5rem;
}
.flex {
    display: flex;
    justify-content: center;
    align-items: center;
}
.assignee {
    font-size: 12px;
    line-height: 13px;
    color: #2F3941;
    font-style: normal;
}
.company-btn {
    position: relative;
    top: 3px;
    left: 3px;
}
.conversation-container {
    margin-top: 5px;
    margin-right: 18px;
}
.not-exist-btn {
    width: 74px;
    height: 18px;
    background-color: rgba(252, 41, 41, 0.13);
    border: 0.5px solid #EA2D2D;
    box-sizing: border-box;
    border-radius: 3px;
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
}
.not-exist {
    position: relative;
    top: -3px;
    color: #FC2929;
}
.related-detail-container {
    text-indent: 20px;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 19px;
}
.related-attribute {
    color: #CFD7DF;
}
.related-value {
    color: #000000;
}
.media-anchor {
    color: #0088CB;
    word-break: break-all;
    cursor: pointer;
}
.media-anchor1 {
    color: #0088CB;
    word-break: break-all;
    cursor: pointer;
    text-overflow: ellipsis !important;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
.detail-left-container {
    width: 60%;
}
.detail-right-container {
    width: 40%;
}
.mat-card-scroll {
    height: 88vh;
}
.comp-head-container {
    position: relative;
    flex-direction: row;
    box-sizing: border-box;
    background-color: white;
    border: 1px solid #D8DCDE;
    box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.08);
    height: 50px;
    margin-left: -8px;
    padding-left: 34px;
}
.classfy-head {
    position: relative;
    flex-direction: row;
    box-sizing: border-box;
    display: flex;
    height: 30px;
    width: 100%;
}
.comp-head {
    font-size: 14px;
    line-height: 16px;
    color: #000000;
    width: -webkit-fill-available;
    margin-bottom: 5px;
    font-weight: 550;
}
.details-container {
    border: 1px solid #D8DCDE;
    background-color: white;
    margin-right: 12px;
    border-radius: 4px;
    margin-bottom: 12px;
    padding: 21px;
    box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.08);
}
.classify-content-container {
    border: 1px solid #D8DCDE;
    border-radius: 4px;
    background: #ffffff;
    padding: 21px;
    box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.08);
    margin-bottom: 21px;
    margin-right: 12px;
}
.scroll-items {
    -ms-overflow-style: none;
    scrollbar-width: none;
    overflow-y: scroll;
    overflow-x: hidden;
    margin-right: 2px;
    height: 73vh;
}
.scroll-items::-webkit-scrollbar {
    display: none;
}
.theme-blue-button-admin[disabled] {
    opacity: 0.4;
}
.attachment-box {
    background: #FFFFFF;
    border: 1px solid #F5F5F7;
    box-sizing: border-box;
    border-radius: 4px;
    padding: 13px !important;
    width: 275px;
    align-items: center;
    vertical-align: middle;
    margin-bottom: 15px;
    margin-right: 15px;
}
.invalid-button {
    background: #FFFFFF;
    border: 1px solid #CFD7DF;
    border-radius: 12px;
    color: #5A6F84;
    padding-left: 14px;
    padding-right: 14px;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    line-height: 7px !important;
    padding-top: 0px;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    width: auto;
    height: 35px;
}
.invalid-container {
    margin-right: 18px;
    align-items: center;
}
.count-badge {
    color: #ED2F45;
    position: absolute;
    font-size: 10px;
}
.input-field {
    width: 100%;
}    

.ftc-container { 
    padding: 1px 0 0 0 !important;
    width: 12% !important;
}
.ccc-tag {
  font-style: normal;
  font-weight: 600;
  font-size: 11px !important;
  line-height: 12px;
  text-align: center;
  color: #0088CB !important;
  min-height: 22px !important;
  background-color: #ffffff;
  border: 1px solid #0088CB;
  border-radius: 8px;
  padding: 5px;
}
.ftc-tag {
    font-style: normal;
    font-weight: 600;
    font-size: 11px !important;
    line-height: 12px;
    text-align: center;
    color: #F89E1B !important;
    min-height: 22px !important;
    background-color: #ffffff;
    border: 1px solid #F89E1B;
    border-radius: 8px;
    padding: 5px;
}
.comp-row1 {
    width: 100% !important;
    padding-right: 0px !important;
    height: 100%;
}
.slider {
    transform: scale(.8);
    color: #74D365;
}
:host ::ng-deep .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar {
    background-color: rgb(144, 212, 144) ;
}
:host ::ng-deep  .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb {
    background-color: rgb(28, 187, 28) ;
}
@media only screen and (max-width: 1250px) {
    .common-toolbar-mobile {
        border-bottom: 1px solid #D8DCDE;
        border-top: 0;
        border-left: none;
        border-right: none;
    }
    .dashboard-admin-heading {
        padding-left: 0;
        padding-top: 0;
        font-weight:600;
        font-size:18px;
        line-height: 23.94px;
        color: #ED2F45;
        padding-left: 20px;
        padding-top: 6px;
    }
    .dashboard-container {
        width: 100%;
        height: 90%;
        background: #E5E5E5;
    }
    .dasboard-subheading {
        padding-right: 20px;
        padding-top: 0px;
        font-weight:400;
        font-size:14px;
        line-height: 18.62px;
        padding-left: 20px;
        padding-top: 3px;
    }
}