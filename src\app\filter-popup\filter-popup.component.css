
::ng-deep .mat-form-field-flex > .mat-form-field-infix { 
    padding: 0.4em 0px !important;
    width: 230px !important;      /* WIDTH OF MAT-FORM-FIELD*/
}
::ng-deep .mat-form-field-appearance-outline .mat-form-field-label { height: 10px !important; }
::ng-deep label.ng-star-inserted { transform: translateY(-0.59375em) scale(.75) !important; }

::ng-deep .mat-select {
    width: 230px !important;
}
/* ::ng-deep .mat-form-field-flex > .mat-form-field-infix  .mat-select { width: 260px !important;min-width: 230px !important; } */
/* ::ng-deep .mat-form-field-appearance-outline .mat-select { height: 10px !important;min-width: 330px !important; } */

.filter-container{
    /* border:1px dashed blue; */
    width: 300px;
    /* height: auto; */
    /* margin: 1%; */
    padding: 2%;
}
.head-container > p {
    color: rgb(184, 184, 184);
    margin-bottom: 10%;
}
.text-container > span{
    vertical-align: middle;
}
#label-icon{
    font-size:medium;
    padding-right: 5px ;
    /* padding-bottom: 0px !important; */
}
div > button{
    color:white;
    background-color: rgb(51, 51, 88);
    font-weight: normal;
    padding:0px 20px;
}





::ng-deep .mat-form-field-appearance-outline :hover{
    outline: none !important;
}

.example-panel.mat-select-panel {
    /* background: rgba(255, 0, 0, 0.5); */
    width: 100px;
  }

/* .mat-select-panel {
    background: red;
} */