<div fxLayout="row">
    <mat-toolbar class="toolbar">
        <div fxFlex="35%" fxLayoutAlign="start">
            <h2 mat-dialog-title fxLayoutAlign="start" style="font-size: 16px;">
                Please confirm the company details :
            </h2>
        </div>
    </mat-toolbar>
</div>

<mat-divider></mat-divider>

<div class="form">
    <form [formGroup]="createcompany">
        <div class="contents">
            <div class="control-container">
                <div style="position:relative; padding-top: 5px;"> Company name :</div>
                <div>
                    <!-- <mat-form-field appearance="outline" class="input-field_cmpname">
                        <input matInput style="
                    font-style: normal;
                    font-weight: normal; height: 20px;" autocomplete="off" formControlName="cname">
                    </mat-form-field> -->
                    <mat-form-field appearance="outline" class="input-field_cmpname" style="
                        font-style: normal; font-weight: normal;">
                        <input matInput type="text" formControlName="cname" [matAutocomplete]="autoCompany" style="
                            font-style: normal; font-weight: normal; height: 20px;">
                        <mat-autocomplete #autoCompany="matAutocomplete" (optionSelected)="onSelectionChange($event)">
                            <mat-option *ngFor="let comp of companyList;" [value]="comp.COMPANY_NAME" style="
                                font-style: normal;font-weight: normal;">
                                {{comp.COMPANY_NAME}}
                            </mat-option>
                        </mat-autocomplete>
                    </mat-form-field>
                </div>
            </div>

            <div class="control-container">
                <div style="position:relative; padding-top: 5px;"> Member name :</div>
                <div>
                    <mat-form-field [floatLabel]="'never'" class="input-field_lastname"
                        style="position:relative; padding-left: 50px; margin-top: -5px;">
                        <input matInput style="
                    font-style: normal;
                    font-weight: normal; height: 20px;" autocomplete="off" formControlName="member_name" readonly>
                    </mat-form-field>
                </div>
            </div>

            <div class="control-container">
                <div style="position:relative; padding-top: 5px;"> Member email :</div>
                <div>
                    <mat-form-field [floatLabel]="'never'" class="input-field_lastname"
                        style="position:relative; padding-left: 50px; margin-top: -5px">
                        <input matInput style="
                    font-style: normal;
                    font-weight: normal; height: 20px;" autocomplete="off" formControlName="member_email" readonly>
                    </mat-form-field>
                </div>
            </div>

            <div class="control-container">
                <div style="position:relative; padding-top: 5px;"> Member contact :</div>
                <div>
                    <mat-form-field [floatLabel]="'never'" class="input-field_lastname"
                        style="position:relative; margin-top: -5px">
                        <input matInput style="
                    font-style: normal;
                    font-weight: normal; height: 20px;" autocomplete="off" formControlName="member_contact" readonly>
                    </mat-form-field>
                </div>
            </div>

            <div class="control-container">
                <div style="position:relative; padding-top: 5px;"> Company address :</div>
                <div>
                    <mat-form-field appearance="outline" class="input-field_lastname" style="padding-left: 30px;">
                        <input matInput style="
                    font-style: normal;
                    font-weight: normal; height: 20px;" autocomplete="off" formControlName="caddress">
                    </mat-form-field>
                </div>
            </div>

            <div class="control-container">
                <div style="position:relative; padding-top: 5px;"> About company :</div>
                <div>
                    <mat-form-field appearance="outline" class="input-field_aboutcmp">
                        <textarea matInput formControlName="about_company"
                            style="font-size: 14px; font-style: normal; font-weight: normal;; height: 50px;"
                            autocomplete="off"></textarea>
                    </mat-form-field>
                </div>
            </div>
        </div>

        <div class="lastdivdr">
            <mat-divider></mat-divider>
        </div>
        <div class="toolbar-btns2">
            <div>
                <mat-dialog-actions>
                    <button mat-flat-button class="cancel-btn" mat-dialog-close>
                        <span class="bolder">Cancel</span>
                    </button>
                    <button mat-flat-button class="confirm-btn" [disabled]="createcompany.invalid" (click)="confirmCompany(createcompany.value)">
                        <span class="bolder">Confirm</span>
                    </button>
                </mat-dialog-actions>
            </div>
        </div>
    </form>
</div>