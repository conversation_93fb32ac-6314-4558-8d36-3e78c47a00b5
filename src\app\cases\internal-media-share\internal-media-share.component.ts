import { Component, HostListener, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';

@Component({
  selector: 'app-internal-media-share',
  templateUrl: './internal-media-share.component.html',
  styleUrls: ['./internal-media-share.component.scss']
})
export class InternalMediaShareComponent implements OnInit {

  media_id;
  complainant: number;
  advertiser: number;
  advertiserShared: boolean;
  complainantShared: boolean;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private cs: ComplaintsService,
    private notify: NotificationService,
    public dialogRef: MatDialogRef<InternalMediaShareComponent>
  ) { }

  ngOnInit(): void {
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
    this.media_id = this.data.mediaId;
    this.advertiser = this.data.advertiser;
    this.complainant = this.data.complainant;
    if (this.advertiser == 1) {
      this.advertiserShared = true;
    }
    if (this.complainant == 1) {
      this.complainantShared = true;
    }
  }

  selectUser(value, key) {
    if (key == "complainant") {
      if (value == true) {
        this.complainant = 1;
      }
      else if (value == false) {
        this.complainant = 0;
      }
    }
    else if (key == "advertiser") {
      if (value == true) {
        this.advertiser = 1;
      }
      else if (value == false) {
        this.advertiser = 0;
      }
    }
  }

  shareFile() {
    this.cs.shareInternalDocuments(this.media_id, this.advertiser, this.complainant).subscribe(res => {
      if (res) {
        this.notify.showNotification(
          res.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status] : "success"),
          res.status
        )
        this.dialogRef.close({ 'state': true });
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    }
    );
  }

  cancelShare() {
    this.dialogRef.close({ 'state': false });
  }

}