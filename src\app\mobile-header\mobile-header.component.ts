import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ConfirmationPopupComponent } from '../confirmation-popup/confirmation-popup.component';
import { AuthService } from '../services/auth.service';
import { ComplaintsService } from '../services/complaints.service';
import { NotificationService } from '../services/notification.service';

@Component({
  selector: 'app-mobile-header',
  templateUrl: './mobile-header.component.html',
  styleUrls: ['./mobile-header.component.scss']
})
export class MobileHeaderComponent implements OnInit {

  confirmationMsg: any = {};
  roleList: any;
  userInfo: any;
  roleName: any;
  roleId: any;
  
  constructor(private router: Router,
    private cs: ComplaintsService,
    private notify: NotificationService,
    private dialog: MatDialog,
    private formBuilder: FormBuilder,
    private authService: AuthService) { }

  ngOnInit(): void {
    this.roleList = JSON.parse(window.localStorage.getItem('role'));
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.roleId = this.userInfo.roleId;
    if (!(this.userInfo.roleId == 7 || this.userInfo.roleId == 9)) {
      this.roleList.forEach(element => {
        if(this.userInfo.roleId == element.ID){
          this.roleName = element.ROLE_NAME[0];
        }
      });
    }
    else if (this.userInfo.roleId == 7 || this.userInfo.roleId == 9) {
      this.roleName = this.userInfo.firstName[0];
    }
  }
  logout() {
    this.confirmationMsg.title = 'Are you sure you want to logout ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.authService.logout().subscribe(
          (res) => {
            window.localStorage.clear();
            this.router.navigate(['auth/login']);
          },
          (err) => {
            window.localStorage.clear();
            this.router.navigate(['auth/login']);
          }
        );
      }
    });
  }

  viewProfile(flag) {
    this.authService.profileFlag = flag;
    this.router.navigateByUrl('auth/my-profile');
  }
  viewNotifications() {
    this.router.navigateByUrl('mobile-notifications');
  }

}
