import { Component, ElementRef, Input, OnInit, ViewChild, Renderer2, Pipe, Output, EventEmitter } from '@angular/core';
import { ComplaintsService } from '../../services/complaints.service';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { MatChipInputEvent } from '@angular/material/chips';
import { saveAs as importedSaveAs } from "file-saver";
import { FormControl } from '@angular/forms';
import { environment } from 'src/environments/environment';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { DialogConfirmComponent } from '../../dialog-confirm/dialog-confirm.component';
import { MatTableDataSource } from '@angular/material/table';
import { SelectionModel } from '@angular/cdk/collections';
import { ViewMediaFileComponent } from '../view-media-file/view-media-file.component';
import { RecommendationComponent } from 'src/app/recommendation/recommendation.component';
import * as moment from 'moment';
import { DatePipe } from '@angular/common';
import { AddTaskComponent } from '../../task-board/add-task/add-task.component';
import { ConfirmationPopupComponent } from '../../confirmation-popup/confirmation-popup.component';
import { NotificationService } from '../../services/notification.service';
import { TasksService } from '../../services/tasks.service';
import { colorObj } from '../../shared/color-object';
import { DateAdapter } from 'angular-calendar';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MY_FORMATS } from 'src/app/shared/calendarFormat';
import { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs/operators';
import { Observable } from 'rxjs/internal/Observable';
import { ViewDocumentComponent } from '../view-document/view-document.component';
import { UploadService } from 'src/app/services/upload.service';
import { ComplaintConversationsComponent } from '../complaint-conversations/complaint-conversations.component';
import { SimilarComplaintDetailsComponent } from 'src/app/similar-complaint-details/similar-complaint-details.component';
import { InternalMediaShareComponent } from '../internal-media-share/internal-media-share.component';
import { ResolutionComponent } from 'src/app/resolution/resolution.component';
import { DomSanitizer } from '@angular/platform-browser';
import { trigger, state, style, transition, animate } from '@angular/animations';

export interface Tag {
  name: string;
}

export interface PeriodicElement {
  ASSIGNEE_USER_ID: number;
  ASSIGNEE_USER_NAME: string;
  COMPLAINT_DESCRIPTION: string;
  COMPLAINT_ID: number;
  DUE_DATE: Date;
  ID: number;
  PRIORITY_ID: number;
  PRIORITY_NAME: string;
  TASK_DESCRIPTION: string;
  TASK_STATUS_ID: number;
  TASK_STATUS_NAME: string;
  UPDATED_DATE: Date
}

export interface TaskTable {
  due: string;
  type: string;
  action: number;
}

export interface NoTaskTable {
  task: string
  status: string;
  priority: string;
  duedate: string;
}

const ELEMENT_DATA: NoTaskTable[] = [
  { task: 'No task created....', status: '', priority: '', duedate: '' }
];

@Component({
  selector: 'app-complaint-detail',
  templateUrl: './complaint-detail.component.html',
  styleUrls: ['./complaint-detail.component.css'],
  animations: [
    trigger('slideInOut', [
      state('in', style({
        transform: 'translate3d(0,0,0)'
      })),
      state('out', style({
        transform: 'translate3d(100%, 0, 0)'
      })),
      transition('in => out', animate('400ms ease-in-out')),
      transition('out => in', animate('400ms ease-in-out'))
    ]),
    trigger('buttonInOut', [
      state('in', style({
        transform: 'translate(-400px, 0)'
      })),
      state('out', style({
        transform: 'translate(0, 0)'
      })),
      transition('in => out', animate('400ms ease-in-out')),
      transition('out => in', animate('400ms ease-in-out'))
    ])
  ],
  providers: [
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
  ],
})
@Pipe({ name: 'safeHtml' })
export class ComplaintDetailComponent implements OnInit {
  taskStatusList: any[];
  tag = [];
  public bucketUrl = `${environment.BUCKET_URL}`;
  docUrl;
  displayColumns: string[] = ['check', 'task', 'status', 'priority', 'duedate'];
  dataSource1 = new MatTableDataSource([]);
  dataSource2 = new MatTableDataSource<NoTaskTable>(ELEMENT_DATA);
  selection = new SelectionModel<PeriodicElement>(true, []);
  roomsFilter: any;
  tomorrow = new Date();
  subTagList: Observable<any[]>;
  isUrl: boolean;
  subTaglists: any = [];
  complaintSource: any = [];
  subtags: any = [];
  @ViewChild('tagInput') tagInput: ElementRef;
  tagCtrl = new FormControl();
  subTagList1: any;
  detail_date: any;
  product_category: any;
  comp_date: any;
  detail_source: any;
  detail_link: any;
  detail_adsource: any;
  company_details: any;
  advertiser_company;
  detail_email: any;
  detail_platform: any;
  detail_channel: any;
  company_id: any;
  docs: any;
  complaint_id: any;
  fileName: any;
  longText2: string;
  longText1: string;
  longText3: string;
  longText6: string;
  longText8: string;
  longText9: string;
  detail_place: any;
  detail_addate: any;
  panelOpenState = false;
  actionButtons1: boolean = false;
  actionButtons2: boolean = false;
  role: any;
  viewId: any;
  isMenuOpen: boolean = false;
  isViewMoreOpen: boolean = false;
  isViewMoreAction: boolean = false;
  viewMoreId: any;
  viewMoreOptionId: any;
  imgURL: string;
  url: string;
  noOfDocs = 0;
  adDocs: any;
  adMedium: any;
  longText4: string;
  longText5: string;
  advertisementMedium: any;
  originalComplaintObj: any;
  media_outlet: any;
  media: any;
  super_category: any;
  edition: any;
  suppliment: any;
  ad_language: any;
  duration: any;
  creative_id: any;
  translation_hyper: any;
  complaint_source_id: number;
  complaint_type_id: number;
  complaintUpdate: boolean = false;
  complaint_source: string;
  influencer_name: any;
  engagements: any;
  publication_url: any;
  profile_url: any;
  influencer_contact: any;
  influencer_email: any;
  seen_date: any;
  case_id: any;
  limit: number;
  detail_adtime: any;
  seen_time: any;
  isResolution: boolean = false;
  advertiser_due = new Date;
  classification_name: any;
  product_name: any;
  @Output() outFilter: EventEmitter<any> = new EventEmitter<any>();
  complaint_status_id: any;
  @Output() refreshComplaints: EventEmitter<any> = new EventEmitter<any>();
  network: any;
  similarComplaintsLength: any;
  timelineState: string = 'out';
  buttonState: string = 'out';

  openTimeline() {
    this.timelineState = this.timelineState === 'out' ? 'in' : 'out';
    this.buttonState = this.timelineState === 'out' ? 'out' : 'in';
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource1.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected() ?
      this.selection.clear() :
      this.dataSource1.data.forEach(element => this.selection.select(element));
  }

  checkboxLabel(element?: PeriodicElement): string {
    if (!element) {
      return `${this.isAllSelected() ? 'select' : 'deselect'} all`;
    }
    return `${this.selection.isSelected(element) ? 'deselect' : 'select'} row ${element.TASK_DESCRIPTION + 1}`;
  }

  form: FormGroup;
  date_form: FormGroup;
  stage_process = new FormControl();
  stages: any[];
  processes: any[];
  assignees: any[];
  priorityList: any[];
  classification: any[];
  clauseList: any[];
  selected2 = { value: 'High', icon: 'add', viewValue: 'High' };
  keyword = 'SUB_TAG_NAME';
  marks: MarkDTO[] = [
    new MarkDTO({ id: 1, name: 'High', number: 1 }),
    new MarkDTO({ id: 2, name: 'Medium', number: 2 }),
    new MarkDTO({ id: 3, name: 'Low', number: 3 }),
  ];
  selectedp = 'High';
  selectable = true;
  removable = true;
  addOnBlur = true;
  separatorKeysCodes: number[] = [ENTER, COMMA];
  tags: Tag[] = [];
  zeroTasks: boolean = false;
  timeLineExist: boolean = true;
  taskLoading: boolean = true;
  timelineLoading: boolean = false;
  taskTimeline = [];
  similarComplaints = [];
  complaintDetails = [];
  advertiserDetails = [];
  advEmail = [];
  advertiserStr: string;
  similarCompLoading: boolean = true;
  userInfo: any;
  userRoleId: number;
  user_id: number;
  hiddenProduct: boolean = false;
  hiddenMore: boolean = true;

  add(event: MatChipInputEvent) {
    const input = event.input;
    const value = (event.value || '').trim();
    if (value) {
      this.tags.push({ name: event.value });
    }
    if (input) {
      input.value = '';
    }
    this.tagCtrl.setValue(null);
    // Clear the input value
    // event.value.clear();
  }

  addSubtag(event: MatChipInputEvent) {
    const input = event.input;
    const value = (event.value || '').trim();
    if (input) {
      input.value = '';
    }
  }

  remove(tag: Tag): void {
    const index = this.tags.indexOf(tag);
    if (index >= 0) {
      this.tags.splice(index, 1);
    }
  }

  removes(tag): void {
    const index = this.subtags.indexOf(tag);
    if (index >= 0) {
      this.subtags.splice(index, 1);
    }
  }
  complaintClaims: any = [];
  complaintCodeViolated: any = [];
  guidelines: any = [];
  advertisementDocs: any = [];
  complaintId: number;
  complaint: any;
  @Input() id: number;
  @Input() ftc: number;
  public dataSource: Array<any> = [];
  public files: any[] = [];
  public resolutionfiles: any[] = [];
  complaintStatusList: any[];
  resolutionList: any[];
  complianceList: any[];
  confirmationMsg: any = {};
  date = new Date;
  taskdate;
  eg_val;
  minDate = new Date();
  detail_status;
  detail_priority;
  detail_company: string = '';
  company_name;
  detail_complaint: string;
  detail_advert;
  detail_advert_head;
  transcription;
  transcription_head;
  detail_firstname;
  detail_lastname;
  detail_address;
  detail_id;
  old_case_id;
  detail_contactno;
  longText: string = '';
  isUploadProgress: boolean = false;
  docFileList: any[] = [];
  complainantFiles = [];
  advertiserFiles = [];
  internalFiles = [];
  assigneeId: number = 0;

  constructor(
    public fb: FormBuilder,
    public dialog: MatDialog,
    private datePipe: DatePipe,
    private renderer: Renderer2,
    private matDialog: MatDialog,
    private notify: NotificationService,
    private cs: ComplaintsService,
    private taskService: TasksService,
    private uploadService: UploadService,
    private sanitized: DomSanitizer
  ) {
    this.form = fb.group({
      requester: [''],
      assigned_to: [''],
      status: [''],
      resolution: [''],
      compliance: [''],
      stage: [''],
      process: [''],
      priority: [''],
      due_date: [''],
      advertiser_due_date: [''],
      tag: [['']],
      subtag: [''],
      sub_tags: ['']
    })
    this.date_form = fb.group({
      tabledate: ['']
    })
    this.subTagList = this.tagCtrl.valueChanges.pipe(
      startWith(''),
      map((subTagComplaint: string | null) => subTagComplaint ? this.filter(subTagComplaint) : this.subTaglists.slice())
    );
    this.tomorrow.setDate(this.tomorrow.getDate() + 1);
  }

  getSelectedLabel(): MarkDTO {
    return this.form.get('priority').value;
  }

  filter(name: any): any[] {
    let filterValue = null;
    if (typeof name === 'string' || name instanceof String) {
      filterValue = name ? name.toLowerCase() : '';
    } else if (typeof name === 'object' && name !== null) {
      filterValue = name['SUB_TAG_NAME'] ? name['SUB_TAG_NAME'].toLowerCase() : '';
    }
    return this.subTaglists.filter(tag =>
      tag.SUB_TAG_NAME.toLowerCase().indexOf(filterValue) === 0);
  }

  safeHTML(unsafe: string) {
    return this.sanitized.bypassSecurityTrustHtml(unsafe);
  }

  ngOnInit(): void {
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.userRoleId = this.userInfo?.roleId;
    if (this.userRoleId == 6) {
      this.form.controls['assigned_to'].disable()
    }
    this.user_id = this.userInfo?.userId;
    this.complaintStatusList = JSON.parse(window.localStorage.getItem('complaintStatus'));
    this.resolutionList = JSON.parse(window.localStorage.getItem('resolutionStatus'));
    this.complianceList = JSON.parse(window.localStorage.getItem('complianceStatus'));
    this.priorityList = JSON.parse(window.localStorage.getItem('priority'));
    this.classification = JSON.parse(window.localStorage.getItem('classification'));
    this.subTaglists = JSON.parse(window.localStorage.getItem('subTagList'));
    this.complaintSource = JSON.parse(window.localStorage.getItem('complaintSource'));
    if (this.priorityList[0].ID == 0) {
      this.priorityList[0].PRIORITY_NAME = "None";
    }
    this.taskStatusList = JSON.parse(window.localStorage.getItem('taskStatus'));
    if (this.taskStatusList[0].ID == 0) {
      this.taskStatusList[0].TASK_STATUS_NAME = "None";
    }
    this.stages = JSON.parse(window.localStorage.getItem('stages'));
    this.processes = JSON.parse(window.localStorage.getItem('processes'));
    this.clauseList = JSON.parse(window.localStorage.getItem('clause'));
    this.form.controls['requester'].disable();
    this.form.controls['tag'].disable();
    if (this.ftc == 0) {
      this.getComplaintById(this.id);
    }
    else if (this.ftc == 1) {
      this.getFTCComplaintById(this.id);
    }
    this.form.get('assigned_to')!.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0) {
          this.getAssigneeList(value);
        }
      }, err => {
      })
    this.cs.getTimeline(this.id);
  }

  getAssigneeList(key) {
    this.cs.getAssigneeList(key, '').subscribe(res => {
      this.assignees = res.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  onSelectionChange(event) {
    this.assignees.forEach(element => {
      if (event.option.value === element.ASSIGNEE_USER_NAME) {
        this.assigneeId = element.ID;
      }
    });
  }

  getAdvertiser() {
    this.cs.getAdvertiser(this.company_id).subscribe((advertisers: any) => {
      this.advertiserDetails = advertisers.data;
      if (this.advertiserDetails.length != 0) {
        if (this.advertiserDetails[0].ADVERTISER_CONTACT.length != 0) {
          for (let item of this.advertiserDetails[0].ADVERTISER_CONTACT) {
            if (item.EMAIL_ID != null && item.EMAIL_ID != "") {
              this.advEmail.push(item.EMAIL_ID);
            }
          }
        }
        if (this.advertiserDetails[0].COMPANY_CONTACT.length != 0) {
          for (let item of this.advertiserDetails[0].COMPANY_CONTACT) {
            if (item.EMAIL_ID != null && item.EMAIL_ID != "") {
              this.advEmail.push(item.EMAIL_ID);
            }
          }
        }
        this.advertiserStr = this.advEmail.toString();
        if (this.advertiserStr.length > 21) {
          this.longText6 = '..'
        }
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  viewMore(id) {
    this.viewId = id;
    this.isMenuOpen = !this.isMenuOpen;
  }

  viewMoreActions(id) {
    this.viewMoreId = id;
    this.isViewMoreAction = !this.isViewMoreAction;
  }

  viewMoreOptions(id) {
    this.viewMoreOptionId = id;
    this.isViewMoreOpen = !this.isViewMoreOpen;
  }

  download(doc, source) {
    this.url = this.bucketUrl + source;
    this.cs.downloadFile(this.url).subscribe(data => {
      importedSaveAs(data, doc)
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    });
  }

  preview(source) {
    this.imgURL = this.bucketUrl + source;
    window.open(this.imgURL, 'window 1', '');
  }

  previewLink(source) {
    if (source.indexOf("https") == -1 && source.indexOf("http") == -1) {
      window.open("https://" + source, "_blank");
    } else {
      window.open(source, "_blank");
    }
  }

  shareFile(id, adv, comp) {
    const dialogRef = this.matDialog.open(InternalMediaShareComponent, {
      width: '476px',
      height: 'auto',
      data: { mediaId: id, advertiser: adv, complainant: comp },
      disableClose: false
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data.state == true) {
          this.getDocuments(this.id);
        }
      }
    );
  }

  selected(subtag, event): void {
    let subTagExists = false;
    if (event.isUserInput) {
      for (let st_i = 0; st_i < this.subtags.length; st_i++) {
        if (this.subtags[st_i]["ID"] == subtag.ID) {
          subTagExists = true;
          break;
        }
      }
      if (subTagExists) {
        this.notify.showNotification(
          "Subtag already selected",
          "top",
          "warning",
          0
        );
      }
      else {
        this.subtags.push(subtag);
      }
      this.tagInput.nativeElement.value = '';
      this.tagCtrl.setValue(null);
    }
  }

  getComplaintById(id) {
    this.cs.getComplaint(id).subscribe(res => {
      this.originalComplaintObj = JSON.parse(JSON.stringify(res.data));
      this.dataSource.push(res);
      this.complaintClaims = res.data.CLAIMS;
      this.complaintCodeViolated = res.data.CODEVIOLATED;
      this.guidelines = res.data.GUIDELINES;
      this.role = res.data.COMPLAINT_TYPE_ID;
      for (let i = 0; i < res.data.ADVERTISEMENT_MEDIUM_DOCUMENT.length; i++) {
        if (res.data.ADVERTISEMENT_MEDIUM_DOCUMENT[i].ATTACHMENT_SOURCE !== '') {
          this.noOfDocs += 1;
        }
      }
      this.adDocs = res.data.ADVERTISEMENT_MEDIUM_DOCUMENT;
      this.adMedium = res.data.ADVERTISEMENT_MEDIUM;
      if (res.data.ADVERTISEMENT_MEDIUM_DOCUMENT.length != 0) {
        for (let medium of res.data.ADVERTISEMENT_MEDIUM_DOCUMENT) {
          let advMedium = {};
          if (medium.ATTACHMENT_SOURCE != "") {
            advMedium = {
              "ID": medium.ID,
              "ATTACHMENT_SOURCE": medium.ATTACHMENT_SOURCE,
              "ATTACHMENT_NAME": medium.ATTACHMENT_NAME,
              "ATTACHMENT_SOURCE_SIZE": medium.SIZE,
              "ATTACHMENT_SOURCE_TYPE": medium.TYPE_OF_DOCUMENT,
              "DATE": medium.CREATED_DATE
            }
            this.advertisementDocs.push(advMedium)
          }
        }
      }
      this.advertisementMedium = res.data.ADVERTISEMENT_MEDIUM;
      for (let i = 0; i < this.advertisementMedium.length; i++) {
        this.advertisementMedium[i].docInfo = [];
        this.adDocs.filter(el => {
          if (el.ADVERTISEMENT_MEDIUM_ID == this.advertisementMedium[i].ID && el.ATTACHMENT_SOURCE !== '') {
            this.advertisementMedium[i].docInfo.push({
              url: el.ATTACHMENT_SOURCE,
              name: el.ATTACHMENT_NAME,
              date: el.CREATED_DATE
            });
          }
        })
      }
      for (let i = 0; i <= this.complaintCodeViolated.length - 1; i++) {
        if (this.complaintCodeViolated[i].CLAUSES_ID != null) {
          this.complaintCodeViolated[i].CLAUSES_ID = ((this.complaintCodeViolated[i].CLAUSES_ID).replace(/"/g, '')).slice(1, -1);
        }
      }
      for (let i = 0; i <= this.guidelines.length - 1; i++) {
        if (this.guidelines[i].G_CLAUSES_ID != null) {
          this.guidelines[i].G_CLAUSES_ID = ((this.guidelines[i].G_CLAUSES_ID).replace(/"/g, '')).slice(1, -1);
        }
      }
      for (let i = 0; i <= this.guidelines.length - 1; i++) {
        if (this.guidelines[i].CELEBRITY_NAME != null) {
          this.guidelines[i].CELEBRITY_NAME = ((this.guidelines[i].CELEBRITY_NAME).replace(/"/g, ' ')).slice(1, -1);
        }
      }
      this.form.controls['requester'].patchValue(this.dataSource[0].data.REQUESTER_USER_NAME);
      this.form.controls['assigned_to'].setValue(this.dataSource[0].data.ASSIGNEE_USER_NAME);
      this.assigneeId = this.dataSource[0].data.ASSIGNEE_USER_ID;
      this.form.controls['status'].patchValue(this.dataSource[0].data.COMPLAINT_STATUS_ID);
      this.complaint_status_id = this.dataSource[0].data.COMPLAINT_STATUS_ID;
      this.form.controls['stage'].patchValue(this.dataSource[0].data.STAGE_ID);
      this.form.controls['process'].patchValue(this.dataSource[0].data.PROCESS_ID);
      this.form.controls['priority'].patchValue(this.dataSource[0].data.PRIORITY_ID);
      this.form.controls['resolution'].patchValue(this.dataSource[0].data.RESOLUTION_STATUS_ID);
      this.form.controls['compliance'].patchValue(this.dataSource[0].data.COMPLIANCE_STATUS_ID);
      this.detail_priority = this.dataSource[0].data.PRIORITY_NAME;
      this.detail_status = this.dataSource[0].data.COMPLAINT_STATUS_NAME;
      this.detail_company = !!this.dataSource[0].data.BRAND_NAME ? this.dataSource[0].data.BRAND_NAME : '';
      this.company_id = this.dataSource[0].data.COMPANY_ID;
      this.complaint_id = this.dataSource[0].data.ID;
      this.detail_email = this.dataSource[0].data.EMAIL_ID;
      this.detail_date = this.dataSource[0].data.CREATED_DATE;
      this.product_name = this.dataSource[0].data.PRODUCT_NAME;
      this.product_category = this.dataSource[0].data.PRODUCT_CATEGORY;
      this.comp_date = this.dataSource[0].data.REGISTERED_DATE;
      this.seen_date = this.dataSource[0].data.DATE;
      if (this.dataSource[0].data.TIME) {
        let d = this.dataSource[0].data.TIME.split(":");
        if (d[0] != '00' || d[1] != '00') {
          this.seen_time = d[0] + ":" + d[1];
        }
      }
      this.detail_source = this.dataSource[0].data.COMPLAINT_SOURCE_NAME;
      this.network = this.dataSource[0].data.NETWORK;
      this.classification_name = this.dataSource[0].data.CLASSIFICATION_NAME;
      this.complaint_source_id = this.dataSource[0].data.COMPLAINT_SOURCE_ID;
      this.complaint_type_id = this.dataSource[0].data.COMPLAINT_TYPE_ID;
      if (this.dataSource[0].data.COMPLAINT_SOURCE_ID == 7 || (this.dataSource[0].data.COMPLAINT_SOURCE_ID == 2 && this.dataSource[0].data.COMPLAINT_TYPE_ID == 6)) {
        this.media_outlet = this.dataSource[0].data.MEDIA_OUTLET;
        var re = new RegExp('^(http(s)?:\\/\\/)?' + // protocol
          '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
          '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
          '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
          '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
          '(\\#[-a-z\\d_]*)?$', 'i');
        if (re.test(this.media_outlet)) {
          this.isUrl = true;
        } else {
          this.isUrl = false;
        }
        this.media = this.dataSource[0].data.MEDIA;
        this.edition = this.dataSource[0].data.EDITION;
        this.super_category = this.dataSource[0].data.PRODUCT_CATEGORY;
        this.ad_language = this.dataSource[0].data.AD_LANGUAGE;
        this.duration = this.dataSource[0].data.DURATION;

        this.suppliment = this.dataSource[0].data.SUPPLIMENT;
        this.creative_id = this.dataSource[0].data.CREATIVE_ID;
        this.translation_hyper = this.dataSource[0].data.TRANSLATION_HYPERLINK;
        if (this.translation_hyper) {
          if (this.translation_hyper.length > 19) {
            this.longText5 = '..';
          } else {
            this.longText5 = '..';
          }
        }
        if (this.media_outlet) {
          if (this.media_outlet.length > 27) {
            this.longText8 = '..';
          }
        }
      }
      if (this.dataSource[0].data.COMPLAINT_SOURCE_ID == 8) {
        this.influencer_name = this.dataSource[0].data.INFLUENCER_NAME;
        this.engagements = this.dataSource[0].data.ENGAGEMENTS;
        this.publication_url = this.dataSource[0].data.PUBLICATION_URL;
        this.profile_url = this.dataSource[0].data.PROFILE_URL;
        this.influencer_contact = this.dataSource[0].data.INFLUENCER_MOBILE;
        this.influencer_email = this.dataSource[0].data.INFLUENCER_EMAIL
      }
      if (this.dataSource[0].data.ADVERTISEMENT_MEDIUM.length != 0) {
        this.detail_adsource = this.dataSource[0].data.ADVERTISEMENT_MEDIUM;
        this.detail_platform = this.dataSource[0].data.ADVERTISEMENT_MEDIUM[0].PLATFORM_ID;
        this.detail_channel = this.dataSource[0].data.ADVERTISEMENT_MEDIUM[0].SOURCE_NAME;
        this.detail_addate = this.dataSource[0].data.ADVERTISEMENT_MEDIUM[0].DATE;
        if (this.dataSource[0].data.ADVERTISEMENT_MEDIUM[0].TIME) {
          let d = this.dataSource[0].data.ADVERTISEMENT_MEDIUM[0].TIME.split(":");
          if (d[0] != '00' || d[1] != '00') {
            this.detail_adtime = d[0] + ":" + d[1];
          }
        }
        this.detail_link = this.dataSource[0].data.ADVERTISEMENT_MEDIUM[0].SOURCE_URL;
        if (this.detail_link) {
          if (this.detail_link.length > 40) {
            this.longText4 = '..';
          } else {
            this.longText4 = ' ';
          }
        }
        this.docUrl = this.bucketUrl + this.detail_link;
        this.detail_place = this.dataSource[0].data.ADVERTISEMENT_MEDIUM[0].SOURCE_PLACE;
      }
      if (this.dataSource[0].data.COMPANY_INFO.length != 0) {
        this.company_details = this.dataSource[0].data.COMPANY_INFO;
      }
      this.company_name = this.dataSource[0].data.COMPANY_NAME;
      let resSubTags = this.dataSource[0].data.SUBTAGS;
      if (Array.isArray(resSubTags) && resSubTags.length > 0) {
        this.subtags = this.subTaglists.filter(o => resSubTags.some(id => o.ID == id))
      }
      this.detail_complaint = this.dataSource[0].data.COMPLAINT_DESCRIPTION;
      this.transcription = this.dataSource[0].data.TRANSCRIPTION;
      if (!!this.transcription) {
        this.transcription_head = (this.dataSource[0].data.TRANSCRIPTION).replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').replace(/amp;/g, ' ');
        if (this.transcription_head?.length > 31) {
          this.longText9 = ' ...'
        }
      }
      this.detail_advert = this.dataSource[0].data.ADVERTISEMENT_DESCRIPTION;
      this.detail_advert_head = (this.dataSource[0].data.ADVERTISEMENT_DESCRIPTION).replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').replace(/amp;/g, ' ');
      if (this.detail_advert_head?.length > 31) {
        this.longText1 = ' ...'
      }
      if (this.detail_company?.length > 10) {
        this.longText2 = ' ...'
      }
      if (this.company_name?.length > 10) {
        this.longText3 = ' ...'
      }
      this.detail_firstname = this.dataSource[0].data.USER_FIRST_NAME;
      this.detail_lastname = this.dataSource[0].data.USER_LAST_NAME;
      this.detail_address = this.dataSource[0].data.ADDRESS;
      this.detail_id = this.dataSource[0].data.CASE_ID;
      this.old_case_id = this.dataSource[0].data.OLD_CASE_ID;
      this.detail_contactno = this.dataSource[0].data.MOBILE;
      this.date = this.dataSource[0].data.DUE_DATE;
      this.advertiser_due = this.dataSource[0].data.ADVERTISER_DUE_DATE;
      if (!!this.advertiser_due) {
        this.form.controls['advertiser_due_date'].patchValue(moment(this.advertiser_due).format('yyyy-MM-DD'));
      }
      if (!!this.date) {
        this.form.controls['due_date'].patchValue(moment(this.date).format('yyyy-MM-DD'));
      }
      this.getTags()
      this.getTaskList(this.id);
      this.getAdvertiser();
      this.getSimilarComplaints();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    });
  }

  getFTCComplaintById(id) {
    this.cs.getFTCComplaint(id).subscribe(res => {
      this.dataSource.push(res);
      this.originalComplaintObj = JSON.parse(JSON.stringify(res.data));;
      this.complaintClaims = res.data.CLAIMS;
      this.complaintCodeViolated = res.data.CODEVIOLATED;
      this.guidelines = res.data.GUIDELINES;
      this.role = res.data.COMPLAINT_TYPE_ID;
      this.adDocs = res.data.ADVERTISEMENT_MEDIUM_DOCUMENT;
      this.adMedium = res.data.ADVERTISEMENT_MEDIUM;
      for (let i = 0; i < res.data.ADVERTISEMENT_MEDIUM_DOCUMENT.length; i++) {
        if (res.data.ADVERTISEMENT_MEDIUM_DOCUMENT[i].ATTACHMENT_SOURCE !== '') {
          this.noOfDocs += 1;
        }
      }
      if (res.data.ADVERTISEMENT_MEDIUM_DOCUMENT.length != 0) {
        for (let medium of res.data.ADVERTISEMENT_MEDIUM_DOCUMENT) {
          let advMedium = {};
          if (medium.ATTACHMENT_SOURCE != "") {
            advMedium = {
              "ID": medium.ID,
              "ATTACHMENT_SOURCE": medium.ATTACHMENT_SOURCE,
              "ATTACHMENT_NAME": medium.ATTACHMENT_NAME,
              "ATTACHMENT_SOURCE_SIZE": medium.SIZE,
              "ATTACHMENT_SOURCE_TYPE": medium.TYPE_OF_DOCUMENT,
              "DATE": medium.CREATED_DATE
            }
            this.advertisementDocs.push(advMedium)
          }
        }
        this.advertisementMedium = res.data.ADVERTISEMENT_MEDIUM;
        for (let i = 0; i < this.advertisementMedium.length; i++) {
          this.advertisementMedium[i].docInfo = [];
          this.adDocs.filter(el => {
            if (el.ADVERTISEMENT_MEDIUM_ID == this.advertisementMedium[i].ID && el.ATTACHMENT_SOURCE !== '') {
              this.advertisementMedium[i].docInfo.push({
                url: el.ATTACHMENT_SOURCE,
                name: el.ATTACHMENT_NAME,
                date: el.CREATED_DATE
              });
            }
          })
        }
      }
      for (let i = 0; i <= this.complaintCodeViolated.length - 1; i++) {
        if (this.complaintCodeViolated[i].CLAUSES_ID != null) {
          this.complaintCodeViolated[i].CLAUSES_ID = ((this.complaintCodeViolated[i].CLAUSES_ID).replace(/"/g, '')).slice(1, -1);
        }
      }
      for (let i = 0; i <= this.guidelines.length - 1; i++) {
        if (this.guidelines[i].G_CLAUSES_ID != null) {
          this.guidelines[i].G_CLAUSES_ID = ((this.guidelines[i].G_CLAUSES_ID).replace(/"/g, '')).slice(1, -1);
        }
      }
      for (let i = 0; i <= this.guidelines.length - 1; i++) {
        if (this.guidelines[i].CELEBRITY_NAME != null) {
          this.guidelines[i].CELEBRITY_NAME = ((this.guidelines[i].CELEBRITY_NAME).replace(/"/g, ' ')).slice(1, -1);
        }
      }
      this.form.controls['requester'].patchValue(this.dataSource[0].data.REQUESTER_USER_NAME);
      this.form.controls['assigned_to'].setValue(this.dataSource[0].data.ASSIGNEE_USER_NAME);
      this.assigneeId = this.dataSource[0].data.ASSIGNEE_USER_ID;
      this.form.controls['status'].patchValue(this.dataSource[0].data.COMPLAINT_STATUS_ID);
      this.complaint_status_id = this.dataSource[0].data.COMPLAINT_STATUS_ID;
      this.form.controls['stage'].patchValue(this.dataSource[0].data.STAGE_ID);
      this.form.controls['process'].patchValue(this.dataSource[0].data.PROCESS_ID);
      this.form.controls['priority'].patchValue(this.dataSource[0].data.PRIORITY_ID);
      this.form.controls['resolution'].patchValue(this.dataSource[0].data.RESOLUTION_STATUS_ID);
      this.form.controls['compliance'].patchValue(this.dataSource[0].data.COMPLIANCE_STATUS_ID);
      this.detail_priority = this.dataSource[0].data.PRIORITY_NAME;
      this.detail_status = this.dataSource[0].data.COMPLAINT_STATUS_NAME;
      this.detail_company = !!this.dataSource[0].data.BRAND_NAME ? this.dataSource[0].data.BRAND_NAME : '';
      this.company_id = this.dataSource[0].data.COMPANY_ID;
      this.complaint_id = this.dataSource[0].data.ID;
      this.detail_email = this.dataSource[0].data.EMAIL_ID;
      this.detail_date = this.dataSource[0].data.CREATED_DATE;
      this.product_category = this.dataSource[0].data.PRODUCT_CATEGORY;
      this.product_name = this.dataSource[0].data.PRODUCT_NAME;
      this.classification_name = this.dataSource[0].data.CLASSIFICATION_NAME;
      this.comp_date = this.dataSource[0].data.REGISTERED_DATE;
      this.seen_date = this.dataSource[0].data.DATE;
      if (this.dataSource[0].data.TIME) {
        let d = this.dataSource[0].data.TIME.split(":");
        if (d[0] != '00' || d[1] != '00') {
          this.seen_time = d[0] + ":" + d[1];
        }
      }
      this.detail_source = this.dataSource[0].data.COMPLAINT_SOURCE_NAME;
      this.network = this.dataSource[0].data.NETWORK;
      if (this.dataSource[0].data.ADVERTISEMENT_MEDIUM.length != 0) {
        this.detail_adsource = this.dataSource[0].data.ADVERTISEMENT_MEDIUM;
        this.detail_platform = this.dataSource[0].data.ADVERTISEMENT_MEDIUM[0].PLATFORM_ID;
        this.detail_channel = this.dataSource[0].data.ADVERTISEMENT_MEDIUM[0].SOURCE_NAME;
        this.detail_addate = this.dataSource[0].data.ADVERTISEMENT_MEDIUM[0].DATE;
        if (this.dataSource[0].data.ADVERTISEMENT_MEDIUM[0].TIME) {
          let d = this.dataSource[0].data.ADVERTISEMENT_MEDIUM[0].TIME.split(":");
          if (d[0] != '00' || d[1] != '00') {
            this.detail_adtime = d[0] + ":" + d[1];
          }
        }
        this.detail_link = this.dataSource[0].data.ADVERTISEMENT_MEDIUM[0].SOURCE_URL;
        this.docUrl = this.bucketUrl + this.detail_link;
        this.detail_place = this.dataSource[0].data.ADVERTISEMENT_MEDIUM[0].SOURCE_PLACE;
      }
      this.company_name = this.dataSource[0].data.COMPANY_NAME;
      let resSubTags = this.dataSource[0].data.SUBTAGS;
      if (Array.isArray(resSubTags) && resSubTags.length > 0) {
        this.subtags = this.subTaglists.filter(o => resSubTags.some(id => o.ID == id))
      }
      this.detail_complaint = this.dataSource[0].data.COMPLAINT_DESCRIPTION;
      this.transcription = this.dataSource[0].data.TRANSCRIPTION;
      if (!!this.transcription) {
        this.transcription_head = (this.dataSource[0].data.TRANSCRIPTION).replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').replace(/amp;/g, ' ');
        if (this.transcription_head?.length > 31) {
          this.longText9 = ' ...'
        }
      }
      this.detail_advert = this.dataSource[0].data.ADVERTISEMENT_DESCRIPTION;
      this.detail_advert_head = (this.dataSource[0].data.ADVERTISEMENT_DESCRIPTION).replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').replace(/amp;/g, ' ');
      if (this.detail_advert_head.length > 41) {
        this.longText1 = ' ...'
      }
      if (this.detail_company.length > 10) {
        this.longText2 = ' ...'
      }
      if (this.company_name.length > 10) {
        this.longText3 = ' ...'
      }
      this.detail_firstname = this.dataSource[0].data.USER_FIRST_NAME;
      this.detail_lastname = this.dataSource[0].data.USER_LAST_NAME;
      this.detail_address = this.dataSource[0].data.ADDRESS;
      this.detail_id = this.dataSource[0].data.CASE_ID;
      this.old_case_id = this.dataSource[0].data.OLD_CASE_ID;
      this.detail_contactno = this.dataSource[0].data.MOBILE;
      this.date = this.dataSource[0].data.DUE_DATE;
      this.advertiser_due = this.dataSource[0].data.ADVERTISER_DUE_DATE;
      this.form.controls['advertiser_due_date'].patchValue(moment(this.advertiser_due).format('yyyy-MM-DD'));
      this.form.controls['due_date'].patchValue(moment(this.date).format('yyyy-MM-DD'));
      this.getTags()
      this.getTaskList(this.id);
      this.getAdvertiser();
      this.getSimilarComplaints();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    });
  }

  showMoreDetails() {
    this.hiddenMore = !this.hiddenMore
    this.hiddenProduct = !this.hiddenProduct
  }

  openRecommendationDialog() {
    const dialogRef = this.matDialog.open(RecommendationComponent, {
      width: '880px',
      height: 'auto',
      data: { 'complaint_id': this.id, 'status_id': this.complaint_status_id },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
      }
    );
  }

  openResolutionDialog() {
    const dialogRef = this.matDialog.open(ResolutionComponent, {
      width: '880px',
      height: 'auto',
      data: this.id,
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
      }
    );
  }

  openConversationDialog() {
    const dialogRef = this.matDialog.open(ComplaintConversationsComponent, {
      width: '1000px',
      height: 'auto',
      position: {
        top: '0px',
        left: '27vw'
      },
      data: { details: this.dataSource, advertiser: this.advertiserStr, from: 'manage-cases' },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
      }
    );
  }

  openNewTab() {
    const dialogRef = this.matDialog.open(ViewMediaFileComponent, {
      width: '476px',
      height: 'auto',
      data: { adMedium: this.adMedium, adDocs: this.adDocs },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
      }
    );
  }

  getTaskList(id) {
    this.limit = 20;
    this.taskService.getTaskDataById(id, this.limit).subscribe(res => {
      if (res.data.length == 0) {
        this.zeroTasks = true;
      } else {
        this.zeroTasks = false;
      }
      if (res.data[0]) {
        this.dataSource1 = new MatTableDataSource(res.data[0].DATA);
      }
      this.taskLoading = false;
    }, err => {
      this.taskLoading = false;
      this.zeroTasks = true;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  getTags() {
    if (this.detail_complaint !== '') {
      this.cs.getComplaintClassification(this.detail_complaint, this.complaint_source_id, this.id).subscribe(res => {
        const value = (res.data.ml_classification_name || '').trim();
        if (value) {
          this.tags.push({ name: value });
        }
      }, err => {
        // Do nothing
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
  }

  changeValuePriority(event) {
    for (let prio of this.priorityList) {
      if (prio.ID == event.value.toString()) {
        this.detail_priority = prio.PRIORITY_NAME;
      }
    }
  }

  //For Documents TAB
  // onFileChange(pFileList: File[]) {
  //   this.files = Object.keys(pFileList).map(key => pFileList[key]);
  //   this.fileName = this.files[0].name;
  // }

  async onFileChange(event: any) {
    // ToDo: check here 
    console.log('add files in documents -> internal');
    if (!this.uploadService.validateFileExtension(event)) {
      // this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        "File format is not supported!",
        "top",
        "warning",
        0
      );
      return;
    }
    let sizeOfAllFiles = this.uploadService.getTotalFileSize(event.target.files);
    let totalFileSelected = event.target.files.length;
    if (totalFileSelected < 11 && sizeOfAllFiles <= 36700160) {
      this.isUploadProgress = true;
      for (let i = 0; i <= event.target.files.length - 1; i++) {
        var selectedFile = event.target.files[i];
        this.internalFiles.push(selectedFile);
        let tempObjforGetsigned = {
          id: this.id,
          section: 'internal',
          filename: selectedFile['name'],
          type: selectedFile['type']
        }
        await this.uploadService.getSignedUrl(tempObjforGetsigned).then(async (res) => {
          if (res && res['data'] && res['data']['SIGNED_URL']) {
            let tempObjForUpload = {
              url: res['data']['SIGNED_URL'],
              file: selectedFile
            }
            await this.uploadSignedFile(tempObjForUpload);
            let saveBody = {
              ID: this.id,
              KEY: res['data']['PATH'],
              SOURCE_NAME: selectedFile['name'],
              TYPE: selectedFile['type'],
              TAB: 'internal'
            }
            await this.saveDocumentToDB(saveBody);
          }
        })
          .catch((err) => {
            this.isUploadProgress = false;
            this.internalFiles.splice(i, 1);
            this.handleError(err);
          });
      }
      this.isUploadProgress = false;
      // this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        'Documents uploaded successfully',
        "top",
        (!!colorObj[200] ? colorObj[200] : "success"),
        200
      );
    } else {
      // this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        "Max size 35MB and Max 10 files allowed",
        "top",
        "warning",
        0
      );
    }
  }

  async saveDocumentToDB(tempObj) {
    await this.cs.saveFileSourceToDB(tempObj).then((data) => {
      return data;
    })
      .catch((err) => {
        this.isUploadProgress = false;
        let body = {
          ID: tempObj['ID'],
          KEY: tempObj['KEY']
        }
        this.deleteFileFromS3(body)
        this.handleError(err);
      });
    this.getDocuments(this.id);
  }

  deleteFileFromS3(obj: any) {
    this.uploadService.deleteObjectFromS3(obj).subscribe((res) => {
      return res;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
      return err;
    })
  }

  getFileName(file) {
    if (file['name']) {
      return file['name'];
    } else if (file['ATTACHMENT_SOURCE']) {
      return file['ATTACHMENT_SOURCE'].substring(file['ATTACHMENT_SOURCE'].lastIndexOf("/") + 1);
    }
  }

  async uploadSignedFile(tempObj) {
    let progressInit = 0;
    await this.uploadService.uploadFilethroughSignedUrl(tempObj, progressInit => {
    }).catch((err) => {
      this.isUploadProgress = false;
      this.handleError(err);
    });
  }

  handleError(err: any) {
    this.notify.showNotification(
      err.error.message,
      "top",
      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
      err.error.status
    );
  }

  deleteFile(f) {
    this.files = this.files.filter(function (w) { return w.name != f.name });
    // this._snackBar.open("Successfully delete!", 'Close', {
    //   duration: 2000,
    // });
  }

  removeFile(ID, ATTACHMENT_SOURCE) {
    this.confirmationMsg.title = 'Are you sure you want to delete the file ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: ID, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.cs.deleteComplaintDocuments(dialogResult.id, ATTACHMENT_SOURCE)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              this.getDocuments(this.id);
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  updateComplaint(model) {
    // if (model.due_date == 'Invalid date' || model.due_date == null || model.due_date == '') {
    //   this.notify.showNotification(
    //     "Update Due Date",
    //     "top",
    //     "warning",
    //     0
    //   )
    //   return;
    // }
    // if (model.advertiser_due_date == 'Invalid date' || model.advertiser_due_date == null) {
    //   this.notify.showNotification(
    //     "Update Advertiser Due Date",
    //     "top",
    //     "warning",
    //     0
    //   )
    //   return;
    // }
    if (this.userRoleId != 7 && (model.assigned_to === null || model.assigned_to === '')) {
      this.notify.showNotification(
        "Please Choose Assignee Name",
        "top",
        "warning",
        0
      )
      return;
    }
    // if (model.status == 7 && (model.resolution == 0 || model.resolution === '')) {
    //   this.notify.showNotification(
    //     "Please Choose Resolution",
    //     "top",
    //     "warning",
    //     0
    //   )
    //   return;
    // }
    let obj = {
      "ID": this.id,
      "COMPLAINT_STATUS_ID": null,
      "RESOLUTION_ID": null,
      "COMPLIANCE_STATUS_ID": null,
      "PRIORITY_ID": null,
      "STAGE_ID": null,
      "PROCESS_ID": null,
      "ASSIGNEE_USER_ID": null,
      "DUE_DATE": null,
      "ADVERTISER_DUE_DATE": null,
      "SUBTAGS": null,
    }
    if (this.originalComplaintObj['COMPLAINT_STATUS_ID'] != model.status) {
      obj['COMPLAINT_STATUS_ID'] = model.status;
      this.complaintUpdate = true;
    }
    if (this.originalComplaintObj['RESOLUTION_STATUS_ID'] != model.resolution) {
      obj['RESOLUTION_ID'] = model.resolution;
      this.complaintUpdate = true;
    }
    if ((model.resolution == 1 || model.resolution == 3 || model.resolution == 5 || model.resolution == 6 || model.resolution == 8) && this.originalComplaintObj['COMPLIANCE_STATUS_ID'] != model.compliance) {
      obj['COMPLIANCE_STATUS_ID'] = model.compliance;
      this.complaintUpdate = true;
    }
    if (this.originalComplaintObj['PRIORITY_ID'] != model.priority) {
      obj['PRIORITY_ID'] = model.priority;
      this.complaintUpdate = true;
    }
    if (this.originalComplaintObj['STAGE_ID'] != model.stage) {
      obj['STAGE_ID'] = model.stage;
      this.complaintUpdate = true;
    }
    if (this.originalComplaintObj['PROCESS_ID'] != model.process) {
      obj['PROCESS_ID'] = model.process;
      this.complaintUpdate = true;
    }
    if (this.originalComplaintObj['ASSIGNEE_USER_ID'] != this.assigneeId) {
      obj['ASSIGNEE_USER_ID'] = this.assigneeId;
      this.complaintUpdate = true;
    }
    if (model.due_date !== 'Invalid date' && moment(this.originalComplaintObj['DUE_DATE']).format('yyyy-MM-DD') != moment(model.due_date).format('yyyy-MM-DD')) {
      obj['DUE_DATE'] = moment(model.due_date).format('yyyy-MM-DD');
      this.complaintUpdate = true;
    }
    if (model.advertiser_due_date !== 'Invalid date' && moment(this.originalComplaintObj['ADVERTISER_DUE_DATE']).format('yyyy-MM-DD') != moment(model.advertiser_due_date).format('yyyy-MM-DD')) {
      obj['ADVERTISER_DUE_DATE'] = moment(model.advertiser_due_date).format('yyyy-MM-DD');
      this.complaintUpdate = true;
    }
    if ((this.originalComplaintObj['SUBTAGS'].length != this.subtags.length) || (JSON.stringify(this.originalComplaintObj['SUBTAGS']) != JSON.stringify(this.subtags))) {
      let result = this.subtags.map(a => a.ID);
      obj['SUBTAGS'] = result;
      this.complaintUpdate = true;
    }

    if (this.complaintUpdate) {
      this.cs.updateComplaint(obj).subscribe(res => {
        this.originalComplaintObj = res.data;
        this.detail_status = this.originalComplaintObj['COMPLAINT_STATUS_NAME'];
        this.complaintUpdate = false;
        this.notify.showNotification(
          res.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status] : "success"),
          res.status
        )
        this.complaint_status_id = this.originalComplaintObj['COMPLAINT_STATUS_ID'];
        // this.form.reset();
        let data = {
          ftc: this.ftc,
          'cond': 'update',
          'id': this.id,
          'status': this.originalComplaintObj['COMPLAINT_STATUS_NAME'],
          'statusId': this.originalComplaintObj['COMPLAINT_STATUS_ID'],
          'assignee': this.originalComplaintObj['ASSIGNEE_USER_NAME']
        }
        // this.cs.emit<Object>(data);
        this.outFilter.emit(data);
        // 'update', this.ftc, this.id, 
        // this.originalComplaintObj['COMPLAINT_STATUS_NAME'], this.originalComplaintObj['COMPLAINT_STATUS_ID']
      }, err => {
        this.complaintUpdate = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      })
    }
  }

  deleteComplaint() {
    this.confirmationMsg.title = 'Are you sure you want to delete the complaint ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.cs.deleteComplaint(this.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
            }
            let data = {
              ftc: this.ftc,
              'cond': 'delete',
              'id': this.id,
              'status': this.originalComplaintObj['COMPLAINT_STATUS_NAME'],
              'statusId': this.originalComplaintObj['COMPLAINT_STATUS_ID'],
              'assignee': this.originalComplaintObj['ASSIGNEE_USER_NAME']
            }
            this.cs.emit<Object>(data);
            this.refreshComplaints.emit(data);
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  openConfirmDialog(pIndex): void {
    const dialogRef = this.dialog.open(DialogConfirmComponent, {
      panelClass: 'modal-xs'
    });
    dialogRef.componentInstance.fName = this.files[pIndex].name;
    dialogRef.componentInstance.fIndex = pIndex;
    dialogRef.afterClosed().subscribe(result => {
      if (result !== undefined) {
        this.deleteFromArray(result);
      }
    });
  }

  deleteFromArray(index) {
    this.files.splice(index, 1);
  }

  addNewTask() {
    const dialogRef = this.dialog.open(AddTaskComponent, {
      width: '1500px',
      height: '380px',
      data: { name: "manage-new", comp_name: this.detail_complaint, comp_id: this.id, case_id: this.detail_id },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'refresh') {
          this.getTaskList(this.id);
        }
      }
    );
  }

  updateTask(row, dataSource) {
    const dialogRef = this.dialog.open(AddTaskComponent, {
      width: '1500px',
      height: '600px',
      data: { row, dataSource, name: "manage-update" },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(res => {
      this.getTaskList(this.id);
    })
  }

  onTabChanged(event) {
    if (event.index == 0) {
      this.taskLoading = true;
      this.zeroTasks = false;
      this.getTaskList(this.id);
    }
    else if (event.index == 1) {
      this.getDocuments(this.id);
    }
    else if (event.index == 2) {
      this.timelineLoading = true;
      this.timeLineExist = true;
      this.getTaskTimeline();
    }
  }

  getDocuments(id) {
    this.cs.getDocumentsByComplaint(id).subscribe(res => {
      this.docFileList = res.data;
      this.complainantFiles = this.docFileList['COMPLAINANT'];
      this.advertiserFiles = this.docFileList['ADVERTISER'];
      this.internalFiles = this.docFileList['INTERNAL'];
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    });
  }

  getTaskTimeline() {
    this.cs.getComplaintTimeline(this.id).subscribe((timeline: any) => {
      this.timelineLoading = false;
      this.timeLineExist = true;
      this.taskTimeline = timeline.data;
      for (let item of this.taskTimeline) {
        item.date = this.datePipe.transform(item.date, 'EEE, d MMM y - h:mm a');
        for (let val of item.updates) {
          if (val.label == 'Complaint Received On') {
            val.value = this.datePipe.transform(val.value, 'dd/MM/yyyy, h:mm a');
          }
          if (val.label == 'Due date changed to' || val.label == 'Advertiser due date changed to') {
            val.value = this.datePipe.transform(val.value, 'dd/MM/yyyy');
          }
        }
      }
    }, err => {
      this.timelineLoading = false;
      this.timeLineExist = false;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  viewDocument() {
    const dialogRef = this.dialog.open(ViewDocumentComponent, {
      width: '525px',
      height: '472px',
      data: this.complaint_id,
      disableClose: true
    });
  }

  getSimilarComplaints() {
    this.case_id = "";
    this.cs.getSimilarComplaints(this.detail_id, this.case_id).subscribe((complaints: any) => {
      this.similarCompLoading = false;
      this.similarComplaintsLength = complaints.data.length;
      this.similarComplaints = complaints.data;
      for (let item of this.similarComplaints) {
        item.COMPLAINT_DESCRIPTION = (item.COMPLAINT_DESCRIPTION).replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').replace(/amp;/g, ' ');
      }
    }, err => {

    })
  }

  openSimilarComplaintDetail(caseId) {
    const dialogRef = this.dialog.open(SimilarComplaintDetailsComponent, {
      width: '1500px',
      height: '550px',
      data: { CASE_ID: caseId },
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe((data) => { });
  }

  onTabsChanged(event) {
    if (event.index == 1) {
      this.getSimilarComplaints();
    }
  }

  getColor(status) {
    switch (status) {
      case 'New':
        return '#47d147';
      case 'On Hold':
        return '#00ccff';
      case 'In Progress':
        return '#e6e600';
      case 'Closed':
        return '#ff704d';
    }
  }

  getBGC(status) {
    switch (status) {
      case 'New':
        return '#c6ffb3';
      case 'On Hold':
        return '#ccf2ff';
      case 'In Progress':
        return '#ffffcc';
      case 'Closed':
        return '#ffc2b3';
    }
  }

  getPriority(priority) {
    switch (priority) {
      case 'High':
        return 'red';
      case 'Medium':
        return 'rgb(235, 94, 38)';
      case 'Low':
        return 'rgb(72, 197, 255)';
    }
  }
}

//Priority Value
class MarkDTO {
  id: number;
  name: string;
  number: number;
  constructor(mark?: any) {
    this.id = mark && mark.id || null;
    this.name = mark && mark.name || null;
    this.number = mark && mark.number || null;
  }
  get color(): string {
    let color = '';
    switch (this.number) {
      case 1: color = 'red'; break;
      case 2: color = 'rgb(235, 94, 38)'; break;
      case 3: color = 'rgb(72, 197, 255)'; break;
    }
    return color;
  }
}

function duedate(duedate: any): any {
  throw new Error('Function not implemented.');
}