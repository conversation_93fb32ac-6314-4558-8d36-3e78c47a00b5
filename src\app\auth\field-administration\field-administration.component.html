<div class="common-toolbar-mobile" style="width: 100%; height: auto;" *ngIf="isMobile">
  <app-mobile-header></app-mobile-header>
</div>
<span class="dashboard-admin-heading" *ngIf="isMobile">
  <p style="text-align: center;margin-top: 20px;margin-bottom: 3px;"> Welcome {{userName}}!</p>
</span>
<div class="dasboard-subheading" *ngIf="isMobile">
  <p style="text-align: center;">Dear ASCI Officers, please move to a larger device for seamlessly accessing all
      functionalities of the system</p>
</div>
<div class="dashboard-container" *ngIf="isMobile">
</div>

<app-icons *ngIf="!isMobile"></app-icons>
<div class="common-toolbar" fxLayout="row" style="width: 100%; height: 50px;" *ngIf="!isMobile">
  <div class="heading-container">
    <app-heading [pagename]="pagename"></app-heading>
  </div>
  <div class="options-container">
    <app-toolbar-options></app-toolbar-options>
  </div>
</div>

<app-mat-spinner-overlay overlay="true" *ngIf="loading && !isMobile">
</app-mat-spinner-overlay>

<mat-toolbar class="toolbar2" fxLayout="row" *ngIf="!isMobile">
    <div fxFlex="75%"></div>
    <div class="header-search">
      <input type="text" name="search" placeholder="Search.." [formControl]="KEYWORD" (keyup)="applyFilter($event.target.value)" autocomplete="off"
        style="font-style: normal; font-weight: normal; font-size: 14px; color: #2F3941;">
    </div>
    <div>
      <button mat-button *ngIf="companies" class="add-btn" (click)="addCompany()"
        style="width: 140px; margin-top: 5px;">
        <mat-icon style="font-size: 16px; padding-top: 3px; margin-left: -5px;">add</mat-icon>
        <span class="bolder">Add Company</span>
      </button>
      <button mat-button class="add-btn" *ngIf="subTags" (click)="addSubTag()" 
        style="width: 140px; margin-top: 5px;">
        <mat-icon style="font-size: 16px; padding-top: 3px; margin-left: -5px;">add</mat-icon>
        <span class="bolder">Add New Tag</span>
      </button>
    </div>
    <div>
      <button mat-button *ngIf="companies" class="merge-btn" (click)="openMergePopup()"
        style="width: 140px; margin-top: 5px;" [disabled]="idArray.length == 0 || idArray.length == 1">
        <span class="bolder">Merge Company </span>
      </button>
    </div>
</mat-toolbar>
<mat-tab-group style="margin-left: 50px; margin-top: -50px;" class="subtag-tabs" *ngIf="!isMobile"
  (selectedTabChange)="onTabChanged($event)">
  <mat-tab label="Companies" class="asci-member">
    <mat-card class="card" *ngIf="!loading">
      <div class="table-scroll">
        <table class="admin-table" mat-table [dataSource]="dataSource1" matSort #table>
          <div>
            <ng-container matColumnDef="check">
              <th mat-header-cell *matHeaderCellDef style="width: 7%;">
                <mat-checkbox class="select-chkb" disabled>
                </mat-checkbox>
              </th>
              <td mat-cell *matCellDef="let element">
                <mat-checkbox class="chkb-list"
                  (change)="selectCompany($event.checked, element.ID, element.status)"></mat-checkbox>
              </td>
            </ng-container>
            <ng-container matColumnDef="company_name">
              <th mat-header-cell *matHeaderCellDef style="width: 25%;"><b>Company Name</b>
                <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;"
                  *ngIf="!compname_asc && !compname_desc" (click)="sortCompanies('COMPANY_NAME', 'ASC')">
                <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="compname_asc"
                  (click)="sortCompanies('COMPANY_NAME', 'DESC')">
                <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="compname_desc"
                  (click)="sortCompanies('', '')">
              </th>
              <td mat-cell *matCellDef="let element" class="borderleft"> {{element.COMPANY_NAME}} </td>
            </ng-container>

            <!-- <ng-container matColumnDef="email">
              <th mat-header-cell *matHeaderCellDef style="width: 2%;"><b>Email Address</b></th>
              <td mat-cell *matCellDef="let element"> {{element.EMAIL_ID}} </td>
            </ng-container> -->

            <ng-container matColumnDef="address">
              <th mat-header-cell *matHeaderCellDef style="width: 25%;"><b>Address</b></th>
              <td mat-cell *matCellDef="let element"> {{element.ADDRESS}} </td>
            </ng-container>

            <ng-container matColumnDef="description">
              <th mat-header-cell *matHeaderCellDef style="width: 25%;"><b>Description</b></th>
              <td mat-cell *matCellDef="let element"> {{element.COMPANY_DESCRIPTION}} </td>
            </ng-container>

            <ng-container matColumnDef="action">
              <th mat-header-cell *matHeaderCellDef class="borderright" style=" padding-left: 3%;"><b>Action</b></th>
              <td mat-cell *matCellDef="let element" style=" padding-left: 3%;">
                <div fxLayout="row" fxLayoutGap="10px">
                  <div fxLayout="column" fxLayoutGap="3px" (click)="editCompanies(element)"
                    style="position: relative; left: 5px; top: 10px;">
                    <img src="../assets/images/edit-icon.svg" width="13px">
                    <p style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(0, 0, 0, 0.6);">Edit</p>
                  </div>
                  <div fxLayout="column" fxLayoutGap="2px" *ngIf="element.APPROVED == 0"
                    (click)="approveCompany(element)"
                    style="position: relative; top: 7px; left: 3px; display: flex; align-items: center;">
                    <img src="../../assets/images/Admin-Approve.png" width="15px">
                    <p style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(4, 165, 133, 0.6);">Approve</p>
                  </div>
                  <div fxLayout="column" fxLayoutGap="2px" *ngIf="element.APPROVED == 0"
                    (click)="rejectCompany(element)"
                    style="position: relative; top: 7px; left: 3px; display: flex; align-items: center">
                    <img src="../../assets/images/reject-red.png" width="15px">
                    <p style="
                    font-style: normal;
                    font-weight: normal;
                    font-size: 12px;color: rgba(237, 47, 69, 0.6);">Reject</p>
                  </div>
                </div>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </div>
        </table>
      </div>
    </mat-card>
    <mat-paginator (page)="changePage($event)" [pageSize]="10" [pageIndex]="0" *ngIf="!noData"></mat-paginator> 
    <span class="label" *ngIf="!noData"> {{rangeLabel}} of {{totalCount}}</span> 
    <div class="text-message" *ngIf="noData">
      <span>
        <br>
        No Data available ....
        <br>
      </span>
    </div>
  </mat-tab>

  <mat-tab label="Sub Tags" class="asci-member">
    <mat-card class="card" *ngIf="!loading">
        <div class="table-scroll">
            <table mat-table class="subtag-table" [dataSource]="dataSource" matSort #table>
                <div>
                    <ng-container matColumnDef="SUB_TAG_NAME">
                        <th mat-header-cell *matHeaderCellDef style="width: 90%;"><b>Sub Tag Name</b></th>
                        <td mat-cell *matCellDef="let element" class="borderleft"> {{element.SUB_TAG_NAME}} </td>
                    </ng-container>

                    <ng-container matColumnDef="action">
                        <th mat-header-cell *matHeaderCellDef class="borderright"><b>Action</b></th>
                        <td mat-cell *matCellDef="let element">
                          <div fxLayout="row" fxLayoutGap="10px">
                            <div fxLayout="column" fxLayoutGap="3px" (click)="editSubTag(element)"
                              style="position: relative;top: 10px;  left: 7px;">
                              <img src="../assets/images/edit-icon.svg" width="13px">
                              <p style="
                            font-style: normal;
                            font-weight: normal;
                            font-size: 12px;color: rgba(0, 0, 0, 0.6);">Edit</p>
                            </div>
                          </div>
                        </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="subTagsColumns; sticky: true"></tr>
                    <tr mat-row *matRowDef="let row; columns: subTagsColumns;">
                </div>
            </table>
        </div>
    </mat-card>
    <mat-paginator *ngIf="!noData" (page)="changePage($event)" [pageSize]="10" [pageIndex]="0"></mat-paginator> 
    <span *ngIf="!noData" class="label"> {{rangeLabel}} of {{totalCount}}</span>  
    <div class="text-message" *ngIf="noData">
        <span>
          <br>
          No Sub tags available ....
          <br>
        </span>
    </div>
  </mat-tab>
</mat-tab-group>