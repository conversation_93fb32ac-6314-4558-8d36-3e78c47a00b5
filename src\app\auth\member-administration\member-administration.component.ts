import { SelectionModel } from '@angular/cdk/collections';
import { Component, ElementRef, HostListener, OnInit, QueryList, ViewChildren } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';
import { AuthService } from 'src/app/services/auth.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';
import { AddAdvertiserComponent } from '../add-advertiser/add-advertiser.component';
import { AddEditConsumerComponent } from '../add-edit-consumer/add-edit-consumer.component';
import { AddEditGeneralPublicComponent } from '../add-edit-general-public/add-edit-general-public.component';
import { AddEditGovBodyComponent } from '../add-edit-gov-body/add-edit-gov-body.component';
import { AddUserComponent } from '../add-user/add-user.component';
import { AdmindialogComponent } from '../admindialog/admindialog.component';
import { CreateCompanyComponent } from '../create-company/create-company.component';
import { EditAdvertiserComponent } from '../edit-advertiser/edit-advertiser.component';

@Component({
  selector: 'app-member-administration',
  templateUrl: './member-administration.component.html',
  styleUrls: ['./member-administration.component.scss']
})
export class MemberAdministrationComponent implements OnInit {

 
  pagename: String;
  userInfo: any;
  userName: any;
  internalRespArray: Array<any> = [];
  externalRespArray: Array<any> = [];
  consumerRespArray: Array<any> = [];
  generalPublicRespArray: Array<any> = [];
  govBodyRespArray: Array<any> = [];
  dataSource1: any = [];
  dataSource2: any = [];
  checked = true;
  isAdmin: boolean;
  noData: boolean = false;
  loading: boolean = true;
  confirmationMsg: any = {};
  selectedTab;
  KEYWORD = new FormControl(null);
  internalPageNumber: number = 0;
  externalPageNumber: number = 0;
  consumerPageNumber: number = 0;
  generalPublicPageNumber: number = 0;
  govBodyPageNumber: number = 0;
  private request$: Observable<any>;
  internalUser: boolean;
  externalUser: boolean;
  consumerUser: boolean;
  isChecked: boolean;
  generalPublicUser: boolean;
  govBodyUser: boolean;

  private _intUserData = new BehaviorSubject<any[]>([]);
  private IntUserDataStore: { $intUserData: any[] } = { $intUserData: [] };
  readonly $intUserData = this._intUserData.asObservable();

  private _extUserData = new BehaviorSubject<any[]>([]);
  private ExtUserDataStore: { $extUserData: any[] } = { $extUserData: [] };
  readonly $extUserData = this._extUserData.asObservable();

  totalCount: any;
  startIndex: number;
  pageSize: number = 10;
  lastIndex: number;
  rangeLabel: string;
  limit: number = 0;
  lastBtn: any;
  internalFilterArray: any[];
  internalSortedArray: any[];
  externalFilterArray: any[];
  externalSortedArray: any[];
  internalFilter: boolean = false;
  externalFilter: boolean = false;
  fname_asc: boolean = false;
  fname_desc: boolean = false;
  lname_asc: boolean = false;
  lname_desc: boolean = false;
  email_asc: boolean = false;
  email_desc: boolean = false;
  dept_asc: boolean = false;
  dept_desc: boolean = false;
  role_asc: boolean = false;
  role_desc: boolean = false;
  status_asc: boolean = false;
  status_desc: boolean = false;
  extcompany_asc: boolean = false;
  extcompany_desc: boolean = false;
  sortingKey: any = '';
  sortingOrder: any = '';
  dialogRef: any;
  currentIndex: any;
  adminIndex: any;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }
  }

  constructor(public dialog: MatDialog,
    private router: Router,
    private authService: AuthService,
    private notify: NotificationService,
    private el: ElementRef<HTMLElement>) { }

  @ViewChildren(MatPaginator) paginator = new QueryList<MatPaginator>();
  selection = new SelectionModel<any>(true, []);
  displayedColumns1: string[] = ['first_name', 'last_name', 'email', 'contact', 'department_name', 'user_role', 'status', 'action'];
  displayedColumns2: string[] = ['first_name', 'last_name', 'email', 'contact', 'department_name', 'pro_member', 'status', 'action'];

  ngOnInit(): void {
    this.pagename = "Administration : Member Management";
    this.selectedTab = 0;
    this.internalUser = true;
    this.externalUser = false;
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.userName = this.userInfo.firstName;
  }

  ngAfterViewInit() {
    if (this.selectedTab == 0) {
      this.KEYWORD.setValue('');
      this.getInternalUsers();
    } else if (this.selectedTab == 1) {
      this.KEYWORD.setValue('');
      this.getExternalUsers();
    }
  }

  changePage(event) {
    this.startIndex = (event.pageIndex * this.pageSize) + 1;
    this.lastIndex = this.startIndex + (this.pageSize - 1);
    if (this.lastIndex > this.totalCount) {
      this.lastIndex = this.totalCount;
    }
    this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
    if (this.selectedTab == 0) {
      if (this.internalRespArray.length != 0) {
        this.getInternalUsers();
      }
      else if (this.internalFilter == true) {
        this.getInternalUserList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            if (res.data.length > 0) {
              this.internalRespArray = [];
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.internalPageNumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              // if (this.internalFilterArray.length == 0) {
              //   this.noData = true;
              // } else {
              //   this.noData = false;
              // }
              if (this.internalFilterArray.length != 0 && this.internalSortedArray.length == 0) {
                this.internalFilterArray = this.internalFilterArray.concat(res.data[0].DATA);
                this.IntUserDataStore.$intUserData = this.internalFilterArray;
              }
              else if (this.internalFilterArray.length == 0 && this.internalSortedArray.length != 0) {
                this.internalSortedArray = this.internalSortedArray.concat(res.data[0].DATA);
                this.IntUserDataStore.$intUserData = this.internalSortedArray;
              }
              this.dataSource1.data = this.IntUserDataStore.$intUserData;
              this.dataSource1 = new MatTableDataSource<any>(this.IntUserDataStore.$intUserData);
              this.dataSource1.paginator = this.paginator.toArray()[0];
              this.dataSource1.data.forEach(element => {
                if (element['USER_CONFIRMED'] == 1) {
                  element['status'] = 'Confirmed';
                } else {
                  element['status'] = 'Unconfirmed';
                }
                if (element['USER_ENABLED'] == 1) {
                  element['actionVal'] = true;
                } else {
                  element['actionVal'] = false;
                }
              });
              this._intUserData.next(Object.assign({}, this.IntUserDataStore).$intUserData);
            }
          }, err => {
            this.loading = false;
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
      }
      else {
        this.getInternalUserList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            if (res.data.length > 0) {
              this.internalRespArray = [];
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.internalPageNumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              this.internalSortedArray = this.internalSortedArray.concat(res.data[0].DATA);
              this.IntUserDataStore.$intUserData = this.internalSortedArray;
              this.dataSource1.data = this.IntUserDataStore.$intUserData;
              this.dataSource1 = new MatTableDataSource<any>(this.IntUserDataStore.$intUserData);
              this.dataSource1.paginator = this.paginator.toArray()[0];
              this.dataSource1.data.forEach(element => {
                if (element['USER_CONFIRMED'] == 1) {
                  element['status'] = 'Confirmed';
                } else {
                  element['status'] = 'Unconfirmed';
                }
                if (element['USER_ENABLED'] == 1) {
                  element['actionVal'] = true;
                } else {
                  element['actionVal'] = false;
                }
              });
              this._intUserData.next(Object.assign({}, this.IntUserDataStore).$intUserData);
            }
          }, err => {
            this.loading = false;
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
      }
    }
    else if (this.selectedTab == 1) {
      if (this.externalRespArray.length != 0) {
        this.getExternalUsers();
      }
      else if (this.externalFilter == true) {
        this.getExternalUserList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            if (res.data.length > 0) {
              this.externalRespArray = [];
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.externalPageNumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              if (this.externalFilterArray.length != 0 && this.externalSortedArray.length == 0) {
                this.externalFilterArray = this.externalFilterArray.concat(res.data[0].DATA);
                this.ExtUserDataStore.$extUserData = this.externalFilterArray;
              }
              else if (this.externalFilterArray.length == 0 && this.externalSortedArray.length != 0) {
                this.externalSortedArray = this.externalSortedArray.concat(res.data[0].DATA);
                this.ExtUserDataStore.$extUserData = this.externalSortedArray;
              }
              this.dataSource2.data = this.ExtUserDataStore.$extUserData;
              this.dataSource2 = new MatTableDataSource<any>(this.ExtUserDataStore.$extUserData);
              this._extUserData.next(Object.assign({}, this.ExtUserDataStore).$extUserData);
              this.dataSource2.paginator = this.paginator.toArray()[1];
              this.dataSource2.data.forEach(element => {
                if (element['USER_CONFIRMED'] == 1 && element['COMPANY_ID'] != 0) {
                  element['status'] = 'Confirmed';
                } else if ((element['USER_CONFIRMED'] == 0 && element['COMPANY_ID'] != 0)) {
                  element['status'] = 'Unconfirmed';
                } else {
                  element['status'] = 'Not exist';
                }
                if (element['USER_ENABLED'] == 1) {
                  element['action'] = true;
                } else {
                  element['action'] = false;
                }
                if (element['MEMBER_ROLE_ID'] == 1) {
                  element['isAdmin'] = true;
                } else {
                  element['isAdmin'] = false;
                }
                if (element['REGISTERED'] == 1) {
                  element['pro_member'] = true;
                } else {
                  element['pro_member'] = false;
                }
              });
            }
          }, err => {
            this.loading = false;
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
      }
      else {
        this.getExternalUserList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            if (res.data.length > 0) {
              this.externalRespArray = [];
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.externalPageNumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              this.externalSortedArray = this.externalSortedArray.concat(res.data[0].DATA);
              this.ExtUserDataStore.$extUserData = this.externalSortedArray;
              this.dataSource2.data = this.ExtUserDataStore.$extUserData;
              this.dataSource2 = new MatTableDataSource<any>(this.ExtUserDataStore.$extUserData);
              this._extUserData.next(Object.assign({}, this.ExtUserDataStore).$extUserData);
              this.dataSource2.paginator = this.paginator.toArray()[1];
              this.dataSource2.data.forEach(element => {
                if (element['USER_CONFIRMED'] == 1 && element['COMPANY_ID'] != 0) {
                  element['status'] = 'Confirmed';
                } else if ((element['USER_CONFIRMED'] == 0 && element['COMPANY_ID'] != 0)) {
                  element['status'] = 'Unconfirmed';
                } else {
                  element['status'] = 'Not exist';
                }

                if (element['USER_ENABLED'] == 1) {
                  element['action'] = true;
                } else {
                  element['action'] = false;
                }
                if (element['MEMBER_ROLE_ID'] == 1) {
                  element['isAdmin'] = true;
                } else {
                  element['isAdmin'] = false;
                }
                if (element['REGISTERED'] == 1) {
                  element['pro_member'] = true;
                } else {
                  element['pro_member'] = false;
                }
              });
            }
          }, err => {
            this.loading = false;
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
      }
    }
  }

  private onFinalize(): void {
    this.request$ = null;
  }

  getInternalUserList(searchValue) {
    this.internalPageNumber++;
    this.limit = 20;
    this.request$ = this.authService.getInternalMembersList(this.internalPageNumber, searchValue, this.limit, this.sortingKey, this.sortingOrder);
    return this.request$;
  }

  getInternalUsers() {
    this.getInternalUserList(this.KEYWORD.value)
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((res: any) => {
        this.loading = false;
        if (res.data.length > 0) {
          this.totalCount = res.data[0].TOTAL_COUNT;
          if (this.internalPageNumber == 1) {
            if (this.pageSize > this.totalCount) {
              this.rangeLabel = 1 + ' - ' + this.totalCount;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.internalRespArray = this.internalRespArray.concat(res.data[0].DATA);
          if (this.internalRespArray.length == 0) {
            this.noData = true;
          } else {
            this.noData = false;
          }
          this.IntUserDataStore.$intUserData = this.internalRespArray;
          this.dataSource1 = new MatTableDataSource<any>(this.IntUserDataStore.$intUserData);
          this.dataSource1.paginator = this.paginator.toArray()[0];
          this._intUserData.next(Object.assign({}, this.IntUserDataStore).$intUserData);
          this.dataSource1.data.forEach(element => {
            if (element['USER_CONFIRMED'] == 1) {
              element['status'] = 'Confirmed';
            } else {
              element['status'] = 'Unconfirmed';
            }
            if (element['USER_ENABLED'] == 1) {
              element['actionVal'] = true;
            } else {
              element['actionVal'] = false;
            }
          });
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      })
  }

  getExternalUserList(searchVal) {
    this.limit = 20;
    this.externalPageNumber++;
    this.request$ = this.authService.getExternalMembersList(this.externalPageNumber, searchVal, this.limit, this.sortingKey, this.sortingOrder);
    return this.request$;
  }

  getExternalUsers() {
    this.getExternalUserList(this.KEYWORD.value)
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((res: any) => {
        this.loading = false;
        if (res.data.length > 0) {
          this.totalCount = res.data[0].TOTAL_COUNT;
          if (this.externalPageNumber == 1) {
            if (this.pageSize > this.totalCount) {
              this.rangeLabel = 1 + ' - ' + this.totalCount;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.externalRespArray = this.externalRespArray.concat(res.data[0].DATA);
          if (this.externalRespArray.length == 0) {
            this.noData = true;
          } else {
            this.noData = false;
          }
          this.ExtUserDataStore.$extUserData = this.externalRespArray;
          this.dataSource2.data = this.ExtUserDataStore.$extUserData;
          this.dataSource2 = new MatTableDataSource<any>(this.ExtUserDataStore.$extUserData);
          this._extUserData.next(Object.assign({}, this.ExtUserDataStore).$extUserData);
          this.dataSource2.paginator = this.paginator.toArray()[1];
          this.dataSource2.data.forEach(element => {
            if (element['USER_CONFIRMED'] == 1 && element['COMPANY_ID'] != 0) {
              element['status'] = 'Confirmed';
            } else if ((element['USER_CONFIRMED'] == 0 && element['COMPANY_ID'] != 0)) {
              element['status'] = 'Unconfirmed';
            } else {
              element['status'] = 'Not exist';
            }
            if (element['USER_ENABLED'] == 1) {
              element['action'] = true;
            } else {
              element['action'] = false;
            }
            if (element['MEMBER_ROLE_ID'] == 1) {
              element['isAdmin'] = true;
            } else {
              element['isAdmin'] = false;
            }
            if (element['REGISTERED'] == 1) {
              element['pro_member'] = true;
            } else {
              element['pro_member'] = false;
            }
          });
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      })
  }
  

  rejectUser(row, key) {
    this.confirmationMsg.title = 'Are you sure you want to reject the user ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: row.C_ID, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.authService
          .deleteUser(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
              if (key == "Internal") {
                this.IntUserDataStore.$intUserData.forEach((t: any, i) => {
                  if (t.ID === row.ID) {
                    this.IntUserDataStore.$intUserData.splice(i, 1);
                  }
                });
                this._intUserData.next(Object.assign({}, this.IntUserDataStore).$intUserData);
                this.$intUserData.subscribe((res) => {
                  this.internalRespArray = res;
                  this.dataSource1 = new MatTableDataSource(res);
                  this.dataSource1.paginator = this.paginator.toArray()[0];
                })
                this.totalCount = this.totalCount - 1;
                if (this.totalCount !== 0) {
                  if (this.pageSize > this.totalCount) {
                    this.pageSize = this.totalCount;
                    this.rangeLabel = 1 + ' - ' + this.pageSize;
                  }
                  if (this.lastIndex > this.totalCount) {
                    this.lastIndex = this.totalCount;
                    this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                  }
                }
                else {
                  this.noData = true;
                }
              }
              else if (key == "External") {
                this.ExtUserDataStore.$extUserData.forEach((t: any, i) => {
                  if (t.ID === row.ID) {
                    this.ExtUserDataStore.$extUserData.splice(i, 1);
                  }
                });
                this._extUserData.next(Object.assign({}, this.ExtUserDataStore).$extUserData);
                this.$extUserData.subscribe((res) => {
                  this.externalRespArray = res;
                  this.dataSource2 = new MatTableDataSource(res);
                  this.dataSource2.paginator = this.paginator.toArray()[1];
                })
                this.totalCount = this.totalCount - 1;
                if (this.pageSize > this.totalCount) {
                  this.pageSize = this.totalCount;
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
                if (this.lastIndex > this.totalCount) {
                  this.lastIndex = this.totalCount;
                  this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                }
              }
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  onValChange(value, id, C_ID) {
    let action = 'enable';
    if (value.checked == false) {
      action = 'disable';
    }
    this.authService.enableDisableUser(C_ID, action).subscribe(res => {
      if (this.selectedTab == 0) {
        this.getInternalUserById(id);
      } else if (this.selectedTab == 1) {
        this.getExternalUserById(id);
      }
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "warning"),
        err.error.status
      )
      // value.checked = !value.checked;
    })
  }

  onValAdminChange(value, model, index) {
    let val = 1
    if (value.checked == false) {
      val = 2;
      this.isAdmin = false;
    } else {
      this.isAdmin = true;
    }
    // this.adminIndex = index;
    let obj = {
      first_name: model['FIRST_NAME'],
      last_name: model['LAST_NAME'],
      pin: model['PINCODE'],
      address: model['ADDRESS'],
      salutation: model['SALUTATION_ID'],
    }
    this.authService.updateAdvertiser(obj, model['ID'], val).subscribe(
      (res) => {
        this.getExternalUserById(model['ID']);
        this.notify.showNotification(
          res.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status] : "success"),
          res.status
        )
      },
      (err) => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      }
    );
  }

  editUser(row) {
    const dialogRef = this.dialog.open(AdmindialogComponent, {
      width: '610px',
      data: row,
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'u-refresh') {
          this.getInternalUserById(row.ID);
          dialogRef.close('refresh');
        }
        if (data === 'd-refresh') {
          this.removeUserFromStore(row.ID)
          dialogRef.close('refresh');
        }
      }
    );
  }

  removeUserFromStore(userId) {
    this.IntUserDataStore.$intUserData.forEach((t: any, i) => {
      if (t.ID === userId) {
        this.IntUserDataStore.$intUserData.splice(i, 1);
      }
    });
    this.totalCount = this.totalCount - 1;
    if (this.totalCount !== 0) {
      if (this.pageSize > this.totalCount) {
        this.pageSize = this.totalCount;
        this.rangeLabel = 1 + ' - ' + this.pageSize;
      }
      if (this.lastIndex > this.totalCount) {
        this.lastIndex = this.totalCount;
        this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
      }
    } else {
      this.noData = true;
    }
    this._intUserData.next(Object.assign({}, this.IntUserDataStore).$intUserData);
    this.dataSource1 = new MatTableDataSource(this.IntUserDataStore.$intUserData);
    this.dataSource1.paginator = this.paginator.toArray()[0];
  }


  getInternalUserById(userId) {
    this.authService.getInternalMemberById(userId).subscribe(res => {
      let userObj = res.data[0];
      this.IntUserDataStore.$intUserData.forEach((t: any, i) => {
        if (t.ID === userObj.ID) {
          this.IntUserDataStore.$intUserData[i] = userObj;
        }
      });
      this._intUserData.next(Object.assign({}, this.IntUserDataStore).$intUserData);
      this.updateInternalUsersTableData(userObj.ID);
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }


  updateInternalUsersTableData(userId) {
    this.$intUserData.subscribe((res) => {
      this.internalRespArray = res;
      this.dataSource1.data = res;
      this.dataSource1 = new MatTableDataSource(res);
      this.dataSource1.paginator = this.paginator.toArray()[0];
      this.dataSource1.data.forEach(element => {
        if (userId == element.ID) {
          if (element['USER_CONFIRMED'] == 1) {
            element['status'] = 'Confirmed';
          } else {
            element['status'] = 'Unconfirmed';
          }
          if (element['USER_ENABLED'] == 1) {
            element['actionVal'] = true;
          } else {
            element['actionVal'] = false;
          }
        }
      });
    })
  }

  editAdvertiser(row) {
    const dialogRef = this.dialog.open(EditAdvertiserComponent, {
      width: '610px',
      data: { isEditByCompanyAdmin: false, row },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'u-refresh') {
          this.getExternalUserById(row.ID);
          dialogRef.close('refresh');
        }
        if (data === 'd-refresh') {
          this.removeAdvertiserFromStore(row.ID);
          dialogRef.close('refresh');
        }
      }
    );
  }

  createCompany(row) {
    const dialogRef = this.dialog.open(CreateCompanyComponent, {
      width: '610px',
      data: { row },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'refreshed') {
          this.updateExternalUsersTableDate(row.ID);
          dialogRef.close('refresh');
        }
      }
    );
  }

  getExternalUserById(userId) {
    this.authService.getExternalMemberById(userId).subscribe(res => {
      let userObj = res.data[0];
      this.ExtUserDataStore.$extUserData.forEach((t: any, i) => {
        if (t.ID === userObj.ID) {
          this.ExtUserDataStore.$extUserData[i] = userObj;
        }
      });
      this._extUserData.next(Object.assign({}, this.ExtUserDataStore).$extUserData);
      this.updateExternalUsersTableDate(userObj.ID);
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  updateExternalUsersTableDate(userId) {
    this.$extUserData.subscribe((res) => {
      this.externalRespArray = res;
      this.dataSource2.data = res;
      this.dataSource2 = new MatTableDataSource(res);
      this.dataSource2.paginator = this.paginator.toArray()[1];
      this.dataSource2.data.forEach(element => {
        if (userId == element.ID) {
          if (element['USER_ENABLED'] == 1) {
            element['action'] = true;
          } else {
            element['action'] = false;
          }
          if (element['USER_CONFIRMED'] == 1 && element['COMPANY_ID'] != 0) {
            element['status'] = 'Confirmed';
          } else if ((element['USER_CONFIRMED'] == 0 && element['COMPANY_ID'] != 0)) {
            element['status'] = 'Unconfirmed';
          } else {
            element['status'] = 'Not exist';
          }
          if (element['MEMBER_ROLE_ID'] == 1) {
            element['isAdmin'] = true;
          } else {
            element['isAdmin'] = false;
          }
        }
      });
    })
  }

  addUser() {
    const dialogRef = this.dialog.open(AddUserComponent, {
      width: '610px',
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'refresh') {
          this.internalPageNumber = 0;
          this.internalRespArray = [];
          this.dataSource1.data = '';
          this.rangeLabel = 1 + ' - ' + this.pageSize;
          this.getInternalUsers();
        }
      }
    );
  }


  addAdvertiser() {
    const dialogRef = this.dialog.open(AddAdvertiserComponent, {
      width: '610px',
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'refresh') {
          this.externalPageNumber = 0;
          this.externalRespArray = [];
          this.dataSource2.data = '';
          this.rangeLabel = 1 + ' - ' + this.pageSize;
          this.getExternalUsers();
        }
      }
    );
  }

  removeAdvertiserFromStore(compId) {
    this.ExtUserDataStore.$extUserData.forEach((t: any, i) => {
      if (t.ID === compId) {
        this.ExtUserDataStore.$extUserData.splice(i, 1);
      }
    });
    this.totalCount = this.totalCount - 1;
    if (this.totalCount !== 0) {
      if (this.pageSize > this.totalCount) {
        this.pageSize = this.totalCount;
        this.rangeLabel = 1 + ' - ' + this.pageSize;
      }
      if (this.lastIndex > this.totalCount) {
        this.lastIndex = this.totalCount;
        this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
      }
    } else {
      this.noData = true;
    }
    this._extUserData.next(Object.assign({}, this.ExtUserDataStore).$extUserData);
    this.dataSource2 = new MatTableDataSource(this.ExtUserDataStore.$extUserData);
    this.dataSource2.paginator = this.paginator.toArray()[1];
  }

  applyFilter(filterValue: string) {
    if (this.selectedTab == 0) {
      this.startIndex = null;
      this.lastIndex = null;
      this.pageSize = 10;
      this.totalCount = null;
      if (filterValue.length == 0) {
        this.internalPageNumber = 0;
        this.internalFilterArray = [];
        this.dataSource1.data = '';
        this.noData = false;
        this.KEYWORD.setValue('');
        this.internalFilter = false;
        this.getInternalUsers();
      } else {
        this.internalFilter = true;
        this.internalRespArray = [];
        this.internalPageNumber = 0;
        this.noData = false;
        this.KEYWORD.setValue(filterValue);
        this.filterInternalMembers();
      }
    }
    else if (this.selectedTab == 1) {
      this.startIndex = null;
      this.lastIndex = null;
      this.pageSize = 10;
      this.totalCount = null;
      if (filterValue.length == 0) {
        this.externalPageNumber = 0;
        this.noData = false;
        this.externalFilterArray = [];
        this.dataSource2.data = '';
        this.KEYWORD.setValue('');
        this.externalFilter = false;
        this.getExternalUsers();
      } else {
        this.externalFilter = true;
        this.externalRespArray = [];
        this.externalPageNumber = 0;
        this.noData = false;
        this.KEYWORD.setValue(filterValue)
        this.filterExternalMembers();
      }
    }
  }

  filterInternalMembers() {
    this.noData = false;
    this.dataSource1.data = '';
    this.getInternalUserList(this.KEYWORD.value)
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((res: any) => {
        this.internalRespArray = [];
        this.internalSortedArray = [];
        this.internalFilterArray = [];
        if (res.data.length > 0) {
          this.totalCount = res.data[0].TOTAL_COUNT;
          if (this.internalPageNumber == 1) {
            if (res.data[0].TOTAL_COUNT < this.pageSize) {
              this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.noData = false;
          this.internalFilterArray = this.internalFilterArray.concat(res.data[0].DATA);
          this.IntUserDataStore.$intUserData = this.internalFilterArray;
          this.dataSource1.data = this.IntUserDataStore.$intUserData;
          this.dataSource1 = new MatTableDataSource<any>(this.IntUserDataStore.$intUserData);
          this.dataSource1.paginator = this.paginator.toArray()[0];
          this._intUserData.next(Object.assign({}, this.IntUserDataStore).$intUserData);
          this.dataSource1.data.forEach(element => {
            if (element['USER_CONFIRMED'] == 1) {
              element['status'] = 'Confirmed';
            } else {
              element['status'] = 'Unconfirmed';
            }
            if (element['USER_ENABLED'] == 1) {
              element['actionVal'] = true;
            } else {
              element['actionVal'] = false;
            }
          });
        } else {
          this.noData = true;
          this.internalFilterArray = [];
          this.dataSource1.data = '';
        }
      })
  }

  filterExternalMembers() {
    this.noData = false;
    this.dataSource2.data = '';
    this.getExternalUserList(this.KEYWORD.value)
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((res: any) => {
        this.externalRespArray = [];
        this.externalSortedArray = [];
        this.externalFilterArray = [];
        if (res.data.length > 0) {
          this.totalCount = res.data[0].TOTAL_COUNT;
          if (this.externalPageNumber == 1) {
            if (res.data[0].TOTAL_COUNT < this.pageSize) {
              this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.noData = false;
          this.externalFilterArray = this.externalFilterArray.concat(res.data[0].DATA);
          this.ExtUserDataStore.$extUserData = this.externalFilterArray;
          this.dataSource2.data = this.ExtUserDataStore.$extUserData;
          this.dataSource2 = new MatTableDataSource<any>(this.ExtUserDataStore.$extUserData);
          this._extUserData.next(Object.assign({}, this.ExtUserDataStore).$extUserData);
          this.dataSource2.paginator = this.paginator.toArray()[1];
          this.dataSource2.data.forEach(element => {
            if (element['USER_CONFIRMED'] == 1 && element['COMPANY_ID'] != 0) {
              element['status'] = 'Confirmed';
            } else if ((element['USER_CONFIRMED'] == 0 && element['COMPANY_ID'] != 0)) {
              element['status'] = 'Unconfirmed';
            } else {
              element['status'] = 'Not exist';
            }
            if (element['USER_ENABLED'] == 1) {
              element['action'] = true;
            } else {
              element['action'] = false;
            }
            if (element['MEMBER_ROLE_ID'] == 1) {
              element['isAdmin'] = true;
            } else {
              element['isAdmin'] = false;
            }
            if (element['REGISTERED'] == 1) {
              element['pro_member'] = true;
            } else {
              element['pro_member'] = false;
            }
          });
        } else {
          this.noData = true;
          this.externalFilterArray = [];
          this.dataSource2.data = '';
        }
      })
  }

  approve(row, key) {
    this.confirmationMsg.title = 'Are you sure you want to approve the user ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: row.C_ID, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.authService
          .approveUser(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
              if (key == "Internal") {
                this.getInternalUserById(row.ID);
              }
              else if (key == "External") {
                this.getExternalUserById(row.ID);
              }
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  onTabChanged(event) {
    this.selectedTab = event.index;
    if (this.selectedTab == 0) {
      this.rangeLabel = '';
      this.totalCount = '';
      this.internalRespArray = [];
      this.internalSortedArray = [];
      this.internalUser = true;
      this.externalUser = false;
      this.externalFilter = false;
      this.loading = true;
      this.KEYWORD.setValue('');
      this.internalPageNumber = 0;
      this.dataSource1.data = '';
      this.dataSource1 = new MatTableDataSource(this.dataSource1.data);
      this.dataSource1.paginator = this.paginator.toArray()[0];
      this.sortingKey = '';
      this.sortingOrder = '';
      this.fname_asc = false;
      this.fname_desc = false;
      this.lname_asc = false;
      this.lname_desc = false;
      this.email_asc = false;
      this.email_desc = false;
      this.dept_asc = false;
      this.dept_desc = false;
      this.role_asc = false;
      this.role_desc = false;
      this.status_asc = false;
      this.status_desc = false;
      this.getInternalUsers();
    }
    else if (this.selectedTab == 1) {
      this.rangeLabel = '';
      this.internalFilter = false;
      this.totalCount = '';
      this.KEYWORD.setValue('');
      this.externalRespArray = [];
      this.externalSortedArray = [];
      this.internalUser = false;
      this.externalUser = true;
      this.loading = true;
      this.externalPageNumber = 0;
      this.dataSource2.data = '';
      this.dataSource2 = new MatTableDataSource(this.dataSource2.data);
      this.dataSource2.paginator = this.paginator.toArray()[1];
      this.sortingKey = '';
      this.sortingOrder = '';
      this.fname_asc = false;
      this.fname_desc = false;
      this.lname_asc = false;
      this.lname_desc = false;
      this.email_asc = false;
      this.email_desc = false;
      this.extcompany_asc = false;
      this.extcompany_desc = false;
      this.status_asc = false;
      this.status_desc = false;
      this.getExternalUsers();
    }
  }

  sortInternalComplaints(key, order) {
    this.sortingKey = key;
    this.sortingOrder = order;
    this.internalPageNumber = 0;
    if (order != '') {
      if (order == 'ASC') {
        if (key == 'FIRST_NAME') {
          this.fname_asc = true;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'LAST_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = true;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'EMAIL_ID') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = true;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'DEPARTMENT_TYPE_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = true;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'ROLE_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = true;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'USER_CONFIRMED') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = true;
          this.status_desc = false;
        }
      }
      else if (order == 'DESC') {
        if (key == 'FIRST_NAME') {
          this.fname_asc = false;
          this.fname_desc = true;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'LAST_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = true;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'EMAIL_ID') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = true;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'DEPARTMENT_TYPE_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = true;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'ROLE_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = true;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'USER_CONFIRMED') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.dept_asc = false;
          this.dept_desc = false;
          this.role_asc = false;
          this.role_desc = false;
          this.status_asc = false;
          this.status_desc = true;
        }
      }
      this.noData = false;
      this.dataSource1.data = '';
      if (this.internalFilter == true) {
        this.filterInternalMembers();
      }
      else {
        this.noData = false;
        this.dataSource1.data = '';
        this.getInternalUserList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            this.internalRespArray = [];
            this.internalFilterArray = [];
            this.internalSortedArray = [];
            if (res.data.length > 0) {
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.internalPageNumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              this.noData = false;
              this.internalSortedArray = this.internalSortedArray.concat(res.data[0].DATA);
              this.IntUserDataStore.$intUserData = this.internalSortedArray;
              this.dataSource1.data = this.IntUserDataStore.$intUserData;
              this.dataSource1 = new MatTableDataSource<any>(this.IntUserDataStore.$intUserData);
              this.dataSource1.paginator = this.paginator.toArray()[0];
              this._intUserData.next(Object.assign({}, this.IntUserDataStore).$intUserData);
              this.dataSource1.data.forEach(element => {
                if (element['USER_CONFIRMED'] == 1) {
                  element['status'] = 'Confirmed';
                } else {
                  element['status'] = 'Unconfirmed';
                }
                if (element['USER_ENABLED'] == 1) {
                  element['actionVal'] = true;
                } else {
                  element['actionVal'] = false;
                }
              });
            }
          })
      }
    }
    else if (order == '') {
      this.fname_asc = false;
      this.fname_desc = false;
      this.lname_asc = false;
      this.lname_desc = false;
      this.email_asc = false;
      this.email_desc = false;
      this.dept_asc = false;
      this.dept_desc = false;
      this.role_asc = false;
      this.role_desc = false;
      this.status_asc = false;
      this.status_desc = false;
      if (this.internalFilter == true) {
        this.filterInternalMembers();
      }
      else {
        this.dataSource1.data = [];
        this.internalRespArray = [];
        this.noData = false;
        this.internalPageNumber = 0;
        this.getInternalUsers();
      }
    }
  }

  sortExternalComplaints(key, order) {
    this.sortingKey = key;
    this.sortingOrder = order;
    this.externalPageNumber = 0;
    if (order != '') {
      if (order == 'ASC') {
        if (key == 'FIRST_NAME') {
          this.fname_asc = true;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.extcompany_asc = false;
          this.extcompany_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'LAST_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = true;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.extcompany_asc = false;
          this.extcompany_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'EMAIL_ID') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = true;
          this.email_desc = false;
          this.extcompany_asc = false;
          this.extcompany_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'COMPANY_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.extcompany_asc = true;
          this.extcompany_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'USER_CONFIRMED') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.extcompany_asc = false;
          this.extcompany_desc = false;
          this.status_asc = true;
          this.status_desc = false;
        }
      }
      else if (order == 'DESC') {
        if (key == 'FIRST_NAME') {
          this.fname_asc = false;
          this.fname_desc = true;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.extcompany_asc = false;
          this.extcompany_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'LAST_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = true;
          this.email_asc = false;
          this.email_desc = false;
          this.extcompany_asc = false;
          this.extcompany_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'EMAIL_ID') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = true;
          this.extcompany_asc = false;
          this.extcompany_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'COMPANY_NAME') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.extcompany_asc = false;
          this.extcompany_desc = true;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'USER_CONFIRMED') {
          this.fname_asc = false;
          this.fname_desc = false;
          this.lname_asc = false;
          this.lname_desc = false;
          this.email_asc = false;
          this.email_desc = false;
          this.extcompany_asc = false;
          this.extcompany_desc = false;
          this.status_asc = false;
          this.status_desc = true;
        }
      }
      this.dataSource2.data = '';
      this.noData = false;
      if (this.externalFilter == true) {
        this.filterExternalMembers();
      }
      else {
        this.noData = false;
        this.getExternalUserList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            this.externalRespArray = [];
            this.externalFilterArray = [];
            this.externalSortedArray = [];
            if (res.data.length > 0) {
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.externalPageNumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              this.noData = false;
              this.externalSortedArray = this.externalSortedArray.concat(res.data[0].DATA);
              this.ExtUserDataStore.$extUserData = this.externalSortedArray;
              this.dataSource2.data = this.ExtUserDataStore.$extUserData;
              this.dataSource2 = new MatTableDataSource<any>(this.ExtUserDataStore.$extUserData);
              this._extUserData.next(Object.assign({}, this.ExtUserDataStore).$extUserData);
              this.dataSource2.paginator = this.paginator.toArray()[1];
              this.dataSource2.data.forEach(element => {
                if (element['USER_CONFIRMED'] == 1 && element['COMPANY_ID'] != 0) {
                  element['status'] = 'Confirmed';
                } else if ((element['USER_CONFIRMED'] == 0 && element['COMPANY_ID'] != 0)) {
                  element['status'] = 'Unconfirmed';
                } else {
                  element['status'] = 'Not exist';
                }
                if (element['USER_ENABLED'] == 1) {
                  element['action'] = true;
                } else {
                  element['action'] = false;
                }
                if (element['MEMBER_ROLE_ID'] == 1) {
                  element['isAdmin'] = true;
                } else {
                  element['isAdmin'] = false;
                }
                if (element['REGISTERED'] == 1) {
                  element['pro_member'] = true;
                } else {
                  element['pro_member'] = false;
                }
              });
            }
          })
      }
    }
    else if (order == '') {
      this.fname_asc = false;
      this.fname_desc = false;
      this.lname_asc = false;
      this.lname_desc = false;
      this.email_asc = false;
      this.email_desc = false;
      this.extcompany_asc = false;
      this.extcompany_desc = false;
      this.status_asc = false;
      this.status_desc = false;
      if (this.externalFilter == true) {
        this.filterExternalMembers();
      }
      else {
        this.dataSource2.data = [];
        this.externalRespArray = [];
        this.noData = false;
        this.externalPageNumber = 0;
        this.getExternalUsers();
      }
    }
  }

}
