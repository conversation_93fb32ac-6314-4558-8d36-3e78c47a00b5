::ng-deep .mat-form-field-flex > .mat-form-field-infix { padding: 0.4em 0px !important;}
::ng-deep .mat-form-field-appearance-outline .mat-form-field-label { margin-top:-5px;margin-bottom: -5px; }
::ng-deep label.ng-star-inserted { transform: translateY(-0.59375em) scale(.75) !important; }

.document-contents {
    height: 410px;
}

.document-body {
    height: 400px;
}

.document-info-container {
    margin-bottom: 5px !important;
}

:host::ng-deep .mat-dialog-container {
    overflow-y: hidden !important;
}

.mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0em 139px 0em 2px;
}

.toolbar {
    height: 84px;
    padding-left: 15px;
    background-color: #F8F9F9;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #2F3941;
    margin-bottom: 2%;
}
.search-btn {
    color: solid rgba(47, 57, 65, 0.6);
    background: #F3F3F3;
    border: 1px solid rgba(47, 57, 65, 0.6);
}
    
.mat-expansion-panel-spacing {
    margin: 16px;
}
    
mat-expansion-panel {
    border: none;
    box-shadow: none;
}
    
.input-container{
    display: flex;
    flex-direction: column;
    gap:0px;
}
    
.outer-row-container{
    background: #F8F9F9;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
    margin-left: 20px;
    margin-right: 20px;
    /* margin-bottom: 3%; */
}
    
.inner-row-container{
    display: flex;
    flex-direction: row;
    gap:30px;
    margin-left: 0px;
}
    
.input-container{
    display: flex;
    flex-direction: column;
    gap:0px;
}

.text-container{
    height: 20px;
    margin-bottom: 0px;
    color: rgba(0, 0, 0, 0.6);
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    /* border: 1px solid palevioletred; */
}
    
.input-field{
    width:185px !important;
    border: white;
}

.file-img {
    padding-left: 20px;
    padding-top: 5px;
    background-color: rgba(0, 136, 203, 0.25);
    border-radius: 3px 0px 0px 3px;
    height: 28px;
}
    
.fileimge {
    position: relative;
    left: -10px;
    top: -3px;
}
    
.pdf-text {
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    line-height: 16px;
    color: rgba(0, 0, 0, 0.6);
    padding-top: 4px;
}
    
.dwnld-btn {
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    line-height: 16px;
    color: #0088CB;
    border: transparent;
    background-color: #F8F9F9;
}
    
.document-container {
    padding-top: 3%;
    padding-bottom: 2%;
    height: 330px !important;
    overflow-y: scroll;
    overflow-x: hidden;
}

.document-container::-webkit-scrollbar {
    display: none;
}

.document-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
    
:host ::ng-deep .extension-header > .mat-expansion-indicator:after {
    color: #0088CB;
}
      
mat-expansion {
    overflow-x: hidden !important;
    overflow-y: hidden !important;
    max-height: fit-content;
}

.attribute-container{
    padding: 10px 0px 0px;
}
  
.grey-text{
    color: #92A2B1;
    font-size: 14px;
    line-height: 19px;
}

.no-docs {
    color: rgb(160, 160, 156);
    margin-left: 10px;
    font-size: 15px;
}