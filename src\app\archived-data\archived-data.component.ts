import { ChangeDetector<PERSON><PERSON>, Component, HostListener, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { NotificationService } from '../services/notification.service';
import { colorObj } from '../shared/color-object';
import { ComplaintsService } from '../services/complaints.service';
import { MatDialog } from '@angular/material/dialog';
import { ArchivedDetailsComponent } from '../archived-details/archived-details.component';
import { DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';

@Component({
  selector: 'app-archived-data',
  templateUrl: './archived-data.component.html',
  styleUrls: ['./archived-data.component.scss']
})
export class ArchivedDataComponent implements OnInit {

  pagename: String;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }
  }
  archiveForm: FormGroup;
  userInfo: any;
  processes: any[];
  compStatus: any[];
  resolutionList: any[];
  complianceList: any[];
  compMedium: any[];
  stageList: any[];
  showFields: boolean = false;
  searchPageNumber = 1;
  archived_complaints: any = [];
  lastData: number;
  search_keyword = null;
  medium_id = null;
  status_id = null;
  resolution_id = null;
  compliance_id = null;
  stage_id = null;
  process_id = null;
  loading: boolean = false;
  zeroComplaints: boolean = false;

  constructor(
    private fb: FormBuilder,
    private cs: ComplaintsService,
    private notify: NotificationService,
    private cd: ChangeDetectorRef,
    public dialog: MatDialog,
    private sanitized: DomSanitizer,
    private router: Router
  ) {
    this.archiveForm = this.fb.group({
      keyword: new FormControl(''),
      comp_medium: new FormControl(''),
      comp_status: new FormControl(''),
      resolution: new FormControl(''),
      compliance: new FormControl(''),
      stage: new FormControl(''),
      process: new FormControl('')
    })
  }

  ngOnInit(): void {
    this.pagename = "Archive";
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }

    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.compMedium = JSON.parse(window.localStorage.getItem('complaintSource'));
    this.compStatus = JSON.parse(window.localStorage.getItem('complaintStatus'));
    this.resolutionList = JSON.parse(window.localStorage.getItem('resolutionStatus'));
    this.complianceList = JSON.parse(window.localStorage.getItem('complianceStatus'));
    this.stageList = JSON.parse(window.localStorage.getItem('stages'));
    this.processes = JSON.parse(window.localStorage.getItem('processes'));
    this.submitForm();
  }

  safeHTML(unsafe: string) {
    return this.sanitized.bypassSecurityTrustHtml(unsafe);
  }

  collapseForm() {
    this.showFields = !this.showFields;
  }

  submitForm() {
    this.showFields = false;
    this.loading = true;
    this.zeroComplaints = false;
    this.searchPageNumber = 1;
    if (!!this.archiveForm.value.keyword) {
      this.search_keyword = this.archiveForm.value.keyword;
    }
    else {
      this.search_keyword = null;
    }
    if (!!this.archiveForm.value.comp_medium) {
      this.medium_id = this.archiveForm.value.comp_medium;
    }
    if (!!this.archiveForm.value.comp_status) {
      this.status_id = this.archiveForm.value.comp_status;
    }
    if (!!this.archiveForm.value.resolution) {
      this.resolution_id = this.archiveForm.value.resolution;
    }
    if (!!this.archiveForm.value.compliance) {
      this.compliance_id = this.archiveForm.value.compliance;
    }
    if (!!this.archiveForm.value.stage) {
      this.stage_id = this.archiveForm.value.stage;
    }
    if (!!this.archiveForm.value.process) {
      this.process_id = this.archiveForm.value.process;
    }
    this.cs.getArchivedComplaints(this.searchPageNumber, this.search_keyword, this.medium_id, this.status_id, this.resolution_id, this.compliance_id, this.stage_id, this.process_id).subscribe(res => {
      this.archived_complaints = res.data;
      if (res.data.length == 0) {
        this.zeroComplaints = true;
      }
      this.lastData = res.data.length;
      this.loading = false;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
      this.loading = false;
    })
  }

  complaintDetails(ID) {
    const dialogRef = this.dialog.open(ArchivedDetailsComponent, {
      width: '1500px',
      height: '550px',
      data: { ID },
      disableClose: true
    });
  }

  onScrollDown() {
    this.searchPageNumber++;
    this.getNextSet();
  }

  getNextSet() {
    if (this.lastData == 10) {
      this.cs.getArchivedComplaints(this.searchPageNumber, this.search_keyword, this.medium_id, this.status_id, this.resolution_id, this.compliance_id, this.stage_id, this.process_id).subscribe(
        (res) => {
          this.loading = false;
          this.lastData = res.data.length;
          this.archived_complaints = this.archived_complaints.concat(
            res.data
          );
          this.cd.detectChanges();
        },
        (err) => {
          this.notify.showNotification(
            err.error.message,
            'top',
            !!colorObj[err.error.status]
              ? colorObj[err.error.status]
              : 'error',
            err.error.status
          );
        }
      );
    }
  }

  clearForm() {
    this.archiveForm.reset();
    this.search_keyword = null;
    this.medium_id = null;
    this.status_id = null;
    this.resolution_id = null;
    this.compliance_id = null;
    this.stage_id = null;
    this.process_id = null;
    this.archived_complaints = [];
    this.searchPageNumber = 1;
    this.zeroComplaints = false;
    this.submitForm();
  }

  getKeyword(val) {
    if (val && val.length > 0) {
    } else if (!this.archiveForm.value.comp_medium && !this.archiveForm.value.comp_status && !this.archiveForm.value.resolution && !this.archiveForm.value.compliance && !this.archiveForm.value.stage && !this.archiveForm.value.process) {
      this.archiveForm.reset();
      this.search_keyword = null;
      this.medium_id = null;
      this.status_id = null;
      this.resolution_id = null;
      this.compliance_id = null;
      this.stage_id = null;
      this.process_id = null;
      this.archived_complaints = [];
      this.searchPageNumber = 1;
      this.zeroComplaints = false;
      this.submitForm();
    }
  }

  moveToDashboard() {
    this.router.navigateByUrl("/home/<USER>")
  }

}