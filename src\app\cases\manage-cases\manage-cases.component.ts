import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, HostListener, Input, OnDestroy, OnInit, Pipe, QueryList, Renderer2, ViewChild, ViewChildren } from '@angular/core';
import { ComplaintsService } from '../../services/complaints.service';
import { FormBuilder, FormGroup, FormControl, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { FilterPopupComponent } from '../../filter-popup/filter-popup.component';
import { BehaviorSubject, Subscription } from 'rxjs';
import { NotificationService } from '../../services/notification.service';
import { colorObj } from '../../shared/color-object';
import { CdkVirtualScrollViewport, ScrollDispatcher } from '@angular/cdk/scrolling';
import { debounceTime, distinctUntilChanged, filter, first } from 'rxjs/operators';
import { AuthService } from 'src/app/services/auth.service';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { DomSanitizer } from '@angular/platform-browser';

export interface Search {
  id: string;
  name: string;
  type: string;
}

@Component({
  selector: 'app-manage-cases',
  templateUrl: './manage-cases.component.html',
  styleUrls: ['./manage-cases.component.css']
})
@Pipe({ name: 'safeHtml' })
export class ManageCasesComponent implements OnInit, AfterViewInit {
  @ViewChildren('lastListData', { read: ElementRef })
  lastListData: QueryList<ElementRef>;

  complaints: any[];
  FTCcomplaints: any[];
  public show: boolean = false;
  id;
  activeTab = 0;
  public visibility: boolean = false;
  public myLabel: any = '+  New Complaint';
  tab = { "id": null, "ftc": null };
  tabs = [];
  classification: any[];
  userInfo: any;
  userName: any;
  complaintStatusList: any[];
  complaintSource: any[];
  loading: boolean = true;
  listLoading: boolean = false;
  companyList: any[];
  msg: string;
  public searchFilter: any = '';
  searchKey;
  searchKeyword;
  company;
  company_count = [];
  duplicate = 0;
  public company_complaints: Array<any> = [];
  //   Filter Popover
  filterEventSubsciption: Subscription;
  filterContainer: boolean = false;
  displayContainer: boolean = true;
  searchContainer: boolean = true;
  removable = true;
  isSelected: boolean = true;
  complaint_count;
  FTC_complaint_count;
  filter_complaints: any = [];
  filter_ftc_complaints: any = [];
  searchText = '';
  filterClassificationId;
  filterClassificationName;
  filterBrandName;
  filterStatusId;
  filterStatusName;
  filterSimilarCaseID;
  search = [];
  submitted = false;
  isHide: boolean = false;
  lastData: number;
  caseId: string;
  ftc;
  selectedTab;
  activePage;
  @ViewChild(CdkVirtualScrollViewport) virtualScroll: CdkVirtualScrollViewport;
  searchPageNumber = 1;
  filterForm: FormGroup;
  searchForm: FormGroup;
  hiddenSearch: boolean = true;
  navigation: any;
  navState: any = {
    from: ''
  };
  filterSourceId: any;
  filterSourceName: any;
  registered: number = 1;
  isTabChanged: boolean = false;
  mobile: boolean;
  innerWidth: number;
  observer: any;
  selectedCaseIndex;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.mobile = true;
    } else {
      this.mobile = false;
    }
  }

  constructor(private cs: ComplaintsService,
    private route: ActivatedRoute,
    private router: Router,
    public dialog: MatDialog,
    private formBuilder: FormBuilder,
    private notify: NotificationService,
    private renderer: Renderer2,
    private scrollDispatcher: ScrollDispatcher,
    private cd: ChangeDetectorRef,
    private authService: AuthService,
    private http: HttpClient,
    private sanitized: DomSanitizer) {
    this.createFilterForm();
    this.createSearchForm();
    this.navigation = this.router.getCurrentNavigation();
  }

  safeHTML(unsafe: string) {
    return this.sanitized.bypassSecurityTrustHtml(unsafe);
  }

  interSectionObserver() {
    let options = {
      root: null,
      rootMargin: '0px',
      threshold: 0.5
    }

    this.observer = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting) {
        this.onScrollDown();
      }
    }, options);
  }

  async ngOnInit(): Promise<any> {
    this.interSectionObserver();
    if (window.innerWidth <= 1250) {
      this.mobile = true;
    } else {
      this.mobile = false;
    }
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.userName = this.userInfo.firstName;
    this.classification = JSON.parse(window.localStorage.getItem('classification'));
    this.complaintStatusList = JSON.parse(window.localStorage.getItem('complaintStatus'));
    this.complaintSource = JSON.parse(window.localStorage.getItem('complaintSource'));
    this.selectedTab = 0;
    await this.getComplaints(this.searchPageNumber, 0);
    this.getStep();
    this.activePage = "Manage";
    this.cs.setActivePage(this.activePage);
    this.filterForm.get('company')!.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0) {
          this.getCompanyList(value);
        }
      }, err => {
      })
  }

  ngOnChanges(): void {
    this.refreshComplaintList();
  }

  getStep() {
    this.cs.currentStep
      .pipe(first()).subscribe(step => {
        if (step === 'dashboard') {
          this.cs.currentComplaintStatus
            .pipe(first()).subscribe(status => {
              this.search = [];
              this.filter_complaints = [];
              this.filter_ftc_complaints = [];
              this.filterClassificationId = this.filterForm.value.tag;
              this.filterBrandName = this.filterForm.value.company;
              this.filterStatusName = status;
              this.filterSourceId = this.filterForm.value.source;
              this.searchKeyword = this.searchForm.value.searchKey
              this.classification.forEach(el => {
                if (el.ID == this.filterClassificationId) {
                  this.filterClassificationName = el.CLASSIFICATION_NAME
                }
              })

              this.complaintStatusList.forEach(el => {
                if (el.COMPLAINT_STATUS_NAME == this.filterStatusName) {
                  this.filterStatusId = el.ID
                }
              })

              this.complaintSource.forEach(el => {
                if (el.ID == this.filterSourceId) {
                  this.filterSourceName = el.COMPLAINT_SOURCE_NAME
                }
              })

              if (this.filterClassificationId != '' && this.filterClassificationId != null) {
                this.search.push({
                  "type": "classification",
                  "id": this.filterClassificationId,
                  "name": this.filterClassificationName
                });
              }

              if (this.searchKeyword != '' && this.searchKeyword != null) {
                this.search.push({
                  "type": "Key",
                  "id": this.searchKeyword,
                  "name": this.searchKeyword
                });
              }

              if (this.filterBrandName != '' && this.filterBrandName != null) {
                this.search.push({
                  "type": "brand",
                  "id": this.filterBrandName,
                  "name": this.filterBrandName
                });
              }

              if (this.filterStatusId != '' && this.filterStatusId != null) {
                this.search.push({
                  "type": "status",
                  "id": this.filterStatusId,
                  "name": this.filterStatusName
                });
              }
              if (this.filterSourceId != '' && this.filterSourceId != null) {
                this.search.push({
                  "type": "source",
                  "id": this.filterSourceId,
                  "name": this.filterSourceName
                });
              }
              if (this.selectedTab == 0) {
                this.complaint_count = Object.keys(this.complaints).length;
              }
              else if (this.selectedTab == 1) {
                this.FTC_complaint_count = Object.keys(this.FTCcomplaints).length;
              }
              this.filterForm.reset();
              this.searchForm.reset();
              if (this.search.length != 0) {
                this.searchPageNumber = 1;
                if (this.selectedTab == 0) {
                  this.cs.getComplaints(this.filterClassificationId, this.filterBrandName, this.filterStatusId, this.filterSourceId, this.filterSimilarCaseID,this.searchKeyword, this.searchPageNumber, 0, this.registered).subscribe(res => {
                    this.filter_complaints = res.data;
                    this.lastData = res.data.length;
                    if (this.filter_complaints.length > 0 && this.selectedTab == 0) {
                      this.id = this.filter_complaints[0].ID;
                      this.caseId = this.filter_complaints[0].CASE_ID;
                      this.ftc = this.filter_complaints[0].FTC;
                      this.clickEvent(this.id, this.caseId, this.ftc, 0);
                    }
                  }, err => {
                    this.notify.showNotification(
                      err.error.message,
                      "top",
                      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                      err.error.status
                    );
                  })
                }
                else if (this.selectedTab == 1) {
                  this.cs.getComplaints(this.filterClassificationId, this.filterBrandName, this.filterStatusId, this.filterSourceId,this.filterSimilarCaseID, this.searchKeyword, this.searchPageNumber, 1, this.registered).subscribe(res => {
                    this.filter_ftc_complaints = res.data;
                    this.lastData = res.data.length;
                    if (this.filter_ftc_complaints.length > 0 && this.selectedTab == 1) {
                      this.id = this.filter_ftc_complaints[0].ID;
                      this.caseId = this.filter_ftc_complaints[0].CASE_ID;
                      this.ftc = this.filter_ftc_complaints[0].FTC;
                      this.clickEvent(this.id, this.caseId, this.ftc, 0);
                    }
                  }, err => {
                    this.notify.showNotification(
                      err.error.message,
                      "top",
                      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                      err.error.status
                    );
                  })
                }
                this.hiddenSearch = true;
                this.filterContainer = true;
                this.displayContainer = false;
              }
            })
        }
        else if (step === 'archive-data') {
          this.getComplaints(this.searchPageNumber, 0);
        }
      })
  }

  async onTabChanges(event) {
    this.cd.detectChanges();
    if (this.filterContainer == true) {
      this.search = [];
      this.filterContainer = false;
      this.displayContainer = true;
      this.searchKeyword = null;
      this.searchContainer = true;
      this.searchPageNumber = 1;
      this.getComplaints(this.searchPageNumber, 0);
      this.getFTCComplaints(this.searchPageNumber, 1);
    }
    this.selectedTab = event.index;
    if (this.selectedTab == 0) {
      this.searchPageNumber = 1;
      this.getComplaints(this.searchPageNumber, 0);
    }
    else if (this.selectedTab == 1) {
      this.searchPageNumber = 1;
      await this.getFTCComplaints(this.searchPageNumber, 1);
    }
  }

  createFilterForm() {
    this.filterForm = this.formBuilder.group({
      tag: [''],
      company: [''],
      status: [''],
      source: [''],
      similar_case: [''],
    })
  }

  createSearchForm() {
    this.searchForm = this.formBuilder.group({
      searchKey: [''],
    })
  }

  getCompanyList(value) {
    this.authService.getCompanies(value).subscribe(res => {
      this.companyList = res.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  isFromNAMSRequest() {
    const state = this.navigation?.extras?.state as {
      from: string;
    };
    if (!!state) {
      if (state['from'] === 'NAMS' || state['from'] === 'INBOX') {
        this.activeTab = 1;
        this.navState = state;
      }
    }
  }

  ngAfterViewInit(): void {
    this.cd.detectChanges();
    this.lastListData.changes.subscribe((d) => {
      if (d.toArray().length > 1) {
        let data1 = d.toArray()[0].nativeElement; // ccc complaint
        let data2 = d.toArray()[1].nativeElement; // ftc complaint
        if (this.selectedTab == 0) {
          this.observer.observe(data1);
        } else {
          this.observer.observe(data2);
        }
      } else {
        if (d.last)
          this.observer.observe(d.last.nativeElement);
      }
    });
  }

  count: number = 0;
  incre() {
    this.count++;
    this.filterFunction(this.msg);
  }

  filterFunction(data) {
    this.duplicate++;
    this.filterBrandName = data;
    this.search = [];
    this.search.push(this.filterBrandName);

    this.complaint_count = Object.keys(this.complaints).length;
    for (let i = 0; i < this.complaint_count; i++) {
      if (this.complaints[i].company == this.filterBrandName) {
        this.filter_complaints.push(this.complaints[i]);
      }
    }
    this.FTC_complaint_count = Object.keys(this.FTCcomplaints).length;
    for (let i = 0; i < this.FTC_complaint_count; i++) {
      if (this.FTCcomplaints[i].company == this.filterBrandName) {
        this.filter_ftc_complaints.push(this.FTCcomplaints[i]);
      }
    }
    if (this.search.length != 0) {
      this.filterContainer = true;
      this.displayContainer = false;
    }
  }

  clearFilter() {
    this.searchPageNumber = 1;
    this.filterContainer = false;
    this.displayContainer = true;
    this.search = [];
    if (this.filterStatusName || this.filterStatusId || this.filterBrandName || this.filterClassificationId || this.filterClassificationName) {
      this.filterClassificationId = '';
      this.filterClassificationName = '';
      this.filterBrandName = '';
      this.filterStatusId = '';
      this.filterStatusName = '';
      if (this.selectedTab == 0) {
        this.getComplaints(this.searchPageNumber, 0);
      }
      else if (this.selectedTab == 1) {
        this.getFTCComplaints(this.searchPageNumber, 1);
      }
    }
    this.filterForm.reset();
  }

  clearSearchFilter() {
    this.search = [];
    this.searchKeyword = '';
    this.searchForm.reset();
    this.searchPageNumber = 1;
    if (this.selectedTab == 0) {
      this.getComplaints(this.searchPageNumber, 0);
    }
    else if (this.selectedTab == 1) {
      this.getFTCComplaints(this.searchPageNumber, 1);
    }
    this.hiddenSearch = true;
  }

  expandSearch() {
    setTimeout(() => {
      var elem = this.renderer.selectRootElement('#search');
      elem.focus();
    }, 1000);
    this.hiddenSearch = false;
  }

  onSubmit() {
    this.search = [];
    this.filter_complaints = [];
    this.filter_ftc_complaints = [];
    this.filterClassificationId = this.filterForm.value.tag;
    this.filterBrandName = this.filterForm.value.company;
    this.filterStatusId = this.filterForm.value.status;
    this.filterSourceId = this.filterForm.value.source;
    this.filterSimilarCaseID = this.filterForm.value.similar_case;
    this.searchKeyword = this.searchForm.value.searchKey
    this.classification.forEach(el => {
      if (el.ID == this.filterClassificationId) {
        this.filterClassificationName = el.CLASSIFICATION_NAME
      }
    })

    this.complaintStatusList.forEach(el => {
      if (el.ID == this.filterStatusId) {
        this.filterStatusName = el.COMPLAINT_STATUS_NAME
      }
    })

    this.complaintSource.forEach(el => {
      if (el.ID == this.filterSourceId) {
        this.filterSourceName = el.COMPLAINT_SOURCE_NAME
      }
    })

    if (this.filterClassificationId != '' && this.filterClassificationId != null) {
      this.search.push({
        "type": "classification",
        "id": this.filterClassificationId,
        "name": this.filterClassificationName
      });
    }

    if (this.searchKeyword != '' && this.searchKeyword != null) {
      this.search.push({
        "type": "Key",
        "id": this.searchKeyword,
        "name": this.searchKeyword
      });
    }

    if (this.filterBrandName != '' && this.filterBrandName != null) {
      this.search.push({
        "type": "brand",
        "id": this.filterBrandName,
        "name": this.filterBrandName
      });
    }

    if (this.filterSimilarCaseID != '' && this.filterSimilarCaseID != null) {
      this.search.push({
        "type": "similar_case",
        "id": this.filterSimilarCaseID,
        "name": this.filterSimilarCaseID
      });
    }
    if (this.filterStatusId != '' && this.filterStatusId != null) {
      this.search.push({
        "type": "status",
        "id": this.filterStatusId,
        "name": this.filterStatusName
      });
    }
    if (this.filterSourceId != '' && this.filterSourceId != null) {
      this.search.push({
        "type": "source",
        "id": this.filterSourceId,
        "name": this.filterSourceName
      });
    }
    if (this.selectedTab == 0) {
      this.complaint_count = Object.keys(this.complaints).length;
    }
    else if (this.selectedTab == 1) {
      this.FTC_complaint_count = Object.keys(this.FTCcomplaints).length;
    }
    // this.complaint_count = Object.keys(this.complaints).length;
    // this.FTC_complaint_count = Object.keys(this.FTCcomplaints).length;
    this.filterForm.reset();
    this.searchForm.reset();
    if (this.search.length != 0) {
      // this.searchContainer = false;
      this.searchPageNumber = 1;
      if (this.selectedTab == 0) {
        this.cs.getComplaints(this.filterClassificationId, this.filterBrandName, this.filterStatusId, this.filterSourceId, this.filterSimilarCaseID, this.searchKeyword, this.searchPageNumber, 0, this.registered).subscribe(res => {
          this.filter_complaints = res.data;
          this.lastData = res.data.length;
          if (this.filter_complaints.length > 0 && this.selectedTab == 0) {
            this.id = this.filter_complaints[0].ID;
            this.caseId = this.complaints[0].CASE_ID;
            this.ftc = this.complaints[0].FTC;
            this.clickEvent(this.id, this.caseId, this.ftc, 0);
          }
        }, err => {
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          );
        })
      }
      else if (this.selectedTab == 1) {
        this.cs.getComplaints(this.filterClassificationId, this.filterBrandName, this.filterStatusId, this.filterSourceId,this.filterSimilarCaseID, this.searchKeyword, this.searchPageNumber, 1, this.registered).subscribe(res => {
          this.filter_ftc_complaints = res.data;
          this.lastData = res.data.length;
          if (this.filter_ftc_complaints.length > 0 && this.selectedTab == 1) {
            this.id = this.filter_ftc_complaints[0].ID;
            this.caseId = this.complaints[0].CASE_ID;
            this.ftc = this.complaints[0].FTC;
            this.clickEvent(this.id, this.caseId, this.ftc, 0);
          }
        }, err => {
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          );
        })
      }
      this.hiddenSearch = true;
      this.filterContainer = true;
      this.displayContainer = false;
    }
  }

  remove(chipKey: Search): void {
    const index = this.search.indexOf(chipKey);
    if (index >= 0) {
      this.search.splice(index, 1);
    }
    if (chipKey.type == "classification") {
      this.filterClassificationId = null;
      this.filterClassificationName = '';
    } else if (chipKey.type == "brand") {
      this.filterBrandName = null;
    } else if (chipKey.type == "status") {
      this.filterStatusId = null;
      this.filterStatusName = '';
    } else if (chipKey.type == "Key") {
      this.searchKeyword = null;
    } else if (chipKey.type == "source") {
      this.filterSourceId = null;
      this.filterSourceName = '';
    } else if (chipKey.type == "similar_case") {
      this.filterSimilarCaseID = null;
    }
    if (this.search.length == 0) {
      this.filterContainer = false;
      this.displayContainer = true;
      this.searchContainer = true;
      this.searchPageNumber = 1;
      this.getComplaints(this.searchPageNumber, 0);
      this.getFTCComplaints(this.searchPageNumber, 1);
    }
    else {
      this.searchPageNumber = 1;
      this.filterRemaining(this.search);
    }
    this.duplicate = 2;
  }

  filterRemaining(data) {
    if (this.selectedTab == 0) {
      this.cs.getComplaints(this.filterClassificationId, this.filterBrandName, this.filterStatusId, this.filterSourceId, this.filterSimilarCaseID,this.searchKeyword, this.searchPageNumber, 0, this.registered).subscribe(res => {
        this.lastData = res.data.length;
        this.filter_complaints = res.data;
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
    else if (this.selectedTab == 1) {
      this.cs.getComplaints(this.filterClassificationId, this.filterBrandName, this.filterStatusId, this.filterSourceId, this.filterSimilarCaseID, this.searchKeyword, this.searchPageNumber, 1, this.registered).subscribe(res => {
        this.lastData = res.data.length;
        this.filter_ftc_complaints = res.data;
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
  }

  //hiding info box
  visible: boolean = false

  //onclick toggling both
  onclick() {
    // this.ReadMore = !this.ReadMore; //not equal to condition
    this.visible = !this.visible
  }

  openFilterDialog() {
    const dialogRef = this.dialog.open(FilterPopupComponent);
    dialogRef.afterClosed().subscribe(result => {
    });
  }

  onScrollDown() {
    this.searchPageNumber++;
    this.getNextSet(this.searchPageNumber);
  }

  getNextSet(pageNumber) {
    if (this.selectedTab == 0) {
      if (this.lastData == 10) {
        this.cs.getComplaints(this.filterClassificationId, this.filterBrandName, this.filterStatusId, this.filterSourceId, this.filterSimilarCaseID, this.searchKeyword, pageNumber, 0, this.registered).subscribe(res => {
          this.listLoading = false;
          this.lastData = res.data.length;
          if (this.filterContainer) {
            this.filter_complaints = this.filter_complaints.concat(res.data);
          } else {
            this.complaints = this.complaints.concat(res.data);
          }
          this.cd.detectChanges();
        }, err => {
          this.loading = false;
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        });
      }
    }
    else if (this.selectedTab == 1) {
      if (this.lastData == 10) {
        this.cs.getComplaints(this.filterClassificationId, this.filterBrandName, this.filterStatusId, this.filterSourceId,this.filterSimilarCaseID, this.searchKeyword, pageNumber, 1, this.registered).subscribe(res => {
          this.listLoading = false;
          this.lastData = res.data.length;
          if (this.filterContainer) {
            this.filter_ftc_complaints = this.filter_ftc_complaints.concat(res.data);
          } else {
            this.FTCcomplaints = this.FTCcomplaints.concat(res.data);
          }
          this.cd.detectChanges();
        }, err => {
          this.loading = false;
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        });
      }
    }
  }

  async getFTCComplaints(pageNumber, ftc) {
    await this.cs.getComplaints(this.filterClassificationId, this.filterBrandName, this.filterStatusId, this.filterSourceId, this.filterSimilarCaseID,this.searchKeyword, pageNumber, ftc, this.registered).toPromise()
      .then(res => {
        this.FTCcomplaints = res.data;
        this.lastData = res.data.length;
        if (this.FTCcomplaints.length > 0 && this.selectedTab == 1) {
          this.id = this.FTCcomplaints[0].ID;
          this.caseId = this.FTCcomplaints[0].CASE_ID;
          this.ftc = this.FTCcomplaints[0].FTC;
          this.handleComplaintsData();
          this.clickEvent(this.id, this.caseId, this.ftc, 0);
        }
        // this.isFromNAMSRequest();
        this.loading = false;
      }).catch((err) => {
        this.loading = false;
        this.handleError(err);
      })
  }

  async getComplaints(pageNumber, ftc) {
    await this.cs.getComplaints(this.filterClassificationId, this.filterBrandName, this.filterStatusId, this.filterSourceId,this.filterSimilarCaseID, this.searchKeyword, pageNumber, ftc, this.registered).toPromise()
      .then(res => {
        this.complaints = res.data;
        this.lastData = res.data.length;
        if (this.complaints.length > 0) {
          this.id = this.complaints[0].ID;
          this.caseId = this.complaints[0].CASE_ID;
          this.ftc = this.complaints[0].FTC;
          this.handleComplaintsData();
          this.clickEvent(this.id, this.caseId, this.ftc, 0);
        }
        this.isFromNAMSRequest();
        this.loading = false;
      }).catch((err) => {
        this.loading = false;
        this.handleError(err);
      })
  }

  handleError(err) {
    this.notify.showNotification(
      err.error.message,
      "top",
      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
      err.error.status
    )
  }

  handleComplaintsData() {
    // this.msg = this.cs.getMsg();
    // this.cs.getComplaint$.subscribe((data) => {
    //   this.company = data;
    // })
    this.company_count = this.cs.getMsg();
    // this.cs.getComplaintByCompany(this.msg).subscribe(data1 =>{
    //   this.company_complaints.push(data1);
    // } );
    this.company = this.company_count[0];
    this.duplicate = this.company_count[1];
    // if(this.duplicate == 0 && this.msg != undefined)
    // if(this.duplicate == 0 && this.search.length != 0)

    // if(this.msg != '' && this.msg != null)
    // if(this.msg != undefined && this.duplicate == 0)
    if (this.company != undefined && this.duplicate == 1) {
      this.filterFunction(this.company);
    }
    // if(this.duplicate == 1)
    // {
    //   this.filterContainer = true;
    //   this.displayContainer = false;
    // }
  }


  clickEvent(id, caseId, ftc, i) {
    this.selectedCaseIndex = i + 1;
    this.tab = { "id": id, "ftc": ftc };
    this.tabs = [];
    this.tabs.push(this.tab);
    this.activeTab = 0;
    this.id = id;
    this.caseId = caseId;
    this.cs.listComplaint(id);
    this.visibility = true;
  }

  // closeTab(index) {
  //   var tabsBeforeIndex = this.tabs.splice(0, index);
  //   var tabsAfterIndex = this.tabs.splice(index + 1);
  //   this.tabs = tabsBeforeIndex.concat(tabsAfterIndex);
  //   return this;
  // }

  getColor(status) {
    switch (status) {
      case 'New':
        return '#47d147';
      case 'On Hold':
        return '#00ccff';
      case 'In Progress':
        return '#e6e600';
      case 'Closed':
        return '#ff704d';
      case 'Resolved':
        return '#ff4da9';
    }
  }

  getBGC(status) {
    switch (status) {
      case 'New':
        return '#c6ffb3';
      case 'On Hold':
        return '#ccf2ff';
      case 'In Progress':
        return '#ffffcc';
      case 'Closed':
        return '#ffc2b3';
      case 'Resolved':
        return '#ffcce6';
    }
  }

  logout() {
    this.router.navigate(['/login-register']);
  }

  showTagIcon(chiptag) {
    if (chiptag.id == this.filterClassificationId) {
      return 'visible';
    }
    else
      return 'hidden';
  }

  showCompanyIcon(chiptag) {
    if (chiptag.name == this.filterBrandName) {
      return 'visible';
    }
    else
      return 'hidden';
  }

  showSimilarCaseIdIcon(chiptag) {
    if (chiptag.name == this.filterSimilarCaseID) {
      return 'visible';
    }
    else
      return 'hidden';
  }

  showStatusIcon(chiptag) {
    if (chiptag.id == this.filterStatusId) {
      return 'visible';
    }
    else
      return 'hidden';
  }

  showSourceIcon(chiptag) {
    if (chiptag.id == this.filterSourceId) {
      return 'visible';
    }
    else
      return 'hidden';
  }

  onTabChanged(event) {
    if (this.activeTab == 0) {
      this.isTabChanged = false;
    }
    else {
      this.isTabChanged = true;
    }

    if (event.tab.textLabel == '+  New Complaint') {
      this.myLabel = 'New complaint';
      this.cs.complaintRegisterData.subscribe(res => {
        res = [];
      })
    }
    else {
      this.myLabel = '+  New Complaint';
    }
  }

  refreshComplaintList() {
    this.searchPageNumber = 2;
  }

  updateComplaintDetail(data) {
    if (data['ftc']) {
      if (data['cond'] === 'update') {
        if (!this.filterContainer) {
          this.FTCcomplaints.find(v => v.ID == data['id']).COMPLAINT_STATUS_ID = data['statusId'];
          this.FTCcomplaints.find(v => v.ID == data['id']).COMPLAINT_STATUS_NAME = data['status'];
          this.FTCcomplaints.find(v => v.ID == data['id']).ASSIGNEE_USER_NAME = data['assignee'];
        } else {
          this.filter_ftc_complaints.find(v => v.ID == data['id']).COMPLAINT_STATUS_ID = data['statusId'];
          this.filter_ftc_complaints.find(v => v.ID == data['id']).COMPLAINT_STATUS_NAME = data['status'];
          this.filter_ftc_complaints.find(v => v.ID == data['id']).ASSIGNEE_USER_NAME = data['assignee'];
        }
      } else if (data['cond'] === 'delete') {
        if (!this.filterContainer) {
          let index = this.FTCcomplaints.findIndex(el => el.ID == data['id']);
          this.FTCcomplaints = this.FTCcomplaints.filter(el => {
            return el.ID !== data['id'];
          })
          let id = this.FTCcomplaints[index].ID;
          let caseId = this.FTCcomplaints[index].CASE_ID;
          let ftc = this.FTCcomplaints[index].FTC;
          this.clickEvent(id, caseId, ftc, 0);
        } else {
          let index = this.filter_ftc_complaints.findIndex(el => el.ID == data['id']);
          this.filter_ftc_complaints = this.filter_ftc_complaints.filter(el => {
            return el.ID !== data['id'];
          })
          let id = this.filter_ftc_complaints[index].ID;
          let caseId = this.filter_ftc_complaints[index].CASE_ID;
          let ftc = this.filter_ftc_complaints[index].FTC;
          this.clickEvent(id, caseId, ftc, 0);
        }
      }
    } else {
      if (data['cond'] === 'update') {
        if (!this.filterContainer) {
          this.complaints.find(v => v.ID == data['id']).COMPLAINT_STATUS_ID = data['statusId'];
          this.complaints.find(v => v.ID == data['id']).COMPLAINT_STATUS_NAME = data['status'];
          this.complaints.find(v => v.ID == data['id']).ASSIGNEE_USER_NAME = data['assignee'];
        } else {
          this.filter_complaints.find(v => v.ID == data['id']).COMPLAINT_STATUS_ID = data['statusId'];
          this.filter_complaints.find(v => v.ID == data['id']).COMPLAINT_STATUS_NAME = data['status'];
          this.filter_complaints.find(v => v.ID == data['id']).ASSIGNEE_USER_NAME = data['assignee'];
        }
      } else if (data['cond'] === 'delete') {
        if (!this.filterContainer) {
          let index = this.complaints.findIndex(el => el.ID == data['id']);
          this.complaints = this.complaints.filter(el => {
            return el.ID !== data['id'];
          })
          let id = this.complaints[index].ID;
          let caseId = this.complaints[index].CASE_ID;
          let ftc = this.complaints[index].FTC;
          this.clickEvent(id, caseId, ftc, 0);
        } else {
          let index = this.filter_complaints.findIndex(el => el.ID == data['id']);
          this.filter_complaints = this.filter_complaints.filter(el => {
            return el.ID !== data['id'];
          })
          let id = this.filter_complaints[index].ID;
          let caseId = this.filter_complaints[index].CASE_ID;
          let ftc = this.filter_complaints[index].FTC;
          this.clickEvent(id, caseId, ftc, 0);
        }
      }
    }
  }

  refreshComplaints(data) {
    if (this.displayContainer) {
      if (this.selectedTab == 0) {
        this.complaints.splice(this.complaints.findIndex(el => el.ID == data.id), 1);
        let complaintObj = this.complaints[0];
        this.clickEvent(complaintObj['ID'], complaintObj['CASE_ID'], complaintObj['FTC'], 0);
      } else if (this.selectedTab == 1) {
        this.FTCcomplaints.splice(this.FTCcomplaints.findIndex(el => el.ID == data.id), 1);
        let complaintObj = this.FTCcomplaints[0];
        this.clickEvent(complaintObj['ID'], complaintObj['CASE_ID'], complaintObj['FTC'], 0);
      }
    } else {
      if (this.selectedTab == 0) {
        this.filter_complaints.splice(this.filter_complaints.findIndex(el => el.ID == data.id), 1);
        let complaintObj = this.filter_complaints[0];
        this.clickEvent(complaintObj['ID'], complaintObj['CASE_ID'], complaintObj['FTC'], 0);
      } else if (this.selectedTab == 1) {
        this.filter_ftc_complaints.splice(this.filter_ftc_complaints.findIndex(el => el.ID == data.id), 1);
        let complaintObj = this.filter_ftc_complaints[0];
        this.clickEvent(complaintObj['ID'], complaintObj['CASE_ID'], complaintObj['FTC'], 0);
      }
    }
  }
}