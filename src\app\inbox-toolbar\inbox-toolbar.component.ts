import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-inbox-toolbar',
  templateUrl: './inbox-toolbar.component.html',
  styleUrls: ['./inbox-toolbar.component.css']
})
export class InboxToolbarComponent implements OnInit {

  isSearch: boolean = false;

  constructor(private router: Router) { }

  ngOnInit(): void {
  }

  search()
  {
    this.isSearch = !this.isSearch;
  }

  logout()
  {
    this.router.navigate(['/login-register']);
  }

}
