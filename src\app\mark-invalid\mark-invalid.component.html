<div fxLayout="column">
    <div fxLayout="row" style="padding-top: 23px;padding-left:18px;padding-right:18px;background: #F8F9F9;">
        <div style="font-size: 16px;font-weight:600">Mark as invalid complaint</div>
        <div fxFlex="43%"></div>
        <div class="close-div">
            <button mat-icon-button class="close-btn" mat-dialog-close>
                <mat-icon style="margin-bottom: 3px;">close</mat-icon>
            </button>
        </div>
    </div>
    <mat-divider></mat-divider>
    <div fxLayout="row" style="margin-top: 21px;">
        <div fxFlex="100%">
            <form [formGroup]="markIvalidForm">
                <mat-form-field appearance="outline" class="input-field">
                    <input matInput placeholder="Reason of being invalid" autocomplete="off"
                        formControlName="reason" required>
                    <mat-error
                        *ngIf="markIvalidForm.get('reason').touched && markIvalidForm.controls['reason'].errors?.required">
                        Reason is required
                    </mat-error>
                </mat-form-field><br>
                <mat-form-field appearance="outline" class="input-field">
                    <textarea matInput rows="11" placeholder="Please describe, how it can resolved.." autocomplete="off"
                        formControlName="solution" required></textarea>
                    <mat-error
                        *ngIf="markIvalidForm.get('solution').touched && markIvalidForm.controls['solution'].errors?.required">
                        Description is required
                    </mat-error>
                </mat-form-field>
                <span style="margin-left: 70%">
                    <button (click)="cancel()" class="cancel-btn" style="background: none !important;border: none; margin-right: 10px;"
                        [disabled]="markIvalidForm.invalid">Cancel</button>
                    <button class="theme-blue-button-admin"
                        style="border: none; padding-left: 8px; padding-right: 10px;"
                        (click)="onSubmit(markIvalidForm.value)" [disabled]="markIvalidForm.invalid"> Submit</button>
                </span>
            </form>
        </div>
    </div>
</div>