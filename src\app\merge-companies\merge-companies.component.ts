import { Component, ElementRef, HostListener, Inject, OnInit, ViewChild } from '@angular/core';
import { <PERSON><PERSON>rray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';
import { AfterViewInit, Renderer2, OnDestroy } from '@angular/core';
import { AddEditCompanyComponent } from '../auth/add-edit-company/add-edit-company.component';
import { ComplaintsService } from '../services/complaints.service';

@Component({
  selector: 'app-merge-companies',
  templateUrl: './merge-companies.component.html',
  styleUrls: ['./merge-companies.component.scss']
})
export class MergeCompaniesComponent implements OnInit {
  mergeCompanyForm: FormGroup;
  addCompany: boolean = true;
  viewDetails: boolean = false;
  confirmationMsg: any = {};
  mobilenopattern = /^((\\+91-?)|0)?[0-9]{10}$/;
  autoComplete: boolean = false;
  companyList: any[];
  companyId: number = 0;
  complaintId: number = 0;
  regFrom: string = 'company';
  mergeCompanyInfo: any[];
  selectedId: any;
  panelOpenState: boolean = false;
  show: boolean;
  selected: any;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private fb: FormBuilder,
    private renderer: Renderer2,
    private cs: ComplaintsService,
    private notify: NotificationService,
    private dialogRef: MatDialogRef<AddEditCompanyComponent>,
    public dialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public data: any) {
    this.mergeCompanyForm = this.fb.group({
      cname: ['', [Validators.required]],
      caddress: [''],
      contact_info: fb.array([]),
      about_company: [''],
    })
  }

  ngAfterViewInit() {
    setTimeout(() => {
      var elem = this.renderer.selectRootElement('#companyname');
      elem.focus();
    }, 1000);
  }

  ngOnInit(): void {
    this.cs.getMergeCompaniesInfo(this.data).subscribe(res => {
      this.mergeCompanyInfo = res.data;
      this.mergeCompanyInfo.forEach(value => {
        value.forEach(val => {
          val.CONTACT_INFO.forEach(data => {
            let index = (<FormArray>this.mergeCompanyForm.get('contact_info')).length;
            let fg = this.fb.group({
              EMAIL_ID: this.fb.control('', Validators.email),
              MOBILE: this.fb.control('', Validators.pattern(this.mobilenopattern)),
            });
            fg.get('EMAIL_ID').patchValue(data.EMAIL_ID);
            fg.get('MOBILE').patchValue(data.MOBILE);
            (<FormArray>this.mergeCompanyForm.get('contact_info')).push(fg);
          })
        })
      });
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    });
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  openPanel() {
    this.panelOpenState = !this.panelOpenState
  }

  expand(i, id) {
    this.selected = id;
    this.selectedId = i;
    this.viewDetails = true;
  }

  hide(i) {
    this.selectedId = i;
    this.viewDetails = false;
  }

  addContactInfo() {
    let index = (<FormArray>this.mergeCompanyForm.get('contact_info')).length;
    if (index < 3) {
      let fg = this.fb.group({
        EMAIL_ID: this.fb.control('', Validators.email),
        MOBILE: this.fb.control('', Validators.pattern(this.mobilenopattern))
      });
      (<FormArray>this.mergeCompanyForm.get('contact_info')).push(fg);
    }
  }

  removeContactInfo(index: number) {
    (<FormArray>this.mergeCompanyForm.get('contact_info')).removeAt(index);
  }

  getControls() {
    return (this.mergeCompanyForm.get('contact_info') as FormArray).controls;
  }

  merge(model) {
    let obj: Object;
    obj = {
      "MERGE_COMPANY_ID": this.data,
      "COMPANY_NAME": model.cname,
      "COMPANY_DESCRIPTION": model.about_company,
      "ADDRESS": model.caddress,
      "CONTACT_INFO": model.contact_info,
    }
    this.confirmationMsg.title = "Please confirm the entered details are correct since the details can't be reverted upon confirmation";
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      width: '57vh',
      data: { title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult.state == true) {
        this.cs.mergeCompanies(obj).subscribe(res => {
          this.notify.showNotification(
            res.message,
            "top",
            (!!colorObj[res.status] ? colorObj[res.status] : "success"),
            res.status
          )
          this.dialogRef.close('u-refresh');
          // location.reload();
        }, err => {
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        })
      }
    });
  }

}