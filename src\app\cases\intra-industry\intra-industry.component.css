::ng-deep .mat-form-field-flex>.mat-form-field-infix {
  padding: 0.4em 0px !important;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-label {
  margin-top: -5px;
  margin-bottom: -5px;
}

::ng-deep label.ng-star-inserted {
  transform: translateY(-0.59375em) scale(.75) !important;
}

.mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0em 139px 0em 2px;
}

.row-container {
  display: flex;
  flex-direction: row;
  margin-left: 19px;
}

.outer-btn-container {
  margin-left: 19px;
  margin-bottom: 10px;
  width: 100%;
  display: flex;
  flex-direction: row;
}

.outer-btn-container1 {
  margin-left: 19px;
  margin-bottom: 10px;
  width: 100%;
  display: flex;
  flex-direction: row;
}

.outer-row-container1 {
  background: #F8F9F9;
  border: 1px solid #CFD7DF;
  box-sizing: border-box;
  border-radius: 4px;
  width: 89%;
  /* margin-left: 20px; */
  /* margin-right: 20px; */
  padding-right: 20px;
  padding-top: 10px;
  margin-bottom: 3%;
}

.outer-row-container {
  background: #F8F9F9;
  border: 1px solid #CFD7DF;
  box-sizing: border-box;
  border-radius: 4px;
  width: 63vw;
  /* max-width: 100%; */
  /* margin-left: 20px; */
  /* margin-right: 20px; */
  padding-right: 20px;
  padding-top: 10px;
  margin-bottom: 3%;
  display: flex;
  flex-direction: column;
}

.inner-row-container {
  display: flex;
  flex-direction: row;
  gap: 30px;
  margin-left: 19px;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 0px;
  width: 100%;
  /* width: 300px; */
}

.text-container {
  margin-bottom: 0px;
}

.control-container {
  /* height: 40px; */
  margin-top: 0px;
}

.textarea-field {
  width: 63vw;
  height: 150px;
  max-width: 100%;
  box-sizing: border-box;
  border-radius: 4px;
  margin-bottom: 70px;
  color: #000000;
}

.textarea-field1 {
  width: 836px;
  height: 150px;
  max-width: 100%;
  box-sizing: border-box;
  border-radius: 4px;
}

[data-placeholder]:empty:before {
  content: attr(data-placeholder);
  color: rgba(0, 0, 0, 0.6);
}

.input-field {
  width: 98%;
}

.basic-input-field {
  width: 240px;
}

.claims-input-field {
  width: 254px !important;
}

.divider-container {
  width: 100%;
  margin: 10px 20px 30px 20px;
}

.upload-container {
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.upload-btn {
  background-color: #CFD7DF;
  color: rgba(0, 0, 0, 0.6);
  width: 160px;
  height: 45px;
  padding-top: 5px;
  margin-top: 5px;
  /* display: inline-block; */
}

.upload-container input[type=file] {
  font-size: 100px;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
}

#url-icon {
  font-size: small;
  margin-bottom: 10px;
  /* vertical-align: middle; */
}

.url-container-text {
  position: relative;
  top: -8px;
}

.btn-container {
  display: flex;
  flex-direction: row;
  gap: 10px;
  margin-left: 20px;
  margin-bottom: 3%;
}

.next-btn,
.submit-btn {
  width: 80px;
  border-radius: 15px;
  background-color: #0088CB;
  color: white;
}

.cancel-btn,
.back-btn {
  width: 80px;
  border-radius: 15px;
}

.comp-head {
  margin-left: 20px;
  color: #000000;
  font-style: normal;
  font-weight: 700;
  font-size: 13px;
  line-height: 16px;
  padding-top: 10px;
}

.comp-head-mandatory {
  color: red;
  margin-left: 20px;
  position: relative;
  top: 7px;
  font-style: normal;
  font-weight: normal;
  font-size: 11px;
  line-height: 13px;
}

.divider-container {
  width: 95%;
  margin: 10px 20px 30px 20px;
}

.step1-container::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.step1-container {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.step1-container {
  width: 100%;
  margin: 17px 0px;
  overflow-y: scroll;
  overflow-x: hidden;
}

.delete-red {
  background: #FFFFFF;
  border: 1px solid #CFD7DF;
  box-sizing: border-box;
  border-radius: 4px;
  height: 30px;
  margin-top: -1%;
}

.example-boundary {
  width: 63vw;
  height: 12vh;
  background: #FEFEFE;
  border: 1px dashed #CFD7DF;
  box-sizing: border-box;
  border-radius: 4px;
  padding: 2% 35% 2% 37%;
}

.example-boundary1 {
  width: 63vw;
  height: 12vh;
  background: #F8F9F9;
  border: 1px dashed #CFD7DF;
  box-sizing: border-box;
  border-radius: 4px;
  padding: 2% 3% 2% 3%;
  margin-bottom: 5%;
}

.add-source-button {
  background: #FFFFFF;
  color: #5A6F84;
  border: 1px solid #CFD7DF;
  box-sizing: border-box;
  border-radius: 12px;
  justify-content: center;
  margin-bottom: 10px;
}

*,
*:before,
*:after {
  box-sizing: border-box;
}

.tool-list {
  display: flex;
  list-style: none;
  padding: 0;
  overflow: hidden;
  margin-bottom: 0px;
}

.tool--btn {
  background: transparent;
  color: #8b8b8b;
  font-size: 10px;
  display: block;
  border: none;
  padding-top: .5rem;
  margin: .3rem;
}

.adv_docs {
  width: 823px; 
  background: #FFFFFF;
  border: 1px solid #CFD7DF;
  box-sizing: border-box;
  border-radius: 4px;
}

.objection-text {
  box-sizing: border-box;
  position: relative;
  color: #92A2B1;
  height: 150px !important;
  border-radius: 4px !important;
  background-position: 5%;
  justify-content: center !important;
  align-items: center !important;
  margin-bottom: 70px;
}

.link-text {
  color: #0088CB;
  text-decoration: underline;
  cursor: pointer;
}

#output::-webkit-scrollbar {
  display: none;
}

#output {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

#output {
  height: 100px;
  width: 100%;
  padding: 1rem;
  outline: none;
  overflow-y: scroll;
  overflow-x: hidden;
}

#progress-id {
  width: 102%;
  margin-left: 1.33px;
}

.progress-spinner {
  margin: 0 10px;
}

.adv-file-text {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 550px;
  margin: 0px;
  padding: 7px;
}