import { Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { Router } from '@angular/router';
import { DateAdapter } from 'angular-calendar';
import { debounceTime, distinctUntilChanged, first } from 'rxjs/operators';
import { AuthService } from 'src/app/services/auth.service';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { UploadService } from 'src/app/services/upload.service';
import { MY_FORMATS } from 'src/app/shared/calendarFormat';
import { colorObj } from 'src/app/shared/color-object';
import { UserDashboardComponent } from 'src/app/home/<USER>/user-dashboard.component';
import moment from 'moment';
import { QuillConfiguration } from 'src/app/model/quill-configuration';
import { EditorChangeContent, EditorChangeSelection } from 'ngx-quill';
import { environment } from 'src/environments/environment';
import { pipe } from 'rxjs';
import { COMMA, ENTER } from '@angular/cdk/keycodes';

@Component({
  selector: 'app-intra-industry',
  templateUrl: './intra-industry.component.html',
  styleUrls: ['./intra-industry.component.css'],
  providers: [
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
  ]
})
export class IntraIndustryComponent implements OnInit {
  quillConfiguration = QuillConfiguration
  advertiserForm: FormGroup;
  docFileList: File[] = [];
  docsFiles: any[] = [];
  userInfo: any;
  companyNameControl = new FormControl();
  companyList: any[];
  selected;
  companyId: number = 0;
  blankComplaintId: number;
  claimsDocArray: any[];
  buttonName: string;
  files_attached = "No";
  maxDate = new Date();
  isChapterPreamble: boolean = false;
  isGuidelinePreamble: boolean = false;
  @ViewChild('attachments') attachment: any;
  ads: any[];
  chapter: any[];
  guideline: any[];
  clauses: any[];
  isFTCComplaint: boolean;
  platforms: any[];
  selectedClaused: any[];
  printSources: any[];
  promotionTypes: any[];
  platform_id: number;
  whatsappComplaint: boolean = false;
  whatsappComplaintId: number = 0;
  emailComplaint: boolean = false;
  emailComplaintId: any = 0;
  sysGenComplaint: boolean = false;
  sysGenComplaintId: any = 0;
  complaintSourceId: any;
  imgURL: string;
  public bucketUrl = `${environment.BUCKET_URL}`;
  buttons: [];
  filesProgress: any[] = [];
  filesAdvertiseProgress: any[] = [];
  isUploadProgress: boolean = false;
  isUploadAdvProgress: boolean = false;
  fileInfoPath: string = '';
  editable: number = 0;

  complaintData: any[] = [];
  @Output() outFilter: EventEmitter<any> = new EventEmitter<any>();
  guidelines: any[];
  guidelineClauses: any;
  userIndex: number;
  complainantID: any;
  preamble: any;
  date: any;
  disableGuideline: boolean = false;
  blankId: number;
  guidelineObj: FormGroup;
  ascoCodeObj: FormGroup;
  selectedClause: any[];
  keyQuillText = {};
  adQuillText: any;
  complaintQuillText: any;
  celebrityGuideline: boolean = false;

  selectable = true;
  removable = true;
  addOnBlur = true;
  separatorKeysCodes: number[] = [ENTER, COMMA];
  
  constructor(private fb: FormBuilder,
    private router: Router,
    private complaintService: ComplaintsService,
    private authService: AuthService,
    private uploadService: UploadService,
    private notify: NotificationService) {
    this.complaintService.complaintRegisterData.subscribe(res => {
      this.complaintData = res ? res[0] : [];
    })
    this.complaintService.isFTCComplaint.subscribe((data) => {
      this.isFTCComplaint = data['isFTC'];
    })

    this.advertiserForm = this.fb.group({
      company: ['', Validators.required],
      brand: ['', Validators.required],
      product: ['', Validators.required],
      product_category: [''],
      ad_description: ['', Validators.required],
      complaint_description: ['', Validators.required],
      advMedium: fb.array([]),
      asciCode: fb.array([]),
      guideline: fb.array([]),
      claims: fb.array([]),
      claimsDocument: fb.array([]),
    })
  }

  ngOnInit(): void {
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.ads = JSON.parse(window.localStorage.getItem('advertisementSource'));
    this.chapter = JSON.parse(window.localStorage.getItem('chapter'));
    this.clauses = JSON.parse(window.localStorage.getItem('clause'));
    this.guidelines = JSON.parse(window.localStorage.getItem('guideline'));
    this.guidelineClauses = JSON.parse(window.localStorage.getItem('guidelineClause'));

    this.blankId = this.complaintService.getBlankId;

    this.platforms = JSON.parse(window.localStorage.getItem('platform'));
    this.printSources = JSON.parse(window.localStorage.getItem('printSource'));
    this.promotionTypes = JSON.parse(window.localStorage.getItem('promotionalMaterialSource'));
    if (this.userInfo.roleId == 5) {
      this.complainantID = 2;
    }
    else if (this.userInfo.roleId == 8) {
      this.complainantID = 4;
    }
    else {
      this.complaintService.complainantID.subscribe((data) => {
        this.complainantID = data['complainantId'];
      })
    }

    let output = document.getElementById('output');
    let buttons = document.getElementsByClassName('tool--btn');
    for (let btn = 0; btn < buttons.length; btn++) {
      buttons[btn].addEventListener('click', () => {
        let cmd = buttons[btn].getAttribute('data-command');
        if (cmd === 'createlink') {
          let url = prompt("Enter the link here: ", "http:\/\/");
          document.execCommand(cmd, false, url);
        } else {
          document.execCommand(cmd, false, null);
        }
      })
    }

    this.advertiserForm.get('company')!.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0) {
          this.getCompanyList(value);
        }
      }, err => {

      })

    this.complaintService.currentButtonName
      .pipe(first()).subscribe(cond => {
        if (cond === 'Admin') {
          this.buttonName = 'Next';
          this.complaintService.currentStep
            .pipe(first()).subscribe(step => {
              if (step === 'whatsapp') {
                this.complaintService.currentWhatsappComplaintId
                  .pipe(first()).subscribe(id => {
                    if (id != 0) {
                      this.addAdve();
                      this.addCodes();
                      this.addClaimchallenges();
                      this.addGuidelines();
                      this.complaintService.currentWhatsappComplaintObj
                      .pipe(first()).subscribe(whatsappObj => {
                        this.whatsappComplaint = true;
                        let complaintObj = whatsappObj;
                        this.whatsappComplaintId = complaintObj['ID'];
                        this.companyId = complaintObj['COMPANY_ID'];
                        this.complaintSourceId = 2;
                        this.advertiserForm.patchValue({
                          'company': complaintObj['COMPANY_NAME'],
                          'brand': complaintObj['BRAND_NAME'],
                          'product': complaintObj['PRODUCT_NAME'],
                          'product_category': complaintObj['PRODUCT_CATEGORY'],
                          'complaint_description': complaintObj['COMPLAINT_DESCRIPTION'],
                          'ad_description': complaintObj['ADVERTISEMENT_DESCRIPTION'],
                        })
                        this.adQuillText = complaintObj['ADVERTISEMENT_DESCRIPTION'];
                        this.complaintQuillText = complaintObj['COMPLAINT_DESCRIPTION'];
                        this.companyId = complaintObj['COMPANY_ID'];
                        const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
                        const advMediumObj = advMediumArray.controls[0] as FormGroup;
                        if (!!complaintObj['ADVERTISEMENT_SOURCE_NAME']) {
                          this.ads.forEach(el => {
                            if (el['ADVERTISEMENT_SOURCE_NAME'].toLowerCase() === complaintObj['ADVERTISEMENT_SOURCE_NAME'].trim().toLowerCase()) {
                              let timeArray = complaintObj['ADVERTISEMENT_SEEN_TIME'].split(" ");
                              let newDateTime = new Date().setHours(timeArray[0], timeArray[1], 0, 0);
                              const time = new Date(newDateTime);
                              advMediumObj.get('seen_medium').patchValue(el['ID']);
                              advMediumObj.get('date').patchValue(new Date(complaintObj['ADVERTISEMENT_SEEN_DATE']));
                              advMediumObj.get('time').patchValue(time);
                              this.showFields(el, 0);
                              if (!(el['ID'] == 3 || el['ID'] == 5 || el['ID'] == 6)) {
                                advMediumObj.get('method').patchValue(complaintObj['CHANNEL_PAPER_PLATFORM_NAME'])
                              }
                              if (el['ID'] == 3) {
                                this.platforms.forEach(el => {
                                  if (el['PLATFORM_NAME'].toLowerCase() === complaintObj['CHANNEL_PAPER_PLATFORM_NAME'].trim().toLowerCase()) {
                                    advMediumObj.get('platform').patchValue(el['ID'])
                                  }
                                })
                              }
                              if (el['ID'] == 5) {
                                this.printSources.forEach(el => {
                                  if (el['PRINT_SOURCE_NAME'].toLowerCase() === complaintObj['CHANNEL_PAPER_PLATFORM_NAME'].trim().toLowerCase()) {
                                    advMediumObj.get('printSource').patchValue(el['ID'])
                                  }
                                })
                              }
                              if (el['ID'] == 6) {
                                this.promotionTypes.forEach(el => {
                                  if (el['P_M_SOURCE_NAME'].toLowerCase() === complaintObj['CHANNEL_PAPER_PLATFORM_NAME'].trim().toLowerCase()) {
                                    advMediumObj.get('promotionType').patchValue(el['ID'])
                                  }
                                })
                              }
                            }
                          })
                        }
                      }, err => {
                        this.notify.showNotification(
                          err.error.message,
                          "top",
                          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                          err.error.status
                        )
                      });
                    }
                  })
              } else if (step === 'email') {
                this.complaintService.currentEmailComplaintId
                  .pipe(first()).subscribe(id => {
                    if (id != 0) {
                      this.addAdve();
                      this.addCodes();
                      this.addClaimchallenges();
                      this.addGuidelines();
                      this.complaintService.getMailComplaint(id).subscribe((res: any) => {
                        this.emailComplaint = true;
                        let complaintObj = res.data;
                        this.emailComplaintId = complaintObj['ID'];
                        this.companyId = complaintObj['COMPANY_ID'];
                        this.complaintSourceId = 4;
                        this.advertiserForm.patchValue({
                          'company': complaintObj['COMPANY_NAME'],
                          'brand': complaintObj['BRAND_NAME'],
                          'product': complaintObj['PRODUCT_NAME'],
                          'product_category': complaintObj['PRODUCT_CATEGORY'],
                          'complaint_description': complaintObj['COMPLAINT_DESCRIPTION'],
                          'ad_description': complaintObj['ADVERTISEMENT_DESCRIPTION'],
                        })
                        this.adQuillText = complaintObj['ADVERTISEMENT_DESCRIPTION'];
                        this.complaintQuillText = complaintObj['COMPLAINT_DESCRIPTION'];
                        this.companyId = complaintObj['COMPANY_ID'];
                        if(!complaintObj['COMPANY_ID']){
                          // this.step2Form.get('company').setValue('');
                          this.getCompanyName(complaintObj['COMPANY_NAME']);
                        }
                        const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
                        const advMediumObj = advMediumArray.controls[0] as FormGroup;
                        if (!!complaintObj['ADVERTISEMENT_SOURCE_NAME']) {
                          this.ads.forEach(el => {
                            if (el['ADVERTISEMENT_SOURCE_NAME'].toLowerCase() === complaintObj['ADVERTISEMENT_SOURCE_NAME'].trim().toLowerCase()) {
                              let timeArray = complaintObj['ADVERTISEMENT_SEEN_TIME'].split(" ");
                              let newDateTime = new Date().setHours(timeArray[0], timeArray[1], 0, 0);
                              const time = new Date(newDateTime);
                              advMediumObj.get('seen_medium').patchValue(el['ID']);
                              advMediumObj.get('date').patchValue(new Date(complaintObj['ADVERTISEMENT_SEEN_DATE']));
                              advMediumObj.get('time').patchValue(time);
                              this.showFields(el, 0);
                              if (!(el['ID'] == 3 || el['ID'] == 5 || el['ID'] == 6)) {
                                advMediumObj.get('method').patchValue('CHANNEL_PAPER_PLATFORM_NAME')
                              }
                              if (el['ID'] == 3) {
                                this.platforms.forEach(el => {
                                  if (el['PLATFORM_NAME'].toLowerCase() === complaintObj['CHANNEL_PAPER_PLATFORM_NAME'].trim().toLowerCase()) {
                                    advMediumObj.get('platform').patchValue(el['ID'])
                                  }
                                })
                              }
                              if (el['ID'] == 5) {
                                this.printSources.forEach(el => {
                                  if (el['PRINT_SOURCE_NAME'].toLowerCase() === complaintObj['CHANNEL_PAPER_PLATFORM_NAME'].trim().toLowerCase()) {
                                    advMediumObj.get('printSource').patchValue(el['ID'])
                                  }
                                })
                              }
                              if (el['ID'] == 6) {
                                this.promotionTypes.forEach(el => {
                                  if (el['P_M_SOURCE_NAME'].toLowerCase() === complaintObj['CHANNEL_PAPER_PLATFORM_NAME'].trim().toLowerCase()) {
                                    advMediumObj.get('promotionType').patchValue(el['ID'])
                                  }
                                })
                              }
                            }
                          })
                        }
                      }, err => {
                        this.notify.showNotification(
                          err.error.message,
                          "top",
                          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                          err.error.status
                        )
                      });
                    }
                  })
              } else if (step === 'sysGen') {
                if (this.blankId) {
                  this.sysGenComplaint = true;
                  this.sysGenComplaintId = this.blankId;
                  this.complaintService.getComplaint(this.blankId).subscribe(res => {
                    if (Array.isArray(res['data']) && res['data'].length == 0) {
                      this.addAdve();
                      this.addCodes();
                      this.addClaimchallenges();
                      this.addGuidelines();
                      // this.complaintService.currentStep
                      //   .pipe(first()).subscribe(step => {
                      //     if (step === 'whatsapp') {
                      //       this.complaintService.currentWhatsappComplaintId
                      //         .pipe(first()).subscribe(id => {
                      //           if (id != 0) {
                      //             this.complaintService.getWhatsappComplaint(id).subscribe((res: any) => {
                      //               this.whatsappComplaint = true;
                      //               let complaintObj = res.data;
                      //               this.whatsappComplaintId = complaintObj['ID'];
                      //               this.companyId = complaintObj['COMPANY_ID'];
                      //               this.complaintSourceId = 2;
                      //               this.advertiserForm.patchValue({
                      //                 'company': complaintObj['COMPANY_NAME'],
                      //                 'brand': complaintObj['BRAND_NAME'],
                      //                 'product': complaintObj['PRODUCT_NAME'],
                      //                 'product_category': complaintObj['PRODUCT_CATEGORY'],
                      //                 'complaint_description': complaintObj['COMPLAINT_DESCRIPTION'],
                      //                 'ad_description': complaintObj['ADVERTISEMENT_DESCRIPTION'],
                      //               })
                      //               this.companyId = complaintObj['COMPANY_ID'];
                      //               const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
                      //               const advMediumObj = advMediumArray.controls[0] as FormGroup;
                      //               if (!!complaintObj['ADVERTISEMENT_SOURCE_NAME']) {
                      //                 this.ads.forEach(el => {
                      //                   if (el['ADVERTISEMENT_SOURCE_NAME'].toLowerCase() === complaintObj['ADVERTISEMENT_SOURCE_NAME'].trim().toLowerCase()) {
                      //                     let timeArray = complaintObj['ADVERTISEMENT_SEEN_TIME'].split(" ");
                      //                     let newDateTime = new Date().setHours(timeArray[0], timeArray[1], 0, 0);
                      //                     const time = new Date(newDateTime);
                      //                     advMediumObj.get('seen_medium').patchValue(el['ID']);
                      //                     advMediumObj.get('date').patchValue(new Date(complaintObj['ADVERTISEMENT_SEEN_DATE']));
                      //                     advMediumObj.get('time').patchValue(time);
                      //                     this.showFields(el, 0);
                      //                     if (!(el['ID'] == 3 || el['ID'] == 5 || el['ID'] == 6)) {
                      //                       advMediumObj.get('method').patchValue('CHANNEL_PAPER_PLATFORM_NAME')
                      //                     }
                      //                     if (el['ID'] == 3) {
                      //                       this.platforms.forEach(el => {
                      //                         if (el['PLATFORM_NAME'].toLowerCase() === complaintObj['CHANNEL_PAPER_PLATFORM_NAME'].trim().toLowerCase()) {
                      //                           advMediumObj.get('platform').patchValue(el['ID'])
                      //                         }
                      //                       })
                      //                     }
                      //                     if (el['ID'] == 5) {
                      //                       this.printSources.forEach(el => {
                      //                         if (el['PRINT_SOURCE_NAME'].toLowerCase() === complaintObj['CHANNEL_PAPER_PLATFORM_NAME'].trim().toLowerCase()) {
                      //                           advMediumObj.get('printSource').patchValue(el['ID'])
                      //                         }
                      //                       })
                      //                     }
                      //                     if (el['ID'] == 6) {
                      //                       this.promotionTypes.forEach(el => {
                      //                         if (el['P_M_SOURCE_NAME'].toLowerCase() === complaintObj['CHANNEL_PAPER_PLATFORM_NAME'].trim().toLowerCase()) {
                      //                           advMediumObj.get('promotionType').patchValue(el['ID'])
                      //                         }
                      //                       })
                      //                     }
                      //                   }
                      //                 })
                      //               }
                      //             }, err => {
                      //               this.notify.showNotification(
                      //                 err.error.message,
                      //                 "top",
                      //                 (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                      //                 err.error.status
                      //               )
                      //             });
                      //           }
                      //         })
                      //     }
                      //   })
                    } else {
                      this.fillForm(res.data);
                    }
                  },err => {
                    this.notify.showNotification(
                      err.error.message,
                      "top",
                      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                      err.error.status
                    );
                  })
                } else {
                  this.addAdve();
                  this.addCodes();
                  this.addClaimchallenges();
                  this.addGuidelines();
                }
              } else if (step === 'direct') {
                this.addAdve();
                this.addCodes();
                this.addClaimchallenges();
                this.addGuidelines();
              }
            })
        } else {
          this.buttonName = 'Submit';
          if (this.blankId) {
            this.complaintService.getUserComplaintById(this.blankId).subscribe(res => {
              if (Array.isArray(res['data']) && res['data'].length == 0) {
                this.addAdve();
                this.addCodes();
                this.addClaimchallenges();
                this.addGuidelines();
              } else {
                this.fillForm(res.data);
              }
            },err => {
              this.notify.showNotification(
                err.error.message,
                "top",
                (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                err.error.status
              );
            })
          }
        }
      })
    this.getBlankDocumentID();
  }

  fillForm(data) {
    let company = data['COMPANY_ID'] == 0 ? data['SUGGESTED_COMPANY_NAME'] : data['COMPANY_NAME'];
    this.advertiserForm.patchValue({
      'company': company,
      'brand': data['BRAND_NAME'],
      'product': data['PRODUCT_NAME'],
      'product_category': data['PRODUCT_CATEGORY'],
      'complaint_description': data['COMPLAINT_DESCRIPTION'],
      'ad_description': data['ADVERTISEMENT_DESCRIPTION'],
    })
    this.adQuillText = data['ADVERTISEMENT_DESCRIPTION'];
    this.complaintQuillText = data['COMPLAINT_DESCRIPTION'];
    this.companyId = data['COMPANY_ID'];
    if(!data['COMPANY_ID']){
      // this.step2Form.get('company').setValue('');
      this.getCompanyName(company);
    }
    let codeInfo = data['CODEVIOLATED'];
    if (Array.isArray(codeInfo) && codeInfo.length > 0) {
      for (let i = 0; i < codeInfo.length; i++) {
        this.addCodes();
        let clauseArray = this.clauses.filter(el => {
          return (el.CHAPTER_ID == (codeInfo[i]['CHAPTER_ID']))
        })
        if (codeInfo[i]['CHAPTER_ID']) {
          this.preamble = this.chapter[codeInfo[i]['CHAPTER_ID'] - 1].PREAMBLE;
        }
        const asciCodeArray = this.advertiserForm.get('asciCode') as FormArray;
        this.ascoCodeObj = asciCodeArray.controls[i] as FormGroup;
        this.ascoCodeObj.get('chapter').patchValue(codeInfo[i]['CHAPTER_ID']);
        let clauseId = codeInfo[i]['CLAUSES_ID'];
        if (typeof codeInfo[i]['CLAUSES_ID'] === 'string' || codeInfo[i]['CLAUSES_ID'] instanceof String) {
          clauseId = JSON.parse(codeInfo[i]['CLAUSES_ID']);
        }
        this.ascoCodeObj.get('clause').patchValue(Array.isArray(clauseId) ? clauseId : []);
        this.ascoCodeObj.get('clauseArray').patchValue(clauseArray);
        this.ascoCodeObj.get('preamble').patchValue(this.preamble);
        this.isChapterPreamble = true;
      };
    } else {
      this.addCodes();
    }

    let guidelineInfo = data['GUIDELINES'];
    if (Array.isArray(guidelineInfo) && guidelineInfo.length > 0) {
      for (let i = 0; i < guidelineInfo.length; i++) {
        this.addGuidelines();
        const guidelineArray = this.advertiserForm.get('guideline') as FormArray;
        this.guidelineObj = guidelineArray.controls[i] as FormGroup;
        this.guidelineObj.get('gChapter').patchValue(guidelineInfo[i]['G_CHAPTER_ID']);
        let gclauseId = guidelineInfo[i]['G_CLAUSES_ID'];
        if (typeof guidelineInfo[i]['G_CLAUSES_ID'] === 'string' || guidelineInfo[i]['G_CLAUSES_ID'] instanceof String) {
          gclauseId = JSON.parse(guidelineInfo[i]['G_CLAUSES_ID']);
        }
        this.guidelineObj.get('gClause').patchValue(Array.isArray(gclauseId) ? gclauseId : []);
        let guidelineClauseArray = this.guidelineClauses.filter(el => {
          return (el.GUIDELINE_ID == guidelineInfo[i]['G_CHAPTER_ID'])
        })
        if (guidelineInfo[i]['G_CHAPTER_ID']) {
          this.isGuidelinePreamble = this.guidelines[guidelineInfo[i]['G_CHAPTER_ID'] - 1].PREAMBLE;
        }
        this.guidelineObj.get('gPreamble').patchValue(this.isGuidelinePreamble);
        this.guidelineObj.get('gClauseArray').patchValue(guidelineClauseArray);
        let celebrityList = guidelineInfo[i]['CELEBRITY_NAME']
        if (typeof guidelineInfo[i]['CELEBRITY_NAME'] === 'string' || guidelineInfo[i]['CELEBRITY_NAME'] instanceof String) {
          celebrityList = JSON.parse(guidelineInfo[i]['CELEBRITY_NAME']);
        }
        this.guidelineObj.get('gCelebrityArray').patchValue(Array.isArray(celebrityList) ? celebrityList : []);
        this.isGuidelinePreamble = true;
        this.disableGuideline = guidelineInfo[i]['G_CHAPTER_ID'] == 1 ? true : false;
        this.celebrityGuideline = guidelineInfo[i]['G_CHAPTER_ID'] == 10 ? true : false;
      };
    } else {
      this.addGuidelines();
    }
    let claimsInfo = data['CLAIMS'];
    if (Array.isArray(claimsInfo) && claimsInfo.length > 0) {
      for (let i = 0; i < claimsInfo.length; i++) {
        this.addClaimchallenges();
        let claimArray = this.advertiserForm.get('claims') as FormArray;
        let claimArrayObj = claimArray.controls[i] as FormGroup;
        claimArrayObj.get('claimchallenge').patchValue(claimsInfo[i]['CLAIM_CHALLENGED']);
        claimArrayObj.get('annexure').patchValue(claimsInfo[i]['ANNEXURE_NO']);
        this.keyQuillText[i] = claimsInfo[i]['KEY_OBJECTION'];
        claimArrayObj.get('objections').patchValue(this.keyQuillText[i]);
      };
    } else {
      this.addClaimchallenges();
    }
    let mediumInfo = data['ADVERTISEMENT_MEDIUM'];
    if (Array.isArray(mediumInfo) && mediumInfo.length > 0) {
      for (let i = 0; i < mediumInfo.length; i++) {
        this.addAdve();
        const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
        const advMediumObj = advMediumArray.controls[i] as FormGroup;
        advMediumObj.get('seen_medium').patchValue(mediumInfo[i]['ADVERTISEMENT_SOURCE_ID']);
        advMediumObj.get('method').patchValue(mediumInfo[i]['SOURCE_NAME']);
        advMediumObj.get('sourcePlace').patchValue(mediumInfo[i]['SOURCE_PLACE']);
        advMediumObj.get('platform').patchValue(mediumInfo[i]['PLATFORM_ID']);
        advMediumObj.get('date').patchValue(mediumInfo[i]['DATE']);
        if (!!mediumInfo[i]['TIME']) {
          let timeArr = mediumInfo[i]['TIME'].split(':');
          let newDateTime = new Date().setHours(timeArr[0], timeArr[1], 0, 0);
          advMediumObj.get('time').patchValue(new Date(newDateTime));
        }
        advMediumObj.get('add_url').patchValue(mediumInfo[i]['SOURCE_URL']);
        advMediumObj.get('printSource').patchValue(mediumInfo[i]['PRINT_SOURCE_ID']);
        advMediumObj.get('promotionType').patchValue(mediumInfo[i]['P_M_SOURCE_ID']);
        // advMediumObj.get('file_array').patchValue(mediumInfo[i]['ATTACHMENT_DATA']);
      }
    } else {
      this.addAdve();
    }

    let mediumDocInfo = data['ADVERTISEMENT_MEDIUM_DOCUMENT'];
    if (!!mediumDocInfo && Array.isArray(mediumDocInfo) && mediumDocInfo.length != 0) {
      for (let j = 0; j < mediumInfo.length; j++) {
        let mediumDocArray = [];
        mediumDocArray = mediumDocInfo.filter(el => {
          return mediumInfo[j]['ID'] == el['ADVERTISEMENT_MEDIUM_ID'] && el['ATTACHMENT_SOURCE'] !== '';
        })
        if (mediumDocArray.length != 0) {
          const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
          const advMediumObj = advMediumArray.controls[j] as FormGroup;
          mediumDocArray.forEach(el => {
            advMediumObj.value['advFileArray'].push({
              ATTACHMENT_SOURCE: el['ATTACHMENT_SOURCE'],
              ATTACHMENT_NAME: el['ATTACHMENT_NAME'],
              TYPE_OF_DOCUMENT: el['TYPE_OF_DOCUMENT'],
              SIZE: el['SIZE'],
            });
            advMediumObj.value['file_array'].push({
              name: el['ATTACHMENT_NAME']
            })
          })
        }
      }
    }

    this.complaintService.getClaimsDocuments(this.blankId).subscribe(res => {
      let claimsDocInfo = res.data;
      if (Array.isArray(claimsDocInfo) && claimsDocInfo.length > 0) {
        for (let i = 0; i < claimsDocInfo.length; i++) {
          this.addClaimsDocument();
          const claimsDocArray = this.advertiserForm.get('claimsDocument') as FormArray;
          const claimsDocObj = claimsDocArray.controls[i] as FormGroup;
          claimsDocObj.get('attachmentSource').patchValue(claimsDocInfo[i]['ATTACHMENT_SOURCE']);
          claimsDocObj.get('attachmentName').patchValue(claimsDocInfo[i]['ATTACHMENT_NAME']);
          claimsDocObj.get('attachementType').patchValue(claimsDocInfo[i]['TYPE_OF_DOCUMENT']);
          claimsDocObj.get('documentType').patchValue(claimsDocInfo[i]['DOC_TYPE']);
          claimsDocObj.get('doc_name').patchValue(claimsDocInfo[i]['DOC_NAME']);
          claimsDocObj.get('doc_annex').patchValue(claimsDocInfo[i]['ANNEXURE_NO']);
          claimsDocObj.get('size').patchValue(claimsDocInfo[i]['SIZE']);
        }

      }
    },err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  asciCodeFormValues(i) {
    // return this.advertiserForm.get('asciCode') as FormArray;
    // (this.advertiserForm.get('asciCode') as FormArray).controls;
    const asciCodeArray = this.advertiserForm.get('asciCode') as FormArray;
    const ascoCodeObj = asciCodeArray.controls[i] as FormGroup;
    let obj = ascoCodeObj.get('clauseArray').value;
    return obj;
  }

  getCompanyList(value) {
    this.authService.getCompanies(value).subscribe(res => {
      this.companyList = res.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  onSelectionChange(event) {
    this.companyList.forEach(element => {
      if (event.option.value === element.COMPANY_NAME) {
        this.companyId = element.ID;
      }
    });
  }

  companyChange() {
    this.companyId = 0;
  }

  companyInput() {
    if (!(this.userInfo.roleId == 1 || this.userInfo.roleId == 2 || this.userInfo.roleId == 3 || this.userInfo.roleId == 6) && this.companyId == 0) {
      this.companyId = 0;
    } else if ((this.userInfo.roleId == 1 || this.userInfo.roleId == 2 || this.userInfo.roleId == 3 || this.userInfo.roleId == 6) && this.companyId == 0) {
      this.advertiserForm.get('company').setValue('');
    }
  }

  addAdve(advMedium?: any) {
    let advIndex = (<FormArray>this.advertiserForm.get('advMedium')).length;
    if (advIndex < 4) {
      let fg = this.fb.group({
        seen_medium: this.fb.control('', Validators.required),
        method: this.fb.control(''),
        sourcePlace: this.fb.control(''),
        printSource: this.fb.control(''),
        promotionType: this.fb.control(''),
        platform: this.fb.control(''),
        date: this.fb.control('', Validators.required),
        time: this.fb.control(''),
        add_url: this.fb.control(''),
        attachment_source: this.fb.control(''),
        attachment_name: this.fb.control(''),
        attachment_type: this.fb.control(''),
        attachment_size: this.fb.control(''),
        file_array: this.fb.control([]),
        advFileArray: this.fb.control([])
      });
      (<FormArray>this.advertiserForm.get('advMedium')).push(fg);
      this.userIndex = (<FormArray>this.advertiserForm.get('advMedium')).length - 1;

    }
  }

  addCodes(asciCode?: any) {
    let codeIndex = (<FormArray>this.advertiserForm.get('asciCode')).length;
    if (codeIndex < 5) {
      let fg = this.fb.group({
        chapter: this.fb.control('', Validators.required),
        preamble: this.fb.control(''),
        clause: this.fb.control(''),
        guidelines: this.fb.control(''),
        clauseArray: this.fb.control([])
      });
      (<FormArray>this.advertiserForm.get('asciCode')).push(fg);
       let userIndex = (<FormArray>this.advertiserForm.get('asciCode')).length - 1;
    }
  }

  addGuidelines() {
    let codeIndex = (<FormArray>this.advertiserForm.get('guideline')).length;
    let guidelines = [...this.guidelines];
    const guidelineArray = this.advertiserForm.get('guideline') as FormArray;
    if (guidelineArray.length > 0) {
      const guidelineObj = guidelineArray.controls[0] as FormGroup;
      if (guidelineObj.value.gChapter != 1) {
        guidelines.splice(0, 1);
      }
    }
    if (codeIndex < 5) {
      let fg = this.fb.group({
        gChapter: this.fb.control('', Validators.required),
        gClause: this.fb.control(''),
        gClauseArray: this.fb.control([]),
        gCelebrityArray: this.fb.control([]),
        gPreamble: this.fb.control(''),
        gGuidelineArray: this.fb.control(guidelines)
      });

      (<FormArray>this.advertiserForm.get('guideline')).push(fg);
      let gUserIndex = (<FormArray>this.advertiserForm.get('guideline')).length - 1;
    }
  }

  addClaimchallenges(claims?: any) {
    let claimIndex = (<FormArray>this.advertiserForm.get('claims')).length;
    if (claimIndex < 3) {
      let fg = this.fb.group({
        claimchallenge: this.fb.control(''),
        annexure: this.fb.control(''),
        objections: this.fb.control(''),
      });
      (<FormArray>this.advertiserForm.get('claims')).push(fg);
      let userIndex = (<FormArray>this.advertiserForm.get('claims')).length - 1;
    }
  }

  addClaimsDocument() {
    let claimIndex = (<FormArray>this.advertiserForm.get('claimsDocument')).length;
    if (claimIndex < 11) {
      let fg = this.fb.group({
        attachmentName: this.fb.control(''),
        attachmentSource: this.fb.control(''),
        attachementType: this.fb.control(''),
        documentType: this.fb.control(''),
        doc_name: this.fb.control(''),
        doc_annex: this.fb.control(''),
        size: this.fb.control('')
      });
      (<FormArray>this.advertiserForm.get('claimsDocument')).push(fg);
      let userIndex = (<FormArray>this.advertiserForm.get('claimsDocument')).length - 1;
    }
  }

  getControls1() {
    return (this.advertiserForm.get('advMedium') as FormArray).controls;
  }
  getControls2() {
    return (this.advertiserForm.get('asciCode') as FormArray).controls;
  }
  getControls3() {
    return (this.advertiserForm.get('claims') as FormArray).controls;
  }
  getControls4() {
    return (this.advertiserForm.get('claimsDocument') as FormArray).controls;
  }
  getControls5() {
    return (this.advertiserForm.get('guideline') as FormArray).controls;
  }

  removeAdve(index: number) {
    (<FormArray>this.advertiserForm.get('advMedium')).removeAt(index);
  }
  removeCodes(index: number) {
    (<FormArray>this.advertiserForm.get('asciCode')).removeAt(index);
  }
  removeGuidelines(index: number) {
    (<FormArray>this.advertiserForm.get('guideline')).removeAt(index);
  }
  removeClaims(index: number) {
    (<FormArray>this.advertiserForm.get('claims')).removeAt(index);
  }
  removeClaimsDocument(index: number) {
    (<FormArray>this.advertiserForm.get('claimsDocument')).removeAt(index);
  }

  savedraft() {
    this.step2Next('draft');
  }

  step2Next(cond) {
    let submitted = 1;
    if (cond === 'draft') {
      submitted = 0;
    }
    if (this.advertiserForm.valid) {
    }
    let formData: any;
    formData = this.advertiserForm.value;
    let advMedium = formData['advMedium'];
    let advMedArray = [];
    for (let i = 0; i < advMedium.length; i++) {
      if (advMedium[i].seen_medium != 0) {
        let time = advMedium[i].time;
        if (!!time) {
          time = moment(time).format('HH:mm:ss')
        }
        advMedArray.push({
          "ADVERTISEMENT_SOURCE_ID": advMedium[i].seen_medium,
          "SOURCE_NAME": advMedium[i].method,
          "SOURCE_PLACE": advMedium[i].sourcePlace,
          "PLATFORM_ID": advMedium[i].platform,
          "DATE": moment(advMedium[i].date).format('yyyy-MM-DD'),
          "TIME": time,
          "SOURCE_URL": advMedium[i].add_url,
          "PRINT_SOURCE_ID": advMedium[i].printSource,
          "P_M_SOURCE_ID": advMedium[i].promotionType,
          "ATTACHMENT_DATA": advMedium[i].advFileArray
        })
      }
    }
    let asciCode = formData['asciCode'];
    let asciCodeArray = [];
    for (let i = 0; i < asciCode.length; i++) {
      asciCodeArray.push({
        "CHAPTER_ID": asciCode[i].chapter,
        "CLAUSES_ID": asciCode[i].clause,
      })
    }
    let guideline = formData['guideline'];
    let guidelineArray = [];
    for (let i = 0; i < guideline.length; i++) {
      guidelineArray.push({
        "G_CHAPTER_ID": guideline[i].gChapter,
        "G_CLAUSES_ID": guideline[i].gClause,
        "CELEBRITY_NAME": guideline[i].gChapter == 10 ? guideline[i].gCelebrityArray : []
      })
    }
    let claims = formData['claims'];
    let claimsArray = [];
    for (let i = 0; i < claims.length; i++) {
      claimsArray.push({
        "CLAIM_CHALLENGED": claims[i].claimchallenge,
        "ANNEXURE_NO": claims[i].annexure,
        "KEY_OBJECTION": this.keyQuillText[i]
      })
    }
    let claimsDoc = formData['claimsDocument'];
    let claimsDocArray = [];
    for (let i = 0; i < claimsDoc.length; i++) {
      claimsDocArray.push({
        "ATTACHMENT_SOURCE": claimsDoc[i].attachmentSource,
        "ATTACHMENT_NAME": claimsDoc[i].attachmentName,
        "TYPE_OF_DOCUMENT": claimsDoc[i].attachementType,
        'DOC_TYPE': claimsDoc[i].documentType,
        'DOC_NAME': claimsDoc[i].doc_name,
        "ANNEXURE_NO": claimsDoc[i].doc_annex,
        "SIZE": claimsDoc[i].size
      })
    }
    if (this.whatsappComplaint) {
      this.complaintSourceId = 2;
    } else if (this.emailComplaint) {
      this.complaintSourceId = 4;
    } else if (this.complaintSourceId == undefined || this.complaintSourceId == null || this.sysGenComplaint) {
      this.complaintSourceId = 3;
    }
    let reference_id = null;
    if (this.whatsappComplaint) {
      reference_id = this.whatsappComplaintId;
    } else if (this.emailComplaint) {
      reference_id = this.emailComplaintId;
    } else if (this.sysGenComplaint) {
      reference_id = this.sysGenComplaintId;
    }
    if(this.complaintQuillText) {
      this.advertiserForm.patchValue({
        'COMPLAINT_DESCRIPTION': this.complaintQuillText
      })
    }
    if(this.adQuillText) {
      this.advertiserForm.patchValue({
        'ADVERTISEMENT_DESCRIPTION': this.adQuillText
      })
    }
    let obj = {
      "ID": 0,
      "REFERENCE_ID": reference_id,
      "COMPLAINT_TYPE_ID": this.complainantID,
      "COMPANY_ID": this.companyId,
      "PANEL_ID": 1,
      "COMPLAINT_SOURCE_ID": this.complaintSourceId,
      "COMPLAINT_STATUS_ID": 1,
      "PRIORITY_ID": 1,
      "STAGE_ID": 1,
      "CLASSIFICATION_ID": "",
      "GOVERNMENT_DEPARTEMENT_ID": 1,
      "USER_TYPE_ID": this.complainantID,
      "USER_ID": this.userInfo.userId,
      "CREATED_BY_USER_ID": this.userInfo.userId,
      "ASSIGNEE_USER_ID": 0,
      "COMPLAINT_PRODUCT_CATEGORY_ID": 0,
      "EXTENDED_STAGE_ID": 1,
      "EXTENDED_DAYS": "9",
      "BRAND_NAME": this.advertiserForm.value.brand,
      "PRODUCT_NAME": this.advertiserForm.value.product,
      "ADVERTISEMENT_DESCRIPTION": this.adQuillText,
      "COMPLAINT_DESCRIPTION": this.complaintQuillText,
      "COMPANY_NAME": this.advertiserForm.value.company,
      // "OBJECTIONABLE": "",
      // "WEB_LINK": "",
      "NOTIFY": "7",
      "TnC": "1",
      // "SUGGESTION": this.step2Form.value.suggestion,
      "DUE_DATE": "",
      // "ADDITIONAL_CLASSIFICATION": this.step2Form.value.initial_class,
      "ADVERTISOR_NAME": "",
      "PRODUCT_CATEGORY": this.advertiserForm.value.product_category,
      "SUBMITTED": submitted,
      "EDITABLE": this.editable,
      "CLAIMS": claimsArray,
      "CLAIMS_DOCUMENT": claimsDocArray,
      "ADVERTISEMENT_MEDIUM": advMedArray,
      "CODE_VIOLATED": asciCodeArray,
      "GUIDELINES": guidelineArray,
      "files_attached": this.files_attached
    }
    this.complaintService.complaintRegister(obj, 1);
    // this.complaintService.updateBlankComplaintId(0);
    this.router.routeReuseStrategy.shouldReuseRoute = () => false;
    this.router.onSameUrlNavigation = 'reload';
    this.outFilter.emit({ 'cond': 'create', 'obj': obj });
  }

  getFileVal(index) {
    const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
    const advMediumObj = advMediumArray.controls[index] as FormGroup;
    if(advMediumObj.get('file_array').value.length!= 0  || advMediumObj.get('add_url').value){
      return ' '
    } else {
      return 'Please enter the url or upload file'
    }
  }

  async onFileChanged(event: any, index) {
    if (!this.uploadService.validateFileExtension(event)) {
      this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        "File format is not supported!",
        "top",
        "warning",
        0
      );
      return;
    }

    const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
    const advMediumObj = advMediumArray.controls[index] as FormGroup;
    let docFileList = advMediumObj.get('file_array').value;
    let sizeOfAllFiles = this.uploadService.getTotalFileSize(docFileList) + this.uploadService.getTotalFileSize(event.target.files);;
    let totalFileSelected = docFileList.length + event.target.files.length;
    if (totalFileSelected < 11 && sizeOfAllFiles <= 36700160) {
      for (let i = 0; i <= event.target.files.length - 1; i++) {
        if (i === 0) {
          this.getBlankDocumentID();
        }
        var selectedFile = event.target.files[i];
        docFileList.push(selectedFile);
        let tempObjforGetsigned = {
          id: this.blankComplaintId,
          section: 'a_medium',
          filename: selectedFile['name'],
          type: selectedFile['type']
        }
        this.isUploadAdvProgress = true;
        this.filesAdvertiseProgress[index] = { progress: 0 }
        await this.uploadService.getSignedUrl(tempObjforGetsigned)
          .then(async (res) => {
            if (res && res['data'] && res['data']['SIGNED_URL']) {
              advMediumObj.value['advFileArray'].push({
                ATTACHMENT_SOURCE: res['data']['PATH'],
                ATTACHMENT_NAME: selectedFile['name'],
                TYPE_OF_DOCUMENT: selectedFile['type'],
                SIZE: selectedFile['size']
              });
              let tempObjForUpload = {
                url: res['data']['SIGNED_URL'],
                file: selectedFile
              }
              await this.uploadService.uploadFilethroughSignedUrl(tempObjForUpload, progressInit => {
                this.filesAdvertiseProgress[index] = { progress: progressInit };
              })
                .then((data) => {
                  this.isUploadAdvProgress = false;
                  this.notify.showNotification(
                    'File uploaded successfully',
                    "top",
                    (!!colorObj[200] ? colorObj[200] : "success"),
                    200
                  );
                }).catch((err) => {
                  this.isUploadAdvProgress = false;
                  this.handleError(err);
                  const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
                  const advMediumObj = advMediumArray.controls[index] as FormGroup;
                  this.filesAdvertiseProgress[index] = { progress: 0 };
                  advMediumObj.get('file_array').setValue([]);
                  advMediumObj.get('advFileArray').setValue([]);
                })
            }
          })
          .catch(err => {
            this.isUploadAdvProgress = false;
            this.handleError(err)
          });
      }

    } else {
      this.notify.showNotification(
        "Max size 35MB file allowed",
        "top",
        "warning",
        0
      );
    }
  }

  removeVideoFile(index, i) {
    const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
    const advMediumObj = advMediumArray.controls[index] as FormGroup;
    let fileArray = advMediumObj.get('file_array').value;
    let advFileArray = advMediumObj.get('advFileArray').value;
    this.filesAdvertiseProgress[index] = { progress: 0 };
    // advMediumObj.get('file_array').setValue([]);
    let body = {
      ID: this.blankComplaintId,
      KEY: advFileArray[i].ATTACHMENT_SOURCE
    }
    fileArray.splice(i, 1);
    advFileArray.splice(i, 1);
    this.uploadService.deleteObjectFromS3(body).subscribe((res) => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      );
    }, (err) => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  async onFileSelected(event: any) {
    if (!this.uploadService.validateFileExtension(event)) {
      this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        "File format is not supported!",
        "top",
        "warning",
        0
      );
      return;
    }
    let sizeOfAllFiles = this.uploadService.getTotalFileSize(this.docFileList) + this.uploadService.getTotalFileSize(event.target.files);
    let totalFileSelected = this.docFileList.length + event.target.files.length;
    if (totalFileSelected < 11 && sizeOfAllFiles <= 36700160) {
      this.isUploadProgress = true;
      for (let i = 0; i <= event.target.files.length - 1; i++) {
        if (i === 0) {
          this.getBlankDocumentID();
        }
        var selectedFile = event.target.files[i];
        this.docFileList.push(selectedFile);
        this.docsFiles.push(selectedFile.name);
        let tempObjforGetsigned = {
          id: this.blankComplaintId,
          section: 'claims',
          filename: selectedFile['name'],
          type: selectedFile['type']
        }
        await this.uploadService.getSignedUrl(tempObjforGetsigned).then(async (res) => {
          if (res && res['data'] && res['data']['SIGNED_URL']) {
            let tempObjForUpload = {
              url: res['data']['SIGNED_URL'],
              file: selectedFile
            }
            let fg = this.fb.group({
              attachmentName: selectedFile['name'],
              attachmentSource: res.data['PATH'],
              documentType: this.fb.control(''),
              attachementType: selectedFile['type'],
              doc_name: this.fb.control(''),
              doc_annex: this.fb.control(''),
              size: selectedFile['size']
            });
            (<FormArray>this.advertiserForm.get('claimsDocument')).push(fg);
            let length = this.advertiserForm.controls['claimsDocument']['value']['length'] - 1;
            this.filesProgress[length] = { progress: 0 }
            await this.uploadSignedFile(tempObjForUpload);
          }
        })
          .catch((err) => {
            this.isUploadProgress = false;
            this.handleError(err);
          });
      }
      this.isUploadProgress = false;
      this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        'Claim documents uploaded successfully',
        "top",
        (!!colorObj[200] ? colorObj[200] : "success"),
        200
      );
    } else {
      this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        "Max size 35MB and Max 10 files allowed",
        "top",
        "warning",
        0
      );
    }
  }

  async uploadSignedFile(tempObj) {
    let progressInit = 0;
    let length = this.advertiserForm.controls['claimsDocument']['value']['length'] - 1;
    await this.uploadService.uploadFilethroughSignedUrl(tempObj, progressInit => {
      this.filesProgress[length] = { progress: progressInit };
    }).catch((err) => {
      this.isUploadProgress = false;
      this.handleError(err);
    });
  }

  async onFileReplaced(event: any, index) {
    if (!this.uploadService.validateFileExtension(event)) {
      this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        "File format is not supported!",
        "top",
        "warning",
        0
      );
      return;
    }
    let sizeOfAllFiles = this.uploadService.getTotalFileSize(this.docFileList) + this.uploadService.getTotalFileSize(event.target.files);
    if (sizeOfAllFiles <= 36700160) {
      this.isUploadProgress = true;
      var selectedFile = event.target.files[0];
      this.docFileList[index] = selectedFile;
      this.docsFiles[index] = selectedFile.name;
      this.attachment.nativeElement.value = '';
      let cliamsControl = this.advertiserForm.get('claimsDocument') as FormArray;
      let fileInfo = cliamsControl.at(index);
      let body = {
        ID: this.blankComplaintId,
        KEY: fileInfo['value']['attachmentSource']
      }
      await this.uploadService.deleteObjectFromS3(body).toPromise().then(async (res) => {
        let tempObj = {
          id: this.blankComplaintId,
          section: 'claims',
          filename: selectedFile['name'],
          type: selectedFile['type']
        }
        await this.uploadService.getSignedUrl(tempObj).then(async (presigned) => {
          if (presigned && presigned['data'] && presigned['data']['SIGNED_URL']) {
            let tempObjForUpload = {
              url: presigned['data']['SIGNED_URL'],
              file: selectedFile
            }
            await this.uploadService.uploadFilethroughSignedUrl(tempObjForUpload, progressInit => {
              this.filesProgress[index] = { progress: progressInit };
            }).then((resp) => {
              let cliamsControl = this.advertiserForm.get('claimsDocument') as FormArray;
              cliamsControl.at(index).patchValue({ "attachment_source": presigned['data']['PATH'] });
              cliamsControl.at(index).patchValue({ "attachmentName": selectedFile.name });
              this.isUploadProgress = false;
              this.attachment.nativeElement.value = '';
              this.notify.showNotification(
                'Claim document changed successfully',
                "top",
                (!!colorObj[200] ? colorObj[200] : "success"),
                200
              );
            }).catch((err) => {
              this.isUploadProgress = false;
              this.handleError(err);
            })
          }
        }).catch(err => {
          this.isUploadProgress = false;
          this.handleError(err)
        });
      }).catch((err) => {
        this.isUploadProgress = false;
        this.handleError(err);
      })
    } else {
      this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        "Max size 35MB allowed",
        "top",
        "warning",
        0
      );
    }
  }

  getBlankDocumentID() {
    // this.complaintService.currentBlankComplaintId
    //   .pipe(first()).subscribe(id => {
    //     this.blankComplaintId = id;
    //   })
    this.blankComplaintId = this.complaintService.getBlankId;
  }

  removeDocFile(index) {
    let cliamsControl = this.advertiserForm.get('claimsDocument') as FormArray;
    let fileInfo = cliamsControl.at(index);
    let body = {
      ID: this.blankComplaintId,
      KEY: fileInfo['value']['attachmentSource']
    }
    this.uploadService.deleteObjectFromS3(body).subscribe((res) => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      );
      this.docsFiles.splice(index, 1);
      this.docFileList.splice(index, 1);
      this.removeClaimsDocument(index);
    }, (err) => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  cancel() {
    this.filesProgress = [];
    this.filesAdvertiseProgress = [];
    let obj = {}
    // this.advertiserForm.reset();
    this.outFilter.emit({ 'cond': 'back', 'obj': obj });
    // this.advertiserForm.reset();
    // let currentUrl = this.router.url;
    // this.router.routeReuseStrategy.shouldReuseRoute = () => false;
    // this.router.onSameUrlNavigation = 'reload';
    // this.router.navigate([currentUrl]);
    // this.router.navigate(['/user-dashboard']);
  }
  get chapters(): FormControl {
    return this.advertiserForm.value['asciCode'] as FormControl;
  }

  get guidelinesASCI(): FormControl {
    return this.advertiserForm.value['guideline'][0] as FormControl;
  }

  get cLauses(): FormControl {
    return this.advertiserForm.value['asciCode'][0].clauseArray as FormControl;
  }

  changePlatform(plat) {
    this.platform_id = plat.ID;
  }

  textChangedAdDesc($event) {
    if ($event.editor.getLength() > 5000) {
      $event.editor.deleteText(5000, $event.editor.getLength());
    }
    this.adQuillText = $event.html;
  }
  textChangedComplaintDesc($event) {
    if ($event.editor.getLength() > 5000) {
      $event.editor.deleteText(5000, $event.editor.getLength());
    }
    this.complaintQuillText = $event.html;
  }
  textChangedKeyObjections($event,index) {
    if ($event.editor.getLength() > 5000) {
      $event.editor.deleteText(5000, $event.editor.getLength());
    }
    this.keyQuillText[index] =$event.html;
    // let claimArray = this.advertiserForm.get('claims') as FormArray; 
    // claimArray.controls[index].patchValue({'objections':this.keyQuillText});
  }

  showFields(data, i) {
    this.platform_id = 0;
    const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
    const advMediumObj = advMediumArray.controls[i] as FormGroup;
    advMediumObj.get('method').clearValidators();
    // advMediumObj.get('method').updateValueAndValidity();
    advMediumObj.get('sourcePlace').clearValidators();
    // advMediumObj.get('sourcePlace').updateValueAndValidity();
    advMediumObj.get('time').clearValidators();
    // advMediumObj.get('time').updateValueAndValidity();
    advMediumObj.get('platform').clearValidators();
    // advMediumObj.get('platform').updateValueAndValidity();
    advMediumObj.get('printSource').clearValidators();
    // advMediumObj.get('printSource').updateValueAndValidity();
    advMediumObj.get('promotionType').clearValidators();
    // advMediumObj.get('promotionType').updateValueAndValidity;
    // advMediumObj.get('method').setValue('');
    // advMediumObj.get('sourcePlace').setValue('');
    // advMediumObj.get('time').setValue('');
    // advMediumObj.get('platform').setValue('');
    // advMediumObj.get('printSource').setValue('');
    // advMediumObj.get('promotionType').setValue('');

    if (data.ID == 1 || data.ID == 2 || data.ID == 5 || data.ID == 8 ||
      data.ID == 9) {
      advMediumObj.controls["method"].setValidators(Validators.required);
    }
    if (data.ID == 3 || data.ID == 4 || data.ID == 6) {
      advMediumObj.controls["sourcePlace"].setValidators(Validators.required);
    }
    if (data.ID == 1 || data.ID == 2 || data.ID == 3 || data.ID == 9) {
      advMediumObj.controls["time"].setValidators(Validators.required);
    }
    if (data.ID == 3) {
      advMediumObj.controls["platform"].setValidators(Validators.required);
    }
    if (data.ID == 5) {
      advMediumObj.controls["printSource"].setValidators(Validators.required);
    }
    if (data.ID == 6) {
      advMediumObj.controls["promotionType"].setValidators(Validators.required);
    }

    if (data.ID == 3 || data.ID == 4 || data.ID == 6 || data.ID == 7) {
      advMediumObj.controls["method"].reset();
    }
    if (data.ID == 1 || data.ID == 2 || data.ID == 7 ||
      data.ID == 8 || data.ID == 9) {
      advMediumObj.controls["sourcePlace"].reset();
    }
    if (data.ID == 4 || data.ID == 5 || data.ID == 6 || data.ID == 7 || data.ID == 8) {
      advMediumObj.controls["time"].reset();
    }
    if (!(data.ID == 3)) {
      advMediumObj.controls["platform"].reset();
    }
    if (!(data.ID == 5)) {
      advMediumObj.controls["printSource"].reset();
    }
    if (!(data.ID == 6)) {
      advMediumObj.controls["promotionType"].reset();
    }
  }

  selectChapter(chapter, index) {
    let clauseArray = this.clauses.filter(el => {
      return (el.CHAPTER_ID == chapter.ID)
    })
    this.preamble = this.chapter[chapter.ID - 1].PREAMBLE;
    const asciCodeArray = this.advertiserForm.get('asciCode') as FormArray;
    const ascoCodeObj = asciCodeArray.controls[index] as FormGroup;
    ascoCodeObj.get('clauseArray').patchValue(clauseArray);
    ascoCodeObj.get('preamble').patchValue(this.preamble);
    this.isChapterPreamble = true;
  }

  selectGuideline(chapter, index) {
    let guidelineClauseArray = this.guidelineClauses.filter(el => {
      return (el.GUIDELINE_ID == chapter.ID)
    })
    this.isGuidelinePreamble = this.guidelines[chapter.ID - 1].PREAMBLE;
    const guidelineArray = this.advertiserForm.get('guideline') as FormArray;
    const asciCodeObj = guidelineArray.controls[index] as FormGroup;
    asciCodeObj.get('gClauseArray').patchValue(guidelineClauseArray);
    asciCodeObj.get('gPreamble').patchValue(this.isGuidelinePreamble);
    this.isGuidelinePreamble = true;
    this.disableGuideline = chapter.ID == 1 ? true : false;
    this.celebrityGuideline = chapter['ID'] == 10 ? true : false;
  }

  selectClause(index) {
    this.advertiserForm.value;
  }

  resetComplaintForm() {
    this.advertiserForm.reset();
  }

  checkMandatoryFields() {
    const advMedArray = this.advertiserForm.get('advMedium') as FormArray;
    for (let i = 0; i < advMedArray.controls.length; i++) {
      if (advMedArray.controls[i].value.add_url !== "" || advMedArray.controls[i].value.advFileArray.length != 0) {
        this.files_attached = 'Yes';
      } else {
        this.files_attached = 'No';
        break;
      }
    }
    if (this.files_attached == 'Yes') {
      return false;
    } else {
      return true;
    }
  }

  handleError(err: any) {
    this.notify.showNotification(
      err.error.message,
      "top",
      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
      err.error.status
    );
  }

  // changeEditor1(event: EditorChangeContent | EditorChangeSelection) {
  //   this.advertiserForm.patchValue({
  //     'ad_description': event['editor']['root']['innerHTML']
  //   })
  // }

  getCompanyName(companyValue) {
    if(!!companyValue){
      this.authService.getCompanies(companyValue).subscribe(res => {
        if (res.data.length > 0) {
          this.companyId = res.data[0].ID;
          this.advertiserForm.patchValue({
            'company': res.data[0].COMPANY_NAME
          })
        }
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
  }

  preview(source) {
    this.imgURL = this.bucketUrl + source;
    window.open(this.imgURL, 'window 1', '');
  }

  removeCelebrity(celebrity, i){
    const guidelineArray = this.advertiserForm.get('guideline') as FormArray;
    const guidelineObj = guidelineArray.controls[i] as FormGroup;
    const index = guidelineObj.value['gCelebrityArray'].indexOf(celebrity);
    if (index >= 0) {
      guidelineObj.value['gCelebrityArray'].splice(index, 1);
    }
  }

  addCelebrity(event, index){
    const input = event.input;
    const value = (event.value || '').trim();
    const guidelineArray = this.advertiserForm.get('guideline') as FormArray;
    const guidelineObj = guidelineArray.controls[index] as FormGroup;
    if(value){
      guidelineObj.value['gCelebrityArray'].push(event.value);
    }
    if(input){
      input.value = '';
    }
    
  }

}