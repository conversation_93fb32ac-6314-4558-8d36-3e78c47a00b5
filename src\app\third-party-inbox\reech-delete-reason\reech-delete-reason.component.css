::ng-deep .mat-form-field-flex > .mat-form-field-infix { padding: 2px 0px 0.4em 0px !important;bottom:0px; }
:host:ng-deep .mat-form-field-appearance-outline .mat-form-field-label { margin-top:-15px; }
:host:ng-deep label.ng-star-inserted { transform: translateY(-0.59375em) scale(.75) !important; }
:host:ng-deep .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    transform: translateY(-1.1em) scale(.75);
    width: 133.33333%;
}

.toolbar {
    padding-left: 15px;
    height: 30px;
    padding-left: 15px;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #2F3941;
    background-color: white;
}

.search-btn {
    color: solid rgba(47, 57, 65, 0.6);
    background: #F3F3F3;
    border: 1px solid rgba(47, 57, 65, 0.6);
}

.toolbar2 {
    background-color: white;
    margin-top: 25px;
}

.update-btn {
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: #5A6F84;
    background-color: white;
    border-color: #CFD7DF;
    border-radius: 12px;
    margin-right: 10px;
}

.remove-btn {
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: white;
    background-color: #ED2F45;
    border-radius: 12px;
}

.remove-btn[disabled] {
    opacity: 0.4;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: white;
    background-color: #ED2F45;
    border-radius: 12px;
}

.center {
    display: flex;
    justify-content: center;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #2F3941;
    text-align: center;
}

.title {
    display: flex;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #2F3941;
}

.bell-btn {
    color: rgb(88, 87, 87);
    border: 1px solid rgb(209, 205, 205);
    border-radius: 15px;
}

.input-field_dept {
    width:270px !important;
    height: 20px !important;
}