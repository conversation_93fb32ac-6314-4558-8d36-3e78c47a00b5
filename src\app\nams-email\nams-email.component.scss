.email-to-button {
    width: 60px;
    height: 25px;
    text-align: center;
    background: #FFFFFF;
    border: 1px solid #ccc;
    margin-bottom: -1px;
}

.email-error-msg {
    position: relative;
    bottom: 7px;
    font-size: 75%;
    font-weight: 600;
}

:host ::ng-deep .mat-form-field-appearance-standard .mat-form-field-flex {
    padding-top: 0px;
}

.to-field {
    width: 588px;
    font-size: 14px;
    position: relative;
    height: 5px;
    white-space: normal;
    -webkit-box-orient: vertical;
    vertical-align: middle;
}

:host ::ng-deep .mat-standard-chip {
    min-height: 21px;
    margin: 2px;
    margin-bottom: 1px;
    // z-index: 1;
}

:host ::ng-deep .mat-chip-list-wrapper {
    width: 592px;
    height: auto;
    max-height: 50px;
    overflow-x: hidden;
    overflow-y: scroll;
}

:host ::ng-deep .mat-chip-list-wrapper::-webkit-scrollbar {
    width: 3px;
}

:host ::ng-deep .mat-chip-list-wrapper::-webkit-scrollbar-thumb {
    background: #ccc
}

:host ::ng-deep .mat-form-field-flex>.mat-form-field-infix {
    bottom: -3px !important;
}

// :host ::ng-deep .mat-form-field-infix {
//     border-top: none !important;
// }
.subject-field {
    width: 588px;
    height: 30px;
    font-size: 14px;
    position: relative;
    bottom: 8px;
    right: 5px;
    color: rgba(0, 0, 0, 0.4);
}

.tool-list {
    display: flex;
    list-style: none;
    padding: 0;
    overflow: hidden;
    margin-bottom: 0px;
    margin-left: 14px;
}

.tool--btn {
    background-color: white;
    color: #8b8b8b;
    font-size: 10px;
    display: block;
    border: none;
    padding-top: .5rem;
    margin: .3rem;
}

.template-btn {
    border-radius: 10px;
    height: 30px;
    position: relative;
    top: 5px;
    left: 4px;
    margin-right: 10px;
    background: white;
    border: 1px solid #ccc;
    color: #898181;
    width: 115px;
}

.change-btn {
    border-radius: 10px;
    height: 30px;
    position: relative;
    top: 5px;
    left: 4px;
    margin-right: 10px;
    background: white;
    border: 1px solid #ccc;
    color: #898181;
    width: 150px;
}

.clear-btn {
    border-radius: 10px;
    height: 30px;
    position: relative;
    top: 5px;
    margin-left: 170px;
    background: white;
    border: 1px solid white;
    color: #898181;
    width: 80px;
}

.save-btn {
    border-radius: 10px;
    height: 30px;
    position: relative;
    top: 5px;
    margin-left: 10px;
    background: white;
    border: 1px solid #0088CB;
    color: #0088CB;
    width: 170px;
}

// #output::-webkit-scrollbar {
//     display: none;
// }
// #output {
//     -ms-overflow-style: none;
//     scrollbar-width: none; 
// }
.template-file::-webkit-scrollbar {
    display: none;
}

.template-file {
    height: 310px;
    overflow-y: scroll;
    overflow-x: hidden;
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.send-button {
    height: 70px;
    font-size: 12px;
    text-align: center;
    color: #000000;
    background: #FFFFFF;
    border: 1px solid #D8DCDE;
    box-sizing: border-box;
    padding: 3px 5px 65px 0px;
    margin-top: 70px;
}

.send-button[disabled] {
    opacity: 0.3;
}

.red-border-class {
    border-color: red;
}

.error-msg {
    font-size: 75%;
    font-weight: 600;
    position: relative;
    bottom: 15px;
    left: 15px;
}

.mail-toolbar {
    height: 207px;
    width: 100%;
    background: #F8F9F9;
    padding: 0% 2% 1% 2%;
    overflow-y: scroll;
    overflow-x: hidden;
}

.mail-toolbar::-webkit-scrollbar {
    display: none;
}

.mail-toolbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.email-body {
    padding-bottom: 24px;
    color: #7C7C7C;
}

.upload-screen {
    width: 97%;
    outline: none;
    height: 400px;
    margin-bottom: 17px;
    margin-left: 14px;
    margin-top: 13px;
}

#output {
    width: 97%;
    outline: none;
    // overflow-y: scroll;
    // overflow-x: hidden;
    height: 320px;
    margin-bottom: 17px;
    margin-left: 14px;
    margin-top: 13px;
}

.close-btn {
    margin-top: 85px;
    height: 35px;
    width: 35px;
    color: solid rgba(0, 0, 0, 0.6);
    background: #F3F3F3;
    border: 1px solid rgba(0, 0, 0, 0.6);
}

.disable-btn {
    opacity: 0.4;
}

.send-text {
    margin-top: -10px;
}

.upload-heading {
    padding-left: 10px;
    padding-top: 10px;
    font-size: 14px;
}

.doc-fxrow-container {
    width: 100%;
    margin-top: 2%;
    height: 215px;
    overflow-y: scroll;
    overflow-x: hidden;
}

.doc-fxrow-container::-webkit-scrollbar {
    display: none;
}

.doc-fxrow-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.doc-body1 {
    width: 100%;
    height: 100px;
    overflow-y: scroll;
    overflow-x: hidden;
}

.doc-body1::-webkit-scrollbar {
    display: none;
}

.doc-body1 {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.mat-card-doc {
    padding: 0px;
    width: fit-content;
    height: fit-content;
}

.doc-icon-container {
    background-color: #3a3a3a;
    height: 86px;
    width: 154px;
    border-radius: 5px;
    display: flex;
    justify-content: center;
}

.doc-image-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.dropzone {
    height: 25px;
    display: table;
    width: 250px;
    border: 1px dashed #aaa;
    border-radius: 5px;
    margin-left: 10px;
    margin-bottom: 15px;
    margin-right: 10px;
}

.addfile-text-wrapper {
    width: calc(30% - 6px);
    height: 50px;
    display: table-cell;
    vertical-align: middle;
}

input[type="file"] {
    display: none;
}

.upload-scope-container {
    height: max-content;
    width: 100%;
    text-align: center;
}

.upload-label {
    color: rgb(170, 170, 170);
}

.upload-label>span {
    font-size: small;
    font-weight: normal;
    padding-top: 5px;
}

.doc-caption {
    background-color: rgb(255, 255, 255);
    height: fit-content;
    // padding: 13px;
}

.doc-caption>p {
    height: 15px;
    width: 94px;
    padding: 0;
    overflow: hidden;
    position: relative;
    display: inline-block;
    margin: 0 5px 0 5px;
    text-align: center;
    font-size: 12px;
    text-decoration: none;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #000;
    font-weight: 700;
}

.footer {
    height: 63px;
    width: 100%;
}

.footer-btns {
    padding: 15px 0px;
    margin-right: 10px;
}

.cancel-btn {
    height: 35px;
    color: #5A6F84;
    background: #CFD7DF;
    border-radius: 12px;
}

.upload-btn {
    height: 35px;
    color: #FFFFFF;
    background: #0088CB;
    border-radius: 12px;
}

::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: #f5afaf;
}

:host ::ng-deep .mat-checkbox .mat-checkbox-checkmark-path {
    stroke: #e00346 !important;
}

.textarea-field {
    width: 100% !important;
    height: 215px;
    max-width: 100%;
    box-sizing: border-box;
    border-radius: 4px;
    margin-bottom: 25px;
    padding-bottom: 20px;
}

.textarea-field1 {
    width: 100% !important;
    height: 190px;
    max-width: 100%;
    box-sizing: border-box;
    border-radius: 4px;
    margin-bottom: 25px;
}

.textarea-field2 {
    width: 100% !important;
    height: 230px;
    max-width: 100%;
    box-sizing: border-box;
    border-radius: 4px;
}

:host ::ng-deep .ql-container.ql-snow {
    border: 1px solid #fff;
    // height: 80%;
}

:host ::ng-deep .ql-toolbar.ql-snow {
    border: 1px solid #fff;
    border-bottom: 1px solid #ccc;
    box-sizing: border-box;
    font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
    padding: 8px;
}

.attach_btn {
    background-color: white;
    border: 1px solid white;
    z-index: 1;
    position: fixed;
    margin-left: 475px;
    margin-top: 54px;
}

.attach_btn1 {
    background-color: white;
    border: 1px solid white;
    z-index: 1;
    position: fixed;
    margin-left: 475px;
    margin-top: 25px;
}