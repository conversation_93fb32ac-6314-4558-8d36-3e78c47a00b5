<div class="icons">
    <mat-sidenav-container class="mat-sidenav-container">
        <mat-sidenav class="sidenav-fixed" mode="side" opened position="start">
            <div class="logo-container" fxLayoutAlign="center center">
                <img src="../assets/images/logo.png">
            </div>
            <div class="icon-container" *ngIf="userInfo.roleId == 4 || userInfo.roleId == 5 || userInfo.roleId == 7 || userInfo.roleId == 8">
                <div class="btn-container" on-mouseover='over()' [ngClass]="{'activeclass' : pagename === 'Dashboard'}">
                    <button mat-icon-button (click)="dashboard()">
                        <img class="hide-icon" [ngClass]="{'showclass' : pagename !== 'Dashboard'}"
                            src="../assets/images/dashboard-icon.svg" style="bottom: 2px;
                        left: 1px;" />
                        <img class="show-icon1" [ngClass]="{'showclass' : pagename === 'Dashboard'}"
                            src="../assets/images/dashboard_white-icon.svg" style="bottom: 10px;" />
                    </button>
                    <span class="tooltip-span">Dashboard</span>
                </div>
                <div class="btn-container" on-mouseover='over()' [ngClass]="{'activeclass' : pagename === 'Dashboard'}" *ngIf="userInfo.roleId == 5 && userInfo.industryMemberRoleId == 1">
                    <button class="active-btn" mat-icon-button (click)="showIntraAdminPage()">
                        <img class="hide-icon" [ngClass]="{'showclass' : pagename !== 'Administration'}"
                            src="../assets/images/Admin-icon.svg" style="bottom: 2px; left: 0px;" />
                        <img class="show-icon" [ngClass]="{'showclass' : pagename === 'Administration'}"
                            src="../assets/images/Admin_white-icon.svg" style="top: 12px; left: 10px;" />
                    </button>
                    <span class="tooltip-span">Administration</span>
                </div>
                <div class="btn-container" [ngClass]="{'activeclass' : pagename === 'Archive'}">
                    <button mat-icon-button (click)="dataMigration()">
                        <img class="hide-icon2" [ngClass]="{'showclass' : pagename !== 'Archive'}"
                            src="../../assets/images/archive.svg" style="bottom: 2px; left: 0px;" />
                        <img class="show-icon2" [ngClass]="{'showclass' : pagename === 'Archive'}"
                            src="../../assets/images/archive_white.svg" style="top: 12px; left: 12px;" />
                    </button>
                    <span class="tooltip-span">Archive</span>
                </div>
            </div>
            <div class="icon-container" *ngIf="userInfo.roleId == 1 || userInfo.roleId == 2 || userInfo.roleId == 3 || userInfo.roleId == 6">
                <div class="btn-container" on-mouseover='over()' [ngClass]="{'activeclass' : pagename === 'Dashboard'}">
                    <button mat-icon-button (click)="dashboard()">
                        <img class="hide-icon" [ngClass]="{'showclass' : pagename !== 'Dashboard'}"
                            src="../assets/images/dashboard-icon.svg" style="bottom: 2px;
                        left: 1px;" />
                        <img class="show-icon1" [ngClass]="{'showclass' : pagename === 'Dashboard'}"
                            src="../assets/images/dashboard_white-icon.svg" style="bottom: 10px;" />
                    </button>
                    <span class="tooltip-span">Dashboard</span>
                </div>
                <div class="btn-container" [ngClass]="{'activeclass' : pagename === 'Manage'}">
                    <button mat-icon-button (click)="manageCases()">
                        <img class="hide-icon" [ngClass]="{'showclass' : pagename !== 'Manage'}"
                            src="../assets/images/manage-icon.svg" style="bottom: 1px; left: 1px;" />
                        <img class="show-icon" [ngClass]="{'showclass' : pagename === 'Manage'}"
                            src="../assets/images/manage_white-icon.svg" />
                    </button>
                    <span class="tooltip-span">Manage cases</span>
                </div>
                <div class="btn-container" [ngClass]="{'activeclass' : pagename === 'Inbox'}">
                    <button mat-icon-button (click)="inbox()">
                        <img class="hide-icon" [ngClass]="{'showclass' : pagename !== 'Inbox'}"
                            src="../assets/images/inbox-icon.svg" style="bottom: 2px;
                        left: 0px;" />
                        <img class="show-icon" [ngClass]="{'showclass' : pagename === 'Inbox'}"
                            src="../assets/images/inbox_white-icon.svg" style="top: 12px; left: 12px;" />
                    </button>
                    <span class="tooltip-span">Inbox</span>
                </div>
                <div class="nams-btn-container" [ngClass]="{'activeclass' : pagename === 'Third Party Inbox'}">
                    <button class="active-btn" mat-icon-button>
                        <img class="hide-icon" [ngClass]="{'showclass' : pagename !== 'Third Party Inbox'}"
                            src="../assets/images/third_party-icon.svg" style="bottom: 2px; left: 0px;" />
                        <img class="show-icon" [ngClass]="{'showclass' : pagename === 'Third Party Inbox'}"
                            src="../assets/images/third_party_white-icon.svg" style="top: 12px; left: 12px;" />
                    </button>
                    <span class="tooltip-span" style="top: 242px;left: -41px;">NAMS cases&#13;<input type="button"
                            class="nams-btn" (click)="nams()" value="NAMS - TAMS" /> <br><input type="button"
                            class="nams-btn" value="NAMS - REECH" (click)="namsReech()" />
                    </span>
                </div>
                <div class="btn-container" [ngClass]="{'activeclass' : pagename === 'Tasks'}">
                    <button mat-icon-button (click)="taskTable()">
                        <img class="hide-icon" [ngClass]="{'showclass' : pagename !== 'Tasks'}"
                            src="../assets/images/tasks-icon.svg" style="bottom: 2px; left: 0px;" />
                        <img class="show-icon" [ngClass]="{'showclass' : pagename === 'Tasks'}"
                            src="../assets/images/tasks_white-icon.svg" />
                    </button>
                    <span class="tooltip-span">Tasks</span>
                </div>
                <div class="nams-btn-container" [ngClass]="{'activeclass' : pagename === 'userManagement'}"
                    *ngIf="userInfo.roleId == 1 || userInfo.roleId == 2">
                    <button class="active-btn" mat-icon-button>
                        <img class="hide-icon" [ngClass]="{'showclass' : pagename !== 'userManagement'}"
                            src="../assets/images/Admin-icon.svg" style="bottom: 2px; left: 0px;" />
                        <img class="show-icon" [ngClass]="{'showclass' : pagename === 'userManagement'}"
                            src="../assets/images/Admin_white-icon.svg" style="top: 12px; left: 12px;" />
                    </button>
                    <span class="tooltip-span" style="top: 360px;left: -41px;">Administration&#13;<input type="button"
                            class="nams-btn" (click)="userManagement()" value="User Management" /> <br><input
                            type="button" class="nams-btn" (click)="memberManagement()"
                            value="Member Management" /><br><input type="button" class="nams-btn"
                            value="Field Management" (click)="fieldManagement()" />
                    </span>
                </div>
                <div class="btn-container" [ngClass]="{'activeclass' : pagename === 'Meeting'}">
                    <button mat-icon-button (click)="meeting()">
                        <img class="hide-icon" [ngClass]="{'showclass' : pagename !== 'Meeting'}"
                            src="../assets/images/Calendar-icon.svg" style="bottom: 2px; left: 0px;" />
                        <img class="show-icon" [ngClass]="{'showclass' : pagename === 'Meeting'}"
                            src="../assets/images/Calendar_white-icon.svg" style="top: 11px; left: 13px;" />
                    </button>
                    <span class="tooltip-span">Meeting</span>
                </div>
                <div class="btn-container" [ngClass]="{'activeclass' : pagename === 'Archive'}">
                    <button mat-icon-button (click)="dataMigration()">
                        <img class="hide-icon2" [ngClass]="{'showclass' : pagename !== 'Archive'}"
                            src="../../assets/images/archive.svg" style="bottom: 2px; left: 0px;" />
                        <img class="show-icon2" [ngClass]="{'showclass' : pagename === 'Archive'}"
                            src="../../assets/images/archive_white.svg" style="top: 12px; left: 12px;" />
                    </button>
                    <span class="tooltip-span">Archive</span>
                </div>
            </div>
        </mat-sidenav>
        <mat-sidenav-content>
            <router-outlet></router-outlet>
        </mat-sidenav-content>
    </mat-sidenav-container>
</div>