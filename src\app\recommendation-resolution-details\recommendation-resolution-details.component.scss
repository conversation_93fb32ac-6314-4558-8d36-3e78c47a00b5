.close-div {
    position: relative;
    bottom: 9px;
}
.close-btn {
    color: solid rgba(47, 57, 65, 0.6);
    background: #F3F3F3;
    border: 1px solid rgba(47, 57, 65, 0.6);
}
.input-field {
    width: 100%;
}
.recommendations {
    overflow-y: scroll;
    overflow-x: hidden;
    height: 384px;
    margin-left: 14px;
}
.recommendations::-webkit-scrollbar {
    display: none;
}
.recommendations {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.recommendation-box {
    background: #F5F5F5;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 25%);
    border-radius: 4px;
    margin-top: 26px;
    padding: 11px;
    width: 100%;
    box-sizing: border-box;
}
.box-heading {
    color: rgba(0, 0, 0, 0.4);
    font-style: italic;
}