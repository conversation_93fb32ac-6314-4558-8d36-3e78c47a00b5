.main-container {
  width: 100%;
  /* height: 95vh; */
  position: relative;
}
.resend-btn {
    border: none;
    color: black;
    margin-bottom: 16px;
    background: none;
}
.resend-btn[disabled] {
  opacity: 0.3;
  cursor: not-allowed !important;
}
.background-image-container {
  position: fixed;
  z-index: -10;
  width: 100%;
  top: 0;
  background-image: url("../../../assets/images/login-background.png");
  height: 100%;
/* Center and scale the image nicely */
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
.content-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 95vh;
}
.heading-container {
  width: 100%;
}
.heading-container .heading,
.heading-container .logo-container {
  width: 50%;
}
.heading-container .heading-text {
  color: #253858;
  margin: 0 10px;
}
.heading-font-size {
  font-size: 20px;
}
.contest-img{
  width: 75%;
  min-width: 250px;
  max-width: 500px;
}
.headline-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-left: 15%;
}
.headline-text {
  font-size: 45px;
  line-height: 60px;
  color: #ffffff;
}
.tab-heading {
  font-size: 20px;
  line-height: 27px;
}
.track-tab-text {
  color: rgba(37, 56, 88, 0.6);
}
.trackComplaintTab {
  position: inherit;
}
.login-box {
  background: #FFFFFF;
  border-radius: 4px;
  height: 538px;
  position: absolute;
  right: 177px;
  width: 449px;
  padding-left: 15px;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  margin-top: 10%;
}
.login-container {
  margin-left: 30px;
  margin-top: 34px;
}
.login-head {
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 32px;
  color: #000000;
  margin-left: 10px;
}
.signup-link, .login-link {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 18px;
  color: #ED2F45;
  cursor: pointer;
}
.theme-next-button {
  background: #ED2F45;
  border-radius: 12px;
  font-style: normal;
  font-weight: 600;
  margin-left: 107px;
  font-size: 16px;
  line-height: 21px;
  text-align: center;
  color: #FFFFFF;
  width: 109px;
  height: 41px;
  border: none;
}
.divider1 {
  margin-top: 30px;
  width: 326px;
}
.divider {
  margin-top: 52px;
  width: 163px;
}
.divider-or {
  margin-top: 39px;
  margin-left: 6px;
  margin-right: 3px;
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 21px;
  text-align: right;
  color: rgba(0, 0, 0, 0.2);
}
.track-complaint, .back-login , .register-change,.number-change{
  background: #CFD7DF;
  border-radius: 12px;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 21px;
  text-align: center;
  color: #5A6F84;
  border: none;
  width: 369px;
  height: 37px;
  margin-top: 15px;
  cursor:pointer;
}
.login-content-container {
  background-color: #ffffff;
  padding-bottom: 25px;
  border-radius: 0px 5px 5px 5px;
}
.forgot-content-container {
  background-color: #ffffff;
  padding-top: 25px;
  border-radius: 5px 5px 5px 5px;
}
 .createuser-content-container select{
  padding-right: 30px;
  color:grey;
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  background-image: url(/assets/images/dropdown.png);
  background-repeat: no-repeat, repeat;
  background-position: right 1.1em top 50%, 0 0;
  background-size: 0.80em auto, 100%;
}
.createuser-content-container {
  background-color: #ffffff;
  padding-top: 25px;
  overflow:hidden;
  height: 580px;
  width: 34%;
  padding-right: 15px;
  padding-left: 15px;
  border-radius: 5px 5px 5px 5px;
  position: absolute;
  right: 162px;
  bottom: 0px;
  top: 5%;
}
.verification-content-container{
  padding-top: 14px;
  padding-left: 16px;
  border-radius: 5px 5px 5px 5px;
  background: #FFFFFF;
}
.reset-content-container{
  padding-top: 17px;
  overflow: hidden;
  padding-left: 9px;
  padding-right: 9px;
  padding-bottom: 163px;
  border-radius: 5px 5px 5px 5px;
  background: #ffffff;
}
.tara-icon {
  width: 210px;
  background: transparent;
  margin-left: 80px;
}
.track-box {
  background-color: #ffffff;
  border-radius: 0px 5px 5px 5px;
}
.tab-button {
  background: #ffffff;
  box-shadow: 10px -15px 20px rgba(0, 0, 0, 0.25);
  border-radius: 4px 4px 0px 0px;
  border: 0;
  font-weight: 600;
  font-size: 20px;
  line-height: 27px;
  color: #253858;
}
.tabs {
  margin-top: 0px !important;
}
.host ::ng-deep .tabs.mat-tab-header {
  border-bottom: 0px;
}
.left {
  margin-right: 2%;
}
.inputs-container::-webkit-scrollbar {
  display: none;
}
.inputs-container {
  padding: 25px;
  overflow: scroll;
  height: 85%;
  overflow-x: hidden;
}
.input-container {
  /* margin: 10px; */
  margin: 15px 10px;
  margin-bottom: 0px !important;
  min-width: 350px;
}
.login-spinner{
  height: 70px;
  left: 50%;
  position: absolute;
  top: 26%;
  transform: translate(-50%,-50%);
  width: 70px;
  z-index: 3;
  opacity: 0.5;
  border-radius: 10px;
}
.confirm-password-error,
.match-error {
  margin-left: 13px;
  color: #ED2F45;
}
.password-error {
  margin-left: 17px;
  color: #ED2F45;
}
.btn-box-container {
  margin: 15px 10px;
}
.login-btn-container {
  min-width: 250px;
}
.login-tab-container,
.track-tab-container {
  border: 1px dashed rgb(172, 96, 9);
  height: 61px;
}
.forgot-head {
  margin-top: 25px;
  margin-left: 36px;
}
.tab-button {
  padding: 15px;
}
.hyperlink {
  cursor: pointer;
}
.verify-text-container {
  margin: 25px 0;
}
.verify-text-container > p {
  /* border: 1px solid rgb(180, 20, 20); */
  width: 360px;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 19px;
  color: #2f3941;
  /* margin: 15px 10px; */
}
.verify-input-container {
  /* width: 50px; */
  width: 46px;
  height: 50px;
  /* background: #FAFAFA; */
  /* background: #ee3a3a; */
  /* border: 1px solid #D8DCDE; */
  box-sizing: border-box;
  border-radius: 4px;
  margin: 15px 10px;
}
:host ::ng-deep .mat-tab-label {
  /*  , ::ng-deep.mat-tab-label-active  ADJUST MAT TAB LABEL WIDTH */

  /* background-color:rgb(204, 204, 204); */
  width: fit-content !important;
  opacity: 1 !important;
  /* background: rgba(255, 255, 255, 0.8); */
  /* background: rgba(134, 134, 134, 0.6); */
  /* background: rgba(107, 107, 107, 0.6); */
  background-color: rgb(255, 255, 255, 0.8);
  min-width: 0px !important;
  /* padding: 15px !important; */
  border-radius: 5px 5px 0px 0px;
  margin-right: 4px !important;
  box-shadow: 10px -15px 20px rgba(0, 0, 0, 0.25);
  margin-bottom: 0px;
}
:host ::ng-deep .mat-tab-label-active {
  opacity: 1 !important;
  /* background-color: rgb(255, 255, 255); */
  background-color: white;
  border-radius: 5px 5px 0px 0px;
}
:host ::ng-deep .mat-ink-bar {
  display: none !important;
}
.ng-invalid.ng-touched {
  border-color: red;
}
.red-border-class {
  border-color: red;
}
.spinner-wrapper {
  display: flex;
  top:50%;
  left: 25%;
}
.pswd-leng{
  border: 1px solid black;
  font-size: 10px;
}
.verify-min-width{
  min-width: 383px;
}
@media only screen and (max-width: 389px) {
  .headline-container {
    margin-left: 0;
    align-self: start;
  }
  .login-box-container {
    margin-left: 0;
    /* height: 100%;
    width: 90%; */
  }
  .chatbot-container{
    /* left: 57%;
    bottom: 1%;
    position: absolute; */
    /* left: 99%;
    bottom: 1%;
    position: absolute; */
    /* position: absolute;
    left: 110%;
    bottom: 3%; */
  }
  .contest-div1 {
    display: none;
  }
}
@media only screen and (max-width: 389px) {
  .chatbot-container{
    /* left: 57%;
    bottom: 1%;
    position: absolute; */
    /* left: 99%;
    bottom: 1%;
    position: absolute; */
    position: absolute;
    left: 110%;
    bottom: 3%;
  }
  .contest-div1 {
    display: none;
  }
}

@media only screen and (max-width: 1250px) {
  .headline-container {
    margin-left: 0;
    align-self: start;
    padding: 10% 5% 5% 5%;
  }
}
@media only screen and (max-width: 1250px) {
.main-container {
  width: 100%;
  /* height: 83vh; */
}
.flex {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.number-change{
  margin-top: 0px;
  /* padding-top: 8px;  */
  /* width: 97.5%; */
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.tara-icon {
  width: 182px;
  margin-left: 0;
}
.createuser-content-container {
  display: flex;
  flex-direction: column;
  align-content: center;
  width: 95%;
  height: 600px;
  overflow: hidden;
  /* margin-right: 3px; */
  margin: 0;
  /* position: relative;
  left: 41px;
  bottom: 9px; */
  position: static;
}

.createuser-content-container .input-container {
  padding: 0px 4px;
}
.content-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100% !important;
  height: auto;
  gap: 1%;
}
.logo-img{
  width: 280px;
}
.login-box-container {
  margin-left: 0;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  /* margin: auto; */
  height: 100%;
  position: relative;
}
.mobile-register-box {
  background: #FFFFFF;
  border-radius: 4px;
  height: 500px;
  width: 94%;
  padding: 30px 1%;
  position: absolute;
}
.mobile-login-box{
  background: #FFFFFF;
  border-radius: 4px;
  height: 500px;
  /* width: 95%; */
  padding: 30px 7%;
  z-index: 3;
  position: absolute;
}
.login-container {
  margin-left: 0;
  margin-top: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
}
.forgot-head {
  margin-top: 0;
  margin-left: 0;
}
.theme-next-button {
  margin-left: 0;
  height: 41px;
  padding: 10px 3%;
}
.inputs-container {
  padding: 25px;
}
.input-container {
  /* margin: 15px 10px; */
  margin-bottom: 0px !important;
  min-width: 350px;
}
.verify-input-container {
  width: 46px;
  height: 50px;
  box-sizing: border-box;
  border-radius: 4px;
  margin: 0;
}
.btn-container{
  padding: 20px 0;
  min-width: 100%;
}
.resend-btn {
  margin-bottom: 6px;
}
.change-no-btn {
  border: none;
  color: black;
  background: none;
  text-align: left !important;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 19px;
  padding-left: 0 !important;
  margin-bottom: 21px;
}
.chatbot-container{
  /* left: 279px;
  position: relative; */
    left: 95%;
    bottom: 5%;
    position: absolute;
  }
}
@media only screen and (max-width: 480px) {
  .main-container {
    width: 95vw;
    /* height: 83vh; */
  }
  .content-container {
    width: 100% !important;
    display: flex;
    /* padding-left: 10px; */
    /* padding: 0 2%; */
    /* padding-left: 10%; */
  }
  .headline-container{
    align-self: flex-start;
    justify-self: flex-start;
  } 
  .login-box{
    width: 100%;
  }
  .login-container {
    margin-left: 0;
    margin-top: 0;
    display: flex;
    flex-direction: column;
  }
  .login-box-container{
    padding-left: 10px;
  }
  .mobile-login-box{
    background: #ffffff;
    /* height: 470px; */
    /* width: 380px; */
    width: 75%;
    padding: 20px 10px;
    margin-left: 2%;
    position: absolute;
  }
  .contest-div1 {
    display: none;
  }
  .input-container{
    min-width: 100%;
  }
  .verify-min-width{
    padding: 1%;
    min-width: min-content;
  }
  
.createuser-content-container {
  padding-top: 25px;
  /* height: 600px; */
  /* width: min-content; */
  width: 85%;
  /* padding-right: 1%; */
  padding-left: 1%;
  position: static;
  /* right: 162px;
  bottom: 0px; */
  }
  .password-error {
    padding-left: 10px;
  }
  .chatbot-container{
    /* right: 100px; */
    left: 99%;
    bottom: 1%;
    position: absolute;
  }
}

@media only screen and (max-width: 560px) {

  .content-container {
    width: 100% !important;
    display: flex;
  }
  .headline-container{
    justify-items: flex-start;
  }
  .chatbot-container{
    left: 95%;
    bottom: 5%;
    position: absolute;
  }
  .contest-div1 {
    display: none;
  }
}

@media only screen and (max-width: 420px) {

  .content-container {
    display: flex;
    /* padding-left: 40px; */
  }
  
  .login-box-container{
    padding-left: 2%;
  }

  .input-container{
    min-width: 90%;
  }
  
  .mobile-login-box{
    background: #FFFFFF;
    padding: 20px;
    /* margin-left: 5%; */
    position: absolute;
  }
  
  .createuser-content-container {
    padding-top: 25px;
    width: 85%;
    padding-left: 1%;
    margin-left: 5%;
    position: static;
    /* right: 162px;
    bottom: 0px; */
  }
  
  .mobile-register-box {
    background: #FFFFFF;
    /* height: 500px; */
    width: 85%;
    /* padding: 30px 1%; */
    margin-left: 8%;
    position: absolute;
  }

  .verify-min-width{
    padding: 1%;
    min-width: min-content;
  }

  .chatbot-container{
    position: absolute;
    left: 105%;
    bottom: 3%;
  }

  .contest-div1 {
    display: none;
  }

}

@media only screen and (max-width: 360px) {
  .btn-container{
    padding-bottom: 0;
  }
  /* .mobile-login-box{
    margin-left: 0;
  } */
  .createuser-content-container {
    padding-top: 25px;
    width: 85%;
    padding-left: 1%;
    margin-left: 10%;
    position: static;
    
    /* right: 162px;
    bottom: 0px; */
  }
  /* .login-container{ 
    padding: 1%;
  } */
  .contest-div1 {
    display: none;
  }
}

@media screen and (min-width: 1025px) and (max-width: 1250px) {
  .mobile-login-box{
    background: #FFFFFF;
    border-radius: 4px;
    height: 500px;
    padding: 30px 3%;
    z-index: 3;
    position: absolute;
    margin-left: 40%;
  }
  .contest-img{
    width: 40%;
    margin-left: 20%;
  }
  .contest-div2 {
    display: none;
  }
}

@media only screen and (max-width: 1024px) {
  .mobile-login-box{
    background: #FFFFFF;
    border-radius: 4px;
    height: 500px;
    /* width: 42%; */
    padding: 30px 3%;
    z-index: 3;
    position: absolute;
    /* margin-left: -18%;
    margin-bottom: 8%; */
  }
  .contest-img{
    margin: 5% 0;
  }
  .contest-div1 {
    display: none;
  }
  .contest-div2 {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
}