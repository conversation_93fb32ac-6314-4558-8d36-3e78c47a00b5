<div fxLayout="column" class="page">
  <div class="body">
    <div class="comp-head" *ngIf="!isMobileScreen">
      COMPLAINANT DETAILS
    </div>
    <div class="comp-head-mandatory" *ngIf="!isMobileScreen">
      * labeled fields are mandatory
    </div>
    <form [formGroup]="step2Form" class="form">
      <div class="step2-consumer-container" style="padding-top: 21px;">

        <div class="row-container">
          <div class="input-container">
            <div class="text-container">
              <p>Advertiser's Company<span style="color: red;">*</span></p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <input matInput type="text" formControlName="company" [matAutocomplete]="autoCompany"
                  (blur)="companyInput()" (input)="companyChange()">
                <mat-autocomplete #autoCompany="matAutocomplete" (optionSelected)="onSelectionChange($event)">
                  <mat-option *ngFor="let company of companyList;" [value]="company.COMPANY_NAME" style="
                  font-style: normal;
                  font-weight: normal;">
                    {{company.COMPANY_NAME}}
                  </mat-option>
                </mat-autocomplete>
                <mat-error class="error-msg" *ngIf="step2Form.controls['company'].errors?.required">
                  Company Name is required
                </mat-error>
              </mat-form-field>
            </div>
          </div>
          <div class="input-container">
            <div class="text-container">
              <p>Brand Name of Product/Service <span style="color: red;">*</span></p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <input matInput formControlName="brand" autocomplete="off">
                <mat-error class="error-msg" *ngIf="step2Form.controls['brand'].errors?.required">
                  Brand Name is required
                </mat-error>
              </mat-form-field>
            </div>
          </div>
          <div class="input-container">
            <div class="text-container">
              <p>Product Name <span style="color: red;">*</span></p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <input matInput formControlName="product" autocomplete="off">
                <mat-error class="error-msg" *ngIf="step2Form.controls['product'].errors?.required">
                  Product Name is required
                </mat-error>
              </mat-form-field>
            </div>
          </div>
        </div> <!-- row container -->

        <div class="row-container">
          <div class="input-container">
            <div class="text-container">
              <p>Product category</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <input matInput formControlName="product_category" autocomplete="off">
              </mat-form-field>
            </div>
          </div>
          <div class="input-container">
            <div class="text-container" *ngIf="!isMobileScreen">
              <p>Where did you spot the Advertisement <span style="color: red;">*</span></p>
            </div>
            <div class="text-container" *ngIf="isMobileScreen">
              <p>Where did you spot the Advertisement <span style="color: red;">*</span></p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <mat-select formControlName="seen_medium">
                  <mat-option *ngFor="let source of ads" (click)="changeAdvType(source)" [value]="source.ID">
                    {{source.ADVERTISEMENT_SOURCE_NAME}}
                  </mat-option>
                </mat-select>
                <mat-error class="error-msg" *ngIf="step2Form.controls['seen_medium'].errors?.required">
                  Please Choose the Advertisement Source Name
                </mat-error>
              </mat-form-field>
            </div>
          </div>
          <div class="input-container" *ngIf="selectedAdvId == 3">
            <div class="text-container">
              <p>Please specify the platform <span style="color: red;">*</span></p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <mat-select formControlName="platform">
                  <mat-option *ngFor="let plat of platforms" [value]="plat.ID" (click)="changePlatform(plat)">
                    {{plat.PLATFORM_NAME}}</mat-option>
                </mat-select>
                <mat-error class="error-msg" *ngIf="step2Form.controls['platform'].errors?.required">
                  Please Choose platform
                </mat-error>
              </mat-form-field>
            </div>
          </div>
        </div>

        <div class="row-container">
          <div class="input-container" *ngIf="selectedAdvId == 3 && platform_id == 9">
            <div class="text-container">
              <p>Specify the platform name <span style="color: red;">*</span> </p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <input matInput formControlName="sourcePlace" autocomplete="off">
                <mat-error class="error-msg" *ngIf="step2Form.controls['sourcePlace'].errors?.required">
                  Please Specify the platform
                </mat-error>
              </mat-form-field>
              <!-- <mat-error class="error-msg" *ngIf=" step2Form.controls['sourcePlace'].value == '' && (step2Form.controls['sourcePlace'].dirty ||step2Form.controls['sourcePlace'].touched)" style="margin-top: -22px;font-size: 11px;">
                Please Specify the platform
            </mat-error> -->
            </div>
          </div>
          <div class="input-container"
            *ngIf="selectedAdvId == 1 || selectedAdvId == 2 || selectedAdvId == 5 || selectedAdvId == 6 || selectedAdvId == 8 || selectedAdvId == 9"
            style="margin-right: 26px;">
            <div class="text-container">
              <p *ngIf="selectedAdvId == 1 || selectedAdvId == 2">Please specify the channel <span
                  style="color: red;">*</span></p>
              <p *ngIf="selectedAdvId == 5" style="width: 230px;position: relative;bottom: 13px;">Where have you seen
                the advertisement?<span style="color: red;">*</span></p>
              <p *ngIf="selectedAdvId == 6">Type of material <span style="color: red;">*</span></p>
              <p *ngIf="selectedAdvId == 8">Sender <span style="color: red;">*</span></p>
              <p *ngIf="selectedAdvId == 9">Source <span style="color: red;">*</span></p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field"
                *ngIf="!(selectedAdvId == 5 || selectedAdvId == 6)">
                <input matInput formControlName="channel" autocomplete="off">
                <mat-error class="error-msg"
                  *ngIf="step2Form.controls['channel'].errors?.required && (step2Form.controls['channel'].dirty ||step2Form.controls['channel'].touched) ">
                  Please Specify the Source
                </mat-error>
              </mat-form-field>
              <mat-form-field appearance="outline" class="input-field" *ngIf="selectedAdvId == 5">
                <mat-select formControlName="printSource">
                  <mat-option *ngFor="let print of printSources" [value]="print.ID">
                    {{print.PRINT_SOURCE_NAME}}</mat-option>
                </mat-select>
                <mat-error class="error-msg" *ngIf="step2Form.controls['printSource'].errors?.required">
                  Please Choose Source Name
                </mat-error>
              </mat-form-field>
              <mat-form-field appearance="outline" class="input-field" *ngIf="selectedAdvId == 6">
                <mat-select formControlName="promotionType">
                  <mat-option *ngFor="let promType of promotionTypes" [value]="promType.ID">
                    {{promType.P_M_SOURCE_NAME}}</mat-option>
                </mat-select>
                <mat-error class="error-msg" *ngIf="step2Form.controls['promotionType'].errors?.required">
                  Please Choose the type of material
                </mat-error>
              </mat-form-field>
              <!-- <mat-error class="error-msg" *ngIf=" selectedAdvId == 6 && step2Form.controls['promotionType'].value == '' && (step2Form.controls['promotionType'].dirty ||step2Form.controls['promotionType'].touched)" style="margin-top: -22px;font-size: 12px;">
                Please Choose the type of material
            </mat-error> -->
            </div>
          </div>
          <div class="input-container"
            *ngIf="selectedAdvId == 4 || selectedAdvId == 5 || selectedAdvId == 6 || selectedAdvId == 9">
            <div class="text-container">
              <p *ngIf="selectedAdvId == 4" style="width: 234px;position: relative;bottom: 14px;">Name the place where
                you saw the hoarding? <span style="color: red;">*</span>
              </p>
              <p *ngIf="selectedAdvId == 5">Specify the publication name & edition <span style="color: red;">*</span>
              </p>
              <p *ngIf="selectedAdvId == 6">Name the place where you saw the Ad? <span style="color: red;">*</span></p>
              <p *ngIf="selectedAdvId == 9">Place if applicable</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field" *ngIf="!(selectedAdvId == 5)">
                <input matInput formControlName="sourcePlace" autocomplete="off">
                <mat-error class="error-msg" *ngIf="step2Form.controls['sourcePlace'].errors?.required">
                  Please Specify Source Name
                </mat-error>
              </mat-form-field>
              <mat-form-field appearance="outline" class="input-field" *ngIf="selectedAdvId == 5">
                <input matInput formControlName="channel" autocomplete="off">
                <mat-error class="error-msg" *ngIf="step2Form.controls['channel'].errors?.required">
                  Please Specify Source Name
                </mat-error>
              </mat-form-field>
            </div>
          </div>
          <div class="input-container">
            <div class="text-container">
              <p *ngIf="selectedAdvId != 7 && selectedAdvId != 2">Date on which the ad was seen <span
                  style="color: red;">*</span></p>
              <p *ngIf="selectedAdvId == 7">MFD/PKD Date <span style="color: red;">*</span></p>
              <p *ngIf="selectedAdvId == 2">Date on which the ad was heard <span style="color: red;">*</span></p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <input matInput [matDatepicker]="picker" formControlName="date" [max]="maxDate" autocomplete="off">
                <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
                <mat-error class="error-msg" *ngIf="step2Form.controls['date'].errors?.required">
                  Please select the date
                </mat-error>
              </mat-form-field>
            </div>
          </div>
          <div class="input-container" *ngIf="selectedAdvId == 1 || selectedAdvId == 2 || selectedAdvId == 3">
            <div class="text-container">
              <p>What was the time<span style="color: red;">*</span></p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <input matInput class="time" placeholder="{{selectedTime}}" formControlName="time" matTimepicker
                  autocomplete="off">
                <span matSuffix><img src="../../../assets/images/Clock.png" style="position: relative; top: -7px;"
                    matTimepicker></span>
                <mat-error class="error-msg" *ngIf="step2Form.controls['time'].errors?.required">
                  Please select the time
                </mat-error>
              </mat-form-field>
            </div>
          </div>
        </div> <!-- row container -->

        <!-- <div class="row-container">
          <div class="input-container">
            <div class="text-container">
              <p>Contact involvement</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <mat-select formControlName="contact_involved">
                  <mat-option value="television">Self</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
          <div class="input-container">
            <div class="text-container">
              <p>
                Reference <mat-icon class="input-icon">error_outline</mat-icon>
              </p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <input matInput formControlName="reference" required>
              </mat-form-field>
            </div>
          </div>
        </div> -->

        <!-- <div class="row-container" [ngClass]="isMobileScreen ? 'flex' : 'non-flex'"> -->
        <div class="row-container">

          <div class="input-container" *ngIf="!isMobileScreen">
            <div class="text-container">
              <p>Do you have a copy of the advertisement and/or other supporting documentation?</p>
            </div>
            <div class="control-container" fxLayout="row">
              <div class="upload-container">
                <button mat-button class="upload-btn" [class.spinner]="isUploadProgress" [disabled]="isUploadProgress"
                  for="doc_file" (click)="fileInput.click()">
                  <mat-icon style="font-size: large;">cloud_upload</mat-icon>&nbsp;Upload your file
                  <input type="file" formControlName="doc_file" class="doc_file" id="doc_file" #fileInput hidden
                    multiple="true"
                    accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt,.jpeg,.png,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv"
                    (change)="onFileSelected($event)" />
                </button>
              </div>
              <div fxFlex="5%"></div>
              <div class="url-container">
                <mat-form-field appearance="outline" style="width: 320px;">
                  <span matPrefix>
                    <span id="url-icon" class="glyphicon glyphicon-link"></span>&nbsp;&nbsp;
                  </span>
                  <input matInput type="url" formControlName="add_url" placeholder="Add url" autocomplete="off">
                  <!-- <mat-error *ngIf="add_url.hasError('url') && !add_url.hasError('required')">
                          Please enter a valid URL
                      </mat-error> -->
                </mat-form-field>
              </div> <!-- url-container -->
            </div>
          </div>

          <div fxLayout="column" fxLayoutGap="10px" *ngIf="isMobileScreen">

            <div class="inner-row-container" *ngIf="isMobileScreen">
              <div class="input-container" fxLayout="column" fxLayoutGap="23px">
                <div class="text-container">
                  <span>Do you have a copy of the advertisement and/or other supporting documentation? Please
                    upload/paste a link.
                  </span>
                </div>
                <div class="control-container">
                  <mat-form-field appearance="outline" class="url-field">
                    <span matPrefix class="url-container-text">
                      <img src="../../assets/images/url.png">
                      &nbsp;
                    </span>
                    <input matInput type="url" formControlName="add_url" placeholder="Add url" autocomplete="off">
                  </mat-form-field>
                </div>
              </div>
            </div>
            <div class="inner-row-container flex" *ngIf="isMobileScreen">
              <span style="color: rgba(0, 0, 0, 0.6);">or</span>
            </div>
            <div class="inner-row-container" *ngIf="isMobileScreen">
              <div class="example-boundary">
                <button mat-button class="upload-files-btn" [class.spinner]="isUploadProgress"
                  [disabled]="isUploadProgress" for="doc_file" (click)="fileInput.click()">
                  <div fxLayout="column" style="cursor: pointer;padding-top: 10px;">
                    <div fxLayout="column" fxLayoutGap="5px">
                      <div class="flex">
                        <img src="../../assets/images/upload-icon.png">
                      </div>
                      <div fxLayout="row" fxLayoutAlign="center center">
                        <p style="color: #0088CB;">Upload&nbsp;</p>
                        <p style="color: #707070;">your files here</p>
                        <!-- <p class="progress-spinner""
                          *ngIf="(filesAdvertiseProgress[in]?.progress > 0 && filesAdvertiseProgress[in]?.progress !== 100)">
                          <mat-spinner strokeWidth="3" [diameter]="20"></mat-spinner>
                        </p> -->
                      </div>
                    </div>
                    <input type="file" formControlName="doc_file" class="doc_file" id="doc_file" style="display: none"
                      #fileInput hidden multiple="true"
                      accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt,.jpeg,.png,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv"
                      (change)="onFileSelected($event)" />
                  </div>
                </button>
              </div>
            </div>

            <div class="file-progress-container" *ngIf="files_attached === 'Yes' && isMobileScreen">
              <div *ngFor="let videos of step2Form.controls['file_array'].value;let index = index" fxLayout="row"
                fxLayoutGap="5px" class="row-container filename-holder">
                <div fxFlex="95%" fxLayoutAlign="start" style="color: rgb(150, 148, 148);padding-left: 5px;">
                  <p class="adv-file-text">{{videos.ATTACHMENT_NAME}}</p>
                </div>
                <div fxFlex="5%" fxLayoutAlign="end" style="height: 33px;">
                  <button mat-button mat-icon-button (click)="preview(videos.ATTACHMENT_SOURCE)">
                    <img src="../../../assets/images/View.png" style="margin: 0px 10px 10px 10px;">
                  </button>
                </div>
                <div fxFlex="5%" fxLayoutAlign="end" style="height: 33px;">
                  <button mat-button mat-icon-button aria-label="Clear"
                    (click)="removeVideoFile(index, videos.ATTACHMENT_SOURCE)">
                    <img src="../../assets/images/close-red.png" style="margin: 0px 10px 10px 10px;">
                  </button>
                </div>
              </div>
              <section class="progress-bar-upload" *ngIf="fileProgress > 0">
                <mat-progress-bar [color]="'primary'" [value]="fileProgress">
                </mat-progress-bar>
              </section>
            </div>
          </div>

          <div class="input-container">
            <div class="text-container">
              <p>Initial classification <span style="color: red;">*</span></p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <mat-select formControlName="initial_class"
                  *ngIf="userInfo.roleId == 1 || userInfo.roleId == 2 || userInfo.roleId == 3 || userInfo.roleId == 6">
                  <mat-option *ngFor="let classify of classification" matTooltip="{{classify.CLASSIFICATION_NAME}}"
                    [value]="classify.ID">
                    <span>{{classify.CLASSIFICATION_NAME}}</span>
                  </mat-option>
                </mat-select>
                <mat-select formControlName="initial_class"
                  *ngIf="!(userInfo.roleId == 1 || userInfo.roleId == 2 || userInfo.roleId == 3 || userInfo.roleId == 6)">
                  <mat-option *ngFor="let classify of classification" matTooltip="{{classify.CLASSIFICATION_NAME}}"
                    [value]="classify.ID">
                    <span>{{classify.CLASSIFICATION_NAME}}</span>
                  </mat-option>
                </mat-select>
                <mat-error class="error-msg" *ngIf="step2Form.controls['initial_class'].errors?.required">
                  Please choose Initial Classification
                </mat-error>
              </mat-form-field>
            </div>
          </div>
        </div> <!-- row container -->

        <div *ngIf="files_attached === 'Yes' && !isMobileScreen">
          <div *ngFor="let videos of step2Form.controls['file_array'].value; let index = index" fxLayout="row"
            fxLayoutGap="5px" class="row-container filename-holder">
            <div fxFlex="95%" fxLayoutAlign="start" style="color: rgb(150, 148, 148);">
              <p class="adv-file-text">{{videos.ATTACHMENT_NAME}}</p>
            </div>
            <div fxFlex="5%" fxLayoutAlign="end" style="height: 33px;">
              <button mat-button mat-icon-button (click)="preview(videos.ATTACHMENT_SOURCE)">
                <img src="../../../assets/images/View.png" style="margin: 0px 10px 2px 10px;">
              </button>
            </div>
            <div fxFlex="5%" fxLayoutAlign="end" style="height: 33px;">
              <button mat-button mat-icon-button aria-label="Clear"
                (click)="removeVideoFile(index, videos.ATTACHMENT_SOURCE)" *ngIf="!isChabotEditForm">
                <img src="../../assets/images/close-red.png" style="margin: 0px 10px 2px 10px;">
              </button>
              <button mat-button mat-icon-button aria-label="Clear"
                (click)="removeChatbotFile(index, videos.ATTACHMENT_SOURCE)" *ngIf="isChabotEditForm">
                <img src="../../assets/images/close-red.png" style="margin: 0px 10px 2px 10px;">
              </button>
            </div>
            <!-- <div class="row-container filename-holder">
              <strong>{{videos.ATTACHMENT_NAME}}</strong>
            </div> -->
          </div>
          <section class="progress-bar-upload" *ngIf="fileProgress > 0">
            <mat-progress-bar [color]="'primary'" [value]="fileProgress">
            </mat-progress-bar>
          </section>
        </div>

        <div class="row-container" style="margin-top: 10px;" *ngIf="!isMobileScreen">
          <div class="input-container">
            <div class="text-container">
              <p>Describe the Advertisement (max 5000 characters)<span style="color: red;">*</span></p>
            </div>
            <mat-form-field appearance="outline" class="textarea-field">
              <textarea matInput formControlName="description" rows="3" maxlength="5000"
                placeholder="Tell us about the advertisement in brief... &#13;&#10;Eg: The advertisement shows two kids playing in mud and dirtying themselves, mother of one says don't worry you can take a bath with this soap and it has 10times more power to protect against disease causing bacteria and viruses."
                style="height: 150px; text-align:justify; text-justify: inter-word;" autocomplete="off"></textarea>
              <mat-error class="error-msg" *ngIf="step2Form.controls['description'].errors?.required">
                Describe the advertisement with max length 5000
              </mat-error>
            </mat-form-field>
          </div>
        </div> <!-- row container -->

        <div class="row-container" style="margin-top: 10px;" *ngIf="isMobileScreen">
          <div class="input-container">
            <div class="text-container">
              <p>Describe the Advertisement (max 5000 characters)<span style="color: red;">*</span></p>
            </div>
            <mat-form-field appearance="outline" class="textarea-field1">
              <textarea matInput formControlName="description" rows="3" maxlength="5000"
                placeholder="Tell us about the advertisement in brief... &#13;&#10;Eg: The advertisement shows two kids playing in mud and dirtying themselves, mother of one says don't worry you can take a bath with this soap and it has 10times more power to protect against disease causing bacteria and viruses."
                style="height: 150px; text-align:justify; text-justify: inter-word;" autocomplete="off"></textarea>
              <mat-error class="error-msg" *ngIf="step2Form.controls['description'].errors?.required">
                Describe the advertisement with max length 5000
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <div class="row-container" style="margin-top: 10px;" *ngIf="!isMobileScreen">
          <div class="input-container">
            <div class="text-container">
              <p>Specify the Claims/Visual Frames you find objectionable (max 5000 characters)<span
                  style="color: red;">*</span></p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="textarea-field">
                <textarea matInput formControlName="compDesc" rows="3" maxlength="5000"
                  placeholder="Your claims regarding complaints &#13;&#10;Eg: Claim 1: 10 times more power protect against disease causing bacteria and viruses. &#13;&#10;OR Visual 1: Representation of coronavirus like virus implying soap gives 10times more protection against covid."
                  style="width:800px !important; height: 150px; text-align:justify; text-justify: inter-word;"
                  autocomplete="off"></textarea>
                <mat-error class="error-msg" *ngIf="step2Form.controls['compDesc'].errors?.required">
                  Describe the claims regarding complaints with maxlength 5000
                </mat-error>
              </mat-form-field>
            </div>
          </div>
        </div>
        <!-- row container -->

        <div class="row-container" style="margin-top: 10px;" *ngIf="isMobileScreen">
          <div class="input-container">
            <div class="text-container">
              <p>Specify the Claims/Visual Frames you find objectionable (max 5000 characters)<span
                  style="color: red;">*</span></p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="textarea-field1">
                <textarea matInput formControlName="compDesc" rows="3" maxlength="5000"
                  placeholder="Your claims regarding complaints &#13;&#10;Eg: Claim 1: 10 times more power protect against disease causing bacteria and viruses. &#13;&#10;OR Visual 1: Representation of coronavirus like virus implying soap gives 10times more protection against covid."
                  style="width:800px !important; height: 150px; text-align:justify; text-justify: inter-word;"
                  autocomplete="off"></textarea>
                <mat-error class="error-msg" *ngIf="step2Form.controls['compDesc'].errors?.required">
                  Describe the claims regarding complaints with maxlength 5000
                </mat-error>
              </mat-form-field>
            </div>
          </div>
        </div>

        <!-- <div class="row-container">
          <div class="input-container">
            <div class="text-container">
              <p>Initial classification</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <mat-select formControlName="initial_class">
                  <mat-option value="Promoting communal disharmony">Promoting communal disharmony</mat-option>
                  <mat-option value="Misleading">Misleading</mat-option>
                  <mat-option value="Being offensive">Being offensive</mat-option>
                  <mat-option value="Offensive to religious beliefs">Offensive to religious beliefs</mat-option>
                  <mat-option value="Graphics copyright">Graphics copyright</mat-option>
                  <mat-option value="Offensive portray of goddess">Offensive portray of goddess</mat-option>
                  <mat-option value="Indecent exposure">Indecent exposure</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
          <div class="input-container">
            <div class="text-container">
              <p>Has the issue resolved</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <mat-select formControlName="issue_resolved">
                  <mat-option value="No">No, try to do it locally</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
        </div>  -->

        <!-- <div class="row-container" style="margin-top: 10px;">
          <div class="input-container">
            <div class="text-container">
              <p>What would the complainant like us to do to resolve the issue?</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="textarea-field">
                <textarea matInput formControlName="suggestion" rows="3"
                  placeholder="Write suggestion.."></textarea>
                <mat-hint><strong>Don't disclose personal info</strong></mat-hint>
              </mat-form-field>
            </div>
          </div>
        </div>  -->
        <!-- row container -->

      </div> <!-- step2-consumer-container  -->

      <div class="divider-container" *ngIf="!isMobileScreen">
        <mat-divider></mat-divider>
      </div>

      <div class="btn-container">
        <div class="next-container">
          <button mat-flat-button class="next-btn" ng-click="tabGroup.selectedIndex=4;" [disabled]="step2Form.invalid"
            (click)="step2Next('submit')">
            <span class="bolder">{{buttonName}}</span>
          </button>
        </div>
        <div class="back-container">
          <button mat-stroked-button class="back-btn" (click)="back()" *ngIf="!backDisable">
            <span class="bolder">Back</span>
          </button>
          <button mat-stroked-button class="back-btn" (click)="cancel()" *ngIf="backDisable">
            <span class="bolder">Cancel</span>
          </button>
        </div>
      </div>
    </form>
  </div>
</div>