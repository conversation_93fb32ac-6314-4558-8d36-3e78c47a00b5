import { Component, HostListener, Inject, OnInit, Renderer2 } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';
import { NotificationService } from 'src/app/services/notification.service';
import { TagsService } from 'src/app/services/tags.service';
import { colorObj } from 'src/app/shared/color-object';

@Component({
  selector: 'app-add-edit-subtag',
  templateUrl: './add-edit-subtag.component.html',
  styleUrls: ['./add-edit-subtag.component.scss']
})
export class AddEditSubtagComponent implements OnInit {

  addform: FormGroup;
  editSubTag: boolean = false;
  subTagInfo: any;
  subTagId: number = 0;
  confirmationMsg: any = {};
  subTaglists: any = [];
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private fb: FormBuilder,
    private tagService: TagsService,
    private renderer: Renderer2,
    private notify: NotificationService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<AddEditSubtagComponent>,
    public dialog: MatDialog
  ) {
    this.addform = this.fb.group({
      SUB_TAG_NAME: ['', [Validators.required]],
    })
  }

  ngAfterViewInit() {
    setTimeout(() => {
      var elem = this.renderer.selectRootElement('#subTag');
      elem.focus();
    }, 1000);
  }

  ngOnInit(): void {
    this.subTaglists = JSON.parse(window.localStorage.getItem('subTagList'));
    if (this.data.func === 'edit') {
      this.editSubTag = true;
      this.subTagInfo = this.data.element;
      this.subTagId = this.subTagInfo['ID'];
      this.addform.patchValue({
        SUB_TAG_NAME: this.subTagInfo['SUB_TAG_NAME']
      })
    }
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  save_updateSubtag(model) {
    if (this.editSubTag) {
      this.updateSubtag(model);
    } else {
      this.saveSubtag(model);
    }
  }

  saveSubtag(model) {
    this.tagService.createSubTag(model).subscribe(res => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      let subtaglist = res.data;
      subtaglist.shift();
      window.localStorage['subTagList'] = JSON.stringify(subtaglist);
      this.dialogRef.close('add');
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  updateSubtag(model) {
    this.tagService.updateSubTag(model, this.subTagId).subscribe(res => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      let subtaglist = res.data;
      subtaglist.shift();
      window.localStorage['subTagList'] = JSON.stringify(subtaglist);
      this.dialogRef.close('edit');
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  removeTag() {
    this.confirmationMsg.title = 'Are you sure you want to delete the Sub Tag ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.data.C_ID, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.tagService.removeTag(this.subTagId).subscribe(res => {
          this.notify.showNotification(
            res.message,
            "top",
            (!!colorObj[res.status] ? colorObj[res.status] : "success"),
            res.status
          )
          let subtaglist = res.data;
          window.localStorage['subTagList'] = JSON.stringify(subtaglist);
          this.dialogRef.close('remove');
        }, err => {
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        })
      }
    })
  }

}