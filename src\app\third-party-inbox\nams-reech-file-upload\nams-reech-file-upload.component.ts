import { Component, HostListener, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { NotificationService } from 'src/app/services/notification.service';
import { ThirdPartyService } from 'src/app/services/third-party.service';
import { UploadService } from 'src/app/services/upload.service';
import { colorObj } from 'src/app/shared/color-object';

@Component({
  selector: 'app-nams-reech-file-upload',
  templateUrl: './nams-reech-file-upload.component.html',
  styleUrls: ['./nams-reech-file-upload.component.scss']
})
export class NamsReechFileUploadComponent implements OnInit {

  selectedFile: any;
  isUploadProgress: boolean = false;
  dataSource = new MatTableDataSource([]);
  displayedColumns: string[] = ['FILE_NAME', 'CREATED_DATE'];
  namsFilePage: number = 1;
  resArray: any[] = [];
  lastData: number = 0
  fileAdded: boolean = false;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private uploadService: UploadService,
    private notify: NotificationService,
    private namsService: ThirdPartyService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<NamsReechFileUploadComponent>,
  ) { }

  ngOnInit(): void {
    this.getFiles();
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  getFiles() {
    this.namsService.getNamsReechFileList(this.namsFilePage, this.data.key).subscribe(res => {
      this.resArray = this.resArray.concat(res.data);
      this.dataSource = new MatTableDataSource(this.resArray);
      this.lastData = res.data.length;
    }, err => {
      this.handleError(err);
    })
  }

  onScrollDown() {
    this.namsFilePage++;
    this.getFiles();
  }

  uploadFile(event: any) {
    if (!this.uploadService.validateFileExtension(event)) {
      // this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        "File format is not supported!",
        "top",
        "warning",
        0
      );
      return;
    }
    this.fileAdded = true;
    this.selectedFile = event.target.files[0];
  }

  removeFile() {
    this.fileAdded = false;
    this.selectedFile = [];
  }

  async submit() {
    let tempObjforGetsigned = {
      FILE_NAME: this.selectedFile['name'],
      FILE_TYPE: this.selectedFile['type'],
      SECTION: 'sheets',
      NETWORK_TAB: this.data.key
    }
    await this.namsService.getReechSignedUrl(tempObjforGetsigned).then(async (res) => {
      if (res && res['data'] && res['data']['SIGNED_URL']) {
        let tempObjForUpload = {
          url: res['data']['SIGNED_URL'],
          file: this.selectedFile
        }
        await this.uploadSignedFile(tempObjForUpload);
        let saveBody = {
          FILE_KEY: res['data']['PATH'],
          FILE_NAME: this.selectedFile['name'],
          FILE_TYPE: this.selectedFile['type'],
          SIZE: this.selectedFile['size'],
          NETWORK_TAB: this.data.key
        }
        await this.saveDocumentToDB(saveBody);
        this.isUploadProgress = false;
        this.fileAdded = false;
        // this.attachment.nativeElement.value = '';
        this.selectedFile = [];
        this.notify.showNotification(
          'Documents uploaded successfully',
          "top",
          (!!colorObj[200] ? colorObj[200] : "success"),
          200
        );
      }
    })
      .catch((err) => {
        this.isUploadProgress = false;
        this.handleError(err);
      });
  }

  async uploadSignedFile(tempObj) {
    let progressInit = 0;
    await this.uploadService.uploadFilethroughSignedUrl(tempObj, progressInit => {
    }).catch((err) => {
      this.fileAdded = false;
      this.isUploadProgress = false;
      this.handleError(err);
    });
  }

  async saveDocumentToDB(tempObj) {
    await this.namsService.saveReechFileSourceToDB(tempObj).then((data) => {
      return data;
    })
      .catch((err) => {
        this.fileAdded = false;
        this.isUploadProgress = false;
        let body = {
          ID: tempObj['ID'],
          KEY: tempObj['KEY']
        }
        this.deleteFileFromS3(body)
        this.handleError(err);
      });
    this.resArray = [];
    this.namsFilePage = 1;
    this.getFiles();
  }

  deleteFileFromS3(obj: any) {
    this.uploadService.deleteObjectFromS3(obj).subscribe((res) => {
      return res;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
      return err;
    })
  }

  handleError(err: any) {
    this.notify.showNotification(
      err.error.message,
      "top",
      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
      err.error.status
    );
  }

}