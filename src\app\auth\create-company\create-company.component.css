::ng-deep .mat-form-field-flex > .mat-form-field-infix { padding: 0px 0px 0.4em 0px !important; }
:host:ng-deep .mat-form-field-appearance-outline .mat-form-field-label { margin-top:-15px; }
:host:ng-deep label.ng-star-inserted { transform: translateY(-0.59375em) scale(.75) !important; }
:host:ng-deep .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    transform: translateY(-1.1em) scale(.75);
    width: 133.33333%;
}
 :host::ng-deep .mat-form-field-underline {
    display: none;
} 
 
.toolbar {
    height: 84px;
    padding-left: 15px;
    background-color: #F8F9F9;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #2F3941;
}
.search-btn
    {
        color: solid rgba(47, 57, 65, 0.6);
        background: #F3F3F3;
        border: 1px solid rgba(47, 57, 65, 0.6);
    }
    .contents{
        margin-left: 5px;
        padding-top: 15px;
        /* margin-right: 15px; */
    }
    .form {
        -ms-overflow-style: none;
        scrollbar-width: none;
        }
    .names
{
      flex-direction: row;
      display: flex;
      grid-gap: 242px;
      padding-top: 10px;
    /* font-size: 12px; */
    color: #2F3941;
}
.input-field{
    width:268px !important;
}
.control-container{
    margin-top: 0px;
    flex-direction: row;
    display: flex;
}
.input-field_cmpname
{
    width:300px !important;
    padding-left: 40px; 
}
.input-field_lastname
{
    width:288px !important;
    padding-left: 40px;
}
.input-field_aboutcmp
{
    width:300px !important;
    padding-left: 40px;
}
.lastdivdr
{
    padding-top: 5%;
    width: 560px;
}
.toolbar-btns2
{
    float: right;
    padding-top: 2%;
}
.cancel-btn
{
    color: #284255;
    background-color: white;
    border-radius: 14px;
    margin-right: 10px;
}
.confirm-btn{
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: white;
    background-color: #0088CB;
    border-radius: 12px;
}

