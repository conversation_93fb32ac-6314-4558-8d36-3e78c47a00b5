import { Component, HostListener, OnInit, Renderer2 } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { colorObj } from 'src/app/shared/color-object';
import { environment } from 'src/environments/environment';
import { AuthService } from '../../services/auth.service';
import { NotificationService } from '../../services/notification.service';
@Component({
  selector: 'app-add-user',
  templateUrl: './add-user.component.html',
  styleUrls: ['./add-user.component.css']
})

export class AddUserComponent implements OnInit {

  addform: FormGroup;
  value: any;
  emailPattern = environment.emailPatterm;
  mobilenopattern = /^((\\+91-?)|0)?[0-9]{10}$/;
  usernamepattern = /^[a-z A-Z]+$/;
  passwordPattern = /^(?=.*?[A-Z])(?=.*?[a-z])(?!.* )(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,15}$/;
  userRoles: any[];
  deptList: any[];
  titles: any[];
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(private fb: FormBuilder,
    private authService: AuthService,
    private notify: NotificationService,
    private renderer: Renderer2,
    private dialogRef: MatDialogRef<AddUserComponent>) {
    this.addform = this.fb.group({
      department: ['', [Validators.required]],
      role: ['', [Validators.required]],
      salutation: ['', [Validators.required]],
      password: ['', [Validators.required, Validators.pattern(this.passwordPattern)]],
      fname: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(25), Validators.pattern(this.usernamepattern)]],
      lname: ['', [Validators.required, Validators.maxLength(25), Validators.pattern(this.usernamepattern)]],
      emailId: ['', [Validators.required, Validators.pattern(this.emailPattern)]],
      phone: ['', [Validators.required, Validators.pattern(this.mobilenopattern)]],
    })
  }

  ngOnInit(): void {
    const roles = JSON.parse(window.localStorage.getItem('role'));
    this.titles = JSON.parse(window.localStorage.getItem('salutation'));
    this.userRoles = roles.filter(element => {
      return (element.ADMINISTRATION_VISIBILITY == 1)
    });
    this.deptList = JSON.parse(window.localStorage.getItem('departmentType'));
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  Password() {
    var numberChars = "0123456789";
    var specialChars = "@#%&!$*"
    var upperChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    var lowerChars = "abcdefghijklmnopqrstuvwxyz";
    var allChars = numberChars + upperChars + lowerChars;
    var randPasswordArray = Array(16);
    randPasswordArray[0] = numberChars;
    randPasswordArray[1] = upperChars;
    randPasswordArray[2] = lowerChars;
    randPasswordArray[3] = specialChars;
    randPasswordArray = randPasswordArray.fill(allChars, 5);
    this.addform.patchValue({
      'password': this.shuffleArray(randPasswordArray.map(function (x) { return x[Math.floor(Math.random() * x.length)] })).join('')
    })
  }

  shuffleArray(array) {
    for (var i = array.length - 1; i > 0; i--) {
      var j = Math.floor(Math.random() * (i + 1));
      var temp = array[i];
      array[i] = array[j];
      array[j] = temp;
    }
    return array;
  }

  register(model) {
    model.role = model.role.toString();
    model.phone = model.phone.toString();
    this.authService.createUserByAdmin(model).subscribe(res => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      this.dialogRef.close('refresh');
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

}