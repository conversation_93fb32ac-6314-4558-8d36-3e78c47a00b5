<img src="assets/images/X.png" class="introduction-msg-close" (click)= "closeIntro()" [hidden]="hiddenIntroMsg">
<div class="intro" [hidden]="hiddenIntroMsg" (click)="openPanel()">
    <img src="assets/images/maskgroup.png" class="mask-group">
    <img src="assets/images/chatbot.png" class="chatbot-mask-group">
    <span class="introduction-msg" >
        <div class="intro-first-msg" fxLayout="row"><br><img src="assets/images/wave.png" class="introduction-wave-img">Hi! Do you want to lodge a complaint?</div> 
        <div class="intro-second-msg">I am TARA and I am here to assist you today</div>
    </span>
</div>
<img src="assets/images/chatbot.png" (click)="openPanel()" class="chatbot-img">
<div class="chatbox-container">
    <mat-accordion class="prevent-click">
        <mat-expansion-panel class="custom-header" hideToggle="true" #example [expanded]="panelOpenState">
             <mat-expansion-panel-header *ngIf="example.expanded">
                <mat-panel-title>
                    <span class="mat-heading">
                        <button mat-icon-button class="asci-logo" aria-label="asci logo">
                            <img src="assets/images/ASCI-logo.png">
                          </button>
                        <span style="margin-left: 6px;">Complaint Assistant</span>
                        <img src="assets/images/close.png" (click)="close()" class="close-icon">
                    </span>
                </mat-panel-title>
            </mat-expansion-panel-header>
            <div class="chatbox-message" #scrollMe>
                <ng-container *ngFor="let message of displayContent">
                    <div class="message" *ngIf="message.text != null && message.author != 'verify' && message.author != 'error'"
                     [ngClass]="{ 'from': message.author === 'bot','to': message.author === 'user' }">
                        <div *ngIf="message.type != 'image' && message.type != 'video' && message.type != 'application' && message.type != 'text' && message.type != 'pdf'">
                        {{ message.text }}<br><span class="chat-time">{{message.time| date:'h:mm a'}}</span>
                        </div>
                        <div *ngIf="message.type === 'image'">
                            <img src="../../../assets/images/img-thumbnail.png" class="thumbnail" ><br><span class="text-ellipsis">{{ message.text }}</span><br><span class="chat-time">{{message.time| date:'h:mm a'}}</span><br>
                            <!-- <img src="{{docURL}}" class="thumbnail" ><br>{{ message.text }}<br><span class="chat-time">{{message.time| date:'h:mm a'}}</span><br> -->
                        </div>
                        <div *ngIf="message.type === 'video'">
                            <img src="../../../assets/images/video-thumbnail.png" class="thumbnail" ><br><span class="text-ellipsis">{{ message.text }}</span><br><span class="chat-time">{{message.time| date:'h:mm a'}}</span><br>
                        </div>
                        <div *ngIf="message.type === 'application' || message.type === 'text' || message.type === 'pdf'">
                            <img src="../../../assets/images/doc-thumbnail.png" class="thumbnail" ><br><span class="text-ellipsis">{{ message.text }}</span><br><span class="chat-time">{{message.time| date:'h:mm a'}}</span><br>
                        </div>
                    </div>

                    <div class="notification-msg" *ngIf="message.information != null" [hidden]="infoHidden">
                         <img src="assets/images/info-chatbot.png"> {{warningMsg}}
                    </div>

                    <div class="time-notification-msg" *ngIf="showTime">
                        <img src="assets/images/info-chatbot.png"> Time left: {{timeLeft}}
                   </div>

                    <div *ngIf="message.text != 'Whom do you represent ?' && message.text != 'Where did you see the advertisement?' && message.text != 'Please select the platform' && message.text != 'According to you the advertisement is' && message.text != 'Specify the type of promotional material'" 
                        fxLayout="row" fxLayoutGap="5%" class="objective-button">
                        <div *ngFor="let option of message.options" [hidden]="showObjectiveMessage" [@simpleFadeAnimation1]="'in'">
                            <button (click)="handleSelectionOfOption(option)" [disabled]="message != currentStep || isExpiryCompleted" >{{option.name}}</button>
                        </div>
                    </div>

                    <div *ngIf="message.text === 'Whom do you represent ?'" fxLayout="row" fxLayoutGap="5%" class="objective-button1">
                        <div *ngFor="let option of message.options1" [hidden]="showObjectiveMessage1" [@simpleFadeAnimation1]="'in'">
                            <div>
                                <button (click)="handleSelectionOfOption(option)" [disabled]="message != currentStep" fxFlex="30%">{{option.name}}</button>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="message.text === 'Whom do you represent ?'" fxLayout="row" fxLayoutGap="5%" class="objective-button1">
                        <div *ngFor="let option of message.options2" [hidden]="showObjectiveMessage1" [@simpleFadeAnimation1]="'in'">
                            <div>
                                <button (click)="handleSelectionOfOption(option)" [disabled]="message != currentStep" fxFlex="30%">{{option.name}}</button>
                            </div>
                        </div>
                    </div>

                    <div *ngIf="message.text === 'Where did you see the advertisement?'" fxLayout="row" fxLayoutGap="5%" class="objective-button">
                        <div *ngFor="let option of message.options1" [hidden]="showObjectiveMessage1" [@simpleFadeAnimation1]="'in'">
                            <div>
                                <button (click)="handleSelectionOfOption(option)" [disabled]="message != currentStep">{{option.name}}</button>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="message.text === 'Where did you see the advertisement?'" fxLayout="row" fxLayoutGap="5%" class="objective-button">
                        <div *ngFor="let option of message.options2" [hidden]="showObjectiveMessage1" [@simpleFadeAnimation1]="'in'">
                            <div>
                                <button (click)="handleSelectionOfOption(option)" [disabled]="message != currentStep">{{option.name}}</button>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="message.text === 'Where did you see the advertisement?'" fxLayout="row" fxLayoutGap="5%" class="objective-button">
                        <div *ngFor="let option of message.options3" [hidden]="showObjectiveMessage1" [@simpleFadeAnimation1]="'in'">
                            <div>
                                <button (click)="handleSelectionOfOption(option)" [disabled]="message != currentStep">{{option.name}}</button>
                            </div>
                        </div>
                    </div>

                    <div *ngIf="message.text === 'Please select the platform'" fxLayout="row" fxLayoutGap="5%" class="objective-button">
                        <div *ngFor="let option of message.options1" [hidden]="showObjectiveMessage1" [@simpleFadeAnimation1]="'in'">
                            <div>
                                <button (click)="handleSelectionOfOption(option)" [disabled]="message != currentStep">{{option.name}}</button>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="message.text === 'Please select the platform'" fxLayout="row" fxLayoutGap="5%" class="objective-button">
                        <div *ngFor="let option of message.options2" [hidden]="showObjectiveMessage1" [@simpleFadeAnimation1]="'in'">
                            <div>
                                <button (click)="handleSelectionOfOption(option)" [disabled]="message != currentStep">{{option.name}}</button>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="message.text === 'Please select the platform'" fxLayout="row" fxLayoutGap="5%" class="objective-button">
                        <div *ngFor="let option of message.options3" [hidden]="showObjectiveMessage1" [@simpleFadeAnimation1]="'in'">
                            <div>
                                <button (click)="handleSelectionOfOption(option)" [disabled]="message != currentStep">{{option.name}}</button>
                            </div>
                        </div>
                    </div>

                    <div *ngIf="message.text === 'According to you the advertisement is'" fxLayout="row" fxLayoutGap="5%" class="objective-button">
                        <div *ngFor="let option of message.options1" [hidden]="showObjectiveMessage1" [@simpleFadeAnimation1]="'in'">
                            <div>
                                <button (click)="handleSelectionOfOption(option)" [disabled]="message != currentStep">{{option.name}}</button>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="message.text === 'According to you the advertisement is'" fxLayout="row" fxLayoutGap="5%" class="objective-button">
                        <div *ngFor="let option of message.options2" [hidden]="showObjectiveMessage1" [@simpleFadeAnimation1]="'in'">
                            <div>
                                <button (click)="handleSelectionOfOption(option)" [disabled]="message != currentStep">{{option.name}}</button>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="message.text === 'According to you the advertisement is'" fxLayout="row" fxLayoutGap="5%" class="objective-button">
                        <div *ngFor="let option of message.options3" [hidden]="showObjectiveMessage1" [@simpleFadeAnimation1]="'in'">
                            <div>
                                <button (click)="handleSelectionOfOption(option)" [disabled]="message != currentStep">{{option.name}}</button>
                            </div>
                        </div>
                    </div>

                    <div *ngIf="message.text === 'Specify the type of promotional material'" fxLayout="row" fxLayoutGap="10%" class="objective-button">
                        <div *ngFor="let option of message.options1" [hidden]="showObjectiveMessage1" [@simpleFadeAnimation1]="'in'">
                            <div>
                                <button (click)="handleSelectionOfOption(option)" [disabled]="message != currentStep">{{option.name}}</button>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="message.text === 'Specify the type of promotional material'" fxLayout="row" fxLayoutGap="11%" class="objective-button">
                        <div *ngFor="let option of message.options2" [hidden]="showObjectiveMessage1" [@simpleFadeAnimation1]="'in'">
                            <div>
                                <button (click)="handleSelectionOfOption(option)" [disabled]="message != currentStep">{{option.name}}</button>
                            </div>
                        </div>
                    </div>

                    <div class ="verification-message" *ngIf="successNotification != ''" [@simpleFadeAnimation2]="'in'" [hidden]="hideNotification">
                        {{successNotification}}
                    </div>
                    <div class ="error-message" *ngIf="errorNotification != ''" [@simpleFadeAnimation2]="'in'" [hidden]="hideNotification">
                        {{errorNotification}}
                    </div>
                    <div class ="error-message" *ngIf="message.author == 'error'" [@simpleFadeAnimation2]="'in'">
                        {{message.text}}
                    </div>
                    <div class ="skip-message" *ngIf="skipMessage && currentStepId == message.step">
                        By skipping this you will not be able to get updates on your complaint.
                    </div>
                    <div fxLayout="row" *ngIf="skipMessage && currentStepId == message.step">
                        <button class ="skip-button" fxFlex="30%" (click)="skipOption(message, 'back')">
                            Go back
                        </button>
                        <div fxFlex="40%"></div>
                        <button class ="skip-button" fxFlex="30%" (click)="skipOption(message, 'skip')">
                            Skip anyway
                        </button>
                    </div>
                    <div fxLayout="row" *ngIf="message.skipOption == true && currentStepId == message.step && !skipMessage">
                        <div fxFlex="60%"></div>
                        <button class ="skip-button" fxFlex="40%" (click)="skipOption(message, 'skip')">
                            Skip this question
                        </button>
                    </div>
                </ng-container>
                <!-- <ng-container *ngFor="let message of messages">
                    <div *ngIf="message.type === 'Image'" >
                        <img [src]="message.content" class="file-upload" /><br><span class="chat-time">{{message.time| date:'h:mm a'}}</span><br>
                    </div>
                    <div *ngIf="message.type === 'Video'" >
                        <video [src]="message.content" height="100" controls></video><br>
                    </div>
                </ng-container> -->
                <div class="intro-msg">
                    <input type="hidden" value="Let's Start">
                    <label for="nameField" *ngIf="displayContent.length == 0">
                        <span>
                            <img src="assets/images/wave.png" class="wave-img"/><p class="intro-msg1">Hi, There </p><br>
                        </span>
                        <p class="intro-msg2">I am Tara, I am here to assist you today</p><br>
                        <button class="hi-button" (click)="getMessage()">Let's start</button><br>
                    </label><br>
                </div>
            </div>
            <div class="chatbot-message-send">
                <button md-mini-fab type="button" class="attachButton" (click)="fileInput.click()" [disabled]="disableUpload">
                    <label for="fileToUpload">
                        <img src="assets/images/attach.png" class="attach-img" style="cursor: pointer;">
                    </label>
                </button>
                <input style="display: none" #attachments type="file" (change)="onFileChange($event)" multiple="true"
                accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.xls,.xlsm,.pptx,.pdf,.csv,.txt,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv" #fileInput>
                <input [formControl]="text_message" class="messagetype" placeholder="Type your message..." (keyup.enter)="onClickOfNextArrowButton(text_message.value)" type="text" #msgs v-model="text" [readonly]="disableInput">
                <button (click)="onClickOfNextArrowButton(text_message.value)" class="sendButton" [disabled]="disableInput"><img src="assets/images/send.png" class="send-img"></button>
            </div>
        </mat-expansion-panel>
    </mat-accordion>
</div>   