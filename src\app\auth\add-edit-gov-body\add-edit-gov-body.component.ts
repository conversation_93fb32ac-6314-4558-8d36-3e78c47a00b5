import { Component, HostListener, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';
import { AuthService } from 'src/app/services/auth.service';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-add-edit-gov-body',
  templateUrl: './add-edit-gov-body.component.html',
  styleUrls: ['./add-edit-gov-body.component.scss']
})

export class AddEditGovBodyComponent implements OnInit {

  addform: FormGroup;
  value: any;
  confirmationMsg: any = {};
  companyList: any[];
  mobilenopattern = /^((\\+91-?)|0)?[0-9]{10}$/;
  usernamepattern = /^[a-z A-Z]+$/;
  passwordPattern = /^(?=.*?[A-Z])(?=.*?[a-z])(?!.* )(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,15}$/;
  pinPattern = /^[0-9]{6}$/;
  emailPattern = environment.emailPatterm;
  deptId: number;
  userInfo: any;
  selectedGovBody = "";
  userRoleId: number;
  roleId: number;
  govDept: any;
  isEdit: boolean = true;
  titles: any[];
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private fb: FormBuilder,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private authService: AuthService,
    private cs: ComplaintsService,
    private notify: NotificationService,
    private dialogRef: MatDialogRef<AddEditGovBodyComponent>,
    public dialog: MatDialog
  ) {
    this.addform = this.fb.group({
      govt_body: ['', [Validators.required]],
      govt_body_name: ['', [Validators.required]],
      salutation: ['', [Validators.required]],
      postal_code: ['', [Validators.required, Validators.pattern(this.pinPattern)]],
      password: ['', [Validators.required, Validators.pattern(this.passwordPattern)]],
      first_name: ['', [Validators.required, Validators.pattern(this.usernamepattern), Validators.minLength(3), Validators.maxLength(25)]],
      last_name: ['', [Validators.required, Validators.pattern(this.usernamepattern), Validators.maxLength(25)]],
      email: ['', [Validators.required, Validators.pattern(this.emailPattern)]],
      phone_number: ['', [Validators.required, Validators.pattern(this.mobilenopattern)],
      ],
    });
  }

  ngOnInit(): void {
    this.govDept = JSON.parse(window.localStorage.getItem('governmentDepartment'));
    this.titles = JSON.parse(window.localStorage.getItem('salutation'));
    this.isEdit = this.data.isEdit;
    if (this.data.row) {
      this.selectedGovBody = this.data.row['GOVERNMENT_DEPARTMENT_ID'];
      this.addform.patchValue({
        'first_name': this.data.row['FIRST_NAME'],
        'last_name': this.data.row['LAST_NAME'],
        'govt_body_name': this.data.row['NEW_GOVERNMENT_DEPARTMENT_NAME'],
        'salutation': this.data.row['SALUTATION_ID'],
        'govt_body': this.data.row['GOVERNMENT_DEPARTMENT_ID'],
        'postal_code': this.data.row['PINCODE'],
        'email': this.data.row['EMAIL_ID'],
        'phone_number': this.data.row['MOBILE']
      })
    }
    if (this.isEdit) {
      this.addform.controls['email'].disable();
      this.addform.controls['phone_number'].disable();
      this.addform.controls['govt_body'].disable();
      this.addform.controls['govt_body_name'].disable();
      this.addform.get('password').clearValidators();
      this.addform.get('password').updateValueAndValidity();
    }
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  getGovBodyId() {
    if (this.selectedGovBody != '10') {
      this.addform.get('govt_body_name').clearValidators();
    }
  }

  password() {
    var numberChars = "0123456789";
    var specialChars = "@#%&!$*"
    var upperChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    var lowerChars = "abcdefghijklmnopqrstuvwxyz";
    var allChars = numberChars + upperChars + lowerChars;
    var randPasswordArray = Array(16);
    randPasswordArray[0] = numberChars;
    randPasswordArray[1] = upperChars;
    randPasswordArray[2] = lowerChars;
    randPasswordArray[3] = specialChars;
    randPasswordArray = randPasswordArray.fill(allChars, 5);
    this.addform.patchValue({
      'password': this.shuffleArray(randPasswordArray.map(function (x) { return x[Math.floor(Math.random() * x.length)] })).join('')
    })
  }

  shuffleArray(array) {
    for (var i = array.length - 1; i > 0; i--) {
      var j = Math.floor(Math.random() * (i + 1));
      var temp = array[i];
      array[i] = array[j];
      array[j] = temp;
    }
    return array;
  }

  register(model) {
    this.roleId = 4;
    this.cs.createUserByComplaint(model, this.roleId).subscribe(
      (res) => {
        this.notify.showNotification(
          res.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status] : "success"),
          res.status
        )
        this.dialogRef.close('refresh');
      },
      (err) => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      }
    );
  }

  removeUser() {
    this.confirmationMsg.title = 'Are you sure you want to delete the user ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.data.row['C_ID'], title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.authService
          .deleteUser(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              this.dialogRef.close('d-refresh');
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  update(model) {
    this.authService.updateGovBodyUser(model, this.data.row['ID']).subscribe(
      (res) => {
        this.notify.showNotification(
          res.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status] : "success"),
          res.status
        )
        this.dialogRef.close('u-refresh');
      },
      (err) => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      }
    );
  }

}