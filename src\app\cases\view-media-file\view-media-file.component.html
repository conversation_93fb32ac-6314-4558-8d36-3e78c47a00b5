<div class="body" fxLayout="column" fxLayoutGap="20px">
    <div fxLayout="row" class="header">
        <div class="head-container">
            <h3 class="heading">Media files against complaint</h3>
        </div>
        <span style="flex: 1 1 auto;"></span>
        <div fxLayoutAlign="end center">
            <button mat-icon-button class="close-btn" mat-dialog-close>
                <mat-icon class="close-icon">close</mat-icon>
            </button>
        </div>
    </div>

    <div fxLayout="column">
        <mat-accordion class="media-expansion" multi>
            <mat-expansion-panel [ngClass]="{'media-expsn': panelOpenState == false, 'media-expsn1': panelOpenState == true}" *ngFor="let docs of adMedium">
              <mat-expansion-panel-header class="panel-header" [collapsedHeight]="collapsedHeight" *ngIf="docs.docInfo.length != 0">
                <mat-panel-title>
                    <div fxLayout="row">
                        <div class="attribute-container1">
                          <p>
                            <span class="grey-text">Medium : </span>
                            <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '1'">Television</span>
                            <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '2'">Radio</span>
                            <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '3'">Digital Media</span>
                            <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '4'">Hoardings</span>
                            <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '5'">Print</span>
                            <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '6'">Promotional Material</span>
                            <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '7'">Packaging</span>
                            <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '8'">SMS</span>
                            <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '9'">Others</span>
                          </p>
                        </div>
                        <div class="attribute-container2">
                          <p>
                            <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '1' || docs.ADVERTISEMENT_SOURCE_ID == '2'">Channel : {{docs.SOURCE_NAME| slice: 0:10}} {{docs.SOURCE_NAME.length>11? '..': ' '}}</span>
                            <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '3'">Platform :
                              <span *ngIf="docs.PLATFORM_ID == 1">Facebook</span>
                              <span *ngIf="docs.PLATFORM_ID == 2">Instagram</span>
                              <span *ngIf="docs.PLATFORM_ID == 3">YouTube</span>
                              <span *ngIf="docs.PLATFORM_ID == 4">Twitter</span>
                              <span *ngIf="docs.PLATFORM_ID == 5">LinkedIn</span>
                              <span *ngIf="docs.PLATFORM_ID == 6">Website</span>
                              <span *ngIf="docs.PLATFORM_ID == 7">Google Ad</span>
                              <span *ngIf="docs.PLATFORM_ID == 8">Mobile App</span>
                              <span *ngIf="docs.PLATFORM_ID == 9">{{docs.SOURCE_PLACE}}</span>
                            </span>
                            <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '4' || docs.ADVERTISEMENT_SOURCE_ID == '6'" matTooltip="{{docs.SOURCE_PLACE}}">Place : {{docs.SOURCE_PLACE| slice: 0:10}}{{docs.SOURCE_PLACE.length>11? '..': ' '}}</span>
                            <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '5'" matTooltip="{{docs.SOURCE_NAME}}">Print Source : {{docs.SOURCE_NAME| slice: 0:10}} {{docs.SOURCE_NAME.length>11? '..': ' '}}</span>
                            <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '7'">MFD/PKD Date : {{docs.DATE | date:'dd/MM/yyyy'}}</span>
                            <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '8'" matTooltip="{{docs.SOURCE_NAME}}">Sender : {{docs.SOURCE_NAME| slice: 0:10}} {{docs.SOURCE_NAME.length>11? '..': ' '}}</span>
                            <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '9'" matTooltip="{{docs.SOURCE_NAME}}">Source :  {{docs.SOURCE_NAME| slice: 0:10}} {{docs.SOURCE_NAME.length>11? '..': ' '}}</span>
                          </p>
                        </div>
                    </div>
                </mat-panel-title>
              </mat-expansion-panel-header>

              <div fxLayout="column" *ngFor="let doc of docs.docInfo; let ind = index">
                <div class="panel-body" fxLayout="row">
                  <div class="doc-icon-container flex">
                    <button mat-icon-button class="doc-btn flex">
                      <img src="../../assets/images/media_doc.svg">
                    </button>
                  </div>
                  <div class="link-container">
                    <a class="doc-link" matTooltip="{{doc.name}}">{{doc.name | slice: 0:40}}{{doc.name.length>41? '..': ''}}</a>
                  </div>
                  <div class="icon-container flex">
                    <button mat-icon-button class="icon-btn flex" (click)="preview(doc.name,doc.url)">
                      <img src="../../assets/images/media_eye.svg">
                    </button>
                  </div>
                  <div class="icon-container flex" (click)="download(doc.name,doc.url)">
                    <button mat-icon-button class="icon-btn flex">
                      <img src="../../assets/images/media_download.svg">
                    </button>
                  </div>
                </div>
                <div fxLayout="row" style="margin-bottom: 5px; margin-top: 5px; margin-left: 5px;">
                  <div>
                    <img src="../../../assets/images/calender-icon.png">
                  </div>
                  <div style="font-size: 11px; margin-left: 12px; margin-top: 3px; color: rgba(0, 0, 0, 0.6);">
                    {{doc.date | date:'dd/MM/yyyy h:mm a' }}
                  </div>
                </div>
              </div>
            </mat-expansion-panel>
        </mat-accordion> 
    </div>    
</div>