import { HttpClient, HttpEvent, HttpEventType, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ThirdPartyService {

  public resourceUrl = `${environment.API_BASE_URL}`;

  private namsComplaintDate = new BehaviorSubject(0);
  currentNamsComplaintDate = this.namsComplaintDate.asObservable();

  updateNamsComplaintDate(namsComplaintDate: number) {
    this.namsComplaintDate.next(namsComplaintDate)
  }

  constructor(private http: HttpClient) { }

  listComplaintsList(pageNumber, limit) {
    let options = { headers: this.getHeaders() };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/nams/tams/complaint?PAGE=${pageNumber}&limit=${limit}`, options);
  }

  listReechComplaintsList(pageNumber, limit, network) {
    let options = { headers: this.getHeaders() };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/nams/reech/complaint?PAGE=${pageNumber}&limit=${limit}&NETWORK=${network}`, options);
  }

  getComplaintsByAdvertiser(val) {
    let options = { headers: this.getHeaders() };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/nams/tams/advertiser/list?KEYWORD=${val}`, options);
  }

  getComplaintsByMedium(val) {
    let options = { headers: this.getHeaders() };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/nams/tams/medium/list?KEYWORD=${val}`, options);
  }

  filterComplaintsNams(filter, key, order) {
    let options = { headers: this.getHeaders() };
    let query = "?";
    for (let i = 0; i < filter.length; i++) {
      query = query + filter[i].name + "=" + encodeURIComponent(filter[i].value)
      if (i < filter.length - 1) {
        query = query + "&"
      }
    }
    return this.http.get<any>(`${this.resourceUrl}/web/asci/nams/tams/complaint${query}&SORTING_KEY=${key}&SORTING_ORDER=${order}`, options);
  }

  filterComplaintsReech(filter, key, order) {
    let options = { headers: this.getHeaders() };
    let query = "?";
    for (let i = 0; i < filter.length; i++) {
      query = query + filter[i].name + "=" + encodeURIComponent(filter[i].value)
      if (i < filter.length - 1) {
        query = query + "&"
      }
    }
    return this.http.get<any>(`${this.resourceUrl}/web/asci/nams/reech/complaint${query}&SORTING_KEY=${key}&SORTING_ORDER=${order}`, options);
  }

  getComplaintsByNetwork(val) {
    let options = { headers: this.getHeaders() };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/nams/reech/network/list?KEYWORD=${val}`, options);
  }

  getComplaintsByInfluencerName(val) {
    let options = { headers: this.getHeaders() };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/nams/reech/influencer/list?KEYWORD=${val}`, options);
  }

  getDetails(id) {
    let options = { headers: this.getHeaders() };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/nams/tams/complaint?ID=${id}`, options);
  }

  getReechDetails(id) {
    let options = { headers: this.getHeaders() };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/nams/reech/complaint?ID=${id}`, options);
  }

  deleteNamsComplaint(id): Observable<any> {
    let arr = [];
    for (let dat of id) {
      if (dat != null) {
        arr.push(dat);
      }
    }
    let obj = {
      'ID': arr
    }
    let options = { headers: this.getHeaders() };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/nams/tams/complaint/delete`, obj, options);
  }

  deleteReechComplaint(id, reason): Observable<any> {
    let arr = [];
    for (let dat of id) {
      if (dat != null) {
        arr.push(dat);
      }
    }
    let obj = {
      'ID': arr,
      'REASON': reason
    }
    let options = { headers: this.getHeaders() };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/nams/reech/complaint/delete`, obj, options);
  }

  rejectNamsComplaint(id): Observable<any> {
    let arr = [];
    for (let dat of id) {
      if (dat != null) {
        arr.push(dat);
      }
    }
    let obj = {
      'ID': arr
    }
    let options = { headers: this.getHeaders() };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/nams/tams/complaint/reject`, obj, options);
  }

  rejectReechComplaint(id, reason): Observable<any> {
    let arr = [];
    for (let dat of id) {
      if (dat != null) {
        arr.push(dat);
      }
    }
    let obj = {
      'ID': arr,
      'REASON': reason
    }
    let options = { headers: this.getHeaders() };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/nams/reech/complaint/reject`, obj, options);
  }

  shortlistNamsComplaint(id): Observable<any> {
    let arr = [];
    for (let dat of id) {
      if (dat != null) {
        arr.push(dat);
      }
    }
    let obj = {
      'ID': arr
    }
    let options = { headers: this.getHeaders() };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/nams/tams/complaint/shortlist`, obj, options);
  }

  shortlistReechComplaint(id): Observable<any> {
    let arr = [];
    for (let dat of id) {
      if (dat != null) {
        arr.push(dat);
      }
    }
    let obj = {
      'ID': arr
    }
    let options = { headers: this.getHeaders() };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/nams/reech/complaint/shortlist`, obj, options);
  }

  listChatbotList(pageNumber) {
    let options = { headers: this.getHeaders() };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/chatbot/complaint?PAGE=${pageNumber}`, options);
  }

  getChatbotDetails(id) {
    let options = { headers: this.getHeaders() };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/chatbot/complaint?ID=${id}`, options);
  }

  createChatbotComplaint(obj): Observable<any> {
    let options = { headers: this.getHeaders() };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/chatbot/complaint`, obj, options);
  }

  createBlankComplaint(): Observable<any> {
    let obj = {}
    let options = { headers: this.getHeaders() };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/chatbot/complaint`, obj, options);
  }

  uploadChatbotAttachments(files, complaintId, section) {
    let options = { headers: this.getHeaders() };
    const formData: any = new FormData();
    const file: Array<File> = files;
    for (let i = 0; i < files.length; i++) {
      formData.append("docs", files[i]);
    }
    return this.http.post<any>(`${this.resourceUrl}/web/asci/chatbot/complaint/attach-document?ID=${complaintId}&SECTION=${section}`, formData, options);
  }

  deleteChatbotComplaint(id): Observable<any> {
    let obj = {
      'ID': id
    }
    let options = { headers: this.getHeaders() };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/chatbot/complaint/delete`, obj, options);
  }

  deleteChatbotDocFromDB(ID, ATTACHMENT_SOURCE): Observable<any> {
    let obj = {
      'CHATBOT_COMPLAINT_ID': ID,
      'KEY': ATTACHMENT_SOURCE
    }
    let options = { headers: this.getHeaders() };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/chatbot/document/delete`, obj, options);
  }

  getSignedUrl(obj: Object) {
    let options = { headers: this.getHeaders() };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/nams/tams/get-presigned-url?FILE_NAME=${obj['FILE_NAME']}&FILE_TYPE=${obj['FILE_TYPE']}&SECTION=${obj['SECTION']}`, options)
      .toPromise();
  }

  getReechSignedUrl(obj: Object) {
    let options = { headers: this.getHeaders() };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/nams/reech/get-presigned-url?FILE_NAME=${obj['FILE_NAME']}&FILE_TYPE=${obj['FILE_TYPE']}&SECTION=${obj['SECTION']}&NETWORK_TAB=${obj['NETWORK_TAB']}`, options)
      .toPromise();
  }

  uploadFilethroughSignedUrl(obj: Object, onProgress: (p: number) => void): Promise<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
      'x-amz-acl': 'bucket-owner-full-control'
    });

    return this.http.put<any>(obj['url'], obj['file'], { headers, reportProgress: true, observe: 'events' })
      .pipe(
        tap((event: HttpEvent<any>) => {
          if (event.type !== HttpEventType.UploadProgress) return;
          const progress = Math.round(event.loaded * 100 / event.total);
          onProgress(progress);
        }),
      )
      .toPromise();
  }

  saveFileSourceToDB(body: any) {
    let options = { headers: this.getHeaders() };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/nams/tams/save-to-db`, body, options).toPromise();
  }

  saveReechFileSourceToDB(body: any) {
    let options = { headers: this.getHeaders() };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/nams/reech/save-to-db`, body, options).toPromise();
  }

  getNamsFileList(page) {
    let options = { headers: this.getHeaders() };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/nams/tams/sheets?limit=10&offset=0&PAGE=${page}`, options);
  }

  getNamsReechFileList(page, network) {
    let options = { headers: this.getHeaders() };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/nams/reech/sheets?limit=10&offset=0&PAGE=${page}&NETWORK_TAB=${network}`, options);
  }

  getHeaders() {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    return headers;
  }

}