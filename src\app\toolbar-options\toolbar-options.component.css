.toolbar
{
    background-color: white;
    border: none;
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: flex-end;
}

.toolbar-btns
{
    width: 100%; 
    display: flex;
    justify-content: flex-end;
    align-items: center;
    /* border: 1px solid red; */
}

.admin-div{ 
    display: flex;
    align-items: center;
    text-align: center;
 }
 /* .arrow{
    justify-content: center;
    align-items: center;
    position: fixed;
 } */
.admin-menu{
    margin-top: 50px !important;
}
.admin-search-menu{
    margin-top: 180px !important;
}
.admin-option-container{
    width: 194px;
}
.admin-search-container{
    width: 280px;
}
.option-divider{
    width: 90%;
    margin: 0px 10px !important;
    border: 1.5px solid rgba(47, 57, 65, 0.2);
    border-top: 0;
    border-right: 0;
    border-left: 0;
}
.option-btn{
    line-height: 25%;
    height: 550%;
    padding: 5px 15px;
}
.search-option-btn{
    line-height: 10%;
    height: 550%;
    padding: 2px 15px;
}
.option-text{
    color: #000000;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 200%;
}
mat-option{
    height: 35px;
}
.search-container{ 
    display: flex;
    align-items: center;
    text-align: center;
}
.search-btn{
    height: 36px;
    width: 35px;
    transform: scale(0.9);
    border:0px;
    border-radius: 12px;
    border: 1px solid #0088CB;
    display: flex;
    align-items: center;
    justify-content: center;
}
.search-box {
    border-radius: 30px;
    width: 315px;
    height: 36px;
    border:none;
    font-size: 12px;
    font-weight: 700;
    border: 1px solid #D8DCDE;
}
:host::ng-deep .mat-form-field-underline {
    display: none;
}
.search-input{
    position: relative;
    padding-left: 8px;
    width: 260px;
    font-size: 12px;
    font-weight: 700;
    top: -3.8px;
}
