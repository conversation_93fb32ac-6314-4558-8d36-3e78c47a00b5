import { SelectionModel } from '@angular/cdk/collections';
import { Component, HostListener, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { UploadService } from 'src/app/services/upload.service';
import { colorObj } from 'src/app/shared/color-object';

@Component({
  selector: 'app-view-upload-document',
  templateUrl: './view-upload-document.component.html',
  styleUrls: ['./view-upload-document.component.scss']
})
export class ViewUploadDocumentComponent implements OnInit {

  docFileList: any[] = [];
  isUploadProgress: boolean = false;
  selectedFiles = [];
  selection = new SelectionModel<any>(true, []);
  files = [];
  dat = {};
  complainantFiles = [];
  advertiserFiles = [];
  internalFiles = [];
  userData: any;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private dialogRef: MatDialogRef<ViewUploadDocumentComponent>,
    private uploadService: UploadService,
    private notify: NotificationService,
    private cs: ComplaintsService,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) { }

  ngOnInit(): void {
    this.getDocuments(this.data.ID);
    this.userData = JSON.parse(window.localStorage.getItem('userInfo'));
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  async onFileChange(event: any) {
    // ToDo: check here 
    if (!this.uploadService.validateFileExtension(event)) {
      this.notify.showNotification(
        "File format is not supported!",
        "top",
        "warning",
        0
      );
      return;
    }
    let sizeOfAllFiles = this.uploadService.getTotalFileSize(this.internalFiles) + this.uploadService.getTotalFileSize(event.target.files);
    // let totalFileSelected = this.internalFiles.length + event.target.files.length;
    if (sizeOfAllFiles <= 36700160) {
      this.isUploadProgress = true;
      for (let i = 0; i <= event.target.files.length - 1; i++) {
        var selectedFile = event.target.files[i];
        this.internalFiles.push(selectedFile);
        let tempObjforGetsigned = {
          id: this.data.ID,
          section: this.userData.roleId == 5 ? 'company_member' : 'internal',
          filename: selectedFile['name'],
          type: selectedFile['type']
        }
        await this.uploadService.getSignedUrl(tempObjforGetsigned).then(async (res) => {
          if (res && res['data'] && res['data']['SIGNED_URL']) {
            let tempObjForUpload = {
              url: res['data']['SIGNED_URL'],
              file: selectedFile
            }
            await this.uploadSignedFile(tempObjForUpload);
            let saveBody = {
              ID: this.data.ID,
              KEY: res['data']['PATH'],
              SOURCE_NAME: selectedFile['name'],
              TYPE: selectedFile['type'],
              TAB: this.userData.roleId == 5 ? 'advertiser' : 'internal',
            }
            await this.saveDocumentToDB(saveBody);
          }
        })
          .catch((err) => {
            this.isUploadProgress = false;
            this.internalFiles.splice(i, 1);
            this.handleError(err);
          });
      }
      this.isUploadProgress = false;
      this.notify.showNotification(
        'Documents uploaded successfully',
        "top",
        (!!colorObj[200] ? colorObj[200] : "success"),
        200
      );
    } else {
      this.notify.showNotification(
        "Max size 35MB allowed",
        "top",
        "warning",
        0
      );
    }
  }

  async saveDocumentToDB(tempObj) {
    await this.cs.saveFileSourceToDB(tempObj).then((data) => {
      return data;
    })
      .catch((err) => {
        this.isUploadProgress = false;
        let body = {
          ID: tempObj['ID'],
          KEY: tempObj['KEY']
        }
        this.deleteFileFromS3(body)
        this.handleError(err);
      });
    this.getDocuments(this.data.ID);
  }

  deleteFileFromS3(obj: any) {
    this.uploadService.deleteObjectFromS3(obj).subscribe((res) => {
      return res;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
      return err;
    })
  }

  getFileName(file) {
    if (file['name']) {
      return file['name'];
    } else if (file['ATTACHMENT_SOURCE']) {
      return file['ATTACHMENT_SOURCE'].substring(file['ATTACHMENT_SOURCE'].lastIndexOf("/") + 1);
    }
  }

  getDocuments(id) {
    this.cs.getDocumentsByComplaint(id).subscribe(res => {
      this.docFileList = res.data;
      this.complainantFiles = this.docFileList['COMPLAINANT'];
      this.advertiserFiles = this.docFileList['ADVERTISER'];
      this.internalFiles = this.docFileList['INTERNAL'];
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    });
  }

  async uploadSignedFile(tempObj) {
    let progressInit = 0;
    await this.uploadService.uploadFilethroughSignedUrl(tempObj, progressInit => {
    }).catch((err) => {
      this.isUploadProgress = false;
      this.handleError(err);
    });
  }

  handleError(err: any) {
    this.notify.showNotification(
      err.error.message,
      "top",
      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
      err.error.status
    );
  }

  cancelUpload() {
    this.selectedFiles = [];
  }

  uploadDocs() {
    this.files = this.selection.selected;
    for (let i = 0; i < this.files.length; i++) {
      this.dat = { "filename": this.files[i].ATTACHMENT_SOURCE_NAME, "path": this.files[i].ATTACHMENT_SOURCE }
      this.selectedFiles.push(this.dat);
    }
    this.removeDuplicates(this.selectedFiles);
    this.dialogRef.close(this.selectedFiles);
  }

  async removeDuplicates(data) {
    let unique = [];
    data.forEach(element => {
      if (!unique.includes(element)) {
        unique.push(element);
      }
    })
    this.selectedFiles = unique;
  }

}