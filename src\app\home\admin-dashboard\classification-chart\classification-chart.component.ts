import { AfterViewInit, Component, ElementRef, OnInit } from '@angular/core';
import * as d3 from 'd3';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';

@Component({
  selector: 'app-classification-chart',
  templateUrl: './classification-chart.component.html',
  styleUrls: ['./classification-chart.component.scss']
})
export class ClassificationChartComponent implements OnInit, AfterViewInit {

  loading: boolean = true;

  constructor(
    private el: ElementRef,
    private cs: ComplaintsService,
    private notify: NotificationService
  ) { }

  ngOnInit(): void { }

  ngAfterViewInit(): void {
    this.getChartData();
  }

  getChartData() {
    this.cs.getMasterData().subscribe(res => {
      const data = [
        {
          letter: "A",
          frequency: 0.08167
        },
        {
          letter: "B",
          frequency: 0.01492
        },
        {
          letter: "C",
          frequency: 0.02782
        },
        {
          letter: "D",
          frequency: 0.04253
        },
        {
          letter: "E",
          frequency: 0.12702
        },
        {
          letter: "F",
          frequency: 0.02288
        },
        {
          letter: "G",
          frequency: 0.02015
        }
      ];
      this.drawChart(data);
    }, err => {
      this.loading = false;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  drawChart(data: any): void {
    this.loading = false;
    const element = this.el.nativeElement.querySelector('#classification');
    element.innerHTML = '';

    // Specify the chart’s dimensions, based on a bar’s height.
    const barHeight = 25;
    const marginTop = 30;
    const marginRight = 20;
    const marginBottom = 20;
    const marginLeft = 30;
    const width = element.clientWidth;
    const height = Math.ceil((data.length + 0.1) * barHeight) + marginTop + marginBottom;

    // Create the scales.
    const x = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.frequency)])
      .range([marginLeft, width - marginRight]);

    const y = d3.scaleBand()
      .domain(d3.sort(data, d => -d.frequency).map(d => d.letter))
      .rangeRound([marginTop, height - marginBottom])
      .padding(0.1);

    // Create a value format.
    const format = x.tickFormat(20, "%");

    // Create the SVG container.
    const svg = d3.create("svg")
      .attr("width", width)
      .attr("height", height)
      .attr("viewBox", [0, 0, width, height])
      .attr("style", "max-width: 100%; height: auto; font: 10px sans-serif;");

    // Append a rect for each letter.
    svg.append("g")
      .attr("fill", "steelblue")
      .selectAll()
      .data(data)
      .join("rect")
      .attr("x", x(0))
      .attr("y", (d) => y(d.letter))
      .attr("width", (d) => x(d.frequency) - x(0))
      .attr("height", y.bandwidth());

    // Append a label for each letter.
    svg.append("g")
      .attr("fill", "white")
      .attr("text-anchor", "end")
      .selectAll()
      .data(data)
      .join("text")
      .attr("x", (d) => x(d.frequency))
      .attr("y", (d) => y(d.letter) + y.bandwidth() / 2)
      .attr("dy", "0.35em")
      .attr("dx", -4)
      .text((d) => format(d.frequency))
      .call((text) => text.filter(d => x(d.frequency) - x(0) < 20) // short bars
        .attr("dx", +4)
        .attr("fill", "black")
        .attr("text-anchor", "start"));

    // Create the axes.
    svg.append("g")
      .attr("transform", `translate(0,${marginTop})`)
      .call(d3.axisTop(x).ticks(width / 80, "%"))
      .call(g => g.select(".domain").remove());

    svg.append("g")
      .attr("transform", `translate(${marginLeft},0)`)
      .call(d3.axisLeft(y).tickSizeOuter(0));

    element.appendChild(svg.node());
  }

}