import { Component, HostListener, Inject, OnInit } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-reech-delete-reason',
  templateUrl: './reech-delete-reason.component.html',
  styleUrls: ['./reech-delete-reason.component.scss']
})
export class ReechDeleteReasonComponent implements OnInit {

  reason = new FormControl('', Validators.required);
  reasonDelete: any[];
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    public dialogRef: MatDialogRef<ReechDeleteReasonComponent>,
    @Inject(MAT_DIALOG_DATA) public message: any,
  ) { }

  ngOnInit(): void {
    this.reasonDelete = JSON.parse(window.localStorage.getItem('reasonDelete'));
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  onSubmit(): void {
    // Close the dialog, return true
    this.dialogRef.close({ 'state': true, 'id': this.message.id, 'reason': this.reason.value });
  }

  onClose(): void {
    // Close the dialog, return false
    this.dialogRef.close({ 'state': false, 'id': this.message.id, 'reason': this.reason.value });
  }

}