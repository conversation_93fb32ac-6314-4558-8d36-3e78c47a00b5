<div class="timeline-container">
    <div class="fixed-header">
        <div fxLayout="row" fxLayoutAlign="start center">
            <span style="padding: 8px 10px;font-size:14px;font-weight:600;">Complaint timeline:</span>
        </div>
        <mat-divider></mat-divider>
    </div>
    
    <div class="scroll-container">
        <div class="time-container">
        <div *ngIf="!timeLineExist">
            No timeline created ....
        </div>
        <div class="timeline" *ngIf="!timelineLoading">
            <ul>
            <li *ngFor="let item of taskTimeline">
                <div mat-line>
                <p *ngFor="let val of item.updates">
                    <span class="time-enents">{{val.label}} &nbsp;</span>
                    <span style="font-style: normal;font-weight: normal;font-size: 14px;color: #000000;">{{val.value}}</span>
                    </p>
                </div>
                <div>
                <p style="font-style: normal;font-weight: normal;font-size: 12px;color: #555454;">{{item.date}}</p>
                </div>
            </li>
            </ul>
        </div>
        </div>
        <mat-card *ngIf="timelineLoading"
        style="display: flex; justify-content: center; align-items: center; background: white;">
        <mat-progress-spinner color="primary" mode="indeterminate" diameter="70">
        </mat-progress-spinner>
        </mat-card>
    </div>
</div>