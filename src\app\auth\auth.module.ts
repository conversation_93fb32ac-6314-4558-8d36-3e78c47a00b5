import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { LoginRegisterComponent } from './login-register/login-register.component';
import { AdmindialogComponent } from './admindialog/admindialog.component';
import { AddUserComponent } from './add-user/add-user.component';
import { MyProfileComponent } from './my-profile/my-profile.component';
import { AuthComponent } from './auth.component';
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { AuthRoutingModule } from "./auth-routing.module";
import { MaterialModule } from "../material/material.module";
import { CommonModule } from "@angular/common";
import { ClipboardModule } from '@angular/cdk/clipboard';
import { SharedModule } from "../shared/shared.module";
import { FlexLayoutModule } from '@angular/flex-layout';
import { ChatbotComponent } from "./chatbot/chatbot.component";
import { UserAdministrationComponent } from './user-administration/user-administration.component';
import { AddAdvertiserComponent } from './add-advertiser/add-advertiser.component';
import { EditAdvertiserComponent } from './edit-advertiser/edit-advertiser.component';
import { AddEditCompanyComponent } from './add-edit-company/add-edit-company.component';
import { CreateCompanyComponent } from './create-company/create-company.component';
import { CompanyCreatedComponent } from './company-created/company-created.component';
import { FieldAdministrationComponent } from './field-administration/field-administration.component';
import { AddEditSubtagComponent } from './add-edit-subtag/add-edit-subtag.component';
import { AddEditConsumerComponent } from './add-edit-consumer/add-edit-consumer.component';
import { AddEditGeneralPublicComponent } from './add-edit-general-public/add-edit-general-public.component';
import { AddEditGovBodyComponent } from './add-edit-gov-body/add-edit-gov-body.component';
import { IntraAdministrationComponent } from './intra-administration/intra-administration.component';
import { MemberAdministrationComponent } from './member-administration/member-administration.component';
import { InfiniteScrollModule } from "ngx-infinite-scroll";

@NgModule({
  declarations: [
    AuthComponent,
    LoginRegisterComponent,
    AdmindialogComponent,
    ChatbotComponent,
    AddUserComponent,
    MyProfileComponent,
    UserAdministrationComponent,
    AddAdvertiserComponent,
    EditAdvertiserComponent,
    AddEditCompanyComponent,
    CreateCompanyComponent,
    CompanyCreatedComponent,
    FieldAdministrationComponent,
    AddEditSubtagComponent,
    AddEditConsumerComponent,
    AddEditGeneralPublicComponent,
    AddEditGovBodyComponent,
    IntraAdministrationComponent,
    MemberAdministrationComponent
  ],
  imports: [
    AuthRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    MaterialModule,
    CommonModule,
    ClipboardModule,
    SharedModule,
    FlexLayoutModule,
    InfiniteScrollModule
  ],
  entryComponents: [
    AdmindialogComponent,
    AddUserComponent,
    AddAdvertiserComponent,
    AddEditConsumerComponent,
    AddEditGeneralPublicComponent,
    AddEditGovBodyComponent,
    EditAdvertiserComponent,
    AddEditCompanyComponent,
    CreateCompanyComponent,
    CompanyCreatedComponent,
    AddEditSubtagComponent
  ],
  providers: [],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class AuthModule { }