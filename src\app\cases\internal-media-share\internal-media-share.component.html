<div class="body" fxLayout="column">
    <div fxLayout="row" class="header">
        <div class="head-container" fxLayout="row">
            <div class="heading"><img src="../../../assets/images/Group 1224.png"></div>
            <h3 class="heading" style="padding-left: 13px;">Sharing file with</h3>
        </div>
        <span style="flex: 1 1 auto;"></span>
    </div>
    <mat-divider></mat-divider>
    <div fxLayout="column" style="padding-top: 20px;" fxLayoutGap="20px">
        <div fxLayout="row">
            <div fxFlex="50%">
                <mat-checkbox [checked]="true" [disabled]="true" style="opacity: 0.5;"></mat-checkbox>
                <span style="padding-left: 8px;">ASCI only (Internal)</span>
            </div>
            <div fxFlex="50%">
                <mat-checkbox (change)="selectUser($event.checked, 'complainant')" [checked]="complainantShared">Complainants</mat-checkbox>
            </div>
        </div>
        <div fxLayout="row">
            <div fxFlex="50%">
                <mat-checkbox (change)="selectUser($event.checked, 'advertiser')" [checked]="advertiserShared">Advertisers</mat-checkbox>
            </div>
        </div>
    </div>
    <div class="footer" fxLayout="column" fxLayoutAlign="end end">
        <div class="footer-btns" fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="20px">
            <div>
                <button mat-button class="cancel-btn" (click)="cancelShare()">Cancel</button>
            </div>
            <div>
                <button mat-button class="done-btn" (click)="shareFile()">Done</button>
            </div>
        </div>
    </div>
</div>