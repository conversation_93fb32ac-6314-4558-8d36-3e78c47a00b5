.common-toolbar {
  border-bottom: 1px solid #D8DCDE;
  border-top: 0;
  border-left: none;
  border-right: none;
}
.heading-container {
  width: 60%; 
}
.options-container {
  width:40%;
}
.vertical-line {
  width: 1px;
  background-color: rgb(212, 207, 207); 
}
/* div.toolbar {
  background-color: white;
} */
.calendar-container::-webkit-scrollbar {
  display: none;
}
/* Hide scrollbar for IE, Edge and Firefox */
.calendar-container {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
.calendar-container {
  height: 100vh;
  overflow-y: scroll;
  overflow-x: hidden;
}
div.manage {
  /* width:300px; */
  margin-left: 4.41%;
}
.toolbar1 {
  background-color: white;
}
.btn-group button {
  /* background-color: white; */
  border: 2px solid red; 
  /* color: white;  */
  border-style: solid;
  border-color: black;
  cursor: pointer;
  /* float: left;  */
}
/* Clear floats (clearfix hack) */
.btn-group:after {
  content: "";
  clear: both;
  display: table;
}  
.btn-group button:not(:last-child) {
  border-right: none; /* Prevent double borders */
}  
/* Add a background color on hover */
.btn-group button:hover {
  background-color: #c5cec6c5;
}
.fill-remaining-space {
  flex: 1 1 auto;
}
.mat-divider-vertical {
  height: 100%;
  width:0px !important;
  position: inherit !important;
}
/* details for the 1st toolbar */
.toolbar {
  background-color: white;
  height: 60px; 
  width: auto;
  margin-left: 40px;
}
.calendar-head {
  padding-left:20px;
}
.toolbar-spacer {
  flex: 1 1 auto;
}
.toolbar-btns {
  float:right;
  display: flex;
  flex-direction: row;
  grid-gap:20px;
  /* padding-right: 10px; */
}
.search-btn {
  color: gray;
  border:1px solid gray;
}
.bell-btn {
  color: crimson;
  border:1px solid gray;
}
.admin-btn {
  color: crimson;
  background-color: rgb(236, 174, 174);
  border: 1px solid crimson;
  border-radius: 30px;
}
.admin-option-container {
  height: 150px;
  width: 150px;
}
.option-btn {
  line-height: 15px;
  height: 30px;
}
.option-text {
  line-height: 15px;
}
@media only screen and (max-width: 1250px) {
  .common-toolbar-mobile {
    border-bottom: 1px solid #D8DCDE;
    border-top: 0;
    border-left: none;
    border-right: none;
  }
  .dashboard-admin-heading {
    padding-left: 0;
    padding-top: 0;
    font-weight:600;
    font-size:18px;
    line-height: 23.94px;
    color: #ED2F45;
    padding-left: 20px;
    padding-top: 6px;
  }
  .dashboard-container {
    width: 100%;
    height: 90%;
    background: #E5E5E5;
  }
  .dasboard-subheading {
    padding-right: 20px;
    padding-top: 0px;
    font-weight:400;
    font-size:14px;
    line-height: 18.62px;
    padding-left: 20px;
    padding-top: 3px;
  }
}