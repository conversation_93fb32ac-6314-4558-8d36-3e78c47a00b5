import { AfterViewInit, Component, ElementRef, OnInit } from '@angular/core';
import * as d3 from 'd3';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';

@Component({
  selector: 'app-complaint-source-chart',
  templateUrl: './complaint-source-chart.component.html',
  styleUrls: ['./complaint-source-chart.component.scss']
})
export class ComplaintSourceChartComponent implements OnInit, AfterViewInit {

  loading: boolean = true;

  constructor(
    private el: ElementRef,
    private cs: ComplaintsService,
    private notify: NotificationService
  ) { }

  ngOnInit(): void { }

  ngAfterViewInit(): void {
    this.getChartData();
  }

  getChartData() {
    this.cs.getMasterData().subscribe(res => {
      const data = [
        {
          id: "A",
          value: 3938
        },
        {
          id: "B",
          value: 3812
        },
        {
          id: "C",
          value: 6714
        },
        {
          id: "D",
          value: 743
        },
        {
          id: "E",
          value: 3534
        },
        {
          id: "F",
          value: 5731
        },
        {
          id: "G",
          value: 7840
        }
      ];
      this.drawChart(data);
    }, err => {
      this.loading = false;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  drawChart(data: any): void {
    this.loading = false;
    const element = this.el.nativeElement.querySelector('#complaint_source');
    element.innerHTML = '';

    // Specify the dimensions of the chart.
    const width = element.clientWidth;
    const height = width;
    const margin = 1; // to avoid clipping the root circle stroke
    const name = d => d.id.split(".").pop(); // "Strings" of "flare.util.Strings"
    const group = d => d.id.split(".")[1]; // "util" of "flare.util.Strings"
    const names = d => name(d).split(/(?=[A-Z][a-z])|\s+/g); // ["Legend", "Item"] of "flare.vis.legend.LegendItems"

    // Specify the number format for values.
    const format = d3.format(",d");

    // Create a categorical color scale.
    const color = d3.scaleOrdinal(d3.schemeTableau10);

    // Create the pack layout.
    const pack = d3.pack()
      .size([width - margin * 2, height - margin * 2])
      .padding(3);

    // Compute the hierarchy from the (flat) data; expose the values
    // for each node; lastly apply the pack layout.
    const root = pack(d3.hierarchy({ children: data })
      .sum(d => d.value));

    // Create the SVG container.
    const svg = d3.create("svg")
      .attr("width", width)
      .attr("height", height)
      .attr("viewBox", [-margin, -margin, width, height])
      .attr("style", "max-width: 100%; height: auto; font: 10px sans-serif;")
      .attr("text-anchor", "middle");

    // Place each (leaf) node according to the layout’s x and y values.
    const node = svg.append("g")
      .selectAll()
      .data(root.leaves())
      .join("g")
      .attr("transform", d => `translate(${d.x},${d.y})`);

    // Add a title.
    node.append("title")
      .text(d => `${d.data.id}\n${format(d.value)}`);

    // Add a filled circle.
    node.append("circle")
      .attr("fill-opacity", 0.7)
      .attr("fill", d => color(group(d.data)))
      .attr("r", d => d.r);

    // Add a label.
    const text = node.append("text")
      .attr("clip-path", d => `circle(${d.r})`);

    // Add a tspan for each CamelCase-separated word.
    text.selectAll()
      .data(d => names(d.data))
      .join("tspan")
      .attr("x", 0)
      .attr("y", (d, i, nodes) => `${i - nodes.length / 2 + 0.35}em`)
      .text(d => d);

    // Add a tspan for the node’s value.
    text.append("tspan")
      .attr("x", 0)
      .attr("y", d => `${names(d.data).length / 2 + 0.35}em`)
      .attr("fill-opacity", 0.7)
      .text(d => format(d.value));

    element.appendChild(svg.node());
  }

}