.header-container {
  background: #000000;
  width: 100%;
  height: 84px;
  padding: 5%;
}
.headline-container,
.headline-container > div > span {
  color: #fff;
  font-size: 20px;
  font-weight: normal;
}
.bell-btn {
  padding-right: 14px;
  width: 39px;
  height: 26px;
}
.profile-btn {
  color: #FFFFFF;
  background: #0088CB;
  width: 36px;
  height: 36px;
  text-align: center;
  font-style: normal;
  font-weight: bold;
  font-size: 20px;
  line-height: 27px;
}
.comp-head {
  margin-left: 20px;
  color: #000000;
  font-style: normal;
  font-weight: 700;
  font-size: 13px;
  line-height: 16px;
  padding-top: 10px;
}
.comp-head-mandatory {
  color: red;
  margin-left: 20px;
  position: relative;
  top: 7px;
  font-style: normal;
  font-weight: normal;
  font-size: 11px;
  line-height: 13px;
}
.row-container {
  display: flex;
  flex-direction: row;
  margin-left: 20px;
  /* margin-right: 30px; */
}
.filename-holder {
  border: 1px solid #436ab3;
  width: 778px;
  padding: 6.5px;
}
.progress-bar-upload {
  margin-left: 20px;
  width: 778px;
}
@keyframes spinner {
  to {transform: rotate(360deg);}
}
.spinner:before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  margin-left: -10px;
  border-radius: 50%;
  border: 2px solid #ccc;
  border-top-color: #000;
  animation: spinner .6s linear infinite;
}
.row-container>div {
  margin-bottom: 8px;
  margin-right: 30px;
}
.input-container {
  display: flex;
  flex-direction: column;
  gap: 0px;
  /* width: 300px; */
}
.text-container {
  height: 20px;
  margin-bottom: 0px;
}
.control-container {
  /* height: 40px; */
  margin-top: 0px;
}
.input-field {
  width: 240px !important;
}
.upload-container {
  position: relative;
  overflow: hidden;
  display: inline-block;
}
.upload-btn {
  background-color: rgb(190, 202, 212);
  color: rgb(88, 88, 88);
  width: 160px;
  height: 35px;
  margin-top: 4px;
}
.upload-container input[type=file] {
  font-size: 100px;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
}
#url-icon {
  font-size: small;
  margin-bottom: 10px;
  position: relative;
  top: -6px;
}
.textarea-field {
  width: 783px !important;
}
.divider-container {
  width: 100%;
  margin: 10px 20px 10px 20px;
}
.btn-container {
  display: flex;
  flex-direction: row;
  margin-left: 20px;
}
.btn-container>div {
  margin-right: 10px;
}
.next-btn {
  width: 80px;
  border-radius: 15px;
  background-color: #0088CB;
  color: white;
}
.back-btn {
  width: 80px;
  border-radius: 15px;
}
.head {
  height: 84px;
}
.body {
  height: 100%;
  width: 100%;
  /* padding: 20px; */
}
.time::placeholder {
  color: #000000;
}

@media screen and (min-width: 350px) and (max-width: 1250px) {
  .flex {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .input-field {
    width: 100% !important;
  }
  .non-flex {
    background-color: yellow;
    display: flex;
  }
  .head {
    min-height: 84px;
  }
  .page,
  .form {
    width: 100%;
  }
  .body {
    height: calc(100% - 84px);
    width: 100%;
    padding: 20px;
    background: #F8F9F9;
  }
  :host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-flex {
    padding-bottom: 5px;
  }
  :host ::ng-deep .mat-select-trigger {
    padding-bottom: 5px;
    height: 0px;
  }
  .form {
    width: 100%;
  }
  .row-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  .row-container, .btn-container {
    margin-left: 0;
  }
  .row-container>div {
    margin-bottom: 0;
    margin-right: 0;
  }
  .input-container {
    width: 100%;
  }
  .example-boundary {
    /* width: 823px; */
    width: 100%;
    /* height: 80px; */
    max-width: 100%;
    background: #FEFEFE;
    border: 1px dashed #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
    /* padding: 3% 35% 2% 37%; */
    /* margin-bottom: 5%; */
  }
  .url-field {
    /* width: 823px; */
    width: 100%;
    margin-top: 13px;
  }
  .url-container-text {
    position: relative;
    top: -8px;
  }
  .upload-files-btn {
    width: 100%;
  }
  .filename-holder {
    width: 100%;
    padding: 6.5px;
  }
  .progress-bar-upload {
    margin-left: 0;
    width: 70%;
  }
  .tool-list {
    display: flex;
    list-style: none;
    padding: 0;
    overflow: hidden;
    margin-bottom: 0px;
  }
  .tool--btn {
    /* background-color: white; */
    background: transparent;
    color: #8b8b8b;
    font-size: 10px;
    display: block;
    border: none;
    padding-top: .5rem;
    margin: .3rem;
  }
  .textarea-field {
    width: 100% !important;
    /* width: 75%; */
    height: 150px;
    max-width: 100%;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
    /* padding: 3% 3% 2% 3%; */
    margin-bottom: 3%;
  }
  .textarea-field1 {
    width: 100%;
    height: 140px;
    max-width: 100%;
    box-sizing: border-box;
    border-radius: 4px;
    margin-bottom: 65px;
  }
  .text-container {
    height: auto;
    margin-bottom: 0px;
  }
}