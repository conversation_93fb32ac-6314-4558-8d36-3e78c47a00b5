<div class="file-container">
  <div class="mat-card-container" fxLayout="column" fxLayoutGap="1%">
      <div class="mat-card-scroll" fxLayout="column" fxLayoutGap="3%">
          <div class="details-content-container" fxLayout="column" fxLayoutGap="2%">
              <div class="file-head-container" fxLayout="row">
                  <div class="file-head" style="padding-top: 5px;width: 18%;">REECH file upload</div>
                  <div fxFlex="83%"></div>
                  <div class="close-div" style="margin-top: -24px;">
                      <mat-dialog-actions mat-dialog-close>
                          <button mat-icon-button class="close-btn" mat-dialog-close>
                              <mat-icon style="margin-bottom: 5px;">close</mat-icon>
                          </button>
                      </mat-dialog-actions>
                  </div>
              </div>
              <div>
                  <mat-divider class="classfy-head-divider"></mat-divider>
              </div>

              <div style="width: 100%;">
                <div fxLayout="row" fxLayoutGap="2%">
                    <div class="file-upload-main" style="width: 78%;">
                        <img src="../../assets/images/upload.png" style="padding-right: 8px;">
                        <span class="bolder" style="font-size: 16px;line-height: 24px;">File Upload</span>
                        <div class="file-upload">
                            <div class="browse-files">
                                <img src="../../assets/images/fileimage.png"
                                    style="position: relative;top: 10px;left: 57px;">
                                <div class="browse-file-heading">
                                    <input hidden type="file" #uploader (change)="uploadFile($event)"
                                        accept=".xls,.xlsx,.csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel" />
                                    <span class="bolder" style="text-decoration: underline; cursor: pointer;"
                                        (click)="uploader.click()">Browse File</span>
                                    <!-- <span style="color:#5A6F84;"> or paste the link below</span> -->
                                </div>
                                <!-- <div class="panel-body" fxLayout="row">
                                    <div class="doc-icon-container flex">
                                      <button mat-icon-button class="doc-btn flex">
                                        <img src="../../assets/images/Link.png" style="position: relative;bottom: 8px;width: 11px;">
                                      </button>
                                    </div>
                                    <div class="link-container">
                                        <input matInput>
                                    </div>
                                  </div> -->
                            </div>
                            <div class="panel-body1" fxLayout="row">
                                <div class="doc-icon-container flex">
                                    <button mat-icon-button class="doc-btn flex">
                                        <img src="../../assets/images/media_doc.svg"
                                            style="position: relative;bottom: 8px;">
                                    </button>
                                </div>
                                <div class="link-container">
                                    <p>{{selectedFile?.name}}</p>
                                </div>
                                <button mat-button mat-icon-button aria-label="Clear" (click)="removeFile()">
                                    <img src="../../assets/images/close-red.png" class="close-icon">
                                </button>
                            </div>
                            <mat-divider></mat-divider>
                            <div class="upload-buttons">
                                <!-- <button mat-stroked-button class="cancel-button" (click)="cancel()">Cancel</button> -->
                                <button mat-flat-button class="upload-button" [disabled]="!fileAdded" (click)="submit()">
                                    <span class="bolder">Upload</span>
                                </button>
                            </div>
                        </div>
                        <app-mat-spinner-overlay overlay="true" *ngIf="isUploadProgress">
                        </app-mat-spinner-overlay>
                    </div>
                    <div class="file-history" style="width: 58%;">
                        <div fxLayout="row" fxLayoutGap="7px">
                            <mat-icon style="position: relative;height:19px;">access_time</mat-icon>
                            <span class="bolder" style="font-size: 16px;line-height: 24px;">File History</span>
                        </div>
                      
                    <div class="nams-table" infiniteScroll [infiniteScrollDistance]="2" [infiniteScrollThrottle]="300" 
                        (scrolled)="onScrollDown()" [scrollWindow]="false">
                          <table mat-table #table [dataSource]="dataSource" matSort>
                              <ng-container matColumnDef="FILE_NAME">
                                  <th mat-header-cell *matHeaderCellDef style="width: 65%; font-weight: bold;"> File
                                      Name </th>
                                  <td mat-cell *matCellDef="let row" [matTooltip]="row.FILE_NAME" style="width: 65%;">
                                      {{row.FILE_NAME | slice:0:35}} </td>
                              </ng-container>
                              <ng-container matColumnDef="CREATED_DATE">
                                  <th mat-header-cell *matHeaderCellDef style="width: 35%; padding-left: 10px; font-weight: bold;"> Upload
                                      Date </th>
                                  <td mat-cell *matCellDef="let row" style="width: 35%; padding-left: 20px;">
                                      {{row.CREATED_DATE | date:'dd/MM/yyyy'}} </td>
                              </ng-container>
                              <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
                              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                          </table>
                      </div>
                  </div>
                </div>
            </div>

          </div>
      </div>
  </div>
</div>
