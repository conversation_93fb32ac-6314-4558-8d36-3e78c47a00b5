import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ChatbotComponent } from "./chatbot/chatbot.component";
import { AuthGuardService } from "../services/auth-guard.service";
import { AuthComponent } from "./auth.component";
import { LoginRegisterComponent } from "./login-register/login-register.component";
import { MyProfileComponent } from "./my-profile/my-profile.component";
import { UserAdministrationComponent } from "./user-administration/user-administration.component";
import { FieldAdministrationComponent } from "./field-administration/field-administration.component";
import { IntraAdministrationComponent } from "./intra-administration/intra-administration.component";
import { MemberAdministrationComponent } from "./member-administration/member-administration.component";

const routes: Routes = [
  {
    path: '',
    component: AuthComponent,
    children: [
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full'
      },
      {
        path: 'login',
        component: LoginRegisterComponent
      },
      {
        path: 'chatbot',
        component: ChatbotComponent
      },
      {
        path: 'user-administration',
        canActivate: [AuthGuardService],
        data: {
          role: [1,2]
        },
        component: UserAdministrationComponent
      },
      {
        path: 'intra-administration',
        canActivate: [AuthGuardService],
        data: {
          role: [5]
        },
        component: IntraAdministrationComponent
      },
      {
        path: 'field-administration',
        canActivate: [AuthGuardService],
        data: {
          role: [1,2]
        },
        component: FieldAdministrationComponent
      },
      {
        path: 'member-administration',
        canActivate: [AuthGuardService],
        data: {
          role: [1,2]
        },
        component: MemberAdministrationComponent
      },
      {
        path: 'my-profile',
        canActivate: [AuthGuardService],
        data: {
          role: [1,2,3,4,5,6,7,8,9]
        },
        component: MyProfileComponent
      },
      
    ]
  }
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AuthRoutingModule { }

