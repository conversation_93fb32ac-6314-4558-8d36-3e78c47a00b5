import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientModule } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AppComponent } from './app.component';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MaterialModule } from './material/material.module';
import { HomeComponent } from './home/<USER>';
import { InboxComponent } from './inbox/inbox.component';
import { InboxToolbarComponent } from './inbox-toolbar/inbox-toolbar.component';
import { CalendarComponent } from './calendar/calendar.component';
import { ClipboardModule } from '@angular/cdk/clipboard';
import { DatePipe } from '@angular/common';
import { FlatpickrModule } from 'angularx-flatpickr';
import { CalendarModule, DateAdapter } from 'angular-calendar';
import { adapterFactory } from 'angular-calendar/date-adapters/date-fns';
import { NgbModalModule } from '@ng-bootstrap/ng-bootstrap';
import { AutoCompleteComponent } from './auto-complete/auto-complete.component';
import { FilterPopupComponent } from './filter-popup/filter-popup.component';
import { Ng2SearchPipeModule } from 'ng2-search-filter';
import { DialogConfirmComponent } from './dialog-confirm/dialog-confirm.component';
import { PopoverModule } from 'ngx-smart-popover';
import { FilterPipe } from './shared/filter.pipe';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { AppRoutingModule } from './app-routing.module';
import { SharedModule } from './shared/shared.module';
import { CasesComponent } from './cases/cases.component';
import { ThirdPartyInboxComponent } from './third-party-inbox/third-party-inbox.component';
import { CaseModule } from './cases/cases.module';
import { EventsComponent } from './calendar/events/events.component';
import { AddEventsComponent } from './calendar/add-events/add-events.component';
import { NamsEmailComponent } from './nams-email/nams-email.component';
import { AddEditCompanyComponent } from './auth/add-edit-company/add-edit-company.component';
import { ComplaintConversationsComponent } from './cases/complaint-conversations/complaint-conversations.component';
import { MobileNotificationsComponent } from './mobile-notifications/mobile-notifications.component';
import { SimilarComplaintDetailsComponent } from './similar-complaint-details/similar-complaint-details.component';
import { RecommendationComponent } from './recommendation/recommendation.component';
import { ResolutionComponent } from './resolution/resolution.component';
import { QuillModule } from 'ngx-quill';
import { RecommendationResolutionDetailsComponent } from './recommendation-resolution-details/recommendation-resolution-details.component';
import { MergeCompaniesComponent } from './merge-companies/merge-companies.component';
import { MarkInvalidComponent } from './mark-invalid/mark-invalid.component';
import { MeetingComponent } from './meeting/meeting.component';
import { MigrateDataComponent } from './migrate-data/migrate-data.component';
import { MatTimepickerModule } from 'mat-timepicker';
import { HomeModule } from './home/<USER>';
import { ArchivedDataComponent } from './archived-data/archived-data.component';
import { ArchivedDetailsComponent } from './archived-details/archived-details.component';

@NgModule({
  declarations: [
    AppComponent,
    HomeComponent,
    InboxComponent,
    InboxToolbarComponent,
    CalendarComponent,
    AutoCompleteComponent,
    FilterPopupComponent,
    DialogConfirmComponent,
    FilterPipe,
    CasesComponent,
    ThirdPartyInboxComponent,
    EventsComponent,
    AddEventsComponent,
    NamsEmailComponent,
    MobileNotificationsComponent,
    SimilarComplaintDetailsComponent,
    RecommendationComponent,
    ResolutionComponent,
    RecommendationResolutionDetailsComponent,
    MergeCompaniesComponent,
    MarkInvalidComponent,
    MeetingComponent,
    MigrateDataComponent,
    ArchivedDataComponent,
    ArchivedDetailsComponent
  ],
  entryComponents: [
    EventsComponent,
    AddEventsComponent,
    NamsEmailComponent,
    AddEditCompanyComponent,
    ComplaintConversationsComponent,
    RecommendationComponent,
    SimilarComplaintDetailsComponent,
    ResolutionComponent,
    RecommendationResolutionDetailsComponent,
    MergeCompaniesComponent,
    MarkInvalidComponent,
    ArchivedDetailsComponent
  ],
  imports: [
    AppRoutingModule,
    BrowserModule,
    BrowserAnimationsModule,
    HttpClientModule,
    FormsModule, ReactiveFormsModule,
    ReactiveFormsModule.withConfig({ warnOnNgModelWithFormControl: 'never' }),
    FlexLayoutModule,
    MaterialModule,
    Ng2SearchPipeModule,
    PopoverModule,
    NgbModalModule,
    ClipboardModule,
    MatAutocompleteModule,
    SharedModule,
    InfiniteScrollModule,
    CaseModule,
    HomeModule,
    MatTimepickerModule,
    FlatpickrModule.forRoot(),
    CalendarModule.forRoot({
      provide: DateAdapter,
      useFactory: adapterFactory,
    }),
    QuillModule.forRoot()
  ],
  exports: [],
  providers: [DatePipe],
  bootstrap: [AppComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AppModule { }