<div class="common-toolbar-mobile" style="width: 100%; height: auto;" *ngIf="isMobile">
    <app-mobile-header></app-mobile-header>
</div>
<span class="dashboard-admin-heading" *ngIf="isMobile">
    <p style="text-align: center;margin-top: 20px;margin-bottom: 3px;"> Welcome {{userName}}!</p>
</span>
<div class="dasboard-subheading" *ngIf="isMobile">
    <p style="text-align: center;">Dear ASCI Officers, please move to a larger device for seamlessly accessing all
        functionalities of the system</p>
</div>
<div class="dashboard-container" *ngIf="isMobile">
</div>

<app-mat-spinner-overlay overlay="true" *ngIf="loading">
</app-mat-spinner-overlay>

<app-icons *ngIf="!isMobile"></app-icons>

<div class="common-toolbar" fxLayout="row" style="width: 100%;height: 50px;" *ngIf="!isMobile">
    <div class="heading-container">
        <app-heading [pagename]="pagename"></app-heading>
    </div>
    <div class="options-container">
        <app-toolbar-options></app-toolbar-options>
    </div>
</div>

<div class="migration-container">
    <form [formGroup]="advertiserForm" class="form">
        <div class="comp-head">
            COMPLAINANT DETAILS
        </div>
        <div class="comp-head-mandatory">
            * labled fields are mandatory
        </div>

        <div class="step1-container">
            <div class="row-container">
                <div class="input-container1">
                    <div class="text-container">
                        Creating complaint on behalf of<span style="color: red;"> * </span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <mat-select formControlName="on_bahalf">
                                <mat-option *ngFor="let person of behalf" [value]="person.ID"
                                    (click)="changeComplainant(person.ID)">
                                    {{person.USER_TYPE_NAME}}
                                </mat-option>
                            </mat-select>
                            <mat-error class="error-msg" *ngIf="advertiserForm.controls['on_bahalf'].errors?.required">
                                Please choose complainant type
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container1">
                    <div class="text-container">
                        Complainant name
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="complainant_name" autocomplete="off">
                        </mat-form-field>
                    </div>
                </div>
            </div>
        </div>

        <div class="divider-container">
            <mat-divider></mat-divider>
        </div>

        <div class="comp-head">
            COMPLAINT DETAILS
        </div>
        <div class="comp-head-mandatory">
            * labled fields are mandatory
        </div>

        <div class="step1-container">
            <div class="row-container">
                <div class="input-container">
                    <div class="text-container">
                        Advertiser's company<span style="color: red;"> * </span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput type="text" formControlName="company" [matAutocomplete]="autoCompany"
                                (blur)="companyInput()" (input)="companyChange()">
                            <mat-autocomplete #autoCompany="matAutocomplete"
                                (optionSelected)="onSelectionChange($event)">
                                <mat-option *ngFor="let company of companyList;" [value]="company.COMPANY_NAME" style="
                                font-style: normal;
                                font-weight: normal;">
                                    {{company.COMPANY_NAME}}
                                </mat-option>
                            </mat-autocomplete>
                            <mat-error class="error-msg" *ngIf="advertiserForm.controls['company'].errors?.required">
                                Company name is required
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container">
                    <div class="text-container">
                        Brand name of product / service <span style="color: red;">*</span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="brand" autocomplete="off">
                            <mat-error class="error-msg" *ngIf="advertiserForm.controls['brand'].errors?.required">
                                Brand name is required
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container">
                    <div class="text-container">
                        Product name <span style="color: red;">*</span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="product" autocomplete="off">
                            <mat-error class="error-msg" *ngIf="advertiserForm.controls['product'].errors?.required">
                                Product name is required
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
            </div>

            <div class="row-container">
                <div class="input-container">
                    <div class="text-container">
                        Product category
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="product_category" autocomplete="off">
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container">
                    <div class="text-container">
                        Complaint number<span style="color: red;"> * </span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="old_comp_num" autocomplete="off">
                            <mat-error class="error-msg"
                                *ngIf="advertiserForm.controls['old_comp_num'].errors?.required">
                                Complaint number is required
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container">
                    <div class="text-container">
                        Reference ID / Old C.No / IRP No
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="ref_id" autocomplete="off">
                        </mat-form-field>
                    </div>
                </div>
            </div>

            <div class="row-container">
                <div [ngClass]="{ 'input-container': advertiserForm.controls['comp_medium'].value == 2, 'input-container1': advertiserForm.controls['comp_medium'].value != 2 }">
                    <div class="text-container">
                        GAMA Tracking ID
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="gama_id" autocomplete="off">
                        </mat-form-field>
                    </div>
                </div>
                <div [ngClass]="{ 'input-container': advertiserForm.controls['comp_medium'].value == 2, 'input-container1': advertiserForm.controls['comp_medium'].value != 2 }">
                    <div class="text-container">
                        Complaint medium<span style="color: red;"> * </span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <mat-select formControlName="comp_medium"
                                *ngIf="advertiserForm.controls['on_bahalf'].value != 1 && advertiserForm.controls['on_bahalf'].value != 2 && advertiserForm.controls['on_bahalf'].value != 3 && advertiserForm.controls['on_bahalf'].value != 4 && advertiserForm.controls['on_bahalf'].value != 5 && advertiserForm.controls['on_bahalf'].value != 6 && advertiserForm.controls['on_bahalf'].value != 7">
                                <mat-option *ngFor="let medium of compMedium" [value]="medium.ID">
                                    {{medium.COMPLAINT_SOURCE_NAME}}
                                </mat-option>
                            </mat-select>
                            <mat-select formControlName="comp_medium"
                                *ngIf="advertiserForm.controls['on_bahalf'].value == 1">
                                <mat-option *ngFor="let medium of compMedium" [value]="medium.ID"
                                    [disabled]="medium.ID == 6 || medium.ID == 7 || medium.ID ==8">
                                    {{medium.COMPLAINT_SOURCE_NAME}}
                                </mat-option>
                            </mat-select>
                            <mat-select formControlName="comp_medium"
                                *ngIf="advertiserForm.controls['on_bahalf'].value == 2 || advertiserForm.controls['on_bahalf'].value == 4 || advertiserForm.controls['on_bahalf'].value == 5">
                                <mat-option *ngFor="let medium of compMedium" [value]="medium.ID"
                                    [disabled]="medium.ID != 3">
                                    {{medium.COMPLAINT_SOURCE_NAME}}
                                </mat-option>
                            </mat-select>
                            <mat-select formControlName="comp_medium"
                                *ngIf="advertiserForm.controls['on_bahalf'].value == 3">
                                <mat-option *ngFor="let medium of compMedium" [value]="medium.ID"
                                    [disabled]="medium.ID == 1 || medium.ID == 6 || medium.ID == 7 || medium.ID ==8">
                                    {{medium.COMPLAINT_SOURCE_NAME}}
                                </mat-option>
                            </mat-select>
                            <mat-select formControlName="comp_medium"
                                *ngIf="advertiserForm.controls['on_bahalf'].value == 6">
                                <mat-option *ngFor="let medium of compMedium" [value]="medium.ID"
                                    [disabled]="medium.ID != 7">
                                    {{medium.COMPLAINT_SOURCE_NAME}}
                                </mat-option>
                            </mat-select>
                            <mat-select formControlName="comp_medium"
                                *ngIf="advertiserForm.controls['on_bahalf'].value == 7">
                                <mat-option *ngFor="let medium of compMedium" [value]="medium.ID"
                                    [disabled]="medium.ID != 7 && medium.ID != 8">
                                    {{medium.COMPLAINT_SOURCE_NAME}}
                                </mat-option>
                            </mat-select>
                            <mat-error class="error-msg"
                                *ngIf="advertiserForm.controls['comp_medium'].errors?.required">
                                Complaint medium is required
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container" *ngIf="advertiserForm.controls['comp_medium'].value == 2">
                    <div class="text-container">
                        Whatsapp ID
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="whatsapp_id" autocomplete="off">
                        </mat-form-field>
                    </div>
                </div>
            </div>

            <div class="row-container" *ngIf="advertiserForm.controls['comp_medium'].value == 2">
                <div class="input-container">
                    <div class="text-container">
                        Whatsapp profile name
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field2">
                            <input matInput formControlName="whatsapp_profile" autocomplete="off">
                        </mat-form-field>
                    </div>
                </div>
            </div>

            <div class="row-container" *ngIf="(advertiserForm.controls['on_bahalf'].value == 1 || advertiserForm.controls['on_bahalf'].value == 3)">
                <div [ngClass]="{ 'input-container1': selectedAdvId == 0 || selectedAdvId == 7, 'input-container': selectedAdvId != 0 && selectedAdvId != 7}"
                    *ngIf="(advertiserForm.controls['on_bahalf'].value == 1 || advertiserForm.controls['on_bahalf'].value == 3)">
                    <div class="text-container">
                        Where did you spot the advertisement<span style="color: red;"> * </span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <mat-select formControlName="seen_medium">
                                <mat-option *ngFor="let source of ads" (click)="changeAdvType(source)"
                                    [value]="source.ID">
                                    {{source.ADVERTISEMENT_SOURCE_NAME}}
                                </mat-option>
                            </mat-select>
                            <mat-error class="error-msg"
                                *ngIf="advertiserForm.controls['seen_medium'].errors?.required">
                                Please choose advertisement source name
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div [ngClass]="{ 'input-container1': selectedAdvId == 7, 'input-container': selectedAdvId != 7 }"
                    *ngIf="selectedAdvId != 3 && selectedAdvId != 0">
                    <div class="text-container" *ngIf="selectedAdvId == 1 || selectedAdvId == 2">Please specify the
                        channel <span style="color: red;">*</span></div>
                    <div class="text-container" *ngIf="selectedAdvId == 4">Name the place where you saw the hoarding?
                        <span style="color: red;">*</span></div>
                    <div class="text-container" *ngIf="selectedAdvId == 5">Where have you seen the advertisement? <span
                            style="color: red;">*</span></div>
                    <div class="text-container" *ngIf="selectedAdvId == 6">Type of material <span
                            style="color: red;">*</span></div>
                    <div class="text-container" *ngIf="selectedAdvId == 7">MFD/PKD Date <span
                            style="color: red;">*</span></div>
                    <div class="text-container" *ngIf="selectedAdvId == 8">Sender <span style="color: red;">*</span>
                    </div>
                    <div class="text-container" *ngIf="selectedAdvId == 9">Source <span style="color: red;">*</span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field"
                            *ngIf="(selectedAdvId == 1 || selectedAdvId == 2 || selectedAdvId == 8 || selectedAdvId == 9)">
                            <input matInput formControlName="channel" autocomplete="off">
                            <mat-error class="error-msg" *ngIf="advertiserForm.controls['channel'].errors?.required">
                                Please specify the source
                            </mat-error>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="basic-input-field" *ngIf="selectedAdvId == 4">
                            <input matInput formControlName="sourcePlace" autocomplete="off">
                            <mat-error class="error-msg"
                                *ngIf="advertiserForm.controls['sourcePlace'].errors?.required">
                                Please specify source name
                            </mat-error>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="basic-input-field" *ngIf="selectedAdvId == 5">
                            <mat-select formControlName="printSource">
                                <mat-option *ngFor="let print of printSources" [value]="print.ID">
                                    {{print.PRINT_SOURCE_NAME}}</mat-option>
                            </mat-select>
                            <mat-error class="error-msg"
                                *ngIf="advertiserForm.controls['printSource'].errors?.required">
                                Please choose source name
                            </mat-error>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="basic-input-field" *ngIf="selectedAdvId == 6">
                            <mat-select formControlName="promotionType">
                                <mat-option *ngFor="let promType of promotionTypes" [value]="promType.ID">
                                    {{promType.P_M_SOURCE_NAME}}</mat-option>
                            </mat-select>
                            <mat-error class="error-msg"
                                *ngIf="advertiserForm.controls['promotionType'].errors?.required">
                                Please choose the type of material
                            </mat-error>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="basic-input-field" *ngIf="selectedAdvId == 7">
                            <input matInput [matDatepicker]="picker" formControlName="date" [max]="maxDate"
                                autocomplete="off">
                            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                            <mat-datepicker #picker></mat-datepicker>
                            <mat-error class="error-msg" *ngIf="advertiserForm.controls['date'].errors?.required">
                                Please select the date
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container" *ngIf="selectedAdvId == 3">
                    <div class="text-container">
                        Please specify the platform <span style="color: red;">*</span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <mat-select formControlName="platform">
                                <mat-option *ngFor="let plat of platforms" [value]="plat.ID"
                                    (click)="changePlatform(plat)">
                                    {{plat.PLATFORM_NAME}}</mat-option>
                            </mat-select>
                            <mat-error class="error-msg" *ngIf="advertiserForm.controls['platform'].errors?.required">
                                Please choose platform
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container" *ngIf="selectedAdvId == 3 && platform_id == 9">
                    <div class="text-container">
                        Specify the platform name <span style="color: red;">*</span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="sourcePlace" autocomplete="off">
                            <mat-error class="error-msg"
                                *ngIf="advertiserForm.controls['sourcePlace'].errors?.required">
                                Please specify the platform
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container" *ngIf="selectedAdvId == 5">
                    <div class="text-container">
                        Specify the publication name & edition <span style="color: red;">*</span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="channel" autocomplete="off">
                            <mat-error class="error-msg"
                                *ngIf="advertiserForm.controls['channel'].errors?.required  && advertiserForm.controls['seen_medium'].value == 5">
                                Please specify publication name & edition
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container" *ngIf="selectedAdvId == 6 || selectedAdvId == 9">
                    <div class="text-container" *ngIf="selectedAdvId == 6">
                        Name the place where you saw the ad <span style="color: red;">*</span>
                    </div>
                    <div class="text-container" *ngIf="selectedAdvId == 9">
                        Place if applicable
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="sourcePlace" autocomplete="off">
                            <mat-error class="error-msg"
                                *ngIf="advertiserForm.controls['sourcePlace'].errors?.required  && advertiserForm.controls['seen_medium'].value == 6">
                                Please specify source name
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div [ngClass]="{ 'input-container1': (selectedAdvId == 0),'input-container': (selectedAdvId != 0) }"
                    *ngIf="selectedAdvId == 0 || selectedAdvId == 1 || selectedAdvId == 2 || (selectedAdvId == 3 && platform_id != 9) || selectedAdvId == 4 || selectedAdvId == 8">
                    <div class="text-container" *ngIf="selectedAdvId != 2">Date on which the ad was seen<span
                            style="color: red;"> * </span></div>
                    <div class="text-container" *ngIf="selectedAdvId == 2">Date on which the ad was heard<span
                            style="color: red;"> * </span></div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput [matDatepicker]="picker" formControlName="date" [max]="maxDate"
                                autocomplete="off">
                            <span matSuffix><img src="../../assets/images/calendar.svg"
                                    style="position: relative;top: -8px;" (click)="picker.open()"></span>
                            <mat-datepicker #picker></mat-datepicker>
                            <mat-error class="error-msg" *ngIf="advertiserForm.controls['date'].errors?.required">
                                Please select the date
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
            </div>

            <div class="row-container"
                *ngIf="(advertiserForm.controls['on_bahalf'].value == 1 || advertiserForm.controls['on_bahalf'].value == 3)">
                <div class="input-container1"
                    *ngIf="(selectedAdvId == 3 && platform_id == 9) || selectedAdvId == 5 || selectedAdvId == 6 || selectedAdvId == 9">
                    <div class="text-container">Date on which the ad was seen<span style="color: red;"> * </span></div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput [matDatepicker]="picker" formControlName="date" [max]="maxDate"
                                autocomplete="off">
                            <span matSuffix><img src="../../assets/images/calendar.svg"
                                    style="position: relative;top: -8px;" (click)="picker.open()"></span>
                            <mat-datepicker #picker></mat-datepicker>
                            <mat-error class="error-msg" *ngIf="advertiserForm.controls['date'].errors?.required">
                                Please select the date
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div [ngClass]="{ 'input-container1': (selectedAdvId == 3 && platform_id == 9),'input-container': !(selectedAdvId == 3 && platform_id == 9) }"
                    *ngIf="selectedAdvId == 1 || selectedAdvId == 2 || selectedAdvId == 3">
                    <div class="text-container">
                        What was the time<span style="color: red;">*</span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline"
                            [ngClass]="{ 'basic-input-field': (selectedAdvId == 3 && platform_id == 9),'basic-input-field2': !(selectedAdvId == 3 && platform_id == 9) }">
                            <input matInput class="time" placeholder="{{selectedTime}}" formControlName="time"
                                matTimepicker autocomplete="off">
                            <span matSuffix><img src="../../../assets/images/Clock.png"
                                    style="position: relative; top: -7px;" matTimepicker></span>
                            <mat-error class="error-msg" *ngIf="advertiserForm.controls['time'].errors?.required">
                                Please select the time
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
            </div>

            <div class="row-container">
                <div class="input-container" *ngIf="advertiserForm.controls['on_bahalf'].value == 6">
                    <div class="text-container">
                        Advertiser<span style="color: red;"> * </span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="advertiser" autocomplete="off">
                            <mat-error class="error-msg" *ngIf="advertiserForm.controls['advertiser'].errors?.required">
                                Advertiser name is required
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container" *ngIf="advertiserForm.controls['on_bahalf'].value == 6">
                    <div class="text-container">
                        Media <span style="color: red;">*</span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <mat-select formControlName="media">
                                <mat-option *ngFor="let source of ads" [value]="source.ID"
                                    [disabled]="source.ID != 1 && source.ID != 3 && source.ID != 5">
                                    {{source.ADVERTISEMENT_SOURCE_NAME}}
                                </mat-option>
                            </mat-select>
                            <mat-error class="error-msg" *ngIf="advertiserForm.controls['media'].errors?.required">
                                Please choode a media
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container" *ngIf="advertiserForm.controls['on_bahalf'].value == 6">
                    <div class="text-container">
                        Media outlet <span style="color: red;">*</span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="media_outlet" autocomplete="off">
                            <mat-error class="error-msg"
                                *ngIf="advertiserForm.controls['media_outlet'].errors?.required">
                                Media outlet is required
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container" *ngIf="advertiserForm.controls['on_bahalf'].value == 7">
                    <div class="text-container">
                        Medium<span style="color: red;"> * </span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <mat-select formControlName="medium" [disabled]="true">
                                <mat-option *ngFor="let source of ads" [value]="source.ID">
                                    {{source.ADVERTISEMENT_SOURCE_NAME}}
                                </mat-option>
                            </mat-select>
                            <mat-error class="error-msg" *ngIf="advertiserForm.controls['medium'].errors?.required">
                                Please choode a medium
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container" *ngIf="advertiserForm.controls['on_bahalf'].value == 7">
                    <div class="text-container">
                        Platform <span style="color: red;">*</span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <mat-select formControlName="reech_platform">
                                <mat-option *ngFor="let plat of platforms" [value]="plat.ID"
                                    [disabled]="plat.ID != 1 && plat.ID != 2">
                                    {{plat.PLATFORM_NAME}}</mat-option>
                            </mat-select>
                            <mat-error class="error-msg" *ngIf="advertiserForm.controls['platform'].errors?.required">
                                Please choose platform
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container" *ngIf="advertiserForm.controls['on_bahalf'].value == 7">
                    <div class="text-container">
                        Publication link <span style="color: red;">*</span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="publication_link" autocomplete="off">
                            <mat-error class="error-msg"
                                *ngIf="advertiserForm.controls['publication_link'].errors?.required">
                                Publication link is required
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
            </div>

            <div class="row-container">
                <div class="input-container"
                    *ngIf="advertiserForm.controls['on_bahalf'].value == 6 || advertiserForm.controls['on_bahalf'].value == 7">
                    <div class="text-container" *ngIf="advertiserForm.controls['on_bahalf'].value == 6">
                        Date <span style="color: red;">*</span>
                    </div>
                    <div class="text-container" *ngIf="advertiserForm.controls['on_bahalf'].value == 7">
                        Publication date <span style="color: red;">*</span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput [matDatepicker]="picker" formControlName="date" [max]="maxDate"
                                autocomplete="off">
                            <mat-datepicker-toggle matSuffix [for]="picker"
                                style="position: relative;bottom: 3px;"></mat-datepicker-toggle>
                            <mat-datepicker #picker></mat-datepicker>
                            <mat-error class="error-msg" *ngIf="advertiserForm.controls['date'].errors?.required">
                                Please choose the date
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container"
                    *ngIf="advertiserForm.controls['on_bahalf'].value == 6 || advertiserForm.controls['on_bahalf'].value == 7">
                    <div class="text-container">
                        Time <span style="color: red;">*</span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="time" matTimepicker autocomplete="off">
                            <span matSuffix><img src="../../../assets/images/Clock.png"
                                    style="position: relative; top: -7px;" matTimepicker></span>
                            <mat-error class="error-msg" *ngIf="advertiserForm.controls['time'].errors?.required">
                                Please select the time
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container" *ngIf="advertiserForm.controls['on_bahalf'].value == 6">
                    <div class="text-container">
                        Edition
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="edition" autocomplete="off">
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container" *ngIf="advertiserForm.controls['on_bahalf'].value == 7">
                    <div class="text-container">
                        Influencer name <span style="color: red;">*</span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="influencer_name" autocomplete="off">
                            <mat-error class="error-msg"
                                *ngIf="advertiserForm.controls['influencer_name'].errors?.required">
                                Influencer name is required
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
            </div>

            <div class="row-container" *ngIf="advertiserForm.controls['on_bahalf'].value == 6">
                <div class="input-container" *ngIf="advertiserForm.controls['on_bahalf'].value == 6">
                    <div class="text-container">
                        Supplement
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="suppliment" autocomplete="off">
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container">
                    <div class="text-container">
                        Ad language
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="ad_language" autocomplete="off">
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container">
                    <div class="text-container">
                        Creative ID <span style="color: red;">*</span>
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="creative_id" autocomplete="off">
                            <mat-error class="error-msg"
                                *ngIf="advertiserForm.controls['creative_id'].errors?.required">
                                Creative ID is required
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
            </div>

            <div class="row-container" *ngIf="advertiserForm.controls['on_bahalf'].value == 6">
                <div class="input-container1">
                    <div class="text-container">
                        Duration
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="duration" autocomplete="off">
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container1">
                    <div class="text-container">
                        Translation hyperlink
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <span matPrefix>
                                <span id="url-icon" class="glyphicon glyphicon-link"></span>&nbsp;&nbsp;
                            </span>
                            <input matInput type="url" formControlName="translation_hyperlink" autocomplete="off">
                        </mat-form-field>
                    </div>
                </div>
            </div>

            <div class="row-container" *ngIf="advertiserForm.controls['on_bahalf'].value == 7">
                <div class="input-container">
                    <div class="text-container">
                        Influencer profile URL
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="influencer_profile_URL" autocomplete="off">
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container">
                    <div class="text-container">
                        Influencer contact no.
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="influencer_contact_no" maxlength="10" autocomplete="off">
                        </mat-form-field>
                    </div>
                </div>
                <div class="input-container">
                    <div class="text-container">
                        Influencer email address
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field">
                            <input matInput formControlName="influencer_email_address" autocomplete="off">
                        </mat-form-field>
                    </div>
                </div>
            </div>

            <div class="row-container" *ngIf="advertiserForm.controls['on_bahalf'].value == 7">
                <div class="input-container">
                    <div class="text-container">
                        Engagements
                    </div>
                    <div class="control-container">
                        <mat-form-field appearance="outline" class="basic-input-field2">
                            <input matInput formControlName="engagements" autocomplete="off">
                        </mat-form-field>
                    </div>
                </div>
            </div>

            <div formArrayName="advCompany" *ngIf="advertiserForm.controls['on_bahalf'].value == 7">
                <div class="outer-btn-container" *ngFor="let advCompanyControl of getCompanyReech(); let in = index;"
                    formGroupName="{{in}}">
                    <div class="outer-row-container">
                        <div fxLayout="row" style="padding: 1% 0% 2% 2%;">
                            <div>
                                <img src="../../assets/images/drop-arrow.png" style="margin-right: 5px">
                                <mat-label style="color: #0088CB; font-size: 13px;">Advertisement company
                                    {{in+1}}</mat-label>
                            </div>
                            <div fxFlex="81%"></div>
                            <div>
                                <button mat-button matSuffix mat-icon-button aria-label="Clear" class="delete-red"
                                    (click)="removeCompanyReech(in)">
                                    <img src="../assets/images/Trash-icon.svg" style="margin-top: -15px;">
                                </button>
                            </div>
                        </div>
                        <div class="inner-row-container">
                            <div class="input-container">
                                <div class="text-container">
                                    <p>Company name</p>
                                </div>
                                <div class="control-container">
                                    <mat-form-field appearance="outline" class="input-field">
                                        <input matInput type="text" formControlName="advCompany"
                                            [matAutocomplete]="autoCompany">
                                        <mat-autocomplete #autoCompany="matAutocomplete"
                                            (optionSelected)="onAdvCompSelectionChange($event, in)">
                                            <mat-option
                                                *ngFor="let company of advCompanyControl.controls['advCompanyList'].value;"
                                                [value]="company.COMPANY_NAME"
                                                style="font-style: normal; font-weight: normal;">
                                                {{company.COMPANY_NAME}}
                                            </mat-option>
                                        </mat-autocomplete>
                                    </mat-form-field>
                                </div>
                            </div>
                            <div class="input-container">
                                <div class="text-container">
                                    <p>Contact email address</p>
                                </div>
                                <div class="control-container">
                                    <mat-form-field appearance="outline" class="input-field">
                                        <input matInput formControlName="advEmail" autocomplete="off">
                                    </mat-form-field>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <button mat-flat-button class="add-source-button" style="margin-left: 2%;"
                        [disabled]="getCompanyReech().length == 3" (click)="addAdvCompany()">
                        + Add advertisement company
                    </button>
                </div>
            </div>

            <div class="row-container"
                *ngIf="(advertiserForm.controls['on_bahalf'].value == 1 || advertiserForm.controls['on_bahalf'].value == 3 || advertiserForm.controls['on_bahalf'].value == 6 || advertiserForm.controls['on_bahalf'].value == 7)"
                style="margin-top: 15px;">
                <div class="input-container">
                    <div class="text-container">
                        Do you have a copy of the advertisement and/or other supporting documentation?
                    </div>
                    <div class="control-container" fxLayout="row">
                        <div class="upload-container">
                            <button mat-button class="upload-btn" [class.spinner]="isUploadProgress"
                                [disabled]="isUploadProgress" for="doc_file" (click)="fileInput.click()">
                                <mat-icon style="font-size: large;">cloud_upload</mat-icon>&nbsp;Upload your file
                                <input type="file" formControlName="doc_file" class="doc_file" id="doc_file" #fileInput
                                    hidden multiple="true"
                                    accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt,.jpeg,.png,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv"
                                    (change)="onFileSelectedGeneral($event)" />
                            </button>
                        </div>
                        <div fxFlex="8%"></div>
                        <div class="url-container">
                            <mat-form-field appearance="outline" style="width: 859px;">
                                <span matPrefix>
                                    <span id="url-icon" class="glyphicon glyphicon-link"></span>&nbsp;&nbsp;
                                </span>
                                <input matInput type="url" formControlName="add_url" placeholder="Add url"
                                    autocomplete="off">
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </div>

            <div *ngIf="general_files_attached == 'Yes'">
                <div *ngFor="let videos of advertiserForm.controls['file_array_general'].value; let index = index"
                    fxLayout="row" fxLayoutGap="5px" class="row-container filename-holder">
                    <div fxFlex="85%" fxLayoutAlign="start" style="color: rgb(150, 148, 148);">
                        <p class="adv-file-text">{{videos.ATTACHMENT_NAME}}</p>
                    </div>
                    <div fxFlex="5%" fxLayoutAlign="end" style="height: 33px;">
                        <button mat-button mat-icon-button (click)="preview(videos.ATTACHMENT_SOURCE)">
                            <img src="../../../assets/images/View.png" style="margin: 0px 10px 2px 10px;">
                        </button>
                    </div>
                    <div fxFlex="5%" fxLayoutAlign="end" style="height: 33px;">
                        <button mat-button mat-icon-button aria-label="Clear"
                            (click)="removeGeneralFile(index, videos.ATTACHMENT_SOURCE)">
                            <img src="../../assets/images/close-red.png" style="margin: 0px 10px 2px 10px;">
                        </button>
                    </div>
                </div>
                <section class="progress-bar-upload" *ngIf="fileProgress > 0">
                    <mat-progress-bar [color]="'primary'" [value]="fileProgress">
                    </mat-progress-bar>
                </section>
            </div>

            <div class="row-container" style="margin-top: 10px;"
                *ngIf="(advertiserForm.controls['on_bahalf'].value == 6)">
                <div class="input-container">
                    <div class="text-container" style="font-weight: 500; margin-bottom: 10px;">
                        Transcription (max 2000 characters)
                    </div>
                    <quill-editor class="textarea-field" placeholder="Tell us about the transcription in brief..."
                        [modules]="quillConfiguration" formControlName="transcription"
                        (onContentChanged)="textChangedTranscription($event)"
                        style="text-align:justify; text-justify: inter-word;" autocomplete="off">
                    </quill-editor>
                </div>
            </div>

            <div class="row-container" style="margin-top: 10px;"
                *ngIf="(advertiserForm.controls['on_bahalf'].value == 1 || advertiserForm.controls['on_bahalf'].value == 3 || advertiserForm.controls['on_bahalf'].value == 6 || advertiserForm.controls['on_bahalf'].value == 7)">
                <div class="input-container">
                    <div class="text-container" style="font-weight: 500; margin-bottom: 10px;">
                        Describe the Advertisement (max 5000 characters)<span style="color: red;"> * </span>
                    </div>
                    <quill-editor class="textarea-field"
                        placeholder="Tell us about the advertisement in brief... &#13;&#10;Eg: The advertisement shows two kids playing in mud and dirtying themselves, mother of one says don't worry you can take a bath with this soap and it has 10 times more power to protect against disease causing bacteria and viruses."
                        [modules]="quillConfiguration" formControlName="ad_description"
                        (onContentChanged)="textChangedAdDesc($event)"
                        style="text-align:justify; text-justify: inter-word;" autocomplete="off"> </quill-editor>
                    <mat-error class="error-msg"
                        *ngIf="advertiserForm.controls['ad_description'].touched && advertiserForm.controls['ad_description'].errors?.required"
                        style="position: relative;bottom: 19px;font-size: 12px;">
                        Describe the advertisement with max length 5000
                    </mat-error>
                </div>
            </div>

            <div class="row-container"
                *ngIf="(advertiserForm.controls['on_bahalf'].value == 1 || advertiserForm.controls['on_bahalf'].value == 3 || advertiserForm.controls['on_bahalf'].value == 6 || advertiserForm.controls['on_bahalf'].value == 7)">
                <div class="input-container">
                    <div class="text-container" style="margin-bottom: 10px; font-weight: 500;">
                        Specify the Claims/Visual Frames you find objectionable
                        (max 5000 characters)<span style="color: red;"> * </span>
                    </div>
                    <quill-editor class="textarea-field"
                        placeholder="Your claims regarding complaints &#13;&#10;Eg: Claim 1: 10 times more power protect against disease causing bacteria and viruses. &#13;&#10;OR Visual 1: Representation of coronavirus like virus implying soap gives 10times more protection against covid."
                        [modules]="quillConfiguration" formControlName="complaint_description" autocomplete="off"
                        (onContentChanged)="textChangedComplaintDesc($event)"
                        style="text-align:justify; text-justify: inter-word;">
                    </quill-editor>
                    <mat-error class="error-msg"
                        *ngIf="advertiserForm.controls['complaint_description'].touched && advertiserForm.controls['complaint_description'].errors?.required"
                        style="position: relative;bottom: 19px;font-size: 12px;">
                        Specify the claims with max length 5000
                    </mat-error>
                </div>
            </div>

            <div *ngIf="(advertiserForm.controls['on_bahalf'].value == 2 || advertiserForm.controls['on_bahalf'].value == 4 || advertiserForm.controls['on_bahalf'].value == 5)">
                <div class="row-container" style="margin-left:20px;">
                    <div class="text-container" style="margin-bottom: 11px;">
                        <mat-label style="font-weight: 500;">Advertisement medium details (Max 4)</mat-label>
                    </div>
                </div>
                <div formArrayName="advMedium">
                    <div class="outer-btn-container" *ngFor="let advMedium of getControls1(); let in = index;"
                        formGroupName="{{in}}">
                        <div class="outer-row-container">
                            <div fxLayout="row" style="padding: 1% 0% 2% 2%;">
                                <div fxFlex="20%">
                                    <img src="../../assets/images/drop-arrow.png" style="margin-right: 5px">
                                    <mat-label style="color: #0088CB; font-size: 13px;">Advertisement spot
                                        {{in+1}}</mat-label>
                                </div>
                                <div fxFlex="78%"></div>
                                <div fxFlex="2%" fxLayoutAlign="end">
                                    <button mat-button matSuffix mat-icon-button aria-label="Clear" class="delete-red"
                                        [disabled]="advertiserForm.controls.advMedium.controls.length == 1"
                                        (click)="removeAdve(in)">
                                        <img src="../assets/images/Trash-icon.svg" style="margin-top: -15px;">
                                    </button>
                                </div>
                            </div>
                            <div class="inner-row-container">
                                <div class="input-container">
                                    <div class="text-container">
                                        Where did you see the Ad <span style="color: red;">*</span>
                                    </div>
                                    <div class="control-container">
                                        <mat-form-field appearance="outline" class="input-field">
                                            <mat-select formControlName="seen_medium">
                                                <mat-option *ngFor="let source of ads" [value]="source.ID"
                                                    (click)="showFields(source, in)">
                                                    {{source.ADVERTISEMENT_SOURCE_NAME}}</mat-option>
                                            </mat-select>
                                            <mat-error class="error-msg"
                                                *ngIf="advMedium.controls['seen_medium'].value==''">
                                                Please choose advertisement source name
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                </div>
                                <div class="input-container">
                                    <div
                                        *ngIf="advMedium.controls['seen_medium'].value == 1 || advMedium.controls['seen_medium'].value == 2 || advMedium.controls['seen_medium'].value == 3 || advMedium.controls['seen_medium'].value == 5
                                                || advMedium.controls['seen_medium'].value == 6 || advMedium.controls['seen_medium'].value == 7 || advMedium.controls['seen_medium'].value == 8 || advMedium.controls['seen_medium'].value == 9">
                                        <div class="text-container">
                                            <span
                                                *ngIf="advMedium.controls['seen_medium'].value == 1 || advMedium.controls['seen_medium'].value == 2">
                                                Please specify the channel <span style="color: red;">*</span></span>
                                            <span *ngIf="advMedium.controls['seen_medium'].value == 3">Please specify
                                                the
                                                platform <span style="color: red;">*</span></span>
                                            <span *ngIf="advMedium.controls['seen_medium'].value == 5">Where have you
                                                seen
                                                the advertisement?
                                                <span style="color: red;">*</span></span>
                                            <span *ngIf="advMedium.controls['seen_medium'].value == 6">Type of material
                                                <span style="color: red;">*</span></span>
                                            <span *ngIf="advMedium.controls['seen_medium'].value == 8">Sender <span
                                                    style="color: red;">*</span></span>
                                            <span *ngIf="advMedium.controls['seen_medium'].value == 9">Source <span
                                                    style="color: red;">*</span></span>
                                            <span *ngIf="advMedium.controls['seen_medium'].value == 7">MFD/PKD Date
                                                <span style="color: red;">*</span></span>
                                        </div>
                                        <div class="control-container">
                                            <mat-form-field appearance="outline" class="input-field"
                                                *ngIf="!(advMedium.controls['seen_medium'].value == 3 || advMedium.controls['seen_medium'].value == 5 || advMedium.controls['seen_medium'].value == 6 || advMedium.controls['seen_medium'].value == 7)">
                                                <input matInput formControlName="method" autocomplete="off">
                                                <mat-error class="error-msg"
                                                    *ngIf="(advMedium.controls['method'].dirty ||advMedium.controls['method'].touched)">
                                                    Please specify the source
                                                </mat-error>
                                            </mat-form-field>
                                            <mat-form-field appearance="outline" class="input-field"
                                                *ngIf="advMedium.controls['seen_medium'].value == 3">
                                                <mat-select formControlName="platform">
                                                    <mat-option *ngFor="let plat of platforms" [value]="plat.ID"
                                                        (click)="changePlatform(plat)">
                                                        {{plat.PLATFORM_NAME}}</mat-option>
                                                </mat-select>
                                                <mat-error class="error-msg"
                                                    *ngIf="advMedium.controls['platform'].errors?.required  && (advMedium.controls['platform'].dirty ||advMedium.controls['platform'].touched)">
                                                    Please choose the platform
                                                </mat-error>
                                            </mat-form-field>
                                            <mat-form-field appearance="outline" class="input-field"
                                                *ngIf="advMedium.controls['seen_medium'].value == 5">
                                                <mat-select formControlName="printSource">
                                                    <mat-option *ngFor="let print of printSources" [value]="print.ID">
                                                        {{print.PRINT_SOURCE_NAME}}</mat-option>
                                                </mat-select>
                                                <mat-error class="error-msg"
                                                    *ngIf="advMedium.controls['printSource'].errors?.required ">
                                                    Please choose the print source
                                                </mat-error>
                                            </mat-form-field>
                                            <mat-form-field appearance="outline" class="input-field"
                                                *ngIf="advMedium.controls['seen_medium'].value == 6">
                                                <mat-select formControlName="promotionType">
                                                    <mat-option *ngFor="let promType of promotionTypes"
                                                        [value]="promType.ID">
                                                        {{promType.P_M_SOURCE_NAME}}</mat-option>
                                                </mat-select>
                                                <mat-error class="error-msg"
                                                    *ngIf="advMedium.controls['promotionType'].errors?.required">
                                                    Please choose the type of material
                                                </mat-error>
                                            </mat-form-field>
                                            <mat-form-field appearance="outline" class="input-field"
                                                *ngIf="advMedium.controls['seen_medium'].value == 7">
                                                <input matInput [matDatepicker]="picker" formControlName="date"
                                                    [max]="maxDate" autocomplete="off">
                                                <span matSuffix><img src="../../assets/images/calendar.svg" style="position: relative;top: -8px;"
                                                    (click)="picker.open()"></span>
                                                <mat-datepicker #picker></mat-datepicker>
                                                <mat-error class="error-msg"
                                                    *ngIf="advMedium.controls['date'].value==''">
                                                    Please choose the date
                                                </mat-error>
                                            </mat-form-field>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="inner-row-container"
                                *ngIf="advMedium.controls['seen_medium'].value == 3 && platform_id == 9">
                                <div class="input-container">
                                    <div class="text-container">
                                        Specify the platform name <span style="color: red;">*</span>
                                    </div>
                                    <div class="control-container">
                                        <mat-form-field appearance="outline" class="input-field">
                                            <input matInput formControlName="sourcePlace" autocomplete="off">
                                            <mat-error class="error-msg"
                                                *ngIf="advMedium.controls['sourcePlace'].errors?.required">
                                                Please specify the platform
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                </div>
                            </div>

                            <div class="inner-row-container">
                                <div class="input-container" *ngIf="advMedium.controls['seen_medium'].value != 7">
                                    <div class="text-container" *ngIf="advMedium.controls['seen_medium'].value != 2">
                                        Date on which the ad was seen <span style="color: red;">*</span>
                                    </div>
                                    <div class="text-container" *ngIf="advMedium.controls['seen_medium'].value == 2">
                                        Date on which the ad was heard <span style="color: red;">*</span>
                                    </div>
                                    <div class="control-container">
                                        <mat-form-field appearance="outline" class="input-field">
                                            <input matInput [matDatepicker]="picker" formControlName="date"
                                                [max]="maxDate" autocomplete="off">
                                            <span matSuffix><img src="../../assets/images/calendar.svg"
                                                    style="position: relative;top: -8px;"
                                                    (click)="picker.open()"></span>
                                            <mat-datepicker #picker></mat-datepicker>
                                            <mat-error class="error-msg" *ngIf="advMedium.controls['date'].value==''">
                                                Please select the date
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                </div>
                                <div class="input-container"
                                    *ngIf="advMedium.controls['seen_medium'].value == 4 || advMedium.controls['seen_medium'].value == 5
                                            || advMedium.controls['seen_medium'].value == 6 || advMedium.controls['seen_medium'].value == 9">
                                    <div class="text-container">
                                        <span *ngIf="advMedium.controls['seen_medium'].value == 4">Name the place where
                                            you
                                            saw the hoarding?
                                            <span style="color: red;">*</span></span>
                                        <span *ngIf="advMedium.controls['seen_medium'].value == 5">Specify the
                                            publication
                                            name & edition <span style="color: red;">*</span></span>
                                        <span *ngIf="advMedium.controls['seen_medium'].value == 6">Name the place where
                                            you
                                            saw the Ad? <span style="color: red;">*</span></span>
                                        <span *ngIf="advMedium.controls['seen_medium'].value == 9">Place if
                                            applicable</span>
                                    </div>
                                    <div class="control-container">
                                        <mat-form-field appearance="outline" class="input-field"
                                            *ngIf="!(advMedium.controls['seen_medium'].value == 5)">
                                            <input matInput formControlName="sourcePlace" autocomplete="off">
                                            <mat-error class="error-msg"
                                                *ngIf="advMedium.controls['sourcePlace'].errors?.required && !(advMedium.controls['seen_medium'].value == 5)">
                                                Please specify source place
                                            </mat-error>
                                        </mat-form-field>
                                        <mat-form-field appearance="outline" class="input-field"
                                            *ngIf="advMedium.controls['seen_medium'].value == 5">
                                            <input matInput formControlName="method" autocomplete="off">
                                            <mat-error class="error-msg"
                                                *ngIf="advMedium.controls['method'].errors?.required  && advMedium.controls['seen_medium'].value == 5">
                                                Please specify publication name & edition
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                </div>
                                <div class="input-container"
                                    *ngIf="advMedium.controls['seen_medium'].value == 1
                                            || advMedium.controls['seen_medium'].value == 2 || advMedium.controls['seen_medium'].value == 3">
                                    <div class="text-container">
                                        What was the time <span style="color: red;">*</span>
                                    </div>
                                    <div class="control-container">
                                        <mat-form-field appearance="outline" class="input-field">
                                            <input matInput formControlName="time" matTimepicker autocomplete="off">
                                            <span matSuffix><img src="../../../assets/images/Clock.png"
                                                    style="position: relative; top: -7px;" matTimepicker></span>
                                            <mat-error class="error-msg" *ngIf="advMedium.controls['time'].value==''">
                                                Please choose the time
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                </div>
                            </div>
                            <div class="inner-row-container">
                                <div class="input-container">
                                    <div class="text-container">
                                        Do you have a copy of the advertisement and/or other supporting documentation?
                                        Please
                                        upload/paste a link. <span style="color: red;">*</span>
                                    </div>
                                    <div class="control-container">
                                        <mat-form-field appearance="outline" class="input-field" style="width: 98%">
                                            <span matPrefix class="url-container-text">
                                                <img src="../../assets/images/url.png">
                                                &nbsp;
                                            </span>
                                            <input matInput type="url" formControlName="add_url" placeholder="Add url"
                                                autocomplete="off">
                                        </mat-form-field>
                                    </div>
                                </div>
                            </div>
                            <div class="inner-row-container">
                                <div fxFlex="43%"></div>
                                <p style="color: rgba(0, 0, 0, 0.6);">or</p>
                            </div>
                            <div class="inner-row-container">
                                <div class="example-boundary">
                                    <div fxLayout="column" cdkDrag (click)="fileInput.click()" style="cursor: pointer;">
                                        <div>
                                            <img style="margin-left: 27%;" src="../../assets/images/upload-icon.png">
                                        </div>
                                        <div fxLayout="row" style="margin-left: 13%;">
                                            <p style="color: #0088CB;">Upload</p>
                                            <p style="color: #707070;margin-left: 4px;">your files here</p>
                                            <p class="progress-spinner"
                                                *ngIf="(filesAdvertiseProgress[in]?.progress > 0 && filesAdvertiseProgress[in]?.progress !== 100)">
                                                <mat-spinner strokeWidth="3" [diameter]="20"></mat-spinner>
                                            </p>
                                        </div>
                                        <input style="display: none" [disabled]="isUploadAdvProgress" #attachments
                                            type="file" multiple="true"
                                            accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt,.jpeg,.png,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv"
                                            (change)="onFileChanged($event, in)" #fileInput>
                                    </div>
                                </div>
                            </div>
                            <div class="inner-row-container-docs" style="margin-left: 20px; margin-bottom: 20px;">
                                <div fxLayout="column" class="docs_attached"
                                    style="margin-bottom: 20px;margin-top: 30px;">
                                    <div *ngFor="let videos of advMedium.controls['advFileArray'].value; let index = index"
                                        fxLayout="row" fxLayoutGap="5px" class="file-container" style="width: 98%;">
                                        <div fxLayout="row" fxFlex="100%" class="adv_docs">
                                            <div fxLayout="row" fxFlex="100%"
                                                style="vertical-align: middle;align-items: center;">
                                                <div fxFlex="95%" fxLayoutAlign="start"
                                                    style="color: rgb(150, 148, 148);">
                                                    <p class="adv-file-text">{{videos.ATTACHMENT_NAME}}</p>
                                                </div>
                                                <div fxFlex="5%" fxLayoutAlign="end" style="height: 30px;">
                                                    <button mat-button mat-icon-button
                                                        (click)="preview(videos.ATTACHMENT_SOURCE)"
                                                        style="bottom: 5px;">
                                                        <img src="../../../assets/images/View.png"
                                                            style="margin: 0px 10px 0px 10px;">
                                                    </button>
                                                </div>
                                                <div fxFlex="5%" fxLayoutAlign="end" style="height: 30px;">
                                                    <button mat-button mat-icon-button aria-label="Clear"
                                                        (click)="removeVideoFile(in, index)" style="bottom: 5px;">
                                                        <img src="../../assets/images/close-red.png"
                                                            style="margin: 0px 10px 0px 10px;">
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <section *ngIf="filesAdvertiseProgress[in]?.progress > 0">
                                        <mat-progress-bar [color]='primary' [mode]="determinate"
                                            [value]="filesAdvertiseProgress[in]?.progress" style="width: 98%;">
                                        </mat-progress-bar>
                                    </section>
                                </div>
                            </div>
                            <mat-error style="position: relative;bottom: 63px;left: 23px;font-size: 12px;"
                                *ngIf="advMedium.controls['add_url'].dirty ||advMedium.controls['add_url'].touched">
                                {{getFileVal(in)}}
                            </mat-error>
                        </div>
                    </div>
                </div>
                <div fxLayout="row">
                    <div fxFlex="90%" fxLayoutAlign="end" style="margin-left: 19px;">
                        <button mat-flat-button class="add-source-button" (click)="addAdve()">
                            + Add advertisement medium
                        </button>
                    </div>
                </div>
                <div class="row-container" style="margin-top: 10px;">
                    <div class="input-container">
                        <div class="text-container" style="font-weight: 500; margin-bottom: 10px;">
                            Describe the Advertisement (max 5000 characters)<span style="color: red;"> * </span>
                        </div>
                        <quill-editor class="textarea-field"
                            placeholder="Tell us about the advertisement in brief... &#13;&#10;Eg: The advertisement shows two kids playing in mud and dirtying themselves, mother of one says don't worry you can take a bath with this soap and it has 10 times more power to protect against disease causing bacteria and viruses."
                            [modules]="quillConfiguration" formControlName="ad_description"
                            (onContentChanged)="textChangedAdDesc($event)"
                            style="text-align:justify; text-justify: inter-word;" autocomplete="off"> </quill-editor>
                        <mat-error class="error-msg"
                            *ngIf="advertiserForm.controls['ad_description'].touched && advertiserForm.controls['ad_description'].errors?.required"
                            style="position: relative;bottom: 19px;font-size: 12px;">
                            Describe the advertisement with max length 5000
                        </mat-error>
                    </div>
                </div>

                <div class="row-container" style="margin-left:20px;">
                    <div class="text-container" style="margin-bottom: 11px;">
                        <mat-label style="font-weight: 500;">ASCI Code provision violated (Max 5)</mat-label>
                        <span style="color: red;"> * </span>
                    </div>
                </div>
                <div formArrayName="asciCode">
                    <div class="outer-btn-container" *ngFor="let asciCodeControl of getControls2(); let in = index;"
                        formGroupName="{{in}}">
                        <div class="outer-row-container">
                            <div fxLayout="row" style="padding: 1% 0% 2% 2%;">
                                <div fxFlex="14%">
                                    <img src="../../assets/images/drop-arrow.png" style="margin-right: 5px">
                                    <mat-label style="color: #0088CB; font-size: 13px;">Code violated
                                        {{in+1}}</mat-label>
                                </div>
                                <div fxFlex="78%"></div>
                                <div fxFlex="8%" fxLayoutAlign="end">
                                    <button mat-button matSuffix mat-icon-button aria-label="Clear" class="delete-red"
                                        [disabled]="advertiserForm.controls.asciCode.controls.length == 1"
                                        (click)="removeCodes(in)">
                                        <img src="../assets/images/Trash-icon.svg" style="margin-top: -15px;">
                                    </button>
                                </div>
                            </div>
                            <div class="inner-row-container">
                                <div class="input-container">
                                    <div class="text-container">
                                        Chapter
                                    </div>
                                    <div class="control-container">
                                        <mat-form-field appearance="outline" class="input-field">
                                            <mat-select formControlName="chapter">
                                                <mat-option *ngFor="let chap of chapter; let i = index;"
                                                    [value]="chap.ID" (click)="selectChapter(chap, in)">
                                                    <strong>Ch:{{chap.ID}}</strong> {{chap.CHAPTER_NAME}}
                                                </mat-option>
                                            </mat-select>
                                            <mat-error class="error-msg"
                                                *ngIf="asciCodeControl.controls['chapter'].value==''">
                                                Please choose the chapter
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                </div>
                                <div class="input-container">
                                    <div class="text-container">
                                        Clause
                                    </div>
                                    <div class="control-container">
                                        <mat-form-field appearance="outline" class="input-field">
                                            <mat-select formControlName="clause" multiple>
                                                <mat-option
                                                    *ngFor="let clauseValues of asciCodeControl.controls['clauseArray'].value"
                                                    [value]="clauseValues.CLAUSE_NUMBER"
                                                    matTooltip="{{clauseValues.CLAUSE_DESCRIPTION}}"
                                                    (click)="selectClause(in)">
                                                    <strong>{{clauseValues.CLAUSE_NUMBER}}</strong> :
                                                    {{clauseValues.CLAUSE_DESCRIPTION}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                </div>
                            </div>
                            <div class="inner-row-container" *ngIf="isChapterPreamble" style="padding: 10px 0 ;">
                                <div class="input-container">
                                    <div class="text-container">
                                        <span style="color: #707070;">Chapter Preamble:</span>
                                        {{asciCodeControl.controls['preamble'].value}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div fxLayout="row">
                        <div fxFlex="90%" fxLayoutAlign="end" style="margin-left: 19px;">
                            <button mat-flat-button class="add-source-button" (click)="addCodes()">
                                + Add code violated
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row-container" style="margin-left:20px">
                    <div class="text-container" style="margin-bottom: 11px;">
                        <mat-label style="font-weight: 500;">ASCI Guideline violated (Max 5)</mat-label>
                        <span style="color: red;"> * </span>
                    </div>
                </div>
                <div formArrayName="guideline">
                    <div class="outer-btn-container" *ngFor="let asciCodeControl1 of getControls5(); let in = index;"
                        formGroupName="{{in}}"
                        [ngClass]="{'outer-btn-container1': asciCodeControl1.controls['gPreamble'].value == null}">
                        <div
                            [ngClass]="{'outer-row-container1': asciCodeControl1.controls['gPreamble'].value == null, 'outer-row-container': asciCodeControl1.controls['gPreamble'].value != null}">
                            <div fxLayout="row" style="padding: 1% 0% 2% 2%;">
                                <div fxFlex="18%">
                                    <img src="../../assets/images/drop-arrow.png" style="margin-right: 5px">
                                    <mat-label style="color: #0088CB; font-size: 13px;">Guideline violated
                                        {{in+1}}</mat-label>
                                </div>
                                <div fxFlex="76%"></div>
                                <div fxFlex="6%" fxLayoutAlign="end">
                                    <button mat-button matSuffix mat-icon-button aria-label="Clear" class="delete-red"
                                        [disabled]="advertiserForm.controls.guideline.controls.length == 1 || in == 0"
                                        (click)="removeGuidelines(in)">
                                        <img src="../assets/images/Trash-icon.svg" style="margin-top: -15px;">
                                    </button>
                                </div>
                            </div>
                            <div class="inner-row-container">
                                <div class="input-container">
                                    <div class="text-container">
                                        Select guideline
                                    </div>
                                    <div class="control-container">
                                        <mat-form-field appearance="outline" class="input-field">
                                            <mat-select formControlName="gChapter">
                                                <mat-option
                                                    *ngFor="let guideline of asciCodeControl1.controls['gGuidelineArray'].value; let i = index;"
                                                    matTooltip="{{guideline.GUIDELINE_NAME}}" [value]="guideline.ID"
                                                    (click)="selectGuideline(guideline, in)">
                                                    {{guideline.GUIDELINE_NAME}}
                                                </mat-option>
                                            </mat-select>
                                            <mat-error class="error-msg"
                                                *ngIf="asciCodeControl1.controls['gChapter'].value==''">
                                                Please choose the guideline
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                </div>
                                <div class="input-container" *ngIf="asciCodeControl1.controls['gPreamble'].value != null">
                                    <div class="text-container">
                                        Clause
                                    </div>
                                    <div class="control-container">
                                        <mat-form-field appearance="outline" class="input-field">
                                            <mat-select formControlName="gClause" multiple>
                                                <mat-option
                                                    *ngFor="let clauseValues of asciCodeControl1.controls['gClauseArray'].value"
                                                    [value]="clauseValues.G_CLAUSE_NUMBER"
                                                    matTooltip="{{clauseValues.G_CLAUSE_DESCRIPTION}}"
                                                    (click)="selectClause(in)">
                                                    <strong>{{clauseValues.G_CLAUSE_NUMBER}}</strong> :
                                                    {{clauseValues.G_CLAUSE_DESCRIPTION}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                </div>
                            </div>
                            <div class="inner-row-container" *ngIf="celebrityGuideline">
                                <div class="input-container">
                                    <div class="text-container">
                                        Celebrity names
                                    </div>
                                    <div class="control-container">
                                        <mat-form-field appearance="outline" class="input-field">
                                            <mat-chip-list #chipLists aria-label="celebrity" class="input-control">
                                                <mat-chip
                                                    *ngFor="let celebrity of asciCodeControl1.controls['gCelebrityArray'].value"
                                                    [selectable]="selectable" [removable]="removable"
                                                    (removed)="removeCelebrity(celebrity, in)" style="font-size: 11px;">
                                                    {{celebrity}}
                                                    <mat-icon matChipRemove>cancel</mat-icon>
                                                </mat-chip>
                                                <input placeholder="Enter celebrity names" [matChipInputFor]="chipLists"
                                                    [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                                                    [matChipInputAddOnBlur]="addOnBlur"
                                                    (matChipInputTokenEnd)="addCelebrity($event, in)"
                                                    style="word-wrap: break-word;word-break: break-all">
                                            </mat-chip-list>
                                            <mat-error class="error-msg"
                                                *ngIf="asciCodeControl1.controls['gCelebrityArray'].value==[]">
                                                Please enter celebrity names
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                </div>
                            </div>
                            <div class="inner-row-container"
                                *ngIf="isGuidelinePreamble && asciCodeControl1.controls['gPreamble'].value != null"
                                style="padding: 10px 0 ;">
                                <div class="input-container">
                                    <div class="text-container">
                                        <span style="color: #707070;">Guideline Preamble:</span>
                                        {{asciCodeControl1.controls['gPreamble'].value}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div fxLayout="row">
                        <div fxFlex="90%" fxLayoutAlign="end" style="margin-left: 19px;">
                            <button mat-flat-button class="add-source-button" (click)="addGuidelines()"
                                [disabled]="disableGuideline">
                                + Add guideline violated
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row-container" style="margin-left:20px;">
                    <div class="text-container" style="margin-bottom: 11px;">
                        <mat-label style="font-weight: 600;">Claims raised (Max 3)</mat-label>
                    </div>
                </div>
                <div formArrayName="claims">
                    <div class="outer-btn-container" *ngFor="let claims of getControls3(); let in = index;"
                        formGroupName="{{in}}">
                        <div class="outer-row-container">
                            <div fxLayout="row" style="padding: 1% 0% 2% 2%;">
                                <div fxFlex="20%">
                                    <mat-label style="color: #000000; font-size: 13px; font-weight: 500;">Claim
                                        Challenged
                                        {{in+1}}</mat-label>
                                </div>
                                <div fxFlex="73%"></div>
                                <div fxFlex="7%" fxLayoutAlign="end">
                                    <button mat-button matSuffix mat-icon-button aria-label="Clear" class="delete-red"
                                        [disabled]="advertiserForm.controls.claims.controls.length == 1"
                                        (click)="removeClaims(in)">
                                        <img src="../assets/images/Trash-icon.svg" style="margin-top: -15px;">
                                    </button>
                                </div>
                            </div>
                            <div class="inner-row-container">
                                <div class="input-container">
                                    <div class="text-container">
                                        Claim challenged
                                    </div>
                                    <div class="control-container">
                                        <mat-form-field appearance="outline" class="input-field">
                                            <input matInput formControlName="claimchallenge" autocomplete="off">
                                        </mat-form-field>
                                    </div>
                                </div>
                                <div class="input-container">
                                    <div class="text-container">
                                        Annexure no
                                    </div>
                                    <div class="control-container">
                                        <mat-form-field appearance="outline" class="input-field">
                                            <input matInput formControlName="annexure" autocomplete="off">
                                        </mat-form-field>
                                    </div>
                                </div>
                            </div>
                            <div class="inner-row-container">
                                <div class="input-container">
                                    <div class="text-container">
                                        Key objection (max 5000 characters)
                                    </div>
                                    <div class="objection-text">
                                        <quill-editor class="textarea-field"
                                            placeholder="Describe your key objections.." [modules]="quillConfiguration"
                                            formControlName="objections"
                                            (onContentChanged)="textChangedKeyObjections($event,in)" autocomplete="off"
                                            style="text-align:justify; text-justify: inter-word;"> </quill-editor>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div fxLayout="row">
                        <div fxFlex="90%" fxLayoutAlign="end" style="margin-left: 19px;">
                            <button mat-flat-button class="add-source-button" (click)="addClaimchallenges()">
                                + Add claim
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row-container">
                    <div class="input-container">
                        <div class="text-container" style="margin-bottom: 10px; font-weight: 500;">
                            Summary of list of documents to your complaint / claim
                        </div>
                        <div class="example-boundary1">
                            <div fxLayout="column" cdkDrag (click)="fileInput.click()" style="cursor: pointer;">
                                <div fxLayout="row" fxLayoutAlign="center">
                                    <img src="../../assets/images/upload-icon.png">
                                </div>
                                <div fxLayout="row" fxLayoutAlign="center">
                                    <p style="color: #0088CB;">Upload</p>
                                    <p style="color: #707070; margin-left: 4px;">your files here (max-size : 35MB &
                                        max-files :
                                        10 files)</p>
                                    <p class="progress-spinner" *ngIf="isUploadProgress">
                                        <mat-spinner strokeWidth="3" [diameter]="20"></mat-spinner>
                                    </p>
                                </div>
                                <input style="display: none" #attachments type="file" (change)="onFileSelected($event)"
                                    #fileInput multiple="true"
                                    accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt,.jpeg,.png,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv">
                            </div>
                        </div>
                    </div>
                </div>
                <div formArrayName="claimsDocument">
                    <div class="outer-btn-container" *ngFor="let claimsDoc of getControls4(); let in = index;"
                        formGroupName="{{in}}">
                        <div class="input-container">
                            <div fxLayout="column" fxLayoutGap="8px" class="docs_attached">
                                <div fxLayout="row" fxLayoutGap="5px" class="file-container">
                                    <div class="outer-btn-container">
                                        <div class="outer-row-container">
                                            <div fxLayout="row" style="padding: 1% 0% 2% 2%;">
                                                <div>
                                                    <mat-label
                                                        style="color: #000000; font-size: 13px; font-weight: 500;">Document
                                                        {{in+1}}
                                                    </mat-label>
                                                </div>
                                                <div fxFlex="86%"></div>
                                                <div>
                                                    <button mat-button matSuffix mat-icon-button aria-label="Clear"
                                                        class="delete-red" (click)="removeDocFile(in)">
                                                        <img src="../assets/images/Trash-icon.svg"
                                                            style="margin-top: -15px;">
                                                    </button>
                                                </div>
                                            </div>
                                            <div fxLayout="column" fxLayoutGap="20px">
                                                <div class="inner-row-container">
                                                    <div class="input-container">
                                                        <div fxLayout="row" fxLayoutGap="5px" fxFlex="100%"
                                                            class="adv_docs">
                                                            <div fxFlex="4%" style="height: 33px; width: 35px; background: rgba(0, 136, 203, 0.25);
                                                                      border-radius: 3px 0px 0px 3px;">
                                                                <img src="../../assets/images/File.png"
                                                                    style="margin: 12px 10px;">
                                                            </div>
                                                            <div fxLayout="row" fxFlex="95%"
                                                                style="vertical-align: middle;align-items: center;">
                                                                <div fxFlex="70%" fxLayoutAlign="start"
                                                                    style="color: rgb(150, 148, 148);">
                                                                    {{claimsDoc.controls['attachmentName'].value}}
                                                                </div>
                                                                <div fxFlex="15%" fxLayoutAlign="end"
                                                                    style="margin-bottom: -10px;"
                                                                    (click)="preview(claimsDoc.controls['attachmentSource'].value)">
                                                                    <p class="link-text">View</p>
                                                                </div>
                                                                <div fxFlex="15%" fxLayoutAlign="end"
                                                                    style="margin-bottom: -10px;"
                                                                    (click)="fileInput.click()">
                                                                    <input style="display: none" #attachments
                                                                        type="file"
                                                                        (change)="onFileReplaced($event, in)"
                                                                        accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt"
                                                                        #fileInput>
                                                                    <p class="link-text">Change document</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="inner-row-container">
                                                    <div class="input-container">
                                                        <div class="text-container">
                                                            Type of document
                                                        </div>
                                                        <div class="control-container">
                                                            <mat-form-field appearance="outline"
                                                                class="claims-input-field">
                                                                <input matInput formControlName="documentType"
                                                                    autocomplete="off">
                                                            </mat-form-field>
                                                        </div>
                                                    </div>
                                                    <div class="input-container">
                                                        <div class="text-container">
                                                            Annexure no.
                                                        </div>
                                                        <div class="control-container">
                                                            <mat-form-field appearance="outline"
                                                                class="claims-input-field">
                                                                <input matInput formControlName="doc_annex"
                                                                    autocomplete="off">
                                                            </mat-form-field>
                                                        </div>
                                                    </div>
                                                    <div class="input-container">
                                                        <div class="text-container">
                                                            Document name
                                                        </div>
                                                        <div class="control-container">
                                                            <mat-form-field appearance="outline"
                                                                class="claims-input-field">
                                                                <input matInput formControlName="doc_name"
                                                                    autocomplete="off">
                                                            </mat-form-field>
                                                        </div>
                                                    </div>
                                                </div>
                                                <section id="progress-id">
                                                    <mat-progress-bar [color]='primary' [mode]="determinate"
                                                        [value]="filesProgress[in]?.progress">
                                                    </mat-progress-bar>
                                                </section>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row-container">
                    <div class="input-container">
                        <div class="text-container" style="margin-bottom: 10px; font-weight: 500;">
                            Specify the Claims/Visual Frames you find objectionable
                            (max 5000 characters)<span style="color: red;"> * </span>
                        </div>
                        <quill-editor class="textarea-field"
                            placeholder="Your claims regarding complaints &#13;&#10;Eg: Claim 1: 10 times more power protect against disease causing bacteria and viruses. &#13;&#10;OR Visual 1: Representation of coronavirus like virus implying soap gives 10times more protection against covid."
                            [modules]="quillConfiguration" formControlName="complaint_description" autocomplete="off"
                            (onContentChanged)="textChangedComplaintDesc($event)"
                            style="text-align:justify; text-justify: inter-word;">
                        </quill-editor>
                        <mat-error class="error-msg"
                            *ngIf="advertiserForm.controls['complaint_description'].touched && advertiserForm.controls['complaint_description'].errors?.required"
                            style="position: relative;bottom: 19px;font-size: 12px;">
                            Specify the claims with max length 5000
                        </mat-error>
                    </div>
                </div>
            </div>

            <div class="divider-container">
                <mat-divider></mat-divider>
            </div>

            <div class="comp-head">
                OTHER DETAILS
            </div>
            <div class="comp-head-mandatory">
                * labled fields are mandatory
            </div>

            <div class="step1-container">
                <div class="row-container">
                    <div class="input-container">
                        <div class="text-container">
                            Financial year
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <mat-select formControlName="financial_year">
                                    <mat-option *ngFor="let year of years" [value]="year.value">
                                        {{year.viewValue}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="input-container">
                        <div class="text-container">
                            Complaint received date<span style="color: red;"> * </span>
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <input matInput [matDatepicker]="picker1" formControlName="received_date"
                                    [max]="maxDate" autocomplete="off">
                                <span matSuffix><img src="../../assets/images/calendar.svg"
                                        style="position: relative;top: -8px;" (click)="picker1.open()"></span>
                                <mat-datepicker #picker1></mat-datepicker>
                                <mat-error class="error-msg"
                                    *ngIf="advertiserForm.controls['received_date'].errors?.required">
                                    Please choose the date
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="input-container">
                        <div class="text-container">
                            Complaint registered date<span style="color: red;"> * </span>
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <input matInput [matDatepicker]="picker2" formControlName="registered_date"
                                    [max]="maxDate" autocomplete="off">
                                <span matSuffix><img src="../../assets/images/calendar.svg"
                                        style="position: relative;top: -8px;" (click)="picker2.open()"></span>
                                <mat-datepicker #picker2></mat-datepicker>
                                <mat-error class="error-msg"
                                    *ngIf="advertiserForm.controls['registered_date'].errors?.required">
                                    Please choose the date
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
                <div class="row-container">
                    <div class="input-container">
                        <div class="text-container">
                            Complaint status<span style="color: red;"> * </span>
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <mat-select formControlName="comp_status">
                                    <mat-option *ngFor="let status of compStatus" [value]="status.ID">
                                        {{status.COMPLAINT_STATUS_NAME}}
                                    </mat-option>
                                </mat-select>
                                <mat-error class="error-msg"
                                    *ngIf="advertiserForm.controls['comp_status'].errors?.required">
                                    Please choose the complaint status
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="input-container">
                        <div class="text-container">
                            Resolution<span style="color: red;"> * </span>
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <mat-select formControlName="resolution">
                                    <mat-option *ngFor="let resolution of resolutionList" [value]="resolution.ID">
                                        {{resolution.RESOLUTION_STATUS_NAME}}
                                    </mat-option>
                                </mat-select>
                                <mat-error class="error-msg"
                                    *ngIf="advertiserForm.controls['resolution'].errors?.required">
                                    Please choose the resolution
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="input-container"
                        *ngIf="(advertiserForm.controls['resolution'].value == 1 || advertiserForm.controls['resolution'].value == 3
                    || advertiserForm.controls['resolution'].value == 5 || advertiserForm.controls['resolution'].value == 6 || advertiserForm.controls['resolution'].value == 8)">
                        <div class="text-container">
                            Compliance status
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <mat-select formControlName="compliance">
                                    <mat-option *ngFor="let compliance of complianceList" [value]="compliance.ID">
                                        {{compliance.COMPLIANCE_STATUS_NAME}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="input-container"
                        *ngIf="!(advertiserForm.controls['resolution'].value == 1 || advertiserForm.controls['resolution'].value == 3
                    || advertiserForm.controls['resolution'].value == 5 || advertiserForm.controls['resolution'].value == 6 || advertiserForm.controls['resolution'].value == 8)">
                        <div class="text-container">
                            Stage<span style="color: red;"> * </span>
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <mat-select formControlName="stage">
                                    <mat-option *ngFor="let stage of stageList" [value]="stage.ID">
                                        {{stage.STAGE_NAME}}
                                    </mat-option>
                                </mat-select>
                                <mat-error class="error-msg" *ngIf="advertiserForm.controls['stage'].errors?.required">
                                    Please choose the stage
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
                <div class="row-container">
                    <div class="input-container"
                        *ngIf="(advertiserForm.controls['resolution'].value == 1 || advertiserForm.controls['resolution'].value == 3
                    || advertiserForm.controls['resolution'].value == 5 || advertiserForm.controls['resolution'].value == 6 || advertiserForm.controls['resolution'].value == 8)">
                        <div class="text-container">
                            Stage<span style="color: red;"> * </span>
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <mat-select formControlName="stage">
                                    <mat-option *ngFor="let stage of stageList" [value]="stage.ID">
                                        {{stage.STAGE_NAME}}
                                    </mat-option>
                                </mat-select>
                                <mat-error class="error-msg" *ngIf="advertiserForm.controls['stage'].errors?.required">
                                    Please choose the stage
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="input-container">
                        <div class="text-container">
                            Process
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <mat-select formControlName="process">
                                    <mat-option *ngFor="let process of processes" [value]="process.ID">
                                        {{process.PROCESS_NAME}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="input-container">
                        <div class="text-container">
                            Initial classification<span style="color: red;"> * </span>
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <mat-select formControlName="classification">
                                    <mat-option *ngFor="let classify of classificationList" [value]="classify.ID">
                                        {{classify.CLASSIFICATION_NAME}}
                                    </mat-option>
                                </mat-select>
                                <mat-error class="error-msg"
                                    *ngIf="advertiserForm.controls['classification'].errors?.required">
                                    Please choose initial classification
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="input-container"
                        *ngIf="!(advertiserForm.controls['resolution'].value == 1 || advertiserForm.controls['resolution'].value == 3
                    || advertiserForm.controls['resolution'].value == 5 || advertiserForm.controls['resolution'].value == 6 || advertiserForm.controls['resolution'].value == 8)">
                        <div class="text-container">
                            Complaint due date
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <input matInput [matDatepicker]="picker3" formControlName="complaint_due_date"
                                    autocomplete="off">
                                <span matSuffix><img src="../../assets/images/calendar.svg"
                                        style="position: relative;top: -8px;" (click)="picker3.open()"></span>
                                <mat-datepicker #picker3></mat-datepicker>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
                <div class="row-container">
                    <div class="input-container"
                        *ngIf="(advertiserForm.controls['resolution'].value == 1 || advertiserForm.controls['resolution'].value == 3
                    || advertiserForm.controls['resolution'].value == 5 || advertiserForm.controls['resolution'].value == 6 || advertiserForm.controls['resolution'].value == 8)">
                        <div class="text-container">
                            Complaint due date
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <input matInput [matDatepicker]="picker3" formControlName="complaint_due_date"
                                    autocomplete="off">
                                <span matSuffix><img src="../../assets/images/calendar.svg"
                                        style="position: relative;top: -8px;" (click)="picker3.open()"></span>
                                <mat-datepicker #picker3></mat-datepicker>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="input-container">
                        <div class="text-container">
                            Advertiser due date
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <input matInput [matDatepicker]="picker4" formControlName="advertiser_due_date"
                                    autocomplete="off">
                                <span matSuffix><img src="../../assets/images/calendar.svg"
                                        style="position: relative;top: -8px;" (click)="picker4.open()"></span>
                                <mat-datepicker #picker4></mat-datepicker>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="input-container">
                        <div class="text-container">
                            Subtags
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field"
                                style="word-wrap: break-word;word-break: break-all">
                                <mat-chip-list #chipLists aria-label="SubTag selection" class="input-control">
                                    <mat-chip *ngFor="let tags of subtags" [selectable]="selectable"
                                        [removable]="removable" (removed)="removesSubtags(tags)"
                                        style="font-size: 11px;">
                                        {{tags.SUB_TAG_NAME}}
                                        <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
                                    </mat-chip>
                                    <input #tagInput [matChipInputFor]="chipLists"
                                        [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                                        [matChipInputAddOnBlur]="addOnBlur" (matChipInputTokenEnd)="addSubtag($event)"
                                        style="word-wrap: break-word;word-break: break-all"
                                        [matAutocomplete]="autoSubTags" [formControl]="subtagCtrl">
                                    <mat-autocomplete #autoSubTags="matAutocomplete">
                                        <mat-option *ngFor="let subTag of subTagList | async" [value]="subTag"
                                            style="font-style: normal; font-weight: normal;"
                                            (onSelectionChange)="selectedSubtag(subTag, $event)">
                                            {{subTag.SUB_TAG_NAME}}
                                        </mat-option>
                                    </mat-autocomplete>
                                </mat-chip-list>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="input-container"
                        *ngIf="!(advertiserForm.controls['resolution'].value == 1 || advertiserForm.controls['resolution'].value == 3
                    || advertiserForm.controls['resolution'].value == 5 || advertiserForm.controls['resolution'].value == 6 || advertiserForm.controls['resolution'].value == 8)">
                        <div class="text-container">
                            Review requested by
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field">
                                <input matInput formControlName="review_requested" autocomplete="off">
                            </mat-form-field>
                        </div>
                    </div>
                </div>
                <div class="row-container">
                    <div class="input-container"
                        *ngIf="(advertiserForm.controls['resolution'].value == 1 || advertiserForm.controls['resolution'].value == 3
                    || advertiserForm.controls['resolution'].value == 5 || advertiserForm.controls['resolution'].value == 6 || advertiserForm.controls['resolution'].value == 8)">
                        <div class="text-container">
                            Review requested by
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field2">
                                <input matInput formControlName="review_requested" autocomplete="off">
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </div>

            <div class="divider-container">
                <mat-divider></mat-divider>
            </div>

            <div class="comp-head">
                RECOMMENDATION
            </div>

            <div formArrayName="recommendationArray">
                <div class="outer-btn-container" style="margin-top: 20px;"
                    *ngFor="let recommendationControl of getRecommendation(); let in = index;" formGroupName="{{in}}">
                    <div class="outer-row-container">
                        <div fxLayout="row" style="padding: 1% 0% 2% 2%;">
                            <div>
                                <img src="../../assets/images/drop-arrow.png" style="margin-right: 5px">
                                <mat-label style="color: #0088CB; font-size: 13px;">Recommendation {{in+1}}</mat-label>
                            </div>
                            <div fxFlex="72%"></div>
                            <div style="padding-top: 4px;">
                                <mat-checkbox
                                    formControlName="recommendation_publish"></mat-checkbox>&nbsp;&nbsp;Publish
                            </div>
                            <div fxFlex="5%"></div>
                            <div>
                                <button mat-button matSuffix mat-icon-button aria-label="Clear" class="delete-red"
                                    [disabled]="advertiserForm.controls.recommendationArray.controls.length == 1"
                                    (click)="removeRecommendation(in)">
                                    <img src="../assets/images/Trash-icon.svg" style="margin-top: -15px;">
                                </button>
                            </div>
                        </div>
                        <div class="inner-row-container">
                            <div class="input-container">
                                <div class="text-container">
                                    Summary of recommendation
                                </div>
                                <div class="control-container">
                                    <mat-form-field appearance="outline" class="input-field">
                                        <input matInput formControlName="recommendation_summary" autocomplete="off">
                                    </mat-form-field>
                                </div>
                            </div>
                        </div>
                        <div class="inner-row-container">
                            <div class="input-container">
                                <div class="text-container">
                                    Description of recommendation
                                </div>
                                <div class="control-container">
                                    <mat-form-field appearance="outline" class="input-field">
                                        <textarea rows="5" matInput formControlName="recommendation_desc"
                                            autocomplete="off"></textarea>
                                    </mat-form-field>
                                </div>
                            </div>
                        </div>
                        <div class="inner-row-container">
                            <div class="input-container">
                                <div class="text-container">
                                    Created date
                                </div>
                                <div class="control-container">
                                    <mat-form-field appearance="outline" class="input-field">
                                        <input matInput [matDatepicker]="picker5" formControlName="recommendation_date"
                                            [max]="maxDate" autocomplete="off">
                                        <span matSuffix><img src="../../assets/images/calendar.svg"
                                                style="position: relative;top: -8px;" (click)="picker5.open()"></span>
                                        <mat-datepicker #picker5></mat-datepicker>
                                    </mat-form-field>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div fxLayout="row">
                    <div fxFlex="90%" fxLayoutAlign="end" style="margin-left: 19px;">
                        <button mat-flat-button class="add-source-button" style="margin-left: 2%;"
                            [disabled]="getRecommendation().length == 5" (click)="addRecommendation()">
                            + Add recommendation
                        </button>
                    </div>
                </div>
            </div>

            <div class="divider-container">
                <mat-divider></mat-divider>
            </div>

            <div class="comp-head">
                RESOLUTION
            </div>

            <div class="step1-container">
                <div class="row-container">
                    <div class="input-container">
                        <div class="text-container">
                            Summary of resolution
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field1">
                                <input matInput formControlName="resolution_summary" autocomplete="off">
                            </mat-form-field>
                        </div>
                    </div>
                </div>
                <div class="row-container">
                    <div class="input-container">
                        <div class="text-container">
                            Description of resolution
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field1">
                                <textarea rows="5" matInput formControlName="resolution_desc"
                                    autocomplete="off"></textarea>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
                <div class="row-container">
                    <div class="input-container">
                        <div class="text-container">
                            Created date
                        </div>
                        <div class="control-container">
                            <mat-form-field appearance="outline" class="basic-input-field1">
                                <input matInput [matDatepicker]="picker6" formControlName="resolution_date"
                                    [max]="maxDate" autocomplete="off">
                                <span matSuffix><img src="../../assets/images/calendar.svg"
                                        style="position: relative;top: -8px;" (click)="picker6.open()"></span>
                                <mat-datepicker #picker6></mat-datepicker>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </div>

            <div class="divider-container">
                <mat-divider></mat-divider>
            </div>

            <div class="btn-container">
                <div class="next-container">
                    <button mat-flat-button class="next-btn"
                        [disabled]="checkMandatoryFields() || advertiserForm.invalid" (click)="submitForm()">
                        <span class="bolder">Submit</span>
                    </button>
                </div>
                <div class="cancel-container">
                    <button mat-stroked-button [disabled]="isUploadProgress || isUploadAdvProgress" class="cancel-btn"
                        (click)="cancel()">
                        <mat-icon *ngIf="isUploadProgress || isUploadAdvProgress">
                            <mat-spinner diameter="20">
                            </mat-spinner>
                        </mat-icon>
                        <span class="bolder">Cancel</span>
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>