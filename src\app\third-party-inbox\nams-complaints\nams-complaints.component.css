:host::ng-deep.mat-paginator-page-size-label {
    display: none;
}
:host ::ng-deep .mat-paginator-range-label {
    display: none;
}  
:host::ng-deep.mat-paginator-page-size-value {
    display: none;
}  
:host::ng-deep .mat-paginator-navigation-previous {
    color:#4DA1FF;
    margin-right: 43px;
}
:host::ng-deep .mat-paginator-navigation-next {
    color:#4DA1FF;
}
:host::ng-deep .mat-paginator-range-actions {
    margin-right: 34px;
}
mat-paginator {
    width: 92.4%;
    margin-top: 10px;
    margin-left: 5.8%;
    border-radius: 20px;
    background: #FFFFFF;
    box-sizing: border-box;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
}
.label {
    position: relative;
    bottom: 38px;
    color: rgb(155, 155, 155);
    left: 47%;
}
.common-toolbar {
    border-bottom: 1px solid #D8DCDE;
    border-top: 0;
    border-left: none;
    border-right: none;
}
.heading-container {
    width: 60%;
}
.options-container {
    width:40%;
}
.toolbar2 {
    background-color: rgb(245, 245, 245);
}
.toolbar2 >div {
    margin-left: 0.5%;
}
.comp-values {
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    color: #010101;
}
.header-search input::-webkit-input-placeholder {
    font-size: 11.5px;
}
.header-search input::-moz-placeholder {
    font-size: 11.5px;
}
.date-button input::-webkit-input-placeholder {
    color: #000000 !important;
    opacity: 1;
}
.date-button input::-moz-placeholder {
    color: #000000 !important;
    opacity: 1;
}
.date-button,.clear-button {
    border: 1px solid #D8DCDE;
    border-radius: 12px;
    position: relative;
    top: 3px;
}
.clear-button {
    background-color: rgb(245, 245, 245);
    border: none;
    margin-top: 5px;
}
.date {
    border: none;
    margin-bottom: 4px;
    height: 32px;
}
.vertical-divider {
    height: 47px;
    padding-left: 9px;
}
.text-message {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}
.horizontal-divider {
    position: relative;
    left: 3px;
    width: 12%;
}
.header-search input[type=text] {
    box-sizing: border-box;
    margin-top: 3%;
    position: relative;
    padding-left: 30px;
    color: #92A2B1;
    height: 39px !important;
    /* width: 164px !important; */
    border: 1px solid #CFD7DF!important;
    border-radius: 4px !important;
    border: 1px solid #CFD7DF!important;
    border-radius: 12px !important;
    background: url(../../../assets/images/search.png) no-repeat;
    background-position: 5%;
    background-color: white;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    z-index: 10;
}
.header-search ::placeholder {
    color: rgba(0, 0, 0, 0.4);
}
.send-btn {
    color: #5A6F84;
    background-color: #CFD7DF;
    border-radius: 12px;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    justify-content: center;
    align-items: center;
    height: 33px;
    z-index: 10;
    top: 3px;
    line-height: 33px;
}
.admin-table::-webkit-scrollbar {
    display: none;
}
.admin-table {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.admin-table {
    overflow-y: scroll;
    overflow-x: hidden;
    margin-left: 4.7%;
    width: 95%;
    height: min-content;
    position: relative;
    border-radius: 20px;
    border: 1px solid #D8DCDE;
    border-top: 0px;
}
.mat-row:nth-child(2n+2) {
    background-color: #F5F5F7;
}
.mat-column-BRAND {
    font-style: normal;
    font-weight: normal;
    font-size: 13px;
    color: #0088CB
}
.card::-webkit-scrollbar {
    display: none;
}
.card {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.card {
    background-color:rgb(245, 245, 245) ;
    box-shadow: none;
    border-width: 0px;
    height: fit-content;
    max-height: 67.5vh;
    position: relative;
    align-self: justify;
    width: 100%;
    padding-bottom: 0px;
    overflow-y: scroll;
    overflow-x: hidden;
}
table th {
    position: -webkit-sticky; 
    position: sticky;
    top: 0;
    z-index: 1; 
    background: #fff; 
}
.table-scroll::-webkit-scrollbar {
    display: none;
}
.table-scroll {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.table-scroll {
    overflow-y: scroll;
    overflow-x: hidden;
    height: 67vh;
}
.pen {
    color:#4DA1FF;
}
.aprv {
    color: #74D365;
}
::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: #aae7c8;
}
:host ::ng-deep .mat-checkbox .mat-checkbox-checkmark-path {
    stroke:  #006644 !important;
}
.mat-header-cell {
    font-size: 13px;
    color: #707070;
    font-style: normal;
    height: 35px !important;
}
.option-btn {
    line-height: 25%;
    height: 550%;
    padding: 5px 15px;
}
.option-btn[disabled] {
    opacity: 0.3;
}
.option-text {
    color: #000000;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 200%;
}
.option-divider {
    width: 90%;
    margin: 0px 10px !important;
    border: 1.5px solid rgba(47, 57, 65, 0.2);
    border-top: 0;
    border-right: 0;
    border-left: 0;
}
.ellipsis {
    overflow: hidden;
    width: 320px;
    display: inline-block;
    display: -webkit-inline-box;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    white-space: normal;
    -webkit-box-orient: vertical;
    vertical-align: middle;
    position: relative;
    margin-top: 10px;
    height: 40px !important;
}
.ellipsis1 {
    overflow: hidden;
    width: 160px;
    display: inline-block;
    display: -webkit-inline-box;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    white-space: normal;
    -webkit-box-orient: vertical;
    vertical-align: middle;
    position: relative;
    margin-top: 14px;
}
@media only screen and (max-width: 1250px) {
    .common-toolbar-mobile {
        border-bottom: 1px solid #D8DCDE;
        border-top: 0;
        border-left: none;
        border-right: none;
    }
    .dashboard-admin-heading {
        padding-left: 0;
        padding-top: 0;
        font-weight:600;
        font-size:18px;
        line-height: 23.94px;
        color: #ED2F45;
        padding-left: 20px;
        padding-top: 6px;
    }
    .dashboard-container {
        width: 100%;
        height: 90%;
        background: #E5E5E5;
    }
    .dasboard-subheading {
        padding-right: 20px;
        padding-top: 0px;
        font-weight:400;
        font-size:14px;
        line-height: 18.62px;
        padding-left: 20px;
        padding-top: 3px;
    }
}