::ng-deep .mat-form-field-flex > .mat-form-field-infix {padding: 5px 0px 0.4em 0px !important}
:host:ng-deep .mat-form-field-appearance-outline .mat-form-field-label { margin-top:-15px; }
:host:ng-deep label.ng-star-inserted { transform: translateY(-0.59375em) scale(.75) !important;}
:host:ng-deep .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    transform: translateY(-1.1em) scale(.75);
    width: 133.33333%;
}
:host ::ng-deep .mat-input-element {
    position: relative;
    bottom: 5px;
}
:host ::ng-deep .mat-select-value-text {
    position: relative;
    bottom: 2px;
}
.control-container {
    height: 54px;
}
.input-field {
    width:216px !important;
    margin-left: 8px;
}
.names {
    flex-direction: row;  
    display: flex;
    padding-top: 10px;
    color: #2F3941;
}
.names1 {
    color: #2F3941;  
}
.dept_role {
    flex-direction: row;  
    display: flex;
    grid-gap: 228px;
    color: #2F3941;
}
.input-field_lastname {
    width:288px !important;
    padding-left: 40px;
}
.toolbar {
    padding-left: 15px;
    height: 84px;
    padding-left: 15px;
    background-color: #F8F9F9;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #2F3941;
}
.search-btn {
    color: solid rgba(47, 57, 65, 0.6);
    background: #F3F3F3;  
    border: 1px solid rgba(47, 57, 65, 0.6);      
}
.input-field_phno {
    width: 558px;
}
.input-field_dept {
    width:270px !important;
    height: 20px !important;   
}
.lastdivdr {
    width: 560px;
}
.remove-btn {
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: #ED2F45;
    border: 1px solid #ED2F45;
    border-radius: 12px;
    box-sizing: border-box;
}
.toolbar-btns {
    grid-gap: 4%;
}
.toolbar2 {
    margin-left: -5px;
    background-color: white;
}
.cancel-btn {
    color: #284255;
    background-color: white;
    border-radius: 14px;
    margin-right: 10px;
}
.update-btn {
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: white;
    background-color: #0088CB;
    border-radius: 12px;
}
.edit-form::-webkit-scrollbar {
    display: none;
}
.edit-form {
    -ms-overflow-style: none;
    scrollbar-width: none; 
}
.edit-form {
    width: 580px;
    overflow-y: scroll;
    overflow-x: hidden;
    margin-top: 20px;
}
.contents::-webkit-scrollbar {
    display: none;
}
.contents {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.contents {
    margin-left: 5px;
    height: 316px;
    width: 580px;
    overflow-y: scroll;
    overflow-x: hidden;
}
.mat-dialog-actions {
    margin-right: 15px;
}