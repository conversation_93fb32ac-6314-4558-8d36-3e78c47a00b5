<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<app-icons *ngIf="!mobile"></app-icons>

<div class="common-toolbar" fxLayout="row" style="width: 100%;height: 50px;" *ngIf="!mobile">
  <div class="heading-container">
    <app-heading [pagename]="pagename"></app-heading>
  </div>
  <div class="options-container">
    <app-toolbar-options></app-toolbar-options>
  </div>
</div>
<div class="common-toolbar" style="width: 100%;height: auto;" *ngIf="mobile">
  <app-mobile-header></app-mobile-header>
</div>
<span class="dashboard-admin-heading" *ngIf="mobile">
  <p style="text-align: center;margin-top: 20px;margin-bottom: 3px;"> Welcome {{userName}}!</p>
</span>
<div class="dasboard-subheading" *ngIf="mobile">
  <p style="text-align: center;">Dear ASCI Officers, please move to a larger device for seamlessly accessing all
    functionalities of the system</p>
</div>

<div class="dashboard-container" *ngIf="mobile">
</div>

<div class="dashboard-container" fxLayout="row" *ngIf="!mobile">
  <div class="manage">
    <div class="head-container">
      <div class="comp-head">
        <h3 matSubheader class="head-text">
          <img src="/assets/images/Vector.png" /> Activities on complaints
        </h3>
      </div>
      <span class="head-spacer"></span>
    </div>
    <mat-divider style="margin-left: -7px; margin-top: -1px;"></mat-divider>
    <div class="dashboard-complaint-list">
      <div class="complaint-list" infiniteScroll [infiniteScrollDistance]="2" [infiniteScrollThrottle]="300"
        (scrolled)="onScrollDown()" [scrollWindow]="false">
        <mat-list-item *ngFor="let item of activitiesList">
          <div class="list-item-container" *ngFor="let complaint of item.updates">
            <div class="item-head-container" fxLayout="column">
              <div class="message" fxLayout="row">
                <!-- <p> -->
                <div class="dot" *ngIf="complaint.c_s_id == '1'"
                  style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #0088CB;">
                </div>
                <div class="dot" *ngIf="complaint.c_s_id == '2'"
                  style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #F89E1B;">
                </div>
                <div class="dot" *ngIf="complaint.c_s_id == '3'"
                  style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #ED2F45;">
                </div>
                <div class="dot" *ngIf="complaint.c_s_id == '4'"
                  style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #000000;">
                </div>
                <div class="dot" *ngIf="complaint.c_s_id == '5'"
                  style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #000000;">
                </div>
                <div class="dot" *ngIf="complaint.c_s_id == '6'"
                  style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #000000;">
                </div>
                <div class="dot" *ngIf="complaint.c_s_id == '7'"
                  style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #04A585;">
                </div>
                <div class="dot" *ngIf="complaint.c_s_id == '8'"
                  style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #000000;">
                </div>
                <div class="dot" *ngIf="complaint.c_s_id == null"
                  style="min-width: 12px; margin-top: 1%; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid darkgray;">
                </div>
                <!-- <img src="../../assets/images/Red circle.png" *ngIf="complaint.c_s_id == '5'" style="margin-right: 2%; height: 12px;; margin-top: 1%;"> 
                <img src="../../assets/images/Green circle.png" *ngIf="complaint.c_s_id== '7'" style="margin-top: 1%; margin-right: 2%; height: 12px;"> -->
                <div class="content-div">
                  {{complaint.label}} <span
                    [ngClass]="{'comp_name' : item.COMPLAINT.COMPLAINT_NAME == complaint.value, 'comp_stage' : complaint.label == 'Stage moved to', 'comp_prio1' : complaint.value == 'Low', 'comp_prio2' : complaint.value == 'Medium', 'comp_prio3' : complaint.value == 'High', 'comp_prio4' : complaint.value == 'Urgent'}">
                    {{complaint.value}} </span> <span *ngIf="complaint.label != 'Complaint received for'"> for complaint
                  </span>
                  <span *ngIf="complaint.label != 'Complaint received for'"
                    class="comp_name">{{item.COMPLAINT.COMPLAINT_NAME}}</span>
                </div>
                <!-- </p> -->
              </div>
              <div class="container-text">
                {{item.date}}
              </div>
              <div class="container-text">
                By
                {{item.COMPLAINT.ACTION_BY}}
              </div>
            </div>
          </div>
          <mat-divider></mat-divider>
        </mat-list-item>
      </div>
    </div>

    <!-- <p class="list-time">
      YESTERDAY
    </p>
    <div class="dashboard-complaint-list">
      <mat-list-item *ngFor="let complaint of complaints" (click)="clickEvent(complaint.id)">
        <div class="list-item-container">
          <div class="item-head-container" fxLayout="column">
            <div>
              <p>
                <i class="fa fa-circle-o" fxLayoutGap="10px"
                  style="font-size: 12px;position: relative;margin-top: -5px;color: blue" aria-hidden="true"></i>
                {{complaint.msg}}
              </p>
            </div>
            <div fxLayout="row" fxLayoutGap="20px">
              <div>
                <p style="color: rgb(150, 150, 150);"> {{complaint.time}}</p>
              </div>
              <div>
                <p style="color: rgb(150, 150, 150);">
                  <i class="fa fa-circle" fxLayoutGap="10px" style="font-size: 10px;position: relative;margin-top: -5px;"
                    aria-hidden="true"></i>
                  By
                  {{complaint.solved_by_person}}
                </p>
              </div>
            </div>
          </div>
        </div>
      </mat-list-item>
      <mat-divider></mat-divider><br>
    </div> -->
  </div>
  <div class="dashboard-admin" fxLayout="column">
    <mat-toolbar style="width: 100%;" class="toolbar1">
      <div fxLayout="column" fxFlex="30%" style="margin: 10px 20px">
        <span class="dashboard-admin-heading">
          Welcome {{userName}}!<br>
        </span>
        <span class="dasboard-subheading">
          Overview over the different complaints
        </span>
      </div>
    </mat-toolbar>
    <mat-divider></mat-divider>
    <div fxLayout="row" class="head-row">
      <mat-tab-group class="dashboard-tabs" [(selectedIndex)]="tabIndex" (selectedTabChange)="onTabChanged($event)">
        <mat-tab label="Analytics">
          <div class="box">
            <div fxLayout="row" fxLayoutAlign="end center">
              <mat-form-field class="example-full-width" appearance="outline" *ngIf="selectedTab == 0">
                <mat-select [formControl]="text_year" placeholder="{{initialValue}}"
                  (selectionChange)="selectByYear($event)" [(ngModel)]="year_value">
                  <mat-option *ngFor="let year of years" [value]="year.value" style="padding-left: 7%;">
                    {{year.viewValue}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div fxLayout="column">
              <div fxLayout="column" *ngFor="let item of complaintStats">
                <div fxLayout="row" class="card-row">
                  <mat-card class="card" style="border-bottom: 4px solid #0088CB;"
                    (click)="selectedStatusComplaints('New')">
                    <img src="../../assets/images/New.png" style="position: absolute;left: 27px;">
                    <div fxLayout="row" fxLayoutAlign="center center">
                      <div fxLayout="column" style="margin-right: 16px;">
                        <div class="digit"><b>{{item.NEW_COMPLAINTS}}</b></div>
                        <div class="category" style="margin-left: 4px;">NEW</div>
                      </div>
                    </div>
                  </mat-card>
                  <mat-card class="card" style="border-bottom: 4px solid #F89E1B;"
                    (click)="selectedStatusComplaints('In Progress')">
                    <img src="../../assets/images/process.png" style="position: absolute;left: 27px;">
                    <div fxLayout="row" fxLayoutAlign="center center">
                      <div fxLayout="column" style="margin-left: 22px;">
                        <div class="digit"><b>{{item.IN_PROGRESS}}</b></div>
                        <div class="category">IN PROGRESS</div>
                      </div>
                    </div>
                  </mat-card>
                  <mat-card class="card" style="border-bottom: 4px solid #ED2F45;"
                    (click)="selectedStatusComplaints('On Hold')">
                    <img src="../../assets/images/hold.png" style="position: absolute;left: 27px;">
                    <div fxLayout="row" fxLayoutAlign="center center">
                      <div fxLayout="column" style="margin-right: 19px;">
                        <div class="digit"><b>{{item.ON_HOLD}}</b></div>
                        <div class="category">ON HOLD</div>
                      </div>
                    </div>
                  </mat-card>
                  <mat-card class="card" style="border-bottom: 4px solid #04A585;"
                    (click)="selectedStatusComplaints('Resolution')">
                    <img src="../../assets/images/res.png" style="position: absolute;left: 27px;">
                    <div fxLayout="row" fxLayoutAlign="center center">
                      <div fxLayout="column" style="margin-left: 3%;">
                        <div class="digit"><b>{{item.RESOLVED}}</b></div>
                        <div class="category">RESOLVED</div>
                      </div>
                    </div>
                  </mat-card>
                </div>
                <div fxLayout="row" class="card-row1">
                  <mat-card class="card" style="border-bottom: 4px solid #0088CB;"
                    (click)="selectedStatusComplaints('Non-Issue')">
                    <img src="../../../assets/images/Non-issued.png" style="position: absolute;left: 27px;">
                    <div fxLayout="row" fxLayoutAlign="center center">
                      <div fxLayout="column" style="margin-left: 12px;">
                        <div class="digit" style="margin-left: 4px;"><b>{{item.NON_ISSUE}}</b></div>
                        <div class="category" style="margin-left: 5px;">NON-ISSUE</div>
                      </div>
                    </div>
                  </mat-card>
                  <mat-card class="card" style="border-bottom: 4px solid #F89E1B;"
                    (click)="selectedStatusComplaints('Out of remit/Outside ASCI Purview')">
                    <img src="../../../assets/images/purview.png" style="position: absolute;left: 27px;">
                    <div fxLayout="row" fxLayoutAlign="center center">
                      <div fxLayout="column" style="margin-left: 52px;">
                        <div class="digit"><b>{{item.OUTSIDE_PURVIEW}}</b></div>
                        <div class="category">OUTSIDE PURVIEW</div>
                      </div>
                    </div>
                  </mat-card>
                  <mat-card class="card" style="border-bottom: 4px solid #ED2F45;"
                    (click)="selectedStatusComplaints('Sub-Judice')">
                    <img src="../../../assets/images/sub-judice.png" style="position: absolute;left: 27px;">
                    <div fxLayout="row" fxLayoutAlign="center center">
                      <div fxLayout="column" style="margin-left: 3%;">
                        <div class="digit"><b>{{item.SUB_JUDICE}}</b></div>
                        <div class="category">SUB JUDICE</div>
                      </div>
                    </div>
                  </mat-card>
                  <mat-card class="card" style="border-bottom: 4px solid #000000;"
                    (click)="selectedStatusComplaints('Closed')">
                    <img src="../../../assets/images/closed_complaints.svg" style="position: absolute;left: 27px;">
                    <div fxLayout="row" fxLayoutAlign="center center">
                      <div fxLayout="column" style="margin-left: -5%;">
                        <div class="digit"><b>{{item.CLOSED}}</b></div>
                        <div class="category">CLOSED</div>
                      </div>
                    </div>
                  </mat-card>
                </div>
              </div>
              <div class="reporting-module-container">
                <div class="charts-container">
                  <div class="vertical-bar-chart-container">
                    <div class="chart-heading">Advertisement Medium</div>
                    <mat-divider></mat-divider>
                    <app-complaint-type-chart></app-complaint-type-chart>
                  </div>
                  <div class="bubble-chart-container">
                    <div class="chart-heading">Complaint Sources</div>
                    <mat-divider></mat-divider>
                    <app-complaint-source-chart></app-complaint-source-chart>
                  </div>
                </div>
                <div class="charts-container">
                  <div class="horizontal-bar-chart-container">
                    <div class="chart-heading">Classification</div>
                    <mat-divider></mat-divider>
                    <app-classification-chart></app-classification-chart>
                  </div>
                </div>
                <div class="charts-container">
                  <div class="pie-chart-container">
                    <div class="chart-heading">Classification</div>
                    <mat-divider></mat-divider>
                    <app-chapter-guideline-chart></app-chapter-guideline-chart>
                  </div>
                </div>
                <div class="charts-container">
                  <div class="donut-chart-container">
                    <div class="chart-heading">Complaint Status</div>
                    <mat-divider></mat-divider>
                    <app-complaint-resolution-stage-status-process-chart></app-complaint-resolution-stage-status-process-chart>
                  </div>
                  <div class="zoomable-sunburst-container">
                    <div class="chart-heading">Advertisement Medium</div>
                    <mat-divider></mat-divider>
                    <app-advertisement-medium-chart></app-advertisement-medium-chart>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </mat-tab>
        <mat-tab label="Reports">
          <div class="reports-container" fxLayout="row">
            <div fxLayout="column" fxLayoutGap="5px">
              <div class="reports-head-container">
                <div class="reports-head">
                  <p>Reports&nbsp;:&nbsp;</p>
                </div>
              </div>
              <div fxLayout="row" fxLayoutAlign="center center" class="month-container">
                <div class="reports-head-container" fxLayoutAlign="center center">
                  <div class="range-head">
                    <p>First of&nbsp;:</p>
                  </div>
                </div>
                <mat-form-field class="month-field" appearance="outline">
                  <mat-select [formControl]="from_month" placeholder="Select month"
                    (selectionChange)="selectFromMonth($event)">
                    <mat-option *ngFor="let month of monthList" [value]="month.viewValue" style="padding-left: 7%;">
                      {{month.viewValue}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-form-field class="year-field" appearance="outline">
                  <mat-select [formControl]="from_year" placeholder="Select year"
                    (selectionChange)="selectFromYear($event)">
                    <mat-option *ngFor="let year of yearList" [value]="year.viewValue" style="padding-left: 7%;">
                      {{year.viewValue}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                <div class="reports-head-container" fxLayoutAlign="center center">
                  <div class="range-head">
                    <p>- to -&nbsp;&nbsp;&nbsp; End of&nbsp;:</p>
                  </div>
                </div>
                <mat-form-field class="month-field" appearance="outline">
                  <mat-select [formControl]="to_month" placeholder="Select month"
                    (selectionChange)="selectToMonth($event)">
                    <mat-option *ngFor="let month of monthList" [value]="month.viewValue" style="padding-left: 7%;">
                      {{month.viewValue}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-form-field class="year-field" appearance="outline">
                  <mat-select [formControl]="to_year" placeholder="Select year"
                    (selectionChange)="selectToYear($event)">
                    <mat-option *ngFor="let year of yearList" [value]="year.viewValue" style="padding-left: 7%;">
                      {{year.viewValue}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div class="reports-btn-container" fxLayout="row" fxLayoutGap="10px">
                <div>
                  <button class="theme-blue-button-admin reports-btn" [disabled]="!isMonthselected || regdisabled"
                    (click)="registeredReports()">Registered Complaints
                    <i class="fa fa-spinner fa-spin" *ngIf="regdisabled"></i>
                  </button>
                </div>
                <div>
                  <button mat-button [matMenuTriggerFor]="tams" class="theme-blue-button-admin reports-btn"
                    [disabled]="!isMonthselected || tamsdisabled">
                    NAMS TAMS Processed Complaints
                    <i class="fa fa-spinner fa-spin" *ngIf="tamsdisabled"></i>
                  </button>
                  <mat-menu #tams="matMenu" class="admin-menu" xPosition="before" yPosition="below">
                    <div class="admin-option-container">
                      <button mat-menu-item class="option-btn" (click)="tamsReechReports('tams', 'shortlisted')">
                        <span class="option-text">Shortlisted</span>
                      </button>
                      <button mat-menu-item class="option-btn" (click)="tamsReechReports('tams', 'processed')">
                        <span class="option-text">Processed</span>
                      </button>
                      <button mat-menu-item class="option-btn" (click)="tamsReechReports('tams', 'rejected')">
                        <span class="option-text">Rejected</span>
                      </button>
                      <button mat-menu-item class="option-btn" (click)="tamsReechReports('tams', 'all')">
                        <span class="option-text">All</span>
                      </button>
                    </div>
                  </mat-menu>
                </div>
                <div>
                  <button mat-button [matMenuTriggerFor]="reech" class="theme-blue-button-admin reports-btn"
                    [disabled]="!isMonthselected || reechdisabled">
                    NAMS Reech Processed Complaints
                    <i class="fa fa-spinner fa-spin" *ngIf="reechdisabled"></i>
                  </button>
                  <mat-menu #reech="matMenu" class="admin-menu" xPosition="before" yPosition="below">
                    <div class="admin-option-container">
                      <button mat-menu-item class="option-btn" (click)="tamsReechReports('reech', 'shortlisted')">
                        <span class="option-text">Shortlisted</span>
                      </button>
                      <button mat-menu-item class="option-btn" (click)="tamsReechReports('reech', 'processed')">
                        <span class="option-text">Processed</span>
                      </button>
                      <button mat-menu-item class="option-btn"
                        (click)="tamsReechReports('reech', 'rejected_compliance')">
                        <span class="option-text">Rejected compliance</span>
                      </button>
                      <button mat-menu-item class="option-btn"
                        (click)="tamsReechReports('reech', 'rejected_false_positive')">
                        <span class="option-text">Rejected false positive</span>
                      </button>
                      <button mat-menu-item class="option-btn" (click)="tamsReechReports('reech', 'all')">
                        <span class="option-text">All</span>
                      </button>
                    </div>
                  </mat-menu>
                </div>
              </div>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  </div>
</div>