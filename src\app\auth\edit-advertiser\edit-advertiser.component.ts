import { Component, HostListener, Inject, OnInit, Renderer2 } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';
import { AuthService } from 'src/app/services/auth.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-edit-advertiser',
  templateUrl: './edit-advertiser.component.html',
  styleUrls: ['./edit-advertiser.component.scss']
})
export class EditAdvertiserComponent implements OnInit {

  addform: FormGroup;
  value: any;
  confirmationMsg: any = {};
  companyList: any[];
  emailPattern = environment.emailPatterm;
  mobilenopattern = /^((\\+91-?)|0)?[0-9]{10}$/;
  usernamepattern = /^[a-z A-Z]+$/;
  pincodepattern = /^[0-9]{6}$/;
  deptId: number;
  userInfo: any;
  userRoleId: number;
  titles: any[];
  industryMemberRole: any;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private fb: FormBuilder,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private authService: AuthService,
    private notify: NotificationService,
    private renderer: Renderer2,
    private dialogRef: MatDialogRef<EditAdvertiserComponent>,
    public dialog: MatDialog
  ) {
    this.addform = this.fb.group({
      company: ['', [Validators.required]],
      address: ['', [Validators.required]],
      salutation: ['', [Validators.required]],
      pin: ['', [Validators.required, Validators.pattern(this.pincodepattern)]],
      first_name: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(25), Validators.pattern(this.usernamepattern)]],
      last_name: ['', [Validators.required, Validators.maxLength(25), Validators.pattern(this.usernamepattern)]],
      email: ['', [Validators.required, Validators.pattern(this.emailPattern)]],
      contact: ['', [Validators.required, Validators.pattern(this.mobilenopattern)]],
    });
  }

  ngOnInit(): void {
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.userRoleId = this.userInfo.roleId;
    this.industryMemberRole = this.userInfo.industryMemberRoleId;
    this.titles = JSON.parse(window.localStorage.getItem('salutation'));
    this.getCompanyList();
    this.addform.controls['address'].setValue(this.data.row['ADDRESS']);
    this.addform.controls['pin'].setValue(this.data.row['PINCODE']);
    this.addform.controls['company'].setValue(this.data.row['COMPANY_NAME']);
    this.addform.controls['email'].setValue(this.data.row['EMAIL_ID']);
    this.addform.controls['first_name'].setValue(this.data.row['FIRST_NAME']);
    this.addform.controls['last_name'].setValue(this.data.row['LAST_NAME']);
    this.addform.controls['salutation'].setValue(this.data.row['SALUTATION_ID']);
    this.addform.controls['contact'].setValue(this.data.row['MOBILE']);
    this.addform.controls['email'].disable();
    this.addform.controls['contact'].disable();
    this.addform.controls['company'].disable();
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  getCompanyList() {
    this.authService.getCompaniesAll().subscribe(res => {
      this.companyList = res.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  updateAdvertiser(model) {
    if (this.data.isEditByCompanyAdmin == false) {
      let roleId = 0;
      this.authService.updateAdvertiser(model, this.data.row['ID'], roleId).subscribe(
        (res) => {
          this.notify.showNotification(
            res.message,
            "top",
            (!!colorObj[res.status] ? colorObj[res.status] : "success"),
            res.status
          )
          this.dialogRef.close('u-refresh');
        },
        (err) => {
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        }
      );
    } else {
      this.authService.updateIntraIndustryUserByAdmin(model, this.data.row['ID']).subscribe(
        (res) => {
          this.notify.showNotification(
            res.message,
            "top",
            (!!colorObj[res.status] ? colorObj[res.status] : "success"),
            res.status
          )
          this.dialogRef.close('u-refresh');
        },
        (err) => {
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        }
      );
    }
  }

  removeUser() {
    this.confirmationMsg.title = 'Are you sure you want to delete the user ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.data.row['C_ID'], title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        if (this.data.isEditByCompanyAdmin == false) {
          this.authService.deleteUser(dialogResult.id).subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              this.dialogRef.close('d-refresh');
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
        }
        else {
          this.authService.deleteIntraIndustryUserByAdmin(dialogResult.id).subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              this.dialogRef.close('d-refresh');
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
        }
      }
    });
  }

}