<link
  href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp"
  rel="stylesheet">

<div class="detail-header-container">
  <header class="header" fxLayout="row">
    <div fxFlex="67%" fxLayoutAlign="start">
      <p mat-line class="status-container"
        [ngClass]="{'status-onhold' : detail_status == 'On Hold', 'status-new' : detail_status == 'New', 'status-inprogress' : detail_status == 'In Progress', 'status-resolution' : detail_status == 'Resolution', 'status-out' : detail_status == 'Out of remit/Outside ASCI Purview' || detail_status == 'Sub-Judice', 'status-invalid' : detail_status == 'Non-Issue', 'status-closed' : detail_status == 'Closed'}">
        <mat-chip-list class="status-chip">
          <mat-chip class="comp-status" style="background-color: #ffffff !important;"
            [ngClass]="{'theme-onhold' : detail_status == 'On Hold', 'theme-new' : detail_status == 'New', 'theme-inprogress' : detail_status == 'In Progress', 'theme-resolution' : detail_status == 'Resolution', 'theme-outofremit' : detail_status == 'Out of remit/Outside ASCI Purview' || detail_status == 'Sub-Judice', 'theme-invalid' : detail_status == 'Non-Issue', 'theme-closed' : detail_status == 'Closed'}">
            {{detail_status}}
          </mat-chip>
          <span style="font-size: 13px;"
            [ngClass]="{'text-onhold' : detail_status == 'On Hold', 'text-new' : detail_status == 'New', 'text-inprogress' : detail_status == 'In Progress', 'text-resolution' : detail_status == 'Resolution', 'text-out' : detail_status == 'Out of remit/Outside ASCI Purview' || detail_status == 'Sub-Judice', 'text-invalid' : detail_status == 'Non-Issue', 'text-closed' : detail_status == 'Closed'}">
            Complaint
          </span>
        </mat-chip-list>
      </p>
      <div class="Heading-container">
        <span title="{{detail_company + detail_advert_head}}"
          *ngIf="detail_status != 'Out of remit/Outside ASCI Purview'" mat-line
          style="font-weight: 500;cursor:pointer;font-size: 14px;padding-top: 15px;padding-bottom: 5px;width: max-content !important">
          <img class="hide-icon" src="../assets/images/manage-icon.svg" />
          {{detail_company | slice:0: 20}}{{detail_company.length>21? '..':''}} {{detail_advert_head | slice:0:
          30}}{{longText1}}
        </span>
        <span title="{{detail_company + detail_advert_head}}"
          *ngIf="detail_status == 'Out of remit/Outside ASCI Purview'" mat-line
          style="font-weight: 500;cursor:pointer;font-size: 14px;padding-top: 15px;padding-bottom: 5px;width: max-content !important">
          <img class="hide-icon" src="../assets/images/manage-icon.svg" />
          {{detail_company | slice:0: 9}}{{detail_company.length>10? '..':''}} {{detail_advert_head | slice:0:
          13}}{{detail_advert_head.length >14? '..':''}}
        </span>
        <div mat-line style="color:rgb(177, 177, 177);font-size: 12px;width:283%;" fxLayout="row">
          <div>
            Company: <span title="{{company_name}}" *ngIf="detail_status != 'Out of remit/Outside ASCI Purview'"
              style="color:black;">{{company_name| slice:0: 10}}{{longText3}}</span>
            <span title="{{company_name}}" *ngIf="detail_status == 'Out of remit/Outside ASCI Purview'"
              style="color:black;">{{company_name| slice:0: 5}}{{company_name.length >6? '..':''}}</span>
          </div>
          <div fxFlex="1%"></div>
          <div>
            Brand:
            <span style="color:black;"
              *ngIf="detail_company.length != 0 && detail_status != 'Out of remit/Outside ASCI Purview'"
              title="{{detail_company}}">{{detail_company| slice:0: 10}}{{longText2}}</span>
            <span style="color:black;"
              *ngIf="detail_company.length != 0 && detail_status == 'Out of remit/Outside ASCI Purview'"
              title="{{detail_company}}">{{detail_company| slice:0: 6}}{{detail_company.length>7 ? '..':''}}</span>
          </div>
          <div fxFlex="1%"></div>
          <div>
            Advertiser: <span style="color:black;"
              *ngIf="advertiserDetails.length != 0 && detail_status != 'Out of remit/Outside ASCI Purview'"
              title="{{advertiserStr}}">{{advertiserStr| slice:0: 20}}{{longText6}} </span>
            <span style="color:black;"
              *ngIf="advertiserDetails.length != 0 && detail_status == 'Out of remit/Outside ASCI Purview'"
              title="{{advertiserStr}}">{{advertiserStr| slice:0: 6}}{{advertiserStr.length> 7 ? '..':''}} </span>
            <span style="color:black;" *ngIf="advertiserDetails.length == 0"> - </span>
          </div>
        </div>
      </div>
    </div>
    <div fxFlex="9%"></div>
    <div fxFlex="23%" fxLayout="row" fxLayoutAlign="end" fxLayoutGap="10px" style="padding-left: 9px;">
      <div class="conversation-container">
        <button class="theme-blue-button-admin" (click)="openConversationDialog()"
          style="border: none;padding: 0px 10px;">Conversation</button>
      </div>
      <!-- <div class="flex-container" fxLayout="row" fxLayoutGap="10px"> -->
      <div class="recommendation-container">
        <button mat-icon-button class="float-btn" matTooltip="Resolution" (click)="openResolutionDialog()"
          style="border:1px solid #04A585;">
          <img src="../../assets/images/resolution.png" style="margin-bottom: 5px;">
        </button>
      </div>
      <div class="recommendation-container">
        <button mat-icon-button class="float-btn" matTooltip="Recommendation" (click)="openRecommendationDialog()"
          style="border:1px solid #F89E1B;">
          <img src="../../assets/images/recommendation.png" style="margin-bottom: 5px;">
        </button>
      </div>
      <div class="delete-container" *ngIf="userRoleId == 1 || userRoleId == 2">
        <button mat-icon-button class="float-btn" matTooltip="Delete" (click)="deleteComplaint()"
          style="border:1px solid #ED2F45;">
          <img src="../../assets/images/Trash.png" style="margin-bottom: 5px;">
        </button>
      </div>
      <!-- </div> -->
    </div>
  </header>
</div>

<div class="divider-container">
  <mat-divider></mat-divider>
</div>

<div class="vertical-form" fxLayout="row">
  <div class="tab-div" style="width: 23vw;">
    <mat-tab-group class="tab-group" (selectedTabChange)="onTabsChanged($event)">
      <mat-tab class="nams-tab">
        <ng-template mat-tab-label>
          <span class="nams-label actions">Actions</span>
        </ng-template>
        <form [formGroup]="form" class="form">
          <div class="input-container">
            <div class="text-container">
              <p>Requester</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <span matPrefix><img src="../assets/images/user.png"
                    style="background-color:#92A2B1; width: 19px; height: 19px; border-radius: 9px; margin-bottom: 15px; margin-right: 9px;"></span>
                <input matInput class="input-control" formControlName="requester" placeholder="Requester"
                  autocomplete="off">
              </mat-form-field>
            </div>
          </div>
          <div class="input-container">
            <div class="text-container">
              <p>Assigned to</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <span matPrefix><img src="../assets/images/user.png"
                    style="background-color:#92A2B1;width: 19px; height: 19px; border-radius: 9px; margin-bottom: 15px; margin-right: 9px;"></span>
                <input matInput type="text" formControlName="assigned_to" [matAutocomplete]="autoAssignee"
                  placeholder="Assignee">
                <mat-autocomplete #autoAssignee="matAutocomplete" (optionSelected)="onSelectionChange($event)">
                  <mat-option *ngFor="let assignee of assignees" [value]="assignee.ASSIGNEE_USER_NAME" style="
                  font-style: normal; font-weight: normal; padding-left: 5%;">
                    {{assignee.ASSIGNEE_USER_NAME}}
                  </mat-option>
                </mat-autocomplete>
              </mat-form-field>
            </div>
          </div>
          <div class="input-container">
            <div class="text-container">
              <p>Complaint Status</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <mat-select placeholder="Status" class="input-control" formControlName="status">
                  <mat-option *ngFor="let status of complaintStatusList" [value]="status.ID" style="padding-left: 5%;">
                    {{status.COMPLAINT_STATUS_NAME}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
          <div class="input-container">
            <div class="text-container">
              <p>Resolution</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <mat-select placeholder="Resolution" class="input-control" formControlName="resolution">
                  <mat-option *ngFor="let resolution of resolutionList" [value]="resolution.ID"
                    style="padding-left: 5%;">
                    {{resolution.RESOLUTION_STATUS_NAME}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
          <div class="input-container"
            *ngIf="(form.controls['resolution'].value == 1 || form.controls['resolution'].value == 3
          || form.controls['resolution'].value == 5 || form.controls['resolution'].value == 6 || form.controls['resolution'].value == 8)">
            <div class="text-container">
              <p>Compliance Status</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <mat-select placeholder="Compliance Status" class="input-control" formControlName="compliance">
                  <mat-option *ngFor="let compliance of complianceList" [value]="compliance.ID"
                    style="padding-left: 5%;">
                    {{compliance.COMPLIANCE_STATUS_NAME}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
          <div class="input-container">
            <div class="text-container">
              <p>Stage</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <mat-select placeholder="Stage" formControlName="stage" class="input-control">
                  <mat-option *ngFor="let stage of stages" [value]="stage.ID" style="padding-left: 5%;"
                    matTooltip="{{stage.STAGE_NAME}}">
                    {{stage.STAGE_NAME}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
          <div class="input-container">
            <div class="text-container">
              <p>Process</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <mat-select placeholder="Process" formControlName="process" class="input-control">
                  <mat-option *ngFor="let process of processes" [value]="process.ID" style="padding-left: 5%;"
                    matTooltip="{{process.PROCESS_NAME}}">
                    {{process.PROCESS_NAME}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
          <div class="input-container">
            <div class="text-container">
              <p>Priority</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <mat-select formControlName="priority" class="input-control"
                  (selectionChange)="changeValuePriority($event)">
                  <mat-select-trigger
                    [ngClass]="{'icon_color1': detail_priority == 'High', 'icon_color2': detail_priority == 'Medium', 'icon_color3': detail_priority == 'Low', 'icon_color4': detail_priority == 'Urgent'}">
                    <mat-icon
                      [ngClass]="{'icon_color1': detail_priority == 'High', 'icon_color2': detail_priority == 'Medium', 'icon_color3': detail_priority == 'Low'}"
                      style="font-size: 11px;position: relative;top: 4px;">fiber_manual_record</mat-icon>
                    <span
                      [ngClass]="{'icon_color1': detail_priority == 'High', 'icon_color2': detail_priority == 'Medium', 'icon_color3': detail_priority == 'Low', 'icon_color4': detail_priority == 'Urgent'}"
                      style="font-size: 11px;position: relative;top: 3px;">{{detail_priority}}</span>
                  </mat-select-trigger>
                  <mat-option *ngFor="let prior of priorityList;" [value]="prior.ID" style="padding-left: 10%;">
                    <mat-icon style="font-size: 11px; margin-top: 5%;" [ngClass]="{'icon_color1': prior.PRIORITY_NAME == 'High', 'icon_color2': prior.PRIORITY_NAME == 'Medium',
                        'icon_color3': prior.PRIORITY_NAME == 'Low','icon_color4': prior.PRIORITY_NAME == 'Urgent'}">
                      fiber_manual_record</mat-icon>
                    <span style="margin-left: -10%;font-size: 11px" [ngClass]="{'icon_color1': prior.PRIORITY_NAME == 'High', 'icon_color2': prior.PRIORITY_NAME == 'Medium',
                    'icon_color3': prior.PRIORITY_NAME == 'Low','icon_color4': prior.PRIORITY_NAME == 'Urgent'}">
                      {{prior.PRIORITY_NAME}} </span>
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
          <div class="input-container">
            <div class="text-container">
              <p> Complaint Due date</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <input matInput [matDatepicker]="picker1" placeholder="Due" [min]="minDate" formControlName="due_date"
                  autocomplete="off">
                <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
                <mat-datepicker #picker1></mat-datepicker>
              </mat-form-field>
            </div>
          </div>
          <div class="input-container">
            <div class="text-container">
              <p>Advertiser Due date</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field">
                <input matInput [matDatepicker]="picker2" placeholder="Due" [min]="minDate"
                  formControlName="advertiser_due_date" autocomplete="off">
                <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
                <mat-datepicker #picker2></mat-datepicker>
              </mat-form-field>
            </div>
          </div>
          <div class="divider-container" style="margin-bottom: 10px;margin-top: 10px;">
            <mat-divider></mat-divider>
          </div>
          <div class="input-container">
            <div class="text-container">
              <p>Tags</p>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field"
                style="word-wrap: break-word;word-break: break-all">
                <mat-chip-list #chipList aria-label="Tag selection" class="input-control" [disabled]="true">
                  <mat-chip *ngFor="let tag of tags" [selectable]="selectable" [removable]="removable"
                    (removed)="remove(tag)" style="font-size: 11px;">
                    {{tag.name}}
                    <!-- <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon> -->
                  </mat-chip>
                  <input [matChipInputFor]="chipList" [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                    [matChipInputAddOnBlur]="addOnBlur" (matChipInputTokenEnd)="add($event)" formControlName="tag">
                </mat-chip-list>
              </mat-form-field>
            </div>
          </div>
          <div class="input-container">
            <div class="text-container">
              <span>Subtags </span><span style="font-size: 10px; font-style: italic;">(you can add more tags)</span>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline" class="input-field"
                style="word-wrap: break-word;word-break: break-all">
                <mat-chip-list #chipLists aria-label="SubTag selection" class="input-control">
                  <mat-chip *ngFor="let tags of subtags" [selectable]="selectable" [removable]="removable"
                    (removed)="removes(tags)" style="font-size: 11px;">
                    {{tags.SUB_TAG_NAME}}
                    <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
                  </mat-chip>
                  <input #tagInput [matChipInputFor]="chipLists" [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                    [matChipInputAddOnBlur]="addOnBlur" (matChipInputTokenEnd)="addSubtag($event)"
                    style="word-wrap: break-word;word-break: break-all" [matAutocomplete]="autoSubTags"
                    [formControl]="tagCtrl">
                  <mat-autocomplete #autoSubTags="matAutocomplete">
                    <mat-option *ngFor="let subTag of subTagList | async" [value]="subTag"
                      style="font-style: normal; font-weight: normal;" (onSelectionChange)="selected(subTag, $event)">
                      {{subTag.SUB_TAG_NAME}}
                    </mat-option>
                  </mat-autocomplete>
                </mat-chip-list>
              </mat-form-field>
            </div>
          </div>
        </form>
        <div style="background-color: #F8F9F9;">
          <div class="divider-container" style="margin-bottom: 10px; padding-top: 10px;">
            <mat-divider></mat-divider>
          </div>
          <button mat-button class="update-btn" (click)="updateComplaint(form.value)" style="width: 140px;">
            <span class="bolder">Update complaint</span>
          </button>
        </div>
      </mat-tab>
      <mat-tab class="nams-tab">
        <ng-template mat-tab-label>
          <span class="nams-label">Similar complaints</span>
          <span class="count-badge">{{similarComplaintsLength}}</span>
        </ng-template>
        <mat-card *ngIf="similarCompLoading"
          style="display: flex; justify-content: center; align-items: center; background: white;">
          <mat-progress-spinner color="primary" mode="indeterminate" diameter="70">
          </mat-progress-spinner>
        </mat-card>
        <div class="dashboard-complaint-list" *ngIf="!similarCompLoading">
          <div class="list-item-container" *ngIf="similarComplaints.length == 0">
            <div class="message" fxLayout="row" style="padding: 10px 0px 0px 10px;">
              <p> No match found </p>
            </div>
          </div>
          <mat-list-item *ngFor="let item of similarComplaints">
            <div class="list-item-container" (click)="openSimilarComplaintDetail(item.CASE_ID)">
              <div class="item-head-container" fxLayout="column">
                <div class="message" fxLayout="row" matTooltip="{{item.COMPLAINT_DESCRIPTION}}">
                  <p class="ellipsis"> {{item.COMPLAINT_DESCRIPTION}} </p>
                </div>
                <div fxLayout="row">
                  <div fxLayout="column" style="font-size: 11px;" fxFlex="65%" fxLayoutGap="8px">
                    <div fxLayout="row">
                      <div fxFlex="25%">C.ID : </div>
                      <div fxFlex="75%">{{item.CASE_ID}}</div>
                    </div>
                    <div>
                      <p>Source : {{item.COMPLAINT_SOURCE_NAME}} </p>
                    </div>
                  </div>
                  <div fxLayout="column" style="font-size: 11px;" fxLayoutGap="8px" fxFlex="35%">
                    <div class="name-container" fxLayout="row" matTooltip="{{item.REQUESTER_USER_NAME}}">
                      <mat-icon class="item-icon">perm_identity</mat-icon>
                      <span class="ellipsis1" matSuffix> {{item.REQUESTER_USER_NAME}} </span>
                    </div>
                    <div class="date-container" fxLayout="row">
                      <mat-icon class="item-icon1">calendar_today</mat-icon>
                      <span matSuffix style="position: relative; left:3px; bottom: 1px;"> {{item.CREATED_DATE |
                        date:'dd/MM/yyyy'}} </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <mat-divider></mat-divider>
          </mat-list-item>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>

  <div class="comp-detail-container">
    <div class="comp-fxrow-container">
      <div fxLayout="row">
        <div style="margin-left: 10px; padding-top: 18px;">
          <div mat-line>
            <p>
              <span class="detail-attribute">Case ID : </span>
              {{detail_id}} &nbsp;
              <span *ngIf="old_case_id != null && old_case_id != ''">({{old_case_id}})</span>
            </p>
          </div>
          <div mat-line [hidden]="hiddenMore">
            <p>
              <span class="detail-attribute">Tracking ID : </span>
              {{complaint_id}}
            </p>
          </div>
          <div mat-line *ngIf="product_name">
            <p>
              <span class="detail-attribute">Product : </span>
              {{product_name}}
            </p>
          </div>
          <div mat-line [hidden]="hiddenMore">
            <p>
              <span class="detail-attribute">Contact : </span>
              {{detail_contactno}}
            </p>
          </div>
          <div mat-line [hidden]="hiddenMore">
            <p>
              <span class="detail-attribute">Email Id : </span>
              {{detail_email}}
            </p>
          </div>
          <div *ngFor="let comp of company_details; let i=index;">
            <div mat-line [hidden]="hiddenMore">
              <p>
                <span class="detail-attribute">Advertiser company {{i+2}} : </span>
                {{comp.COMPANY_NAME}}
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore">
              <p>
                <span class="detail-attribute">Advertiser company {{i+2}} email : </span>
                {{!!comp.EMAIL_ID ? comp.EMAIL_ID : 'Not available'}}
              </p>
            </div>
          </div>
          <div *ngFor="let source of detail_adsource; let i=index;">
            <div mat-line [hidden]="hiddenMore" *ngIf="source.ADVERTISEMENT_SOURCE_ID != 0">
              <p>
                <span class="detail-attribute" *ngIf="detail_adsource.length > 1">Media source {{i+1}} : </span>
                <span class="detail-attribute" *ngIf="detail_adsource.length == 1">Media source : </span>
                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 1">Television</span>
                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 2">Radio</span>
                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3">Digital Media</span>
                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 4">Hoardings</span>
                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 5">Print</span>
                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 6">Promotional Material</span>
                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 7">Packaging</span>
                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 8">SMS</span>
                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 9">Others</span>
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore" *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3 && complaint_source_id != 8">
              <p>
                <span class="detail-attribute">Platform name : </span>
                <span *ngIf="source.PLATFORM_ID  == 1">Facebook</span>
                <span *ngIf="source.PLATFORM_ID  == 2">Instagram</span>
                <span *ngIf="source.PLATFORM_ID  == 3">YouTube</span>
                <span *ngIf="source.PLATFORM_ID  == 4">Twitter</span>
                <span *ngIf="source.PLATFORM_ID  == 5">LinkedIn</span>
                <span *ngIf="source.PLATFORM_ID  == 6">Website</span>
                <span *ngIf="source.PLATFORM_ID  == 7">Google Ad</span>
                <span *ngIf="source.PLATFORM_ID  == 8">Mobile App</span>
                <span *ngIf="source.PLATFORM_ID  == 9">Others</span>
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore"
              *ngIf="source.ADVERTISEMENT_SOURCE_ID== 1 || source.ADVERTISEMENT_SOURCE_ID == 2">
              <p>
                <span class="detail-attribute">Channel name : </span>
                {{source.SOURCE_NAME}}
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore" *ngIf="source.ADVERTISEMENT_SOURCE_ID == 4">
              <p>
                <span class="detail-attribute">Place : </span>
                {{source.SOURCE_PLACE}}
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore" *ngIf="source.ADVERTISEMENT_SOURCE_ID == 5">
              <p>
                <span class="detail-attribute">Print source: </span>
                {{source.PRINT_SOURCE_ID == 1 ? 'Newspaper': 'Magazine'}}
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore" *ngIf="source.ADVERTISEMENT_SOURCE_ID == 6">
              <p>
                <span class="detail-attribute">Print source: </span>
                {{source.P_M_SOURCE_ID == 1 ? 'Pamphlet':''}}
                {{source.P_M_SOURCE_ID == 2 ? 'Brochures':''}}
                {{source.P_M_SOURCE_ID == 3 ? 'Hard bills':''}}
                {{source.P_M_SOURCE_ID == 4 ? 'POS':''}}
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore" *ngIf="source.ADVERTISEMENT_SOURCE_ID == 7">
              <p>
                <span class="detail-attribute">MFD/PKD Date : </span>
                {{source.DATE| date:'dd/MM/yyyy'}}
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore" *ngIf="source.ADVERTISEMENT_SOURCE_ID == 8">
              <p>
                <span class="detail-attribute">Sender : </span>
                {{source.SOURCE_NAME}}
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore" *ngIf="source.ADVERTISEMENT_SOURCE_ID == 9">
              <p>
                <span class="detail-attribute">Source : </span>
                {{source.SOURCE_NAME}}
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore"
              *ngIf="source.ADVERTISEMENT_SOURCE_ID != 0 && !source.ADVERTISEMENT_SOURCE_ID">
              <p>
                <span class="detail-attribute">Source : </span>
              </p>
            </div>
            <div *ngIf="complaint_source_id != 7 && complaint_source_id != 8 && !(complaint_source_id == 2 && complaint_type_id != 6) || (complaint_source_id == 2 && complaint_type_id != 6)">
              <div mat-line [hidden]="hiddenMore" *ngIf="source.SOURCE_URL != '' && source.SOURCE_URL != null">
                <p>
                  <span class="detail-attribute" *ngIf="detail_adsource.length > 1">Media URL {{i+1}} : </span>
                  <span class="detail-attribute" *ngIf="detail_adsource.length == 1">Media URL : </span>
                  <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(source.SOURCE_URL)"
                    class="media-anchor" matTooltip="{{source.SOURCE_URL}}">{{source.SOURCE_URL | slice:0:
                    40}}{{source.SOURCE_URL.length>41? '..':''}} </a>
                </p>
              </div>
              <div mat-line [hidden]="hiddenMore"
                *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3 && source.PLATFORM_ID == 9">
                <p>
                  <span class="detail-attribute">Source : </span>
                  {{source.SOURCE_PLACE}}
                </p>
              </div>
            </div>
            <div mat-line [hidden]="hiddenMore" *ngIf="source.ADVERTISEMENT_SOURCE_ID == 5">
              <p>
                <span class="detail-attribute">Publication Name & Edition : </span>
                {{source.SOURCE_NAME}}
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore"
              *ngIf="source.ADVERTISEMENT_SOURCE_ID == 6 || source.ADVERTISEMENT_SOURCE_ID == 9">
              <p *ngIf="source.SOURCE_PLACE">
                <span class="detail-attribute">Place : </span>
                {{source.SOURCE_PLACE}}
              </p>
            </div>
            <div *ngIf="complaint_type_id == 2 || complaint_type_id == 4">
              <div mat-line [hidden]="hiddenMore" style="width: 301px;">
                <p>
                  <span class="detail-attribute" *ngIf="detail_adsource.length > 1">Advertisement Seen Date {{i+1}} :
                  </span>
                  <span class="detail-attribute" *ngIf="detail_adsource.length == 1">Advertisement Seen Date : </span>
                  {{source.DATE | date:'dd/MM/yyyy'}} <span *ngIf="source.TIME">-
                    {{source.TIME.split(':')[0]}}:{{source.TIME.split(':')[1]}}</span>
                </p>
              </div>
            </div>
          </div>
          <div mat-line [hidden]="hiddenMore" *ngIf="complaint_source_id == 8">
            <p>
              <span class="detail-attribute">Platform name : </span>
              {{network}}
            </p>
          </div>
          <div mat-line [hidden]="hiddenMore">
            <p>
              <span class="detail-attribute">Complaint via : </span>
              {{detail_source }}
            </p>
          </div>
          <div mat-line [hidden]="hiddenMore">
            <p>
              <span class="detail-attribute">Registered date : </span>
              {{comp_date | date:'dd/MM/yyyy'}} - {{comp_date | date:'HH:mm'}}
            </p>
          </div>
          <div mat-line [hidden]="hiddenMore">
            <p>
              <span class="detail-attribute">Created Date : </span>
              {{detail_date | date:'dd/MM/yyyy'}} - {{detail_date | date:'HH:mm'}}
            </p>
          </div>
          <div mat-line [hidden]="hiddenMore" *ngIf="product_category">
            <p>
              <span class="detail-attribute">Product category : </span>
              {{product_category}}
            </p>
          </div>
          <div mat-line [hidden]="hiddenMore" *ngIf="classification_name">
            <p>
              <span class="detail-attribute">Classification : </span>
              {{classification_name}}
            </p>
          </div>
          <div *ngIf="complaint_type_id != 2 && complaint_type_id != 4">
            <div mat-line [hidden]="hiddenMore" *ngIf="detail_addate">
              <p>
                <span class="detail-attribute">Advertisement Seen Date : </span>
                {{detail_addate| date:'dd/MM/yyyy'}} <span *ngIf="detail_adtime">- {{detail_adtime}}</span>
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore" *ngIf="seen_date && (detail_addate == '' || detail_addate == null)">
              <p>
                <span class="detail-attribute">Advertisement Seen Date : </span>
                {{seen_date| date:'dd/MM/yyyy'}} <span *ngIf="seen_time">- {{seen_time}}</span>
              </p>
            </div>
          </div>
          <div mat-line [hidden]="hiddenMore"
            *ngIf="complaint_source_id != 7 && complaint_source_id != 8 && !(complaint_source_id == 2 && complaint_type_id != 6) || (complaint_source_id == 2 && complaint_type_id != 6)">
            <p *ngIf="noOfDocs != 0">
              <span class="detail-attribute">Media file : </span>
              <a style="font-size: 13px; cursor: pointer;" (click)="openNewTab()" class="media-anchor">{{noOfDocs}}
                {{noOfDocs > 1 ? 'files': 'file'}} <img src="../../assets/images/media_download.svg"
                  style="position: relative;left:8px;bottom:1px"></a>
            </p>
            <!-- <p *ngIf="noOfDocs == 0 && detail_link">
                <span class="detail-attribute">Media file : </span>
                <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(detail_link)" class="media-anchor" matTooltip="{{detail_link}}">{{detail_link | slice:0: 28}}{{longText4}}</a>
              </p> -->
          </div>
          <div mat-line [hidden]="hiddenMore"
            *ngIf="complaint_source_id == 7 || (complaint_source_id == 2 && complaint_type_id == 6)">
            <p>
              <span class="detail-attribute">Translation hyperlink : </span>
              <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(translation_hyper)" class="media-anchor"
                matTooltip="{{translation_hyper}}">{{translation_hyper | slice:0: 19}}{{longText5}}</a>
            </p>
          </div>
          <div mat-line [hidden]="hiddenMore"
            *ngIf="complaint_source_id == 7 || (complaint_source_id == 2 && complaint_type_id == 6)">
            <p>
              <span class="detail-attribute">Creative Id : </span>
              {{creative_id}}
            </p>
          </div>
          <div mat-line [hidden]="hiddenMore" *ngIf="complaint_source_id == 8">
            <p>
              <span class="detail-attribute">Influencer name : </span>
              {{influencer_name}}
            </p>
          </div>
          <div mat-line [hidden]="hiddenMore" *ngIf="complaint_source_id == 8">
            <p>
              <span class="detail-attribute">Engagements : </span>
              {{engagements}}
            </p>
          </div>
          <div *ngIf="complaint_source_id == 8">
            <div mat-line [hidden]="hiddenMore" *ngIf="complaint_source_id == 8">
              <p>
                <span class="detail-attribute">Influencer contact no. : </span>
                {{influencer_contact}}
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore" *ngIf="complaint_source_id == 8">
              <p>
                <span class="detail-attribute">Influencer email : </span>
                {{influencer_email}}
              </p>
            </div>
          </div>
          <div *ngIf="complaint_source_id == 7 || (complaint_source_id == 2 && complaint_type_id == 6)">
            <div mat-line [hidden]="hiddenMore">
              <p>
                <span class="detail-attribute">Media outlet : </span>
                <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(media_outlet)" class="media-anchor"
                  matTooltip="{{media_outlet}}" *ngIf="isUrl">{{media_outlet | slice:0: 27}}{{longText8}}</a>
                <span *ngIf="!isUrl">{{media_outlet}}</span>
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore">
              <p>
                <span class="detail-attribute">Media : </span>
                {{media}}
              </p>
            </div>
          </div>
          <div *ngIf="complaint_source_id == 8">
            <div mat-line [hidden]="hiddenMore">
              <p>
                <span class="detail-attribute">Publication URL : </span>
                <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(publication_url)" class="media-anchor"
                  matTooltip="{{publication_url}}">{{publication_url | slice:0: 40}}{{publication_url.length>41?
                  '..':''}}</a>
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore" *ngIf="profile_url != null && profile_url != ''">
              <p>
                <span class="detail-attribute">Profile URL : </span>
                <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(profile_url)" class="media-anchor"
                  matTooltip="{{profile_url}}">{{profile_url | slice:0: 40}}{{profile_url.length>41? '..':''}}</a>
              </p>
            </div>
          </div>
          <div *ngIf="complaint_source_id == 7 || (complaint_source_id == 2 && complaint_type_id == 6)">
            <div mat-line [hidden]="hiddenMore" *ngIf="suppliment != null">
              <p>
                <span class="detail-attribute">Supplement : </span>
                {{suppliment}}
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore" *ngIf="edition != null">
              <p>
                <span class="detail-attribute">Edition : </span>
                {{edition}}
              </p>
            </div>
          </div>
          <div *ngIf="complaint_source_id == 7 || (complaint_source_id == 2 && complaint_type_id == 6)">
            <div mat-line [hidden]="hiddenMore">
              <p>
                <span class="detail-attribute">Ad language : </span>
                {{ad_language}}
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore" *ngIf="complaint_source_id == 7 && duration">
              <p>
                <span class="detail-attribute">Duration : </span>
                {{duration}}
              </p>
            </div>
          </div>
          <div
            *ngIf="complaint_source_id == 7 || complaint_source_id == 8 || (complaint_source_id == 2 && complaint_type_id == 6)">
            <div mat-line style="width:288px" [hidden]="hiddenMore" *ngIf="noOfDocs != 0">
              <p>
                <span class="detail-attribute">Media file : </span>
                <a style="font-size: 13px; cursor: pointer;" (click)="openNewTab()" class="media-anchor">{{noOfDocs}}
                  {{noOfDocs > 1 ? 'files': 'file'}} <img src="../../assets/images/media_download.svg"
                    style="position: relative;left:8px;bottom:1px"></a>
              </p>
            </div>
            <div mat-line [hidden]="hiddenMore" *ngIf="noOfDocs == 0 || detail_link">
              <p>
                <span class="detail-attribute">Media URL : </span>
                <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(detail_link)" class="media-anchor"
                  matTooltip="{{detail_link}}">{{detail_link | slice:0: 40}}{{longText4}}</a>
              </p>
            </div>
          </div>
          <div mat-line *ngIf="complaint_source_id == 7 && transcription">
            <p>
              <span class="detail-attribute">Transcription :</span>
            </p>
          </div>
          <div mat-line class="comp-msg-container" *ngIf="complaint_source_id == 7 && transcription">
            <p class="comp-msg" [innerHTML]="safeHTML(transcription)">
              {{transcription}}
            </p>
          </div>
          <div mat-line *ngIf="detail_advert">
            <p>
              <span class="detail-attribute">Advertisement description :</span>
            </p>
          </div>
          <div mat-line class="comp-msg-container" *ngIf="detail_advert">
            <p class="comp-msg" [innerHTML]="safeHTML(detail_advert)">
              {{detail_advert}}
            </p>
          </div>
          <div mat-line [hidden]="hiddenMore">
            <p>
              <span class="detail-attribute">Objectionable frames :</span>
            </p>
          </div>
          <div mat-line class="comp-msg-container" [hidden]="hiddenMore">
            <p class="comp-msg" [innerHTML]="safeHTML(detail_complaint)">
              {{detail_complaint}}
            </p>
          </div>
          <div mat-line class="comp-msg-container" [hidden]="hiddenMore">
            <p class="comp-msg">
            </p>
          </div>
          <a (click)="showMoreDetails()"
            style="cursor:pointer;text-decoration: underline;text-decoration-color:rgb(37, 113, 199);color:rgb(37, 113, 199)">{{
            hiddenMore == true ? "See Requesters Details >" : "Hide Requesters Details >" }}</a>
        </div>
      </div>
      <!-- <div style=" position: relative; bottom: 234px;margin-left:386px;" [hidden]="hiddenMore">
        <div mat-line>
          <p>
            <span class="detail-attribute">Advertiser:</span>
          </p>
        </div>
      </div> -->
      <div class="comp-tab-container">
        <mat-divider style="width: 7px;position: relative;top: 64px;"></mat-divider>
        <mat-tab-group (selectedTabChange)="onTabChanged($event)" animationDuration="0ms" class="detail-subtab">
          <mat-tab label="Tasks">
            <div class="task-table">
              <table mat-table [dataSource]="dataSource2" style="width: 100%; height: 268px;" *ngIf="zeroTasks">
                <ng-container matColumnDef="check">
                  <th mat-header-cell *matHeaderCellDef style="width: 5%;">
                  </th>
                  <td mat-cell *matCellDef="let element" style="width: 5%;">
                  </td>
                  <td mat-footer-cell *matFooterCellDef colspan="4">
                    <button mat-button class="add-btn" style="width: 150px;" (click)="addNewTask()">
                      <mat-icon style="font-size: 15px; margin-top: 5%;">add</mat-icon>
                      <span class="bolder"> Create new task </span>
                    </button>
                  </td>
                </ng-container>

                <ng-container matColumnDef="task">
                  <th mat-header-cell *matHeaderCellDef style="width: 40%;">Task</th>
                  <td mat-cell *matCellDef="let element"
                    style="width: 40%; font-size: 15px; color: #92A2B1; padding-bottom: 15%;">
                    {{element.task}}
                  </td>
                  <td mat-footer-cell *matFooterCellDef></td>
                </ng-container>

                <ng-container matColumnDef="status">
                  <th mat-header-cell *matHeaderCellDef style="width: 15%;">Status
                  </th>
                  <td mat-cell *matCellDef="let element" style="width: 15%;">
                    {{element.status}}
                  </td>
                  <td mat-footer-cell *matFooterCellDef></td>
                </ng-container>

                <ng-container matColumnDef="priority">
                  <th mat-header-cell *matHeaderCellDef style="width: 17%;">Priority
                  </th>
                  <td mat-cell *matCellDef="let element">
                    {{element.priority}}
                  </td>
                  <td mat-footer-cell *matFooterCellDef></td>
                </ng-container>

                <ng-container matColumnDef="duedate" style="width: 23%;">
                  <th mat-header-cell *matHeaderCellDef>Due Date
                  </th>
                  <td mat-cell *matCellDef="let element" style="width: 23%;">
                    {{element.duedate}}
                  </td>
                  <td mat-footer-cell *matFooterCellDef></td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayColumns; sticky: true"></tr>
                <tr mat-row *matRowDef="let row; columns: displayColumns;">
                </tr>
                <tr mat-footer-row *matFooterRowDef="displayColumns; sticky: true" class="example-second-footer-row">
                </tr>
              </table>

              <table mat-table [dataSource]="dataSource1" style="width: 100%;" *ngIf="!zeroTasks">
                <ng-container matColumnDef="check">
                  <th mat-header-cell *matHeaderCellDef>
                  </th>
                  <td mat-cell *matCellDef="let element" (click)="selection.toggle(row)">
                    <mat-checkbox style="margin-bottom: 7px;" (click)="$event.stopPropagation()"
                      (change)="$event ? selection.toggle(element) : null" [checked]="selection.isSelected(element)"
                      [aria-label]="checkboxLabel(element)">
                    </mat-checkbox>
                  </td>
                  <td mat-footer-cell *matFooterCellDef colspan="4">
                    <button mat-button class="add-btn" style="width: 150px;" (click)="addNewTask()">
                      <mat-icon style="font-size: 15px; margin-top: 5%">add</mat-icon>
                      <span class="bolder"> Create new task </span>
                    </button>
                  </td>
                </ng-container>

                <ng-container matColumnDef="task">
                  <th mat-header-cell *matHeaderCellDef>Task</th>
                  <td mat-cell *matCellDef="let element" class="ellipsis2" (click)="updateTask(element,dataSource)">
                    {{element.TASK_NAME}} </td>
                  <td mat-footer-cell *matFooterCellDef></td>
                </ng-container>

                <ng-container matColumnDef="status">
                  <th mat-header-cell *matHeaderCellDef>Status
                  </th>
                  <td mat-cell *matCellDef="let element">
                    <button mat-flat-button class="select flex"
                      [ngClass]="{'theme-status-todo' : 1 == element.TASK_STATUS_ID ,'theme-status-active': 2 == element.TASK_STATUS_ID ,
                      'theme-status-done' : 4 == element.TASK_STATUS_ID, 'theme-status-hold' : 3 == element.TASK_STATUS_ID}">
                      <!-- <mat-select [(value)]="element.TASK_STATUS_ID" class="stat">
                        <mat-option *ngFor="let stat of taskStatusList;" [value]="stat.ID" style="padding-left: 10%;"> -->
                      <span style="font-size: 11px;">{{element.TASK_STATUS_NAME}}</span>
                      <!-- </mat-option>
                      </mat-select> -->
                    </button>
                  </td>
                  <td mat-footer-cell *matFooterCellDef></td>
                </ng-container>

                <ng-container matColumnDef="priority">
                  <th mat-header-cell *matHeaderCellDef>Priority
                  </th>
                  <td mat-cell *matCellDef="let element">
                    <button mat-flat-button class="priority-btn flex" [ngClass]="{'icon_color1': element.PRIORITY_NAME == 'High', 'icon_color2': element.PRIORITY_NAME == 'Medium',
                    'icon_color3': element.PRIORITY_NAME == 'Low', 'icon_color4': element.PRIORITY_NAME == 'Urgent'}">
                      <!-- <mat-select [(value)]="element.PRIORITY_ID" class="prior"  [ngClass]="{'high_prior': element.PRIORITY_NAME == 'High', 'med_prior': element.PRIORITY_NAME == 'Medium',
                        'low_prior': element.PRIORITY_NAME == 'Low', 'icon_color4': element.PRIORITY_NAME == 'Urgent'}"> -->
                      <!-- <mat-select-trigger
                          [ngClass]="{'icon_color1': element.PRIORITY_NAME == 'High', 'icon_color2': element.PRIORITY_NAME == 'Medium',
                          'icon_color3': element.PRIORITY_NAME == 'Low', 'icon_color4': element.PRIORITY_NAME == 'Urgent'}"> -->
                      <mat-icon
                        [ngClass]="{'icon_color1': element.PRIORITY_NAME == 'High', 'icon_color2': element.PRIORITY_NAME == 'Medium', 'icon_color3': element.PRIORITY_NAME == 'Low', 'icon_color4': element.PRIORITY_NAME == 'Urgent'}"
                        style="font-size: 10px;padding-top: 6px;">
                        fiber_manual_record
                      </mat-icon>
                      <span style="font-size: 11px;"
                        [ngClass]="{'icon_color1': element.PRIORITY_NAME == 'High', 'icon_color2': element.PRIORITY_NAME == 'Medium', 'icon_color3': element.PRIORITY_NAME == 'Low', 'icon_color4': element.PRIORITY_NAME == 'Urgent'}">
                        {{element.PRIORITY_NAME}}
                      </span>
                      <!-- </mat-select-trigger>
                        <mat-option *ngFor="let prior of priorityList;" [value]="prior.ID" [ngClass]="{'icon_color1': prior.ID == 3, 'icon_color2': prior.ID == 2,
                        'icon_color3': prior.ID == 1, 'icon_color4': prior.ID == 4}"
                          style="font-size:11px; padding-left: 10%">
                          <mat-icon style="font-size: 10px; padding-top: 8%">fiber_manual_record</mat-icon>
                          <span style="margin-left: -15%;"> {{prior.PRIORITY_NAME}} </span>
                        </mat-option>
                      </mat-select> -->
                    </button>
                  </td>
                  <td mat-footer-cell *matFooterCellDef></td>
                </ng-container>

                <ng-container matColumnDef="duedate">
                  <th mat-header-cell *matHeaderCellDef>Due Date
                  </th>
                  <td mat-cell *matCellDef="let element" [ngClass]="{'rej' :'Today' == element.DUE_DATE }"
                    style="width: 23%;">
                    <form [formGroup]="date_form">
                      <mat-form-field appearance="outline" class="date-field"
                        style="width: 125px; height: 32px !important;">
                        <input matInput formControlName="tabledate" class="date"
                          placeholder="{{element.DUE_DATE | date:'dd/MM/yyyy'}}" [readonly]="true">
                        <span matSuffix><img src="../../assets/images/calendar.svg"
                            style="position: relative;top: -8px; cursor: default;"></span>
                        <!-- <mat-datepicker #picker></mat-datepicker> -->
                      </mat-form-field>
                    </form>
                  </td>
                  <td mat-footer-cell *matFooterCellDef></td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayColumns; sticky: true"></tr>
                <tr mat-row *matRowDef="let row; columns: displayColumns;"
                  [ngClass]="{'col' :'Today' == row.DUE_DATE , 'otherr': 'Today' != row.DUE_DATE} ">
                </tr>
                <tr mat-footer-row *matFooterRowDef="displayColumns; sticky: true" class="example-second-footer-row">
                </tr>
              </table>
              <mat-card *ngIf="taskLoading"
                style="display: flex; justify-content: center; align-items: center; background: white;">
                <mat-progress-spinner color="primary" mode="indeterminate" diameter="70">
                </mat-progress-spinner>
              </mat-card>
            </div>
          </mat-tab>

          <mat-tab label="Documents">
            <div class="doc-container" fxLayout="row">
              <div class="content-head-container">
                <mat-tab-group mat-align-tabs="end">
                  <mat-tab label="Complainant">
                    <div fxFlex fxLayout="column" fxLayoutGap="10px" style="height: 35vh">
                      <mat-accordion multi>
                        <mat-expansion-panel class="intra-expansion" (opened)="panelOpenState = true"
                          (closed)="panelOpenState = false" *ngFor="let docs of advertisementMedium">
                          <mat-expansion-panel-header class="panel-header" *ngIf="docs.docInfo.length != 0">
                            <mat-panel-title class="panel-title">
                              <div fxLayout="row">
                                <div class="attribute-container1">
                                  <p>
                                    <span class="grey-text">Medium : </span>
                                    <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '1'">Television</span>
                                    <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '2'">Radio</span>
                                    <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '3'">Digital Media</span>
                                    <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '4'">Hoardings</span>
                                    <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '5'">Print</span>
                                    <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '6'">Promotional Material</span>
                                    <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '7'">Packaging</span>
                                    <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '8'">SMS</span>
                                    <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '9'">Others</span>
                                  </p>
                                </div>
                                <div class="attribute-container2">
                                  <p>
                                    <span
                                      *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '1' || docs.ADVERTISEMENT_SOURCE_ID == '2'">Channel
                                      : {{docs.SOURCE_NAME}}</span>
                                    <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '3'">Platform :
                                      <span *ngIf="docs.PLATFORM_ID == 1">Facebook</span>
                                      <span *ngIf="docs.PLATFORM_ID  == 2">Instagram</span>
                                      <span *ngIf="docs.PLATFORM_ID  == 3">YouTube</span>
                                      <span *ngIf="docs.PLATFORM_ID  == 4">Twitter</span>
                                      <span *ngIf="docs.PLATFORM_ID  == 5">LinkedIn</span>
                                      <span *ngIf="docs.PLATFORM_ID  == 6">Website</span>
                                      <span *ngIf="docs.PLATFORM_ID  == 7">Google Ad</span>
                                      <span *ngIf="docs.PLATFORM_ID  == 8">Mobile App</span>
                                      <span *ngIf="docs.PLATFORM_ID  == 9">{{docs.SOURCE_PLACE}}</span>
                                    </span>
                                    <span
                                      *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '4' || docs.ADVERTISEMENT_SOURCE_ID == '6'">Place
                                      : {{docs.SOURCE_PLACE}}</span>
                                    <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '5'">Print Source :
                                      {{docs.SOURCE_NAME}}</span>
                                    <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '7'">MFD/PKD Date : {{docs.DATE |
                                      date:'dd/MM/yyyy'}}</span>
                                    <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '8'">Sender :
                                      {{docs.SOURCE_NAME}}</span>
                                    <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '9'">Source :
                                      {{docs.SOURCE_NAME}}</span>
                                  </p>
                                </div>
                              </div>
                            </mat-panel-title>
                          </mat-expansion-panel-header>
                          <app-mat-spinner-overlay overlay="true" *ngIf="isUploadProgress">
                          </app-mat-spinner-overlay>
                          <div fxLayout="column" *ngFor="let file of docs.docInfo; let ind = index">
                            <div class="panel-body" fxLayout="row">
                              <div class="complaint-doc-icon-container flex">
                                <button mat-icon-button class="doc-btn flex" style="padding-left: 12px;">
                                  <img src="../../assets/images/media_doc.svg">
                                </button>
                              </div>
                              <div class="complaint-doc-link-container"
                                style="width:80%;position: relative;top: 3px;left: -4px;">
                                <a class="doc-link" matTooltip="{{file.name}}">{{file.name | slice:
                                  0:60}}{{file.name.length>61? '..': ''}}</a>
                              </div>
                              <div class="complaint-doc-icon-container flex">
                                <button mat-icon-button class="icon-btn flex" (click)="preview(file.url)">
                                  <img src="../../assets/images/media_eye.svg">
                                </button>
                              </div>
                              <div class="complaint-doc-icon-container flex" (click)="download(file.name,file.url)">
                                <button mat-icon-button class="icon-btn flex">
                                  <img src="../../assets/images/media_download.svg">
                                </button>
                              </div>
                            </div>
                            <div fxLayout="row" style="margin-bottom: 8px; margin-left: 15px;">
                              <div>
                                <img src="../../../assets/images/calender-icon.png">
                              </div>
                              <div
                                style="font-size: 11px; margin-left: 12px; margin-top: 3px; color: rgba(0, 0, 0, 0.6);">
                                {{file.date | date:'dd/MM/yyyy h:mm a' }}
                              </div>
                            </div>
                          </div>
                        </mat-expansion-panel>
                      </mat-accordion>
                      <mat-accordion multi>
                        <mat-expansion-panel class="intra-expansion" (opened)="panelOpenState = true"
                          (closed)="panelOpenState = false" *ngFor="let file of complainantFiles; let ind = index">
                          <mat-expansion-panel-header class="panel-header"
                            *ngIf="complainantFiles.length != 0 && file.FIELD_TAB == 'complainant'">
                            <mat-panel-title class="panel-title">
                              <div fxLayout="row">
                                <div class="attribute-container1">
                                  <p>
                                    <span class="grey-text">Uploaded by : </span>
                                    <span>Complainant</span>
                                  </p>
                                </div>
                              </div>
                            </mat-panel-title>
                          </mat-expansion-panel-header>
                          <div fxLayout="column">
                            <div class="panel-body" fxLayout="row">
                              <div class="complaint-doc-icon-container flex" *ngIf="file.FIELD_TAB == 'complainant'">
                                <button mat-icon-button class="doc-btn flex" style="padding-left: 12px;">
                                  <img src="../../assets/images/media_doc.svg">
                                </button>
                              </div>
                              <div class="complaint-doc-link-container" style="width:80%;position: relative;top: 3px;"
                                *ngIf="file.FIELD_TAB == 'complainant'">
                                <a class="doc-link"
                                  matTooltip="{{file.ATTACHMENT_SOURCE_NAME}}">{{file.ATTACHMENT_SOURCE_NAME | slice:
                                  0:60}}{{file.ATTACHMENT_SOURCE_NAME.length>61? '..': ''}}</a>
                              </div>
                              <div class="complaint-doc-icon-container flex" *ngIf="file.FIELD_TAB == 'complainant'">
                                <button mat-icon-button class="icon-btn flex" (click)="preview(file.ATTACHMENT_SOURCE)">
                                  <img src="../../assets/images/media_eye.svg">
                                </button>
                              </div>
                              <div class="complaint-doc-icon-container flex" *ngIf="file.FIELD_TAB == 'complainant'">
                                <button mat-icon-button class="icon-btn flex"
                                  (click)="download(file.ATTACHMENT_SOURCE_NAME, file.ATTACHMENT_SOURCE)">
                                  <img src="../../assets/images/media_download.svg">
                                </button>
                              </div>
                            </div>
                            <div fxLayout="row" style="margin-bottom: 8px; margin-left: 15px;">
                              <div>
                                <img src="../../../assets/images/calender-icon.png">
                              </div>
                              <div
                                style="font-size: 11px; margin-left: 12px; margin-top: 3px; color: rgba(0, 0, 0, 0.6);">
                                {{file.CREATED_DATE | date:'dd/MM/yyyy h:mm a'}}
                              </div>
                            </div>
                          </div>
                        </mat-expansion-panel>
                      </mat-accordion>
                    </div>
                  </mat-tab>
                  <mat-tab label="Advertiser">
                    <app-mat-spinner-overlay overlay="true" *ngIf="isUploadProgress">
                    </app-mat-spinner-overlay>
                    <div class="doc-body" fxLayout="row">
                      <div class="doc-fxrow-container" fxLayout="row wrap" fxLayoutAlign="flex-start">
                        <div fxFlex fxLayout="column" fxLayoutGap="10px" style="height: 35vh">
                          <div fxLayout="row wrap" fxLayoutGap="20px">
                            <!-- loop over the cardList and show the cards -->
                            <div *ngFor="let file of advertiserFiles; let ind = index" fxFlex="26" fxFlex.md="25"
                              fxFlex.sm="50" fxFlex.xs="100" fxLayout="column" style="padding: 5px;">
                              <!-- cards here -->
                              <mat-card class="mat-card-doc mat-elevation-z1" *ngIf="file.FIELD_TAB == 'advertiser'">
                                <div class="doc-icon-container">
                                  <div class="doc-image-container">
                                    <img src="../../../assets/images/File.png" width="20px" height="20px"
                                      alt="doc-icon">
                                  </div>
                                </div>
                                <div fxLayout="row">
                                  <div fxLayout="column">
                                    <div class="doc-caption">
                                      <p mat-line matTooltip="{{file.ATTACHMENT_SOURCE_NAME}}">
                                        {{file.ATTACHMENT_SOURCE_NAME}}</p>
                                      <div style="font-size: 11px; color: rgba(0, 0, 0, 0.6); margin-left: 5px;">
                                        {{file.CREATED_DATE | date:'dd/MM/yyyy h:mm a'}}
                                      </div>
                                    </div>
                                  </div>
                                  <!-- <mat-icon style="position: relative; top: 10px; cursor: pointer;" (click)="viewMoreOptions(file.ID)">more_vert
                                  </mat-icon> -->
                                  <button mat-icon-button [matMenuTriggerFor]="admin">
                                    <mat-icon (click)="viewMoreOptions(file.ID)">more_vert</mat-icon>
                                  </button>
                                  <mat-menu #admin="matMenu" class="action-buttons">
                                    <div class="admin-option-container">
                                      <button mat-menu-item class="option-btn"
                                        (click)="preview(file.ATTACHMENT_SOURCE)">
                                        <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                        <span class="option-text">Preview</span>
                                      </button>
                                      <mat-divider class="option-divider">
                                      </mat-divider>
                                      <button mat-menu-item class="option-btn"
                                        (click)="download(file.ATTACHMENT_SOURCE_NAME, file.ATTACHMENT_SOURCE)">
                                        <span class="option-text"><img
                                            src="../../../assets/images/Download (1).png"></span>
                                        <span class="option-text">Download</span>
                                      </button>
                                    </div>
                                  </mat-menu>
                                </div>
                              </mat-card>
                              <!-- <div class="action-buttons" *ngIf="file.ID == viewMoreOptionId && isViewMoreOpen" style="cursor: pointer;">
                                <div (click)="preview(file.ATTACHMENT_SOURCE)">Preview</div>
                                <div (click)="download(file.ATTACHMENT_SOURCE_NAME, file.ATTACHMENT_SOURCE)">Download</div>
                              </div> -->
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </mat-tab>
                  <mat-tab label="Internal">
                    <app-mat-spinner-overlay overlay="true" *ngIf="isUploadProgress">
                    </app-mat-spinner-overlay>
                    <div class="doc-body" fxLayout="row">
                      <div class="doc-fxrow-container" fxLayout="row wrap" fxLayoutAlign="flex-start">
                        <div fxFlex fxLayout="column" fxLayoutGap="10px" style="height: 35vh">
                          <div fxLayout="row wrap" fxLayoutGap="20px">
                            <!-- loop over the cardList and show the cards -->
                            <div *ngFor="let file of internalFiles; let ind = index" fxFlex="30" fxFlex.md="30"
                              fxFlex.sm="20" fxFlex.xs="100" fxLayout="column" style="padding: 5px;">
                              <!-- cards here -->
                              <mat-card class="mat-card-doc mat-elevation-z1" *ngIf="file.FIELD_TAB == 'internal'">
                                <div class="doc-icon-container">
                                  <div class="doc-image-container">
                                    <img src="../../../assets/images/File.png" width="20px" height="20px"
                                      alt="doc-icon">
                                  </div>
                                </div>
                                <div fxLayout="row">
                                  <div fxLayout="column">
                                    <div class="doc-caption">
                                      <p mat-line matTooltip="{{file.ATTACHMENT_SOURCE_NAME}}">
                                        {{file.ATTACHMENT_SOURCE_NAME}}</p>
                                      <div style="font-size: 11px; color: rgba(0, 0, 0, 0.6); margin-left: 5px;">
                                        {{file.CREATED_DATE | date:'dd/MM/yyyy h:mm a'}}
                                      </div>
                                      <div style="font-size: 11px; color: rgba(0, 0, 0, 0.6); margin-left: 5px;"
                                        *ngIf="file.ADVERTISER == 0 && file.COMPLAINANT == 0">
                                        <span style="padding-right: 5px;"><img
                                            src="../../../assets/images/shared.png"></span>Internal
                                      </div>
                                      <div fxLayout="row"
                                        style="font-size: 11px; color: rgba(0, 0, 0, 0.6); margin-left: 5px;"
                                        *ngIf="file.ADVERTISER == 1 || file.COMPLAINANT == 1">
                                        <div style="padding-right: 5px;"><img src="../../../assets/images/shared.png">
                                        </div>
                                        <div class="tooltip-text">Multiple
                                          <span class="tooltip-class"
                                            *ngIf="file.ADVERTISER == 1 || file.COMPLAINANT == 1">
                                            <span class="tooltip-adv" *ngIf="file.ADVERTISER == 1">Ad</span>
                                            <span class="tooltip-com" *ngIf="file.COMPLAINANT == 1">Co</span>
                                            <span class="tooltip-int">In</span>
                                          </span>
                                        </div>
                                        <div style="padding-left: 5px;"><img
                                            src="../../../assets/images/Vector (3).png"></div>
                                      </div>
                                    </div>
                                  </div>
                                  <!-- <mat-icon style="position: relative; top: 10px; cursor: pointer;" (click)="viewMoreActions(file.ID)">more_vert
                                  </mat-icon> -->
                                  <button mat-icon-button [matMenuTriggerFor]="admin">
                                    <mat-icon (click)="viewMoreOptions(file.ID)">more_vert</mat-icon>
                                  </button>
                                  <mat-menu #admin="matMenu" class="action-buttons">
                                    <div class="admin-option-container">
                                      <button mat-menu-item class="option-btn"
                                        (click)="preview(file.ATTACHMENT_SOURCE)">
                                        <span class="option-text"><img src="../../../assets/images/eye (1).png"></span>
                                        <span class="option-text">Preview</span>
                                      </button>
                                      <mat-divider class="option-divider"></mat-divider>
                                      <button mat-menu-item class="option-btn"
                                        (click)="download(file.ATTACHMENT_SOURCE_NAME, file.ATTACHMENT_SOURCE)">
                                        <span class="option-text"><img
                                            src="../../../assets/images/Download (1).png"></span>
                                        <span class="option-text">Download</span>
                                      </button>
                                      <mat-divider class="option-divider"></mat-divider>
                                      <button mat-menu-item class="option-btn"
                                        (click)="removeFile(file.ID, file.ATTACHMENT_SOURCE)"
                                        *ngIf="file.UPLOADED_BY == user_id">
                                        <span class="option-text" style="padding-left: 10px;"><img
                                            src="../../../assets/images/Trash.svg" style="font-size: 12px;"></span>
                                        <span class="option-text" style="padding-left: 10px;">Delete</span>
                                      </button>
                                      <mat-divider class="option-divider"></mat-divider>
                                      <button mat-menu-item class="option-btn"
                                        (click)="shareFile(file.ID, file.ADVERTISER, file.COMPLAINANT)">
                                        <span class="option-text"><img
                                            src="../../../assets/images/Share-icon.png"></span>
                                        <span class="option-text">Share</span>
                                      </button>
                                    </div>
                                  </mat-menu>
                                </div>
                              </mat-card>
                              <!-- <div class="action-buttons-internal" *ngIf="file.ID == viewMoreId && isViewMoreAction" style="cursor: pointer;">
                                <div (click)="preview(file.ATTACHMENT_SOURCE)">Preview</div>
                                <div (click)="download(file.ATTACHMENT_SOURCE_NAME, file.ATTACHMENT_SOURCE)">Download</div>
                                <div (click)="removeFile(file.ID, file.ATTACHMENT_SOURCE)" *ngIf="file.UPLOADED_BY == user_id">Delete</div>
                              </div> -->
                            </div>
                            <div class="add-file-container">
                              <div class="dropzone" fileDragDrop (filesChangeEmiter)="onFileChange($event)">
                                <div class="addfile-text-wrapper">
                                  <div class="upload-scope-container">
                                    <input type="file" name="file" id="file" (change)="onFileChange($event)"
                                      accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt,.jpeg,.png,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv"
                                      multiple>
                                    <label class="upload-label" for="file" fxLayout="column"
                                      fxLayoutAlign="center center">
                                      <mat-icon style="font-size: xx-large;font-weight: lighter;">add</mat-icon>
                                      <span class="add-textLink">Add files</span>
                                    </label>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </mat-tab>
                </mat-tab-group>
              </div>
            </div>
          </mat-tab>

          <mat-tab label="Claims" *ngIf="role == 2 || role == 4 || role == 5">
            <div class="claims-subhead-container" fxLayout="column" fxLayoutGap="10px">
              <div>
                <h3 class="claims-subhead">Claims by Intra Industries :</h3>
              </div>
              <div>
                <mat-divider></mat-divider>
              </div>
              <div class="no-claims" *ngIf="complaintClaims.length == 0 && complaintCodeViolated.length == 0">
                No claims raised...
              </div>
              <div fxLayout="column" fxLayoutGap="10px"
                *ngIf="complaintClaims.length != 0 || complaintCodeViolated.length != 0">
                <mat-accordion>
                  <mat-expansion-panel class="intra-expansion" (opened)="panelOpenState = true"
                    (closed)="panelOpenState = false">
                    <mat-expansion-panel-header class="panel-header">
                      <mat-panel-title class="panel-title">
                        Claim Details
                      </mat-panel-title>
                    </mat-expansion-panel-header>
                    <div class="intra-divider" style="width: 100%;border-radius: 1px">
                      <mat-divider></mat-divider>
                    </div>
                    <div class="intra-body" fxLayout="row" fxLayoutGap="10px">
                      <div class="raised-container" fxLayout="column">
                        <div class="head-container" fxLayout="row" fxLayoutGap="10px">
                          <div class="arrow-icon-container">
                            <img class="arrow-img-icon" src="../assets/images/arrow_circle-icon.svg " />
                          </div>
                          <div>
                            <h3 class="intra-h3">Claims raised</h3>
                          </div>
                        </div>
                        <div class="claim-challenges-container" fxLayout="column" fxLayoutGap="10px">
                          <div class="challenge-container" *ngFor="let item of complaintClaims; let in = index;">
                            <div class="panel-title">Claim challenged {{in+1}} </div>
                            <div fxLayout="column">
                              <div fxLayout="row" fxLayoutGap="10px">
                                <div class="attribute-container width50">
                                  <p>
                                    <span class="grey-text">Name :</span><br> {{item.CLAIM_CHALLENGED}}
                                  </p>
                                </div>
                                <div class="attribute-container width50">
                                  <p>
                                    <span class="grey-text">Annexure no. :</span><br> {{item.ANNEXURE_NO}}
                                  </p>
                                </div>
                              </div>
                              <div class="attribute-container">
                                <p>
                                  <span class="grey-text"> Key objection :</span><br>
                                  <span [innerHTML]="safeHTML(item.KEY_OBJECTION)">{{item.KEY_OBJECTION}}</span>
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="violated-container" fxLayout="column" fxLayoutGap="5px">
                        <div class="head-container" fxLayout="row" fxLayoutGap="10px">
                          <div class="arrow-icon-container">
                            <img class="arrow-img-icon" src="../assets/images/arrow_circle-icon.svg " />
                          </div>
                          <div>
                            <h3 class="intra-h3">ASCI code violated </h3>
                          </div>
                        </div>
                        <!-- <div class="violated-body-container"> -->
                        <div class="chapters-container">
                          <div class="chapter-text" *ngFor="let item of complaintCodeViolated">
                            <!-- <div *ngFor="let clause of clauseList"> -->
                            <div fxLayout="column">
                              <!-- *ngIf="clause.ID == item.CLAUSES_ID && clause.CHAPTER_ID == item.CHAPTER_ID"> -->
                              <span>
                                <mat-icon class="circle-icon">lens</mat-icon>Chapter {{item.CHAPTER_ID}}
                                <ng-container *ngIf="item.CLAUSES_ID"> : </ng-container>
                              </span>
                              <span style="width:90%;margin-left: 25px;">{{item.CLAUSES_ID}}</span>
                            </div>
                            <!-- </div> -->
                          </div>
                        </div>

                        <div class="head-container" fxLayout="row" fxLayoutGap="10px">
                          <div class="arrow-icon-container">
                            <img class="arrow-img-icon" src="../assets/images/arrow_circle-icon.svg " />
                          </div>
                          <div>
                            <h3 class="intra-h3">ASCI Guideline code violated </h3>
                          </div>
                        </div>
                        <br>
                        <div class="chapters-container">
                          <div class="chapter-text" *ngFor="let item of guidelines">
                            <!-- <div *ngFor="let clause of clauseList"> -->
                            <div fxLayout="column">
                              <!-- *ngIf="clause.ID == item.CLAUSES_ID && clause.CHAPTER_ID == item.CHAPTER_ID"> -->
                              <span>
                                <mat-icon class="circle-icon">lens</mat-icon>Guideline <span
                                  *ngIf="item.G_CHAPTER_ID != 1">{{item.G_CHAPTER_ID - 1}}</span>
                                <span *ngIf="item.G_CHAPTER_ID == 1">N/A</span>
                                <ng-container
                                  *ngIf="item.G_CHAPTER_ID != 1 && item.G_CHAPTER_ID != 2 && item.G_CHAPTER_ID != 7 && item.G_CHAPTER_ID != 8">
                                  : </ng-container>
                              </span>
                              <span style="width:90%;margin-left: 25px;" *ngIf="item.G_CHAPTER_ID != 1">
                                {{item.G_CLAUSES_ID}}
                              </span>
                              <br>
                              <span style="width:90%;margin-left: 25px;"
                                *ngIf="item.G_CHAPTER_ID == 10 && item.CELEBRITY_NAME != ''"><strong>Celebrities</strong>
                                : {{item.CELEBRITY_NAME}}</span>
                            </div>
                            <!-- </div> -->
                          </div>
                        </div>

                        <div fxLayout="column" fxLayoutGap="10px">
                          <button mat-button (click)="viewDocument()" class="violated-popups">
                            <div>
                              <span class="bolder">
                                <img src="..\assets\images\folder-icon.svg">&nbsp; View documents
                              </span>
                            </div>
                          </button>
                        </div>
                        <!-- </div> -->
                      </div>
                    </div>
                  </mat-expansion-panel>
                </mat-accordion>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </div>
    </div>
  </div>
  <div class="timeline-btn-container" fxLayout="column" fxLayoutAlign="start end" [@buttonInOut]="buttonState">
    <button mat-raised-button class="timeline-btn" (click)="openTimeline()">Timeline</button>
  </div>
  <div [@slideInOut]="timelineState" class="timeline-sidebar">
    <app-timeline-sidebar></app-timeline-sidebar>
  </div>
</div>