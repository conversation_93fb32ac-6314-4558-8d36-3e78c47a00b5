import { DatePipe } from '@angular/common';
import { Component, HostListener, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfirmationPopupComponent } from '../confirmation-popup/confirmation-popup.component';
import { AuthService } from '../services/auth.service';
import { ComplaintsService } from '../services/complaints.service';
import { NotificationService } from '../services/notification.service';
import { colorObj } from '../shared/color-object';

@Component({
  selector: 'app-mobile-notifications',
  templateUrl: './mobile-notifications.component.html',
  styleUrls: ['./mobile-notifications.component.scss']
})
export class MobileNotificationsComponent implements OnInit {
  selectedID;
  complaintsList: any;
  confirmationMsg: any = {};
  userData: any;
  year: any;
  isEmpty: boolean;
  dataSource1: any = [];
  dataSource2: any = [];
  storeUserComp: any = [];
  caseidArray: any = [];
  companyComplaintStats: any;
  userComplaintStats: any;
  innerWidth: number;
  mobile: boolean;
  roleId: any;
  roleList: any;
  roleName: any;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.mobile = true;
    } else {
      this.mobile = false;
    }
  }

  constructor(
    private cs: ComplaintsService,
    private route: ActivatedRoute,
    private router: Router,
    private dialog: MatDialog,
    private authService: AuthService,
    private notify: NotificationService,
    private datePipe: DatePipe,
  ) { }

  ngOnInit(): void {
    this.roleList = JSON.parse(window.localStorage.getItem('role'));
    this.userData = JSON.parse(window.localStorage.getItem('userInfo'));
    this.roleId = this.userData.roleId;
    if (!(this.userData.roleId == 7 || this.userData.roleId == 9)) {
      this.roleList.forEach(element => {
        if (this.userData.roleId == element.ID) {
          this.roleName = element.ROLE_NAME[0];
        }
      });
    }
    else if (this.userData.roleId == 7 || this.userData.roleId == 9) {
      this.roleName = this.userData.firstName[0];
    }
    if (window.innerWidth <= 1250) {
      this.mobile = true;
    } else {
      this.mobile = false;
    }
    if (this.userData.roleId == 4 || this.userData.roleId == 7 || this.userData.roleId == 8) {
      this.getUserComplaints(this.year);
      this.getUserComplaintStats(this.year);
    }
    else if (this.userData.roleId == 5) {
      this.getAdvertiserComplaints(this.year);
      this.getUserComplaints(this.year);
      this.getCompanyComplaintStats(this.year);
    }
  }
  getUserComplaints(year) {
    this.cs.getUserComplaints(year).subscribe((complaintsData: any) => {
      this.storeUserComp = complaintsData.data
      this.dataSource1 = complaintsData.data;
      for (let i = 0; i < this.dataSource1.length; i++) {
        if (this.dataSource1[i].SUBMITTED == 0) {
          this.dataSource1[i].COMPLAINT_STATUS_NAME = "In Draft"
        }
      }
      if (this.dataSource1.length == 0) {
        this.isEmpty = true;
      } else {
        this.isEmpty = false;
      }
      for (let i = 0; i < this.storeUserComp.length; i++) {
        this.caseidArray.push(this.storeUserComp[i].CASE_ID);
      }
      this.selectedID = this.caseidArray[0];
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  getAdvertiserComplaints(year) {
    this.cs.getAdvertiserComplaints(year).subscribe((complaintsData: any) => {
      this.dataSource2 = complaintsData.data;
      for (let i = 0; i < this.dataSource2.length; i++) {
        if (this.dataSource2[i].SUBMITTED == 0) {
          this.dataSource2[i].COMPLAINT_STATUS_NAME = "In Draft"
        }
      }
      if (this.dataSource2.length == 0) {
        this.isEmpty = true;
      } else {
        this.isEmpty = false;
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  getUserComplaintStats(year) {
    this.cs.getUserComplaintStats(year).subscribe((complaintStatus: any) => {
      this.userComplaintStats = complaintStatus.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  getCompanyComplaintStats(year) {
    this.cs.getCompanyComplaintStats(year).subscribe((complaintStatus: any) => {
      this.companyComplaintStats = complaintStatus.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  selectActivities(caseid) {
    this.cs.getComplaintTimeline(caseid).subscribe((timeline: any) => {
      this.complaintsList = timeline.data;
      for (let item of this.complaintsList) {
        item.date = this.datePipe.transform(item.date, 'dd/MM/yyyy, h:mm a');
        for (let val of item.updates) {
          if (val.label == 'Complaint Received On' || val.label == 'Due date changed to' || val.label == 'Advertiser due date changed to') {
            val.value = this.datePipe.transform(val.value, 'd MMMM, y, EEEE');
          }
        }
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  viewProfile(flag) {
    this.authService.profileFlag = flag;
    this.router.navigateByUrl('auth/my-profile');
  }

  viewDashboard() {
    this.router.navigateByUrl("/home/<USER>")
  }

  viewNotifications() {
    this.router.navigateByUrl('mobile-notifications');
  }

  logout() {
    this.confirmationMsg.title = 'Are you sure you want to logout ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.authService.logout().subscribe(
          (res) => {
            window.localStorage.clear();
            this.router.navigate(['auth/login']);
          },
          (err) => {
            window.localStorage.clear();
            this.router.navigate(['auth/login']);
          }
        );
      }
    });
  }

}