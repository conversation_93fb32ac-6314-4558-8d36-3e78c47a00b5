import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToolbarOptionsComponent } from '../toolbar-options/toolbar-options.component';
import { HeadingComponent } from '../heading/heading.component';
import { MaterialModule } from '../material/material.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IconsComponent } from '../icons/icons.component';
import { MatSpinnerOverlayComponent } from '../mat-spinner-overlay/mat-spinner-overlay.component';
import { FlexLayoutModule } from '@angular/flex-layout';
import { AddTaskComponent } from '../task-board/add-task/add-task.component';
import { ConfirmationPopupComponent } from '../confirmation-popup/confirmation-popup.component';
import { MomentDateModule } from '@angular/material-moment-adapter';
import { MobileHeaderComponent } from '../mobile-header/mobile-header.component';

@NgModule({
  declarations: [
    ToolbarOptionsComponent,
    HeadingComponent,
    IconsComponent,
    MatSpinnerOverlayComponent,
    AddTaskComponent,
    ConfirmationPopupComponent,
    MobileHeaderComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MaterialModule,
    FlexLayoutModule,
    MomentDateModule,
  ],
  entryComponents: [
    AddTaskComponent,
    ConfirmationPopupComponent
  ],
  exports: [
    ToolbarOptionsComponent,
    HeadingComponent,
    IconsComponent,
    MatSpinnerOverlayComponent,
    MobileHeaderComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class SharedModule { }
