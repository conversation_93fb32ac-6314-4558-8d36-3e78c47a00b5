import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ComplaintResolutionStageStatusProcessChartComponent } from './complaint-resolution-stage-status-process-chart.component';

describe('ComplaintResolutionStageStatusProcessChartComponent', () => {
  let component: ComplaintResolutionStageStatusProcessChartComponent;
  let fixture: ComponentFixture<ComplaintResolutionStageStatusProcessChartComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ComplaintResolutionStageStatusProcessChartComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ComplaintResolutionStageStatusProcessChartComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
