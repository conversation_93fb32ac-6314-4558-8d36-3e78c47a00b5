import { DatePipe } from '@angular/common';
import { Compo<PERSON>, HostListener, OnInit, QueryList, ViewChild, ViewChildren, Pipe } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { GeneralPublicComponent } from 'src/app/cases/general-public/general-public.component';
import { IntraIndustryComponent } from 'src/app/cases/intra-industry/intra-industry.component';
import { ConfirmationPopupComponent } from '../../confirmation-popup/confirmation-popup.component';
import { CreateConfirmDialogComponent } from '../create-confirm-dialog/create-confirm-dialog.component';
import { Complaints } from '../../model/complaints';
import { AuthService } from '../../services/auth.service'
import { ComplaintsService } from '../../services/complaints.service';
import { NotificationService } from '../../services/notification.service';
import { colorObj } from '../../shared/color-object';
import { UploadService } from 'src/app/services/upload.service';
import { isThursday } from 'date-fns';
import { MessageConversationComponent } from '../message-conversation/message-conversation.component';
import { environment } from 'src/environments/environment';
import { saveAs as importedSaveAs } from "file-saver";
import { ViewMediaFileComponent } from 'src/app/cases/view-media-file/view-media-file.component';
import { ViewDocumentComponent } from 'src/app/cases/view-document/view-document.component';
import { RecommendationResolutionDetailsComponent } from 'src/app/recommendation-resolution-details/recommendation-resolution-details.component';
import { ThirdPartyService } from 'src/app/services/third-party.service';

@Component({
  selector: 'app-user-dashboard',
  templateUrl: './user-dashboard.component.html',
  styleUrls: ['./user-dashboard.component.css']
})
@Pipe({ name: 'safeHtml' })
export class UserDashboardComponent implements OnInit {

  COMP_ID: number;
  parentcase_id: any;
  is_FTC: number = 0;
  userComplainant = false;
  userAdvertiser = false;
  dataSource1: any = [];
  dataSource: any = [];
  dataSource2: any = [];
  storeUserComp: any = [];
  storeUserCaseId: any = [];
  caseidArray: any = [];
  selectedID;
  isEmpty: boolean = false;
  complaintTimeline = [];
  timeLineExist: boolean = true;
  timelineLoading: boolean = false;
  userData: any;
  isFTCComplaint: boolean;
  pagename: String;
  user: String;
  complaints: Complaints[];
  complaintsList: any = [];
  complaintDetails: any = [];
  comp_adsource: any;
  userComplaintDocs: any = [];
  confirmationMsg: any = {};
  stateCtrl = new FormControl();
  newComplaint: boolean = false;
  complaintdetails: boolean = false;
  against_comp: boolean = false;
  advertiserComp: boolean = false;
  userComp: Boolean = false;
  years: any = [];
  displayColumns: string[] = ['caseID', 'brandname', 'case', 'status', 'duedate'];
  displayedColumns: string[] = ['caseID', 'brandname', 'case', 'status', 'stage'];
  data;
  data1;
  tableData = [];
  fileList: File[] = [];
  listOfFiles: any[] = [];
  @ViewChild('attachments') attachment: any;
  public files: any[] = [];
  value = 'Clear me';
  compActivities = [];
  blankComplaintID: number;
  draft: boolean = false;
  hideCreateButton: boolean = false;
  docFileList: any[] = [];
  complainantFiles = [];
  advertiserFiles = [];
  internalFiles = [];
  filesProgress: any[] = [];
  isError: boolean = false;
  isUploadProgress: boolean = false;
  dat = {};
  fileName = [];
  userTypeId: any;
  userComplaintStats: any[];
  companyComplaintStats: any[];
  text_year = new FormControl('');
  year = "";
  draftComplaint: boolean = false;
  initialValue: any;
  public bucketUrl = `${environment.BUCKET_URL}`;
  imgURL: string;
  url: string;
  detail_adsource: any;
  noOfDocs = 0;
  adDocs: any;
  adMedium: any;
  complaintClaims: any = [];
  complaintCodeViolated: any = [];
  guidelines: any = [];
  panelOpenState = false;
  add_seenDate: any;
  similarComplaints = [];
  similarCompLoading: boolean = true;
  parent_id: any;
  case_id: any;
  similarComplaintId: any;
  compDetails: any = [];
  similar_complaint_id: any;
  detail_company: any;
  classification_name: any;
  complaint_source_id: number;
  seen_date: any;
  detail_advert: string;
  comp_date: any;
  detail_date: any;
  longText1: string;
  detail_complaint: string;
  detail_platform: any;
  detail_channel: any;
  detail_addate: any;
  similarComplaintClaims: any = [];
  similarComplaintCodeViolated: any = [];
  similarComplaintGuidelines: any = [];
  similar_detail_link: any;
  longText4: string;
  docUrl;
  detail_place: any;
  media_outlet: any;
  media: any;
  super_category: any;
  edition: any;
  suppliment: any;
  ad_language: any;
  creative_id: any;
  translation_hyper: any;
  transcription: any;
  duration: any;
  longText5: string;
  engagements: any;
  publication_url: any;
  profile_url: any;
  company_details: any;
  editable: boolean = false;

  // @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChildren(MatPaginator) paginator = new QueryList<MatPaginator>();
  @ViewChild(GeneralPublicComponent, { static: false }) generalPublicComp: GeneralPublicComponent;
  @ViewChild(IntraIndustryComponent, { static: false }) intraIndustryComp: IntraIndustryComponent;
  mobile: boolean;
  innerWidth: number;
  roleId: any;
  roleList: any;
  roleName: any;
  currentYear: number;
  id: any;
  isInboundEmpty: boolean = false;
  isChatbotEmpty: boolean = false;
  chatbotCompDetails: boolean = false;
  industryMemberRole: any;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.mobile = true;
    } else {
      this.mobile = false;
    }
  }

  constructor(
    private cs: ComplaintsService,
    private thirdPartyService: ThirdPartyService,
    private route: ActivatedRoute,
    private router: Router,
    public matDialog: MatDialog,
    private authService: AuthService,
    private notify: NotificationService,
    private uploadService: UploadService,
    private datePipe: DatePipe,
    private sanitized: DomSanitizer
  ) { }

  safeHTML(unsafe: string) {
    return this.sanitized.bypassSecurityTrustHtml(unsafe);
  }

  ngOnInit(): void {
    this.pagename = "Dashboard";
    this.hideCreateButton = false;
    if (window.innerWidth <= 1250) {
      this.mobile = true;
    } else {
      this.mobile = false;
    }
    this.userData = JSON.parse(window.localStorage.getItem('userInfo'));
    this.roleList = JSON.parse(window.localStorage.getItem('role'));
    this.roleId = this.userData.roleId;
    this.industryMemberRole = this.userData.industryMemberRoleId;
    if (!(this.userData.roleId == 7 || this.userData.roleId == 9)) {
      this.roleList.forEach(element => {
        if (this.userData.roleId == element.ID) {
          this.roleName = element.ROLE_NAME[0];
        }
      });
    }
    else if (this.userData.roleId == 7 || this.userData.roleId == 9) {
      this.roleName = this.userData.firstName[0];
    }
    if (this.userData.roleId == 4) {
      this.userTypeId = 3;
    } else if (this.userData.roleId == 7) {
      this.userTypeId = 1;
    } else if (this.userData.roleId == 5) {
      this.userTypeId = 2;
    } else if (this.userData.roleId == 8) {
      this.userTypeId = 4;
    }
    if (this.userData.roleId == 4 || this.userData.roleId == 7 || this.userData.roleId == 8) {
      this.userComplainant = true;
      this.user = this.userData.firstName;
      this.selectYear();
    }
    else if (this.userData.roleId == 5) {
      this.userAdvertiser = true;
      this.user = this.userData.firstName;
      this.selectYear();
    }
    this.newComplaint = false;
  }

  getUserComplaints(year) {
    this.cs.getUserComplaints(year).subscribe((complaintsData: any) => {
      this.storeUserComp = complaintsData.data;
      for (let i = 0; i < this.storeUserComp.length; i++) {
        if (this.storeUserComp[i].CASE_ID != null && this.storeUserComp[i].CASE_ID != '') {
          this.storeUserCaseId.push(this.storeUserComp[i]);
        }
      }
      this.dataSource1.data = complaintsData.data;
      this.dataSource1 = new MatTableDataSource<any>(complaintsData.data);
      if (this.userComplainant == true && this.userData.roleId == 7) {
        this.dataSource1.paginator = this.paginator.toArray()[0];
      }
      else if (this.userComplainant == true && this.userData.roleId != 7) {
        this.dataSource1.paginator = this.paginator.toArray()[2];
      }
      else if (this.userAdvertiser == true) {
        this.dataSource1.paginator = this.paginator.toArray()[4];
      }
      for (let i = 0; i < this.dataSource1.data.length; i++) {
        if (this.dataSource1.data[i].SUBMITTED == 0) {
          this.dataSource1.data[i].COMPLAINT_STATUS_NAME = "In Draft"
        }
      }
      if (this.dataSource1.data.length == 0) {
        this.isEmpty = true;
      } else {
        this.isEmpty = false;
      }
      for (let i = 0; i < this.storeUserComp.length; i++) {
        this.caseidArray.push(this.storeUserComp[i].CASE_ID);
      }
      this.selectedID = this.caseidArray[0];
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  getUserChatbotComplaints(year) {
    this.cs.getUserChatbotComplaints(year).subscribe((complaintsData: any) => {
      this.dataSource.data = complaintsData.data;
      this.dataSource = new MatTableDataSource<any>(complaintsData.data);
      this.dataSource.paginator = this.paginator.toArray()[1];
      for (let i = 0; i < this.dataSource.data.length; i++) {
        if (this.dataSource.data[i].SUBMITTED == 0) {
          this.dataSource.data[i].COMPLAINT_STATUS_NAME = "In Draft"
        }
      }
      if (this.dataSource.data.length == 0) {
        this.isChatbotEmpty = true;
      } else {
        this.isChatbotEmpty = false;
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  getAdvertiserComplaints(year) {
    this.cs.getAdvertiserComplaints(year).subscribe((complaintsData: any) => {
      this.dataSource2.data = complaintsData.data;
      this.dataSource2 = new MatTableDataSource<any>(complaintsData.data);
      this.dataSource2.paginator = this.paginator.toArray()[3];
      for (let i = 0; i < this.dataSource2.data.length; i++) {
        if (this.dataSource2.data[i].SUBMITTED == 0) {
          this.dataSource2.data[i].COMPLAINT_STATUS_NAME = "In Draft"
        }
      }
      if (this.dataSource2.data.length == 0) {
        this.isInboundEmpty = true;
      } else {
        this.isInboundEmpty = false;
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  openDialog() {
    const dialogRef = this.matDialog.open(CreateConfirmDialogComponent, {
      width: '500px',
      height: 'auto'
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        if (dialogResult.result == true) {
          this.is_FTC = 1;
        }
        else if (dialogResult.result == false) {
          this.is_FTC = 0;
        }
        this.cs.isFTCComplaint.next({ isFTC: dialogResult.result });
        this.createAdvertiserComplaint();
      }
    });
  }

  getUserComplaintStats(year) {
    this.cs.getUserComplaintStats(year).subscribe((complaintStatus: any) => {
      this.userComplaintStats = complaintStatus.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  getCompanyComplaintStats(year) {
    this.cs.getCompanyComplaintStats(year).subscribe((complaintStatus: any) => {
      this.companyComplaintStats = complaintStatus.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  createUserComplaint() {
    this.hideCreateButton = true;
    this.newComplaint = true;
    this.editable = false;
    // if(this.userData.roleId == 8){
    //   this.advertiserComp = true;
    // }else{
    //   this.userComp = true;
    // }
    this.draftComplaint = false;
    this.cs.updateButtonName("User");
    this.createBlankComplaintRecord();
  }

  createAdvertiserComplaint() {
    this.newComplaint = true;
    this.editable = false;
    this.draftComplaint = false;
    this.cs.updateButtonName("User");
    this.createBlankComplaintRecord();
  }

  createBlankComplaintRecord() {
    this.cs.createBlankComplaint(this.userTypeId, this.userData.userId, this.userData.userId, this.is_FTC, new Date()).subscribe(res => {
      this.blankComplaintID = res.data.COMPLAINT_ID;
      this.cs.updateBlankComplaintId = this.blankComplaintID;
      if (this.userData.roleId == 8 || this.userAdvertiser) {
        this.advertiserComp = true;
      } else if (this.userComplainant) {
        this.userComp = true;
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  back() {
    location.reload();
    this.hideCreateButton = false;
    this.newComplaint = false;
  }

  selectComplaint(element) {
    this.COMP_ID = element.ID;
    this.parentcase_id = element.CASE_ID;
    this.hideCreateButton = true;
    if (element.COMPLAINT_STATUS_NAME != "In Draft" && element.EDITABLE == 0) {
      if (element.EDITABLE == 0) {
        this.editable = false;
      }
      this.complaintdetails = true;
      this.chatbotCompDetails = false;
      this.against_comp = false;
      this.userComplaintDocs = [];
      this.getAdvDocuments(this.COMP_ID);
      this.cs.getUserComplaintById(this.COMP_ID).subscribe(res => {
        this.complaintDetails = res.data;
        this.id = this.COMP_ID;
        if (this.complaintDetails.ADVERTISEMENT_MEDIUM.length != 0) {
          this.comp_adsource = this.complaintDetails.ADVERTISEMENT_MEDIUM;
          this.add_seenDate = this.complaintDetails.ADVERTISEMENT_MEDIUM[0].DATE;
        }
        this.noOfDocs = 0;
        if (res.data.ADVERTISEMENT_MEDIUM_DOCUMENT.length != 0) {
          for (let i = 0; i < res.data.ADVERTISEMENT_MEDIUM_DOCUMENT.length; i++) {
            if (res.data.ADVERTISEMENT_MEDIUM_DOCUMENT[i].ATTACHMENT_SOURCE !== '') {
              this.noOfDocs += 1;
            }
          }
        }
        this.adDocs = res.data.ADVERTISEMENT_MEDIUM_DOCUMENT;
        this.adMedium = res.data.ADVERTISEMENT_MEDIUM;
        this.complaintClaims = res.data.CLAIMS;
        this.complaintCodeViolated = res.data.CODEVIOLATED;
        this.guidelines = res.data.GUIDELINES;
        for (let i = 0; i <= this.complaintCodeViolated.length - 1; i++) {
          if (this.complaintCodeViolated[i].CLAUSES_ID != null) {
            this.complaintCodeViolated[i].CLAUSES_ID = ((this.complaintCodeViolated[i].CLAUSES_ID).replace(/"/g, '')).slice(1, -1);
          }
        }
        for (let i = 0; i <= this.guidelines.length - 1; i++) {
          if (this.guidelines[i].G_CLAUSES_ID != null) {
            this.guidelines[i].G_CLAUSES_ID = ((this.guidelines[i].G_CLAUSES_ID).replace(/"/g, '')).slice(1, -1);
          }
        }
        if (res.data.ADVERTISEMENT_MEDIUM_DOCUMENT.length != 0) {
          for (let medium of res.data.ADVERTISEMENT_MEDIUM_DOCUMENT) {
            let advMedium = {};
            if (medium.ATTACHMENT_SOURCE != "") {
              advMedium = {
                "ATTACHMENT_SOURCE": medium.ATTACHMENT_SOURCE,
                "ATTACHMENT_NAME": medium.ATTACHMENT_NAME,
                "ATTACHMENT_SOURCE_SIZE": medium.SIZE,
                "ATTACHMENT_SOURCE_TYPE": medium.TYPE_OF_DOCUMENT,
                "DATE": medium.CREATED_DATE
              }
              this.userComplaintDocs.push(advMedium)
            }
          }
        }
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      })
    }
    else if (element.COMPLAINT_STATUS_NAME == "In Draft" || element.EDITABLE == 1) {
      this.COMP_ID = element.ID;
      this.newComplaint = true;
      this.draftComplaint = true;
      this.cs.updateButtonName("User");
      this.cs.updateEditableForm("System");
      this.cs.updateBlankComplaintId = this.COMP_ID;
      if (this.userData.roleId == 4 || this.userData.roleId == 7) {
        this.userComp = true;
      } else if (this.userData.roleId == 5 || this.userData.roleId == 8) {
        this.advertiserComp = true;
      }
      if (element.EDITABLE == 1) {
        this.editable = true;
      } else {
        this.editable = false;
      }
    }
  }

  selectChatbotComplaint(element) {
    this.COMP_ID = element.ID;
    this.parentcase_id = element.CASE_ID;
    this.hideCreateButton = true;
    if (element.COMPLAINT_STATUS_NAME != "In Draft" && element.EDITABLE == 0) {
      if (element.EDITABLE == 0) {
        this.editable = false;
      }
      this.complaintdetails = true;
      this.chatbotCompDetails = true;
      this.userComplaintDocs = [];
      this.getAdvDocuments(this.COMP_ID);
      this.cs.getUserChatbotComplaintById(this.COMP_ID).subscribe(res => {
        this.complaintDetails = res.data;
        this.id = this.COMP_ID;
        this.noOfDocs = 0;
        if (res.data.ATTACHMENT_DOCUMENT.length != 0) {
          for (let i = 0; i < res.data.ATTACHMENT_DOCUMENT.length; i++) {
            if (res.data.ATTACHMENT_DOCUMENT[i].ATTACHMENT_SOURCE !== '') {
              this.noOfDocs += 1;
            }
          }
        }
        this.adDocs = res.data.ATTACHMENT_DOCUMENT;
        if (res.data.ATTACHMENT_DOCUMENT.length != 0) {
          for (let medium of res.data.ATTACHMENT_DOCUMENT) {
            let advMedium = {};
            if (medium.ATTACHMENT_SOURCE != "") {
              advMedium = {
                "ATTACHMENT_SOURCE": medium.ATTACHMENT_SOURCE,
                "ATTACHMENT_NAME": medium.ATTACHMENT_SOURCE_NAME,
                "ATTACHMENT_SOURCE_SIZE": medium.SIZE,
                "ATTACHMENT_SOURCE_TYPE": medium.ATTACHMENT_SOURCE_TYPE
              }
              this.userComplaintDocs.push(advMedium)
            }
          }
        }
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      })
    }
    else if (element.COMPLAINT_STATUS_NAME == "In Draft" || element.EDITABLE == 1) {
      this.COMP_ID = element.ID;
      this.newComplaint = true;
      this.draftComplaint = true;
      this.cs.updateButtonName("User");
      this.cs.updateEditableForm("Chatbot");
      this.cs.updateBlankComplaintId = this.COMP_ID;
      this.userComp = true;
      if (element.EDITABLE == 1) {
        this.editable = true;
      } else {
        this.editable = false;
      }
    }
  }

  selectComplaintAgainst(element) {
    this.COMP_ID = element.ID;
    this.parentcase_id = element.CASE_ID;
    this.complaintdetails = true;
    this.against_comp = true;
    this.userComplaintDocs = [];
    this.getAdvDocuments(this.COMP_ID);
    this.cs.getAdvertiserComplaintById(this.COMP_ID).subscribe(res => {
      this.complaintDetails = res.data;
      this.id = this.COMP_ID;
      if (this.complaintDetails.ADVERTISEMENT_MEDIUM.length != 0) {
        this.comp_adsource = this.complaintDetails.ADVERTISEMENT_MEDIUM;
      }
      this.noOfDocs = 0;
      if (res.data.ADVERTISEMENT_MEDIUM_DOCUMENT.length != 0) {
        for (let i = 0; i < res.data.ADVERTISEMENT_MEDIUM_DOCUMENT.length; i++) {
          if (res.data.ADVERTISEMENT_MEDIUM_DOCUMENT[i].ATTACHMENT_SOURCE !== '') {
            this.noOfDocs += 1;
          }
        }
      }
      this.adDocs = res.data.ADVERTISEMENT_MEDIUM_DOCUMENT;
      this.adMedium = res.data.ADVERTISEMENT_MEDIUM;
      this.complaintClaims = res.data.CLAIMS;
      this.complaintCodeViolated = res.data.CODEVIOLATED;
      this.guidelines = res.data.GUIDELINES;
      for (let i = 0; i <= this.complaintCodeViolated.length - 1; i++) {
        if (this.complaintCodeViolated[i].CLAUSES_ID != null) {
          this.complaintCodeViolated[i].CLAUSES_ID = ((this.complaintCodeViolated[i].CLAUSES_ID).replace(/"/g, '')).slice(1, -1);
        }
      }
      for (let i = 0; i <= this.guidelines.length - 1; i++) {
        if (this.guidelines[i].G_CLAUSES_ID != null) {
          this.guidelines[i].G_CLAUSES_ID = ((this.guidelines[i].G_CLAUSES_ID).replace(/"/g, '')).slice(1, -1);
        }
      }
      if (res.data.ADVERTISEMENT_MEDIUM_DOCUMENT.length != 0) {
        for (let medium of res.data.ADVERTISEMENT_MEDIUM_DOCUMENT) {
          let advMedium = {};
          if (medium.ATTACHMENT_SOURCE != "") {
            advMedium = {
              "ATTACHMENT_SOURCE": medium.ATTACHMENT_SOURCE,
              "ATTACHMENT_NAME": medium.ATTACHMENT_NAME,
              "ATTACHMENT_SOURCE_SIZE": medium.SIZE,
              "ATTACHMENT_SOURCE_TYPE": medium.TYPE_OF_DOCUMENT,
              "DATE": medium.CREATED_DATE
            }
            this.userComplaintDocs.push(advMedium)
          }
        }
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  previewLink(source) {
    if (source.indexOf("https") == -1) {
      window.open("https://" + source, "_blank");
    } else {
      window.open(source, "_blank");
    }
  }

  closeDetails() {
    this.complaintdetails = false;
    this.hideCreateButton = false;
    this.listOfFiles = [];
  }

  closeCreateComplaint() {
    this.userComp = false;
    this.hideCreateButton = false;
    this.newComplaint = false;
    this.draftComplaint = true;
  }

  viewNotifications() {
    this.router.navigateByUrl('mobile-notifications');
  }

  getTooltipText() {
    return `Duplicate complaint
    IRS
    Formal Investigation
    Under Technical Expert
    Under CCC
    Re-examination
    Under CCCR
    Under IRP`;
  }

  selectActivities(caseid) {
    this.cs.getComplaintTimeline(caseid).subscribe((timeline: any) => {
      this.complaintsList = timeline.data;
      for (let item of this.complaintsList) {
        item.date = this.datePipe.transform(item.date, 'dd/MM/yyyy, h:mm a');
        for (let val of item.updates) {
          if (val.label == 'Complaint Received On' || val.label == 'Due date changed to' || val.label == 'Advertiser due date changed to') {
            val.value = this.datePipe.transform(val.value, 'd MMMM, y, EEEE');
          }
        }
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  createMessage() {
    const dialogRef = this.matDialog.open(MessageConversationComponent, {
      width: '1500px',
      height: 'auto',
      position: {
        top: '0px',
        left: '25vw'
      },
      data: { "details": this.complaintDetails },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'refresh') {
          this.getUserComplaints(this.year);
          this.getAdvertiserComplaints(this.year);
        }
      }
    );
  }

  deleteComplaint() {
    this.confirmationMsg.title = 'Are you sure you want to delete this complaint ?';
    const dialogRef = this.matDialog.open(ConfirmationPopupComponent, {
      data: { id: this.blankComplaintID, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        if (this.draftComplaint) {
          this.cs.deleteDraftComplaint(this.COMP_ID).subscribe(res => {
            this.COMP_ID = 0;
            dialogRef.close('refresh');
            let currentUrl = this.router.url;
            this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = 'reload';
            this.router.navigate([currentUrl]);
            this.getUserComplaints(this.year);
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
        } else {
          this.cs.deleteBlankComplaint(dialogResult.id).subscribe(res => {
            this.blankComplaintID = 0;
            dialogRef.close('refresh');
            let currentUrl = this.router.url;
            this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = 'reload';
            this.router.navigate([currentUrl]);
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
        }
      }
    });
  }

  onTabChanged(event) {
    if (event.index == 0) {
      this.getAdvDocuments(this.COMP_ID);
    }
    else if (event.index == 1) {
      this.timelineLoading = true;
      this.timeLineExist = true;
      this.getComplaintTimeline(this.COMP_ID);
    }
  }

  onTabChangedDetails(event) {
    if (event.index == 1) {
      this.getAdvDocuments(this.COMP_ID);
    }
    else if (event.index == 2) {
      this.timelineLoading = true;
      this.timeLineExist = true;
      this.getComplaintTimeline(this.COMP_ID);
    }
    if (event.index == 4) {
      this.similarCompLoading = true;
      this.getSimilarComplaints(this.parentcase_id);
    }
  }

  openRecommendationorResolutionDialog(val) {
    const dialogRef = this.matDialog.open(RecommendationResolutionDetailsComponent, {
      width: '447px',
      height: 'auto',
      data: { id: this.id, type: val },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
      }
    );
  }

  openNewTab() {
    const dialogRef = this.matDialog.open(ViewMediaFileComponent, {
      width: '476px',
      height: 'auto',
      data: { adMedium: this.adMedium, adDocs: this.adDocs },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
      }
    );
  }

  viewDocument() {
    const dialogRef = this.matDialog.open(ViewDocumentComponent, {
      width: '525px',
      height: '472px',
      data: this.COMP_ID,
      disableClose: true
    });
  }

  getAdvDocuments(id) {
    this.cs.getDocumentsByComplaint(id).subscribe(res => {
      this.docFileList = res.data;
      this.complainantFiles = this.docFileList['COMPLAINANT'];
      this.advertiserFiles = this.docFileList['ADVERTISER'];
      this.internalFiles = this.docFileList['INTERNAL'];
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    });
  }

  getComplaintTimeline(id) {
    this.cs.getComplaintTimeline(id).subscribe((timeline: any) => {
      this.timelineLoading = false;
      this.timeLineExist = true;
      this.complaintTimeline = timeline.data;
      for (let item of this.complaintTimeline) {
        item.date = this.datePipe.transform(item.date, 'EEE, d MMM y - h:mm a');
        for (let val of item.updates) {
          if (val.label == 'Complaint Received On') {
            val.value = this.datePipe.transform(val.value, 'd MMMM, y, EEEE, h:mm a');
          }
          if (val.label == 'Due date changed to' || val.label == 'Advertiser due date changed to') {
            val.value = this.datePipe.transform(val.value, 'd MMMM, y, EEEE');
          }
        }
      }
    }, err => {
      this.timelineLoading = false;
      this.timeLineExist = false;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  setFromFilterOutput(event) {
    if (event.cond === 'create') {
      if (this.draftComplaint) {
        event.obj['ID'] = this.COMP_ID;
      } else {
        event.obj['ID'] = this.blankComplaintID;
      }
      delete event.obj['files_attached'];
      this.blankComplaintID = 0;
      if (event.obj['COMPLAINT_SOURCE_ID'] == 3) {
        this.cs.createComplaint(event.obj).subscribe(res => {
          this.notify.showNotification(
            res.message,
            "top",
            (!!colorObj[res.status] ? colorObj[res.status] : "success"),
            res.status
          )
          this.newComplaint = false;
          this.userComp = false;
          this.advertiserComp = false;
          if (this.userData.roleId == 4 || this.userData.roleId == 8) {
            this.userComplainant = true;
            this.getUserComplaints(this.year);
          }
          else if (this.userData.roleId == 7) {
            this.userComplainant = true;
            this.getUserComplaints(this.year);
            this.getUserChatbotComplaints(this.year);
          }
          else if (this.userData.roleId == 5) {
            this.userAdvertiser = true;
            this.getAdvertiserComplaints(this.year);
            this.getUserComplaints(this.year);
          }
          location.reload();
        }, err => {
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        })
      }
      else if (event.obj['COMPLAINT_SOURCE_ID'] == 1) {
        this.thirdPartyService.createChatbotComplaint(event.obj).subscribe(res => {
          this.notify.showNotification(
            res.message,
            "top",
            (!!colorObj[res.status] ? colorObj[res.status] : "success"),
            res.status
          )
          this.newComplaint = false;
          this.userComp = false;
          this.advertiserComp = false;
          this.userComplainant = true;
          this.getUserComplaints(this.year);
          this.getUserChatbotComplaints(this.year);
          location.reload();
        }, err => {
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        })
      }
    } else if (event.cond === 'back') {
      this.newComplaint = false;
      this.hideCreateButton = false;
      this.userComp = false;
      this.advertiserComp = false;
      if (!this.draftComplaint) {
        this.cs.deleteBlankComplaint(this.blankComplaintID).subscribe(res => {
          this.blankComplaintID = 0;
        }, err => {
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        })
      }
    }
  }

  saveDraft() {
    this.draft = true;
    if (this.newComplaint && this.userComp) {
      this.generalPublicComp.savedraft();
    } else if (this.newComplaint && this.advertiserComp) {
      this.intraIndustryComp.savedraft();
    }
  }

  async onComplainantFile(event: any) {
    console.log("Outbound complaint document upload");
    if (!this.uploadService.validateFileExtension(event)) {
      this.notify.showNotification(
        "File format is not supported!",
        "top",
        "warning",
        0
      );
      return;
    }
    let sizeOfAllFiles = this.uploadService.getTotalFileSize(event.target.files);
    let totalFileSelected = event.target.files.length;
    if (totalFileSelected < 11 && sizeOfAllFiles <= 36700160) {
      this.isUploadProgress = true;
      for (let i = 0; i <= event.target.files.length - 1; i++) {
        var selectedFile = event.target.files[i];
        this.complainantFiles.push(selectedFile);
        let tempObjforGetsigned = {
          id: this.COMP_ID,
          section: 'complainant',
          filename: selectedFile['name'],
          type: selectedFile['type']
        }
        await this.uploadService.getSignedUrl(tempObjforGetsigned).then(async (res) => {
          if (res && res['data'] && res['data']['SIGNED_URL']) {
            let tempObjForUpload = {
              url: res['data']['SIGNED_URL'],
              file: selectedFile
            }
            await this.uploadSignedFile(tempObjForUpload);
            let saveBody = {
              ID: this.COMP_ID,
              KEY: res['data']['PATH'],
              SOURCE_NAME: selectedFile['name'],
              TYPE: selectedFile['type'],
              SIZE: selectedFile['size'],
              TAB: 'complainant'
            }
            await this.saveDocumentToDB(saveBody);
          }
        })
          .catch((err) => {
            this.isUploadProgress = false;
            this.handleError(err);
          });
      }
      this.isUploadProgress = false;
      this.notify.showNotification(
        'Documents uploaded successfully',
        "top",
        (!!colorObj[200] ? colorObj[200] : "success"),
        200
      );
    } else {
      this.notify.showNotification(
        "Max size 35MB and Max 10 files allowed",
        "top",
        "warning",
        0
      );
    }
  }

  async onFileChange(event: any) {
    console.log("Inbound complaint document upload");
    if (!this.uploadService.validateFileExtension(event)) {
      this.notify.showNotification(
        "File format is not supported!",
        "top",
        "warning",
        0
      );
      return;
    }
    let sizeOfAllFiles = this.uploadService.getTotalFileSize(event.target.files);
    let totalFileSelected = event.target.files.length;
    if (totalFileSelected < 11 && sizeOfAllFiles <= 36700160) {
      this.isUploadProgress = true;
      for (let i = 0; i <= event.target.files.length - 1; i++) {
        var selectedFile = event.target.files[i];
        this.advertiserFiles.push(selectedFile);
        let tempObjforGetsigned = {
          id: this.COMP_ID,
          section: 'company_member',
          filename: selectedFile['name'],
          type: selectedFile['type']
        }
        await this.uploadService.getSignedUrl(tempObjforGetsigned).then(async (res) => {
          if (res && res['data'] && res['data']['SIGNED_URL']) {
            let tempObjForUpload = {
              url: res['data']['SIGNED_URL'],
              file: selectedFile
            }
            await this.uploadSignedFile(tempObjForUpload);
            let saveBody = {
              ID: this.COMP_ID,
              KEY: res['data']['PATH'],
              SOURCE_NAME: selectedFile['name'],
              TYPE: selectedFile['type'],
              SIZE: selectedFile['size'],
              TAB: 'advertiser'
            }
            await this.saveDocumentToDB(saveBody);
          }
        })
          .catch((err) => {
            this.isUploadProgress = false;
            this.handleError(err);
          });
      }
      this.isUploadProgress = false;
      this.notify.showNotification(
        'Documents uploaded successfully',
        "top",
        (!!colorObj[200] ? colorObj[200] : "success"),
        200
      );
    } else {
      this.notify.showNotification(
        "Max size 35MB and Max 10 files allowed",
        "top",
        "warning",
        0
      );
    }
  }

  async saveDocumentToDB(tempObj) {
    await this.cs.saveFileSourceToDB(tempObj).then((data) => {
      return data;
    })
      .catch((err) => {
        this.isUploadProgress = false;
        this.handleError(err);
      });
    this.getAdvDocuments(this.COMP_ID);
  }

  getFileName(file) {
    if (file['name']) {
      return file['name'];
    } else if (file['ATTACHMENT_SOURCE']) {
      return file['ATTACHMENT_SOURCE'].substring(file['ATTACHMENT_SOURCE'].lastIndexOf("/") + 1);
    }

  }

  async uploadSignedFile(tempObj) {
    let progressInit = 0;
    await this.uploadService.uploadFilethroughSignedUrl(tempObj, progressInit => {
    }).catch((err) => {
      this.isUploadProgress = false;
      this.handleError(err);
    });
  }

  handleError(err: any) {
    this.notify.showNotification(
      err.error.message,
      "top",
      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
      err.error.status
    );
  }

  removeFile(ID, ATTACHMENT_SOURCE) {
    this.confirmationMsg.title = 'Are you sure you want to delete the file ?';
    const dialogRef = this.matDialog.open(ConfirmationPopupComponent, {
      data: { id: ID, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.cs.deleteComplaintDocuments(dialogResult.id, ATTACHMENT_SOURCE)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              this.getAdvDocuments(this.COMP_ID);
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  selectYear() {
    let currentDate = new Date();
    if (currentDate.getMonth() + 1 > 3) {
      this.currentYear = currentDate.getFullYear();
    } else {
      this.currentYear = currentDate.getFullYear() - 1;
    }
    let earliestYear = this.currentYear - 5;
    while (this.currentYear >= earliestYear) {
      let dateOption = {
        "viewValue": this.currentYear + ' ' + '-' + ' ' + (this.currentYear + 1),
        "value": this.currentYear
      };
      this.years.push(dateOption);
      this.currentYear -= 1;
    }
    this.initialValue = this.years[0].viewValue;
    this.selectByYear(this.years[0]);
  }

  selectByYear(event) {
    this.year = event.value;
    if (this.userData.roleId == 4 || this.userData.roleId == 8) {
      this.getUserComplaints(this.year);
      this.getUserComplaintStats(this.year);
    }
    else if (this.userData.roleId == 7) {
      this.getUserChatbotComplaints(this.year);
      this.getUserComplaints(this.year);
      this.getUserComplaintStats(this.year);
    }
    else if (this.userData.roleId == 5) {
      this.getAdvertiserComplaints(this.year);
      this.getUserComplaints(this.year);
      this.getCompanyComplaintStats(this.year);
    }
  }

  viewProfile(flag) {
    this.authService.profileFlag = flag;
    this.router.navigateByUrl('auth/my-profile');
  }

  download(doc, source) {
    this.url = this.bucketUrl + source;
    this.cs.downloadFile(this.url).subscribe(data => {
      importedSaveAs(data, doc)
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    });
  }

  preview(source) {
    this.imgURL = this.bucketUrl + source;
    window.open(this.imgURL, 'window 1', '');
  }

  logout() {
    this.confirmationMsg.title = 'Are you sure you want to logout ?';
    const dialogRef = this.matDialog.open(ConfirmationPopupComponent, {
      data: { title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.authService.logout().subscribe(
          (res) => {
            window.localStorage.clear();
            this.router.navigate(['auth/login']);
          },
          (err) => {
            window.localStorage.clear();
            this.router.navigate(['auth/login']);
          }
        );
      }
    });
  }

  getSimilarComplaints(parentcase_id) {
    this.case_id = "";
    this.cs.getSimilarComplaints(parentcase_id, this.case_id).subscribe((complaints: any) => {
      this.similarCompLoading = false;
      this.similarComplaints = complaints.data;
      for (let item of this.similarComplaints) {
        item.COMPLAINT_DESCRIPTION = (item.COMPLAINT_DESCRIPTION).replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').replace(/amp;/g, ' ');
      }
      if (!!this.similarComplaints && this.similarComplaints.length > 0) {
        this.similarComplaintId = this.similarComplaints[0].CASE_ID;
        this.selectDetails(this.similarComplaintId);
      }
    }, err => {

    })
  }

  selectDetails(case_id) {
    this.parent_id = "";
    this.noOfDocs = 0;
    this.cs.getSimilarComplaints(this.parent_id, case_id).subscribe(res => {
      this.compDetails = res.data;
      if (this.compDetails.length != 0) {
        this.similar_complaint_id = this.compDetails.ID;
        this.detail_company = this.compDetails.BRAND_NAME;
        this.classification_name = this.compDetails.CLASSIFICATION_NAME;
        this.complaint_source_id = this.compDetails.COMPLAINT_SOURCE_ID;
        this.seen_date = this.compDetails.DATE;
        this.transcription = this.compDetails.TRANSCRIPTION;
        this.duration = this.compDetails.DURATION;
        this.detail_advert = this.compDetails.ADVERTISEMENT_DESCRIPTION;
        this.adDocs = this.compDetails.ADVERTISEMENT_MEDIUM_DOCUMENT;
        this.adMedium = res.data.ADVERTISEMENT_MEDIUM;
        this.comp_date = this.compDetails.REGISTERED_DATE;
        this.detail_date = this.compDetails.CREATED_DATE;
        for (let i = 0; i < this.compDetails.ADVERTISEMENT_MEDIUM_DOCUMENT.length; i++) {
          if (this.compDetails.ADVERTISEMENT_MEDIUM_DOCUMENT[i].ATTACHMENT_SOURCE !== '') {
            this.noOfDocs += 1;
          }
        }
        if (this.detail_advert.length > 10) {
          this.longText1 = ' ...'
        }
        this.detail_complaint = this.compDetails.COMPLAINT_DESCRIPTION;
        if (this.compDetails.ADVERTISEMENT_MEDIUM.length != 0) {
          this.detail_adsource = this.compDetails.ADVERTISEMENT_MEDIUM;
          this.detail_platform = this.compDetails.ADVERTISEMENT_MEDIUM[0].PLATFORM_ID;
          this.detail_channel = this.compDetails.ADVERTISEMENT_MEDIUM[0].SOURCE_NAME;
          this.detail_addate = this.compDetails.ADVERTISEMENT_MEDIUM[0].DATE;
          this.similarComplaintClaims = res.data.CLAIMS;
          this.similarComplaintCodeViolated = res.data.CODEVIOLATED;
          this.similarComplaintGuidelines = res.data.GUIDELINES;
          this.similar_detail_link = this.compDetails.ADVERTISEMENT_MEDIUM[0].SOURCE_URL;
          if (this.similar_detail_link) {
            if (this.similar_detail_link.length > 28) {
              this.longText4 = '..';
            } else {
              this.longText4 = ' ';
            }
          }
          this.docUrl = this.bucketUrl + this.similar_detail_link;
          this.detail_place = this.compDetails.ADVERTISEMENT_MEDIUM[0].SOURCE_PLACE;
        }
        // for (let i = 0; i <= this.complaintCodeViolated.length - 1; i++) {
        //   if (this.complaintCodeViolated[i].CLAUSES_ID != null) {
        //     this.complaintCodeViolated[i].CLAUSES_ID = ((this.complaintCodeViolated[i].CLAUSES_ID).replace(/"/g, '')).slice(1, -1);
        //   }
        // }
        // for (let i = 0; i <= this.guidelines.length - 1; i++) {
        //   if (this.guidelines[i].G_CLAUSES_ID != null) {
        //     this.guidelines[i].G_CLAUSES_ID = ((this.guidelines[i].G_CLAUSES_ID).replace(/"/g, '')).slice(1, -1);
        //   }
        // }
        if (this.compDetails.COMPLAINT_SOURCE_ID == 7) {
          this.media_outlet = this.compDetails.MEDIA_OUTLET;
          this.media = this.compDetails.MEDIA;
          this.edition = this.compDetails.EDITION;
          this.super_category = this.compDetails.PRODUCT_CATEGORY;
          this.ad_language = this.compDetails.AD_LANGUAGE;
          this.suppliment = this.compDetails.SUPPLIMENT;
          this.creative_id = this.compDetails.CREATIVE_ID;
          this.translation_hyper = this.compDetails.TRANSLATION_HYPERLINK;
          if (this.translation_hyper) {
            if (this.translation_hyper.length > 28) {
              this.longText5 = '..';
            } else {
              this.longText5 = '..';
            }
          }
        }
        if (this.compDetails.COMPLAINT_SOURCE_ID == 8) {
          this.engagements = this.compDetails.ENGAGEMENTS;
          this.publication_url = this.compDetails.PUBLICATION_URL;
          this.profile_url = this.compDetails.PROFILE_URL;
        }
        if (this.compDetails.COMPANY_INFO.length != 0) {
          this.company_details = this.compDetails.COMPANY_INFO;
        }
        // this.getDocuments(this.similar_complaint_id);
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  // getDocuments(id) {
  //   this.cs.getDocumentsByComplaint(id).subscribe(res => {
  //     this.docFileList = res.data;
  //     this.complainantFiles = this.docFileList['COMPLAINANT'];
  //     this.advertiserFiles = this.docFileList['ADVERTISER'];
  //     this.internalFiles = this.docFileList['INTERNAL'];
  //   }, err => {
  //     this.notify.showNotification(
  //       err.error.message,
  //       "top",
  //       (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
  //       err.error.status
  //     )
  //   });
  // }

}