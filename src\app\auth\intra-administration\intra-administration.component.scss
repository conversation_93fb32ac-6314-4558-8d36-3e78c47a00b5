:host::ng-deep.mat-paginator-page-size-label {
    display: none;
}

:host ::ng-deep .mat-paginator-range-label {
    display: none;
}

:host::ng-deep.mat-paginator-page-size-value {
    display: none;
}

:host::ng-deep .mat-paginator-navigation-previous {
    color: #4DA1FF;
}

:host::ng-deep .mat-paginator-navigation-next {
    color: #4DA1FF;
}

:host::ng-deep .mat-paginator-range-actions {
    margin-right: 24px;
}

.common-toolbar {
    border-bottom: 1px solid #D8DCDE;
    border-top: 0;
    border-left: none;
    border-right: none;
}

.heading-container {
    padding-top: 16px;
    padding-left: 18px;
    align-items: center;
    vertical-align: middle;
    width: 60%;
}

.options-container {
    width: 40%;
}

.adm-head {
    color: #2f3941;
    font-size: 16px;
    font-weight: 500;
    line-height: 21.28px;
}

.admin-table::-webkit-scrollbar {
    display: none;
}

.admin-table {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.admin-table {
    overflow-y: scroll;
    overflow-x: hidden;
    margin-left: 4%;
    width: 98.5%;
    height: min-content;
    position: relative;
    border-radius: 20px;
    border: 1px solid #D8DCDE;
    border-top: 0px;
}

.add-btn {
    color: white;
    background-color: #0088CB;
    border-radius: 12px;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 38px;
    z-index: 10;
}

.toolbar2 {
    background-color: rgb(245, 245, 245);
    padding-left: 6%;
}

.header-search input[type=text] {
    box-sizing: border-box;
    margin-top: 3%;
    position: relative;
    padding-left: 30px;
    color: #92A2B1;
    height: 38px;
    border: 1px solid #CFD7DF !important;
    border-radius: 4px !important;
    border: 1px solid #CFD7DF !important;
    border-radius: 12px !important;
    background: url(../../../assets/images/search.png) no-repeat;
    background-position: 5%;
    background-color: white;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    z-index: 10;
}

.header-search ::placeholder {
    color: rgba(0, 0, 0, 0.4);
}

.label {
    position: relative;
    bottom: 38px;
    color: rgb(155, 155, 155);
    left: 45%;
}

:host ::ng-deep .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar {
    background-color: rgb(144, 212, 144);
}

:host ::ng-deep .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb {
    background-color: rgb(28, 187, 28);
}

.table-scroll::-webkit-scrollbar {
    display: none;
}

.table-scroll {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.table-scroll {
    overflow-y: scroll;
    overflow-x: hidden;
    height: 63vh;
    background-color: #F5F5F7;
}

.text-message {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.industry-heading {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    color: rgba(0, 0, 0, .54);
    margin-left: -8px;
}

.mat-row:nth-child(2n+2) {
    background-color: #F5F5F7;
}

mat-paginator {
    width: 94%;
    margin-top: 17px;
    margin-left: 5%;
    border-radius: 20px;
    background: #FFFFFF;
    box-sizing: border-box;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
}

.admin-table {
    overflow-y: scroll;
    overflow-x: hidden;
    margin-left: 5%;
    width: 94.3%;
    height: min-content;
    position: relative;
    border-radius: 20px;
    border: 1px solid #D8DCDE;
    border-top: 0px;
}