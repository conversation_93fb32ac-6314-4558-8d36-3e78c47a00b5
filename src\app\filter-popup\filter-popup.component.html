

  <!-- <mat-dialog-content class="mat-typography">  -->
    <div class="filter-container" fxLayout="column" fxlayoutGap="5px">
        <div class="head-container">
          <p>Apply filter based on</p>
        </div>
        <div class="input-container" fxLayout="column" fxlayoutGap="3px">
            <div class="text-container" >
              <span id="label-icon" class="material-icons-outlined">sell</span>
              <mat-label>Classification tag</mat-label>
            </div>
            <div class="control-container">
              <mat-form-field appearance="outline">
                  <input matInput type="text" >
                  <button matSuffix mat-icon-button>
                    <mat-icon>search</mat-icon>
                  </button>
              </mat-form-field>
            </div>
        </div>
        <div class="input-container" fxLayout="column" fxlayoutGap="3px">
          <div class="text-container">
            <span id="label-icon" class="material-icons-outlined">bar_chart</span>
            <mat-label>Brand name</mat-label>
          </div>
          <div class="control-container">
            <mat-form-field appearance="outline">
                <input matInput type="text" >
                <button  matSuffix mat-icon-button>
                  <mat-icon>search</mat-icon>
                </button>
            </mat-form-field>
          </div>
      </div>
      <div class="input-container" fxLayout="column" fxlayoutGap="3px">
          <div class="text-container" >
            <span id="label-icon" class="material-icons-outlined">flag</span> 
            <mat-label>Complaint status</mat-label>
          </div>
          <div class="control-container">
            <mat-form-field appearance="outline" style="min-width: 230px !important;">
              <mat-select panelClass="example-panel" style="min-width: 230px !important;">
                  <!-- <mat-select [formControl]="status"> -->
                  <!-- <mat-select-trigger>
                    {{toppings.value ? toppings.value[0] : ''}}
                    <span *ngIf="toppings.value?.length > 1" class="example-additional-selection">
                      (+{{toppings.value.length - 1}} {{toppings.value?.length === 2 ? 'other' : 'others'}})
                    </span>
                  </mat-select-trigger> -->
                  <!-- <mat-option *ngFor="let topping of toppingList" [value]="topping">Select</mat-option> -->
                  <mat-option>Select</mat-option>
                </mat-select>
            </mat-form-field>
          </div>
      </div>
      <div class="btn-container" fxLayoutAlign="end end">
          <button mat-button mat-dialog-close>Apply</button>
      </div>
    </div> <!-- filter-container -->
  <!-- </mat-dialog-content> -->