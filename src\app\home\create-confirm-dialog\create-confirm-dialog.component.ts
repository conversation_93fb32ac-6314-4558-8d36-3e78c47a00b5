import { Component, Inject, OnInit, ViewChildren } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-create-confirm-dialog',
  templateUrl: './create-confirm-dialog.component.html',
  styleUrls: ['./create-confirm-dialog.component.scss']
})
export class CreateConfirmDialogComponent implements OnInit {
  displayMsg: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<CreateConfirmDialogComponent>
  ) { }

  ngOnInit(): void { }

  radioChange(event) {
    if (event.value == '1') {
      this.displayMsg = true;
    } else {
      this.displayMsg = false;
    }
  }

  displayMessage() {
    this.displayMsg = !this.displayMsg
  }

  confirm() {
    this.dialogRef.close({ 'state': true, 'result': this.displayMsg });
  }

  cancel() {
    this.dialogRef.close({ 'state': false, 'result': this.displayMsg });
  }

}