import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { AuthGuardService } from "../services/auth-guard.service";
import { AdminDashboardComponent } from "./admin-dashboard/admin-dashboard.component";
import { HomeComponent } from "./home.component";
import { UserDashboardComponent } from "./user-dashboard/user-dashboard.component";

const routes: Routes = [
  {
    path: '',
    component: HomeComponent,
    children: [
      {
        path: 'admin-dashboard',
        canActivate:[AuthGuardService],
        data: {
          role: [1,2,3,6]
        },
        component: AdminDashboardComponent
      },
      {
        path: 'user-dashboard',
        canActivate:[AuthGuardService],
        data: {
          role: [4,5,7,8]
        },
        component: UserDashboardComponent
      },
      
    ],
  }
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class HomeRoutingModule { }

