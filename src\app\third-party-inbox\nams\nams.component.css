.common-toolbar{
    border-bottom: 1px solid #D8DCDE;
    border-top: 0;
    border-left: none;
    border-right: none;
}
.heading-container{
    width: 60%;
}
.options-container{
    width:40%;
}


.inbox-page-container{
    background-color: #f1efef;
    height: 100vh;
}
.inbox-body::-webkit-scrollbar {
    display: none;
}
/* Hide scrollbar for IE, Edge and Firefox */
.inbox-body {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  .inbox-body{
      height: 90vh;
      overflow-y: hidden;
      overflow-x: hidden;
  }

  .tab-div{
      width: 26%;
      height: 89vh;
      background-color: #f1efef;
      margin-left: 60px;
  }
  .tab-group{
      height: 91vh;
      overflow-y: hidden !important;
      padding: 0px 5px;
  }

  :host ::ng-deep .mat-ink-bar {    /*  REMOVE MAT-TAB INK BAR */
      display: none !important;
  }
  :host ::ng-deep .mat-tab-label{        /* ADJUST MAT TAB LABEL WIDTH */
      min-width: 0!important;
      padding: 15px!important;
      color:rgb(92, 92, 92);
      background-color:#f1efef;
      opacity: 0.4 !important;
  }
  :host ::ng-deep .mat-tab-label-active{
      opacity: 1 !important;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
      background-color: #FFFFFF !important;
      box-shadow: 4px -4px 8px rgba(0, 0, 0, 0.1);
  }
  :host ::ng-deep .mat-tab-label-container{
    padding-top: 7px;
  }
  :host mat-tab-label{
      max-width: 20px;
  }
  .nams-label,
  .chatbot-label{
      color: #0088CB;
      font-style: normal;
      font-weight: normal;
      font-size: 16px;
      line-height: 19px;
  }

  .select-header {
    background-color: rgb(251, 250, 252);
    border-bottom: 1px solid #CFD7DF;
    height: 53px;
    padding: 0px 4.5%;
}
  :host ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: #E3FCEF !important;
  }
  :host ::ng-deep .mat-checkbox .mat-checkbox-checkmark-path {
    stroke:  #006644 !important;
  }
  :host ::ng-deep .mat-checkbox-ripple .mat-ripple-element {
    background-color: #006644 !important;
  }
  :host ::ng-deep .mat-checkbox-frame {
    border: 1.5px solid #D8DCDE;
  }

  .header-btn{
    color: #5A6F84;
    background: #FFFFFF;
    width: 58px;
    height: 26px;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 12px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
    padding: 6px 16px;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  mat-nav-list::-webkit-scrollbar {
      display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  mat-nav-list {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  :host mat-nav-list {
      background-color: #FFFFFF;
      overflow-y: scroll;
      overflow-x: hidden;
      /* border-top-left-radius: 5px;
      border-top-right-radius: 5px; */
      padding-top: 0px;
  }
  .nams-list { height: 72vh; }
  .chatbot-list { height: 80.5vh; }

  .nams-list-container,
  .chatbot-list-container{
      /* height: max-content !important; */
      /* height: 10% !important; */
      /* height: 20% !important; */
      height: 130px !important;
      padding: 1%;
      /* display: inline-block !important; */
  }
  /* .nams-list-container >div{
    display: inline-block !important;
    } */
  .nams-item-container
  .chatbot-item-container{
    height: 130px !important;
    width: 308px !important;
      /* height: max-content !important; */
      /* height: 20% !important; */
      /* display: inline-block!important; */
  }
  /* .nams-item-container >div{
    display: inline-block!important;
    } */

  .chkb-container{
    padding-top: 4.5%;
  }
    .chkb-list{
        vertical-align: middle;
    }

    .nams-head-container,
    .chatbot-head-container{
        /* font-size: 16px;
        line-height: 21px;
         color: #000000;
        height: 20px; */
         /* padding: 5% 0px 10% 0px; */
        /* padding: 1% 0px 4% 0px ; */
        padding: 5.5% 0px;

        color: #000000;
        font-style: normal;
        font-weight: 600;
        font-size: 14px;
        line-height: 19px;
        display: flex;
        align-items: center;
    }

    @supports (height: max-content !important){
        /* .chatbot-list-container, */
        .nams-list-container{
            height: max-content !important;
            padding: 1%;
        }

        .nams-item-container{
            height: max-content !important;
            width: 308px !important;
        }
        .chatbot-item-container{
            height: max-content !important;
            width: 308px !important;
        }
        .nams-head-container,
        .chatbot-head-container{
            /* font-size: 16px;
            line-height: 21px;
            color: #000000;
            height: 20px;*/
            /* padding: 5% 0px 10% 0px;  */
            padding: 5.5% 0px;

            color: #000000;
            font-style: normal;
            font-weight: 600;
            font-size: 14px;
            line-height: 19px;

        }
    }

.medium-container{
    background: #0088CB;
    width: max-content;
    height: 16px;
    padding: 1px 7px;
    border-radius: 20px;
    display: flex;
    align-items: center;
}
  .medium-list{
      color: #FFFFFF;
      font-style: normal;
      font-weight: 600;
      font-size: 10px;
      line-height: 13px;
      text-transform: uppercase;
  }

.brand-container{
    color: #000000;
    height: 19px;
    width: calc(100%) !important;
    word-wrap: break-word !important;
    word-break: normal;
    overflow: hidden;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
    vertical-align: middle;
}

.brand-text{
    color: #000000;
    width: calc(100%);
    font-size: 14px;
    display: inline-block;
    display: -webkit-inline-box;
    text-overflow: ellipsis !important;
    -webkit-line-clamp: 1;
    white-space: normal;
    -webkit-box-orient: vertical;
    vertical-align: middle;
    position: relative;
    overflow: hidden;
    padding-bottom: 5.5px !important;
    padding-left: 7px;
  }

.nams-msg-container{
    width: calc(100%) !important;
    word-wrap: break-word !important;
    word-break: normal;
    overflow: hidden;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 19px;
    color: #2F3941;
}
  .nams-msg-text{
      font-size: 14px;
      color: #707070;
      text-overflow: ellipsis !important;
      width: calc(100%);
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      line-height: 19px;

  }

.nams-date-container{
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 19px;
    color: #000000;
    padding: 2px 0px 3px 0px;
    position: relative;
    right:207px;
}

  .complaint-container{
      width: 68% !important;
      height: 88vh;
      padding-top: 7px;
  }
  .comp-matcard{
      background-color: #FFFFFF;
      height: 88vh;
      box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid #D8DCDE;
  }
  .mat-card-scroll::-webkit-scrollbar {
    display: none;
  }
  .mat-card-scroll{
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
    height: 75vh;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .comp-head{
      font-size: 12px;
      line-height: 16px;
      color: #000000
  }
  .comp-head{
      margin-bottom: 5px;
      font-weight: 550;
      font-size: 12px;
      color: #000000;
  }
  .comp-divider-container{
      position:absolute;
      top: 23px;
      width: 82%;
      margin-left: 125px;
  }
  .comp-divider{
      position: relative;
  }
  .details-table{
      width: 100%;
  }
  .detail-left-container{
      width: 60%;
  }
  .detail-right-container{
    width: 40%;
  }
  .detail-attribute{
      color: rgba(0, 0, 0, 0.4);
      font-size: 14px;
      line-height: 19px;
      font-weight: bolder;
  }
  .comp-msg-container{
      width: 90%;
      word-wrap: break-word;
      word-break: normal;
      overflow: hidden;
      font-size: 14px;
      line-height: 19px;
  }
  .comp-msg-container p {
    text-overflow: ellipsis !important;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  .language-text{
    height: 38px;
  }
  .detail-value{
      color: #2F3941;
      line-height: 19px;
      font-size: 14px;
      color: #000000
  }
  .media-container{
    width: 74%;
  }
  .media-anchor{
    color: #0088CB;
    word-break: break-all;
    cursor: pointer;
  }
  .media-attribute{
    width: 18%;
    word-wrap: normal;
  }
  .comp-head-container,
  .classfy-head{
      position: relative;
      margin-top: 10px;
      display: grid;
      grid-gap: 10px;
  }
  .divider-container{
      position:absolute;
      top: 8px;
      width: 100%;
      margin-left: 170px;
  }
  .classfy-head-divider{
      position: relative;
  }
  .classification-container{
      display: inline;
  }
  .comp-chips{
      color: #4DA1FF;
      background-color: #E9F5FF;
      width: auto;
      font-size: 12px;
      line-height: 16px;
      font-style: normal;
      font-weight: normal;
      border: 1px solid #4DA1FF;
      border-radius: 5px;
  }
  .classfy-container{
      max-height: fit-content;
  }
  .related-name-container{
      text-indent: 0px;
      height: 25px;
      font-size: 14px;
      padding-bottom: 35px;
    }
  .related-name{
      color: #2AB2FF;
      font-weight: 500;
      font-size: 14px;
      padding-left: 5px;
  }
  .related-detail-container{
    text-indent: 20px;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 19px;
}
    .related-attribute{
        color: #CFD7DF;
    }
    .related-value{
        color: #000000;
    }
  .classfy-icon{
      color: rgb(207, 205, 205);
      font-size:medium;
      margin-right: 10px;
      vertical-align:text-top;
  }
  .chips-btn-container{
      margin-left: 1%;
      position:relative;
      padding-bottom: 30px;
  }
  .relcomp-container{
      margin-left: 10px;
  }
  .person-chips{
      font-size: 10px;
      line-height: 13px;
      display:flex;
      align-items: center;
      border-radius: 50px;
  }
  #person-chip1{
      color: #EB8F8F;
      background-color: #EAD1D1;
      z-index: 1;
  }
  #person-chip2{
      color: #E1CE68;
      background-color: #EAE6D1;
      z-index: 2;
  }
  #person-chip3{
      color: #8B97A3;
      background-color: #CFD7DF;
      z-index: 3;
  }
  .add-btn{
      color: rgb(163, 163, 163);
      font-weight: normal;
      padding-left: 5px;
      padding-top: 4px;
      line-height: 15px;
      border: 1px dashed rgb(163, 163, 163) ;
      border-radius: 20px;
      height: 32px;
      margin-bottom: 25px;
  }
  .footer-fixed{
      height: 50px;
  }
  .footer-content-container{
      width: 100% !important;
  }
  .footer-btn{
      color: #5A6F84;
      border-radius: 12px;
  }
  .footer-right-btn{
      display: flex;
      justify-content: flex-end;
  }
  .delete-icon{
      font-size: large;
      vertical-align: baseline !important;
      margin-right: 10px;
  }

  .complaint-list{
    overflow: scroll;
    height: 100%;
  }