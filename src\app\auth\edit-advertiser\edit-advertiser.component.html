<div fxLayout="row">
    <mat-toolbar class="toolbar">
        <div fxFlex="35%" fxLayoutAlign="start">
            <h2 mat-dialog-title fxLayoutAlign="start" style="font-size: 16px;">
                <img src="../assets/images/edit-icon.svg">&nbsp;
                Edit user
            </h2>
        </div>
        <div fxFlex="60%"></div>
        <div fxFlex="5%" fxLayoutAlign="end">
            <mat-dialog-actions mat-dialog-close style="position: relative; top: -10px; left: 15px;">
                <button mat-icon-button class="search-btn" mat-dialog-close>
                    <mat-icon style="margin-bottom: 4px;">close</mat-icon>
                </button>
            </mat-dialog-actions>
        </div>
    </mat-toolbar>
</div>

<mat-divider></mat-divider>

<div class="edit-form">
    <form [formGroup]="addform">
        <div class="contents">
            <div class="names">
                <div>Title <span style="color: #ff0000;">*</span></div>
                <div fxFlex="15%"></div>
                <div> First name <span style="color: #ff0000;">*</span></div>
                <div fxFlex="26%"></div>
                <div>Last name <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" style="width: 110px;">
                    <mat-select formControlName="salutation">
                        <mat-option *ngFor="let title of titles" [value]="title.ID" style="color:black;"> {{title.SALUTATION_NAME}}</mat-option>
                    </mat-select>
                    <mat-error *ngIf="addform.get('salutation').touched && addform.controls['salutation'].errors?.required">
                        Salutation is required
                    </mat-error>   
                </mat-form-field>
                <mat-form-field appearance="outline" class="input-field">
                    <input matInput formControlName="first_name" style="font-style: normal;font-weight: normal;" autocomplete="off">
                    <mat-error class="error-msg" *ngIf="addform.controls['first_name'].errors?.required">
                        First name is required
                        </mat-error>
                        <mat-error class="error-msg" *ngIf="addform.controls['first_name'].errors?.pattern">
                        Only text is allowed
                        </mat-error>
                        <mat-error class="error-msg" style="width: max-content;" *ngIf="addform.controls['first_name'].errors?.minlength">
                        First name should be of min length 3 & max length 25
                        </mat-error>
                        <mat-error class="error-msg" style="width: max-content;" *ngIf="addform.controls['first_name'].errors?.maxlength">
                        First name should be of min length 3 & max length 25
                        </mat-error>
                </mat-form-field>
                <mat-form-field appearance="outline" class="input-field">
                    <input matInput formControlName="last_name" style="font-style: normal;font-weight: normal;" autocomplete="off">
                    <mat-error class="error-msg" *ngIf="addform.controls['last_name'].errors?.required">
                        Last Name is required
                        </mat-error>
                        <mat-error class="error-msg" *ngIf="addform.controls['last_name'].errors?.pattern">
                        Only text is allowed
                        </mat-error>
                        <mat-error class="error-msg" *ngIf="addform.controls['last_name'].errors?.maxlength">
                        Last Name should be of max length 25
                        </mat-error>
                </mat-form-field>
            </div>
            <div class="names1">
                <div>Phone number <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" class="input-field_phno">
                    <input matInput formControlName="contact" style="font-style: normal;font-weight: normal;" autocomplete="off">
                </mat-form-field>
            </div>
            <div class="names1">
                <div>Email address <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" class="input-field_phno">
                    <input matInput formControlName="email" style="font-style: normal;font-weight: normal;" autocomplete="off">
                </mat-form-field>
            </div>
            <div class="names1">
                <div>Company <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" class="input-field_phno">
                    <input matInput formControlName="company" style="font-style: normal;font-weight: normal;" autocomplete="off">
                </mat-form-field>
            </div>
            <div class="names_ad">
                <div>Address <span style="color: #ff0000;">*</span></div>
                <div>Postal code <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="control-container">
                <mat-form-field appearance="outline" style="width:48%">
                    <input matInput formControlName="address" style="font-style: normal;font-weight: normal;" autocomplete="off">
                    <mat-error *ngIf="addform.controls['address'].errors?.required">
                        Address is required
                    </mat-error> 
                </mat-form-field>
                <mat-form-field appearance="outline" style="width:48%;margin-left: 20px;">
                    <input matInput formControlName="pin" style="font-style: normal;font-weight: normal;" autocomplete="off">
                    <mat-error class="error-msg" *ngIf="addform.controls['pin'].errors?.required">
                        Postal code is required
                    </mat-error> 
                    <mat-error class="error-msg" *ngIf="addform.controls['pin'].errors?.pattern">
                      Enter valid postal code with length 6
                  </mat-error> 
                </mat-form-field>
            </div>
        </div>
        <div class="lastdivdr">
            <mat-divider></mat-divider>
        </div>
        <div fxLayout="row">
            <mat-toolbar class="toolbar2">
                <div fxFlex="35%" fxLayoutAlign="start">
                    <div class="search-div" *ngIf="userRoleId == 1 || userRoleId == 2 || (userRoleId == 5 && industryMemberRole == 1)">
                        <button mat-button class="remove-btn" (click)='removeUser()'> 
                            <span class="bolder">Remove user</span> 
                        </button>
                    </div>
                </div>
                <div fxFlex="35%"></div>
                <div fxFlex="67%"></div>
                <div class="toolbar-btns" fxFlex="16%" fxLayoutAlign="end">
                    <div class="remove-div">
                        <button mat-flat-button class="cancel-btn" mat-dialog-close> 
                            <span class="bolder">Cancel</span> 
                        </button>
                    </div>
                    <div class="update-div">
                        <button mat-flat-button class="update-btn" (click)="updateAdvertiser(addform.value)" [disabled]="addform.invalid"> 
                            <span class="bolder">Update</span> 
                        </button>
                    </div>
                </div>
            </mat-toolbar>
        </div>
    </form>
</div>

