import { Component, OnInit, Input } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { FormBuilder, FormGroup, FormControl, Validators } from '@angular/forms';
import { ComplaintsService } from '../services/complaints.service';
import { Complaints } from '../model/complaints';
import { Observable } from 'rxjs';
import { startWith, map } from 'rxjs/operators';
import { MatDialog, MatDialogConfig } from "@angular/material/dialog";

@Component({
  selector: 'app-heading',
  templateUrl: './heading.component.html',
  styleUrls: ['./heading.component.css']
})
export class HeadingComponent implements OnInit {

  @Input() pagename: String;
  isSearch: boolean = false;
  currentUrl: any;
  complaints: Complaints[];
  pageNumber = 1;
  search_control = new FormControl();
  search_input = 'Angular';
  streets: string[] = ['Tanishq Misleading the Youth', 'Coco-cola Promoting communal Disharmony', 'Coco-cola Misleading the new generation', 'Coco-cola Promoting communal Disharmony'];
  filteredStreets: Observable<string[]>;

  constructor(private router: Router,
    private cs: ComplaintsService,
    private dialog: MatDialog,
    private formBuilder: FormBuilder) { }

  async ngOnInit(): Promise<void> {
    this.filteredStreets = this.search_control.valueChanges.pipe(
      startWith(''),
      map(value => this._filter(value))
    );
    await this.getComplaints();
    this.changeOfRoutes();
  }

  changeOfRoutes() {
    this.router.events.subscribe((ev) => {
      if (ev instanceof NavigationEnd) {
        this.currentUrl = ev.url.split('/')
        let currentPath = this.currentUrl[this.currentUrl.length - 1]
        if (currentPath === 'dasboard') {
          this.pagename = 'Dashboard'
        } else if (currentPath === 'user-administration') {
          this.pagename = 'Administration';
        } else if (currentPath === 'inbox') {
          this.pagename = 'Inbox';
        }
      }
    })
  }

  getComplaints() {

  }

  search() {
    this.isSearch = !this.isSearch;
    this.search_input = '';
  }

  private _filter(value: string): string[] {
    const filterValue = this._normalizeValue(value);
    return this.streets.filter(street => this._normalizeValue(street).includes(filterValue));
  }

  private _normalizeValue(value: string): string {
    return value.toLowerCase().replace(/\s/g, '');
  }

}