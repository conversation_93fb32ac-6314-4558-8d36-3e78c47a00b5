import { Component, HostListener, Inject, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormGroupDirective, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ComplaintsService } from '../services/complaints.service';
import { NotificationService } from '../services/notification.service';
import { colorObj } from '../shared/color-object';

@Component({
  selector: 'app-mark-invalid',
  templateUrl: './mark-invalid.component.html',
  styleUrls: ['./mark-invalid.component.scss']
})
export class MarkInvalidComponent implements OnInit {

  @ViewChild(FormGroupDirective) formGroupDirective: FormGroupDirective;
  markIvalidForm: FormGroup;
  id: any;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private dialogRef: MatDialogRef<MarkInvalidComponent>,
    public fb: FormBuilder,
    private cs: ComplaintsService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private notify: NotificationService) {
    this.markIvalidForm = fb.group({
      reason: ['', Validators.required],
      solution: ['', Validators.required]
    })
  }

  ngOnInit(): void {
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  onSubmit(form) {
    this.id = this.data;
    this.cs.markInvalid(this.id, form).subscribe(res => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      this.dialogRef.close();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    })
  }

  cancel() {
    this.formGroupDirective.resetForm();
  }

}