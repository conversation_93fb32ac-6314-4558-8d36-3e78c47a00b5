<div class="common-toolbar-mobile" style="width: 100%; height: auto;" *ngIf="isMobile">
  <app-mobile-header></app-mobile-header>
</div>
<span class="dashboard-admin-heading" *ngIf="isMobile">
  <p style="text-align: center;margin-top: 20px;margin-bottom: 3px;"> Welcome {{userName}}!</p>
</span>
<div class="dasboard-subheading" *ngIf="isMobile">
  <p style="text-align: center;">Dear ASCI Officers, please move to a larger device for seamlessly accessing all
      functionalities of the system</p>
</div>
<div class="dashboard-container" *ngIf="isMobile">
</div>

<app-icons *ngIf="!isMobile"></app-icons>

<div class="common-toolbar" fxLayout="row" style="width: 100%; height: 50px;" *ngIf="!isMobile">
  <div class="heading-container">
    <app-heading [pagename]="pagename"></app-heading>
  </div>
  <div class="options-container">
    <app-toolbar-options></app-toolbar-options>
  </div>
</div>

<mat-toolbar class="toolbar2" fxLayout="row" *ngIf="!isMobile">
  <div fxLayout="row" class="toolbar2-sub-container" fxLayoutGap="1%">
    <div fxFlex="1%"></div>
    <div fxFlex="10%">
      <span matSubheader class="filter" fxLayoutAlign="start">Filter by :</span>
    </div>
    <div>
      <button mat-flat-button class="complaint-dropdown">
        <div fxLayout="row">
          <div
            style="border-bottom-left-radius: 4px;border-top-left-radius: 4px;border: 1px solid #D8DCDE;height: 35px;width: 140px; margin-left: -16.4px;background-color: #D8DCDE; margin-top: -1px;">
            <span style="position: relative;top: -3px; padding: 0px 5px 0px 5px;
             font-style: normal; font-weight: normal; font-size: 12px; color: #010101;">
              <img src="../assets/images/complaints.svg">&nbsp; Complaint</span>
          </div>
          <mat-form-field [floatLabel]="'never'" class="compl_field" style="height: 30px; width: 500px;">
            <input matInput name="search" [formControl]="COMPLAINT_DESCRIPTION"
              placeholder="Search for the complaint" type="text" class="comp_name" [matAutocomplete]="auto">
            <mat-autocomplete #auto="matAutocomplete" class="comp-values"
              (optionSelected)="complaintSelected($event)">
              <mat-option *ngFor="let complaint of complaints;" [value]=complaint.CASE_ID
                class="autocomplete-option" matTooltip="{{complaint.CASE_ID}} - {{complaint.COMPLAINT_DESCRIPTION}}">
                {{complaint.CASE_ID}} - {{complaint.COMPLAINT_DESCRIPTION}}
              </mat-option>
            </mat-autocomplete>
          </mat-form-field>
        </div>
      </button>
    </div>
    <div class="vertical-line" style="height: 45px;"></div>

    <div>
      <button mat-flat-button class="assignee">
        <img src="../assets/images/users.svg" style="margin-left: -10px; margin-top: -5px;">
        <input matInput [formControl]="ASSIGNEE_USER_NAME" [matAutocomplete]="autoAssignee" placeholder="Assignee"
        style=" position: relative; top: -3px; left: 0%; vertical-align: middle; margin-left: 7px; text-align: start;">
        <mat-autocomplete #autoAssignee="matAutocomplete" (optionSelected)="onSelectionAssignee($event)">
          <mat-option *ngFor="let assignee of assigneeList" [value]="assignee.ASSIGNEE_USER_NAME">
            {{assignee.ASSIGNEE_USER_NAME}}
          </mat-option>
        </mat-autocomplete>
      </button>
    </div>
    <div class="vertical-line" style="height: 45px;"></div>

    <div>
      <button mat-flat-button class="assignee">
        <img src="../assets/images/sliders.svg" style="margin-left: -10px; margin-top: -6px;">
        <mat-select placeholder="Status" class="stat" style="margin-top: -6px;" [formControl]="STATUS_ID" (selectionChange)="filter_comp(1)">
        <mat-option *ngFor="let status of statusList;" [value]="status.ID">
          {{status.TASK_STATUS_NAME}}
        </mat-option>
      </mat-select>
      </button>
    </div>
    <div class="vertical-line" style="height: 45px;"></div>

    <div>
      <button mat-flat-button class="date-button" (click)="picker.open()" style="vertical-align: middle;">
        <mat-icon class="material-icons-outlined" style="margin-top: -1px;">calendar_today</mat-icon>
        <input matInput [matDatepicker]="picker" [formControl]="DUE_DATE" (dateChange)="filter_comp(1)" class="date"
          placeholder="Due Date" [readonly]="true" autocomplete="off">
        <mat-datepicker #picker></mat-datepicker>
      </button>
    </div>
    <div class="vertical-line" style="height: 45px;"></div>

    <div>
      <button class="clear-button" (click)="clearFilter()">
        <span class="material-icons">delete_outline</span>
      </button>
    </div>

    <div class="header-right" style="position: relative;bottom: 2px;">
      <img src="../assets/images/search-task.png" class="seicon">
      <mat-form-field  [floatLabel]="'never'" class="search_field" style="height: 20px; width: 121px;">
        <input matInput placeholder="Search tasks..." class="search-input" [formControl]="SEARCH_KEY" autocomplete="off">
      </mat-form-field>
    </div>
  </div>

  <div fxLayout="row" class="addtask-container">
    <div style="padding: 0% 1%;" class="ab">
      <button mat-flat-button class="add-button" (click)="addTask()">
        <span>
          <mat-icon style="font-size: 14px; padding-top: 4px;">add</mat-icon>&nbsp;Add task
        </span>
      </button>
    </div>
  </div>
</mat-toolbar>

<mat-divider *ngIf="!isMobile"></mat-divider>

<app-mat-spinner-overlay overlay="true" *ngIf="loading && !isMobile">
</app-mat-spinner-overlay>

<div class="text-message" *ngIf="zeroTasks">
  <span>
    <br>
    No task available ....
    <br>
  </span>
</div>

<div class="card-container" *ngIf="!isMobile && !zeroTasks">
  <mat-card class="card">
    <div class="task-table">
      <table mat-table [dataSource]="dataSource">
        <div>
        <ng-container matColumnDef="border">
          <th mat-header-cell *matHeaderCellDef style="width: 1%;"></th>
          <td mat-cell *matCellDef="let element" class="table-left-border"
            [ngClass]="{'coll' : today_date > element.DUE_DATE && element.TASK_STATUS_NAME != 'Done'}"></td>
        </ng-container>

        <ng-container matColumnDef="task">
          <th mat-header-cell *matHeaderCellDef style="width: 17%;">
            <img src="../assets/images/tasks.svg">&nbsp;
            Task
          </th>
          <td mat-cell *matCellDef="let element" style="width: 17%;" (click)="updateTask(element)">
            <span class="ellipsis1">{{element.TASK_NAME}}</span>
          </td>
        </ng-container>

        <ng-container matColumnDef="case">
          <th mat-header-cell *matHeaderCellDef style="width: 18%;">
            <img src="../assets/images/complaints.svg">&nbsp;
            Case
          </th>
          <td mat-cell *matCellDef="let element" style="width: 18%;">
            <!-- <p class="ellipsis"> -->
              <!-- <span style="font-weight: 600; font-style: normal;
              font-size: 12px;
              color: #010101;">Complaint</span> - -->
              <span class="ellipsis">{{element.CASE_ID}}</span>
            <!-- </p> -->
          </td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef style="width: 15%;">
            <img src="../assets/images/sliders.svg">&nbsp;
            Status
          </th>
          <td mat-cell *matCellDef="let element" style="width: 15%;">
            <button mat-flat-button class="select"
              [ngClass]="{'theme-status-todo' : 'New' == element.TASK_STATUS_NAME , 'theme-status-done' : 'Done' == element.TASK_STATUS_NAME, 'theme-status-active' : 'In Progress' == element.TASK_STATUS_NAME, 'theme-status-hold' : 'Hold' == element.TASK_STATUS_NAME}">
              <mat-select [(value)]="element.TASK_STATUS_ID" class="stat"
                (selectionChange)="changeValueStatus($event.value, element.ID)">
                <mat-option *ngFor="let status of statusList;" [value]="status.ID">
                  {{status.TASK_STATUS_NAME}}
                </mat-option>
              </mat-select>
            </button>
          </td>
        </ng-container>

        <ng-container matColumnDef="priority">
          <th mat-header-cell *matHeaderCellDef style="width: 11%;">
            <img src="../assets/images/complaints.svg">&nbsp;
            Priority
          </th>
          <td mat-cell *matCellDef="let element" style="width: 11%;">
            <mat-icon class="circle" *ngIf="element.PRIORITY_ID == 1" style="color:#2ED9FF;">fiber_manual_record
            </mat-icon>
            <mat-icon class="circle" *ngIf="element.PRIORITY_ID == 2" style="color: #F89E1B;">fiber_manual_record
            </mat-icon>
            <mat-icon class="circle" *ngIf="element.PRIORITY_ID == 3" style="color: #FF4242">fiber_manual_record
            </mat-icon>
            <mat-icon class="circle" *ngIf="element.PRIORITY_ID == 4" style="color: #FF4242;">fiber_manual_record
            </mat-icon> &nbsp;<span class="prior">{{element.PRIORITY_NAME}}</span>
          </td>
        </ng-container>

        <ng-container matColumnDef="assignee">
          <th mat-header-cell *matHeaderCellDef style="width: 16%;">
            <img src="../assets/images/users.svg">&nbsp;
            Assignee
          </th>
          <td mat-cell *matCellDef="let element" style="width: 16%;">
            <button mat-flat-button class="assign">
              <span matPrefix>
                <img src="../assets/images/userassignee.svg"
                  style="background-color:#92A2B1;width: 15px; height: 15px; border-radius: 9px;position: relative; vertical-align: middle; align-items: center; top: -8px; margin: 2px -10px;">
              </span>
              <input matInput type="text" [(ngModel)]="element.ASSIGNEE_USER_NAME" [matAutocomplete]="autoAssignee" (keyup)="getAssigneeList($event.target.value)" style="position: relative; margin: 2px 9px; top: -7px; padding-left: 12px; text-align: start;">
              <mat-autocomplete #autoAssignee="matAutocomplete" (optionSelected)="onSelectionChange($event, element.ID)">
                <mat-option *ngFor="let assignee of assigneeList" [value]="assignee.ASSIGNEE_USER_NAME">
                  {{assignee.ASSIGNEE_USER_NAME}}
                </mat-option>
              </mat-autocomplete>
            </button>
          </td>
        </ng-container>

        <ng-container matColumnDef="due_date">
          <th mat-header-cell *matHeaderCellDef style="width: 10%;">
            <img src="../assets/images/calendar.svg">&nbsp;
            Due Date
          </th>
          <td mat-cell *matCellDef="let element" style="width: 10%; border-right: 2px solid #D8DCDE;"
            [ngClass]="{'rej' :'Today' == element.DUE_DATE }">
            {{element.due_date}}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"
          [ngClass]="{'col' :today_date > row.DUE_DATE && row.TASK_STATUS_NAME != 'Done' , 'other':today_date != row.DUE_DATE} ">
        </tr>
        </div>
      </table>
    </div>
  </mat-card>
  <mat-paginator (page)="changePage($event)"  [pageSize]="10" [pageIndex]="0" *ngIf="!zeroTasks"></mat-paginator> 
  <span class="label" *ngIf="!zeroTasks"> {{rangeLabel}} of {{totalCount}}</span>  
</div>