import { Component, HostListener, OnInit, Query<PERSON>ist, ViewChild, ViewChildren } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { BehaviorSubject } from 'rxjs/internal/BehaviorSubject';
import { Observable } from 'rxjs/internal/Observable';
import { finalize } from 'rxjs/operators';
import { NotificationService } from 'src/app/services/notification.service';
import { TagsService } from 'src/app/services/tags.service';
import { AuthService } from 'src/app/services/auth.service';
import { colorObj } from 'src/app/shared/color-object';
import { AddEditSubtagComponent } from '../add-edit-subtag/add-edit-subtag.component';
import { AddEditCompanyComponent } from '../add-edit-company/add-edit-company.component';
import { MergeCompaniesComponent } from 'src/app/merge-companies/merge-companies.component';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';

@Component({
  selector: 'app-field-administration',
  templateUrl: './field-administration.component.html',
  styleUrls: ['./field-administration.component.scss']
})
export class FieldAdministrationComponent implements OnInit {

  pagename: String;
  userInfo: any;
  userName: any;
  selectedTab: number = 0;
  loading: boolean = true;
  dataSource: any = [];
  dataSource1: any = [];
  subTagsColumns: string[] = ['SUB_TAG_NAME', 'action'];
  displayedColumns: string[] = ['check', 'company_name', 'address', 'description', 'action'];
  companyPagenumber: number = 0;
  subtagPageNumber: number = 0;
  private request$: Observable<any>;
  noData: boolean = false;
  totalCount: any;
  pageSize: number = 10;
  rangeLabel: string;
  companyRespArray: Array<any> = [];
  subtagRespArray: Array<any> = [];
  companyFilterArray: Array<any> = [];
  companySortedArray: any[];
  companyFilter: boolean;
  subtagFilterArray: Array<any> = [];
  subtagFilter: boolean = false;
  idArray = [];
  startIndex: number;
  lastIndex: number;
  limit: number = 20;
  lastResCount: number = 0;
  KEYWORD = new FormControl(null);
  sortingKey: any = '';
  sortingOrder: any = '';
  companies: boolean;
  subTags: boolean;
  compname_asc: boolean = false;
  compname_desc: boolean = false;
  confirmationMsg: any = {};

  private _companyData = new BehaviorSubject<any[]>([]);
  private CompanyDataStore: { $companyData: any[] } = { $companyData: [] };
  readonly $companyData = this._companyData.asObservable();

  private _subtagData = new BehaviorSubject<any[]>([]);
  private SubTagDataStore: { $SubTagData: any[] } = { $SubTagData: [] };
  readonly $SubTagData = this._subtagData.asObservable();

  @ViewChildren(MatPaginator) paginator = new QueryList<MatPaginator>();
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private tagService: TagsService,
    private authService: AuthService,
    private notify: NotificationService,
    public dialog: MatDialog,
  ) { }

  ngOnInit(): void {
    this.pagename = "Administration : Field Management";
    this.companies = true;
    this.subTags = false;
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.userName = this.userInfo.firstName;
  }

  ngAfterViewInit() {
    this.KEYWORD.setValue('');
    if (this.selectedTab == 0) {
      this.getCompanies();
    }
    else if (this.selectedTab == 1) {
      this.getSubTagList();
    }
  }

  public onScrollDown(): void {
    if (this.selectedTab == 0) {
      this.getCompanies();
    }
    if (this.selectedTab == 1) {
      this.getSubTagList();
    }
  }

  changePage(event) {
    this.startIndex = (event.pageIndex * this.pageSize) + 1;
    this.lastIndex = this.startIndex + (this.pageSize - 1);
    if (this.lastIndex > this.totalCount) {
      this.lastIndex = this.totalCount;
    }
    this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
    if (this.selectedTab == 0) {
      if (this.companyRespArray.length != 0) {
        this.getCompanies();
      }
      else if (this.companyFilter == true) {
        this.getCompaniesList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            if (res.data.length > 0) {
              this.companyRespArray = [];
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.companyPagenumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              if (this.companyFilterArray.length != 0 && this.companySortedArray.length == 0) {
                this.companyFilterArray = this.companyFilterArray.concat(res.data[0].DATA);
                this.CompanyDataStore.$companyData = this.companyFilterArray;
              }
              else if (this.companyFilterArray.length == 0 && this.companySortedArray.length != 0) {
                this.companySortedArray = this.companySortedArray.concat(res.data[0].DATA);
                this.CompanyDataStore.$companyData = this.companySortedArray;
              }
              this.dataSource1.data = this.CompanyDataStore.$companyData;
              this.dataSource1 = new MatTableDataSource<any>(this.CompanyDataStore.$companyData);
              this._companyData.next(Object.assign({}, this.CompanyDataStore).$companyData);
              this.dataSource1.paginator = this.paginator.toArray()[0];
            }
          }, err => {
            this.loading = false;
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
      }
      else {
        this.getCompaniesList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            if (res.data.length > 0) {
              this.companyRespArray = [];
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.companyPagenumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              this.companySortedArray = this.companySortedArray.concat(res.data[0].DATA);
              this.CompanyDataStore.$companyData = this.companySortedArray;
              this.dataSource1.data = this.CompanyDataStore.$companyData;
              this.dataSource1 = new MatTableDataSource<any>(this.CompanyDataStore.$companyData);
              this._companyData.next(Object.assign({}, this.CompanyDataStore).$companyData);
              this.dataSource1.paginator = this.paginator.toArray()[0];
            }
          }, err => {
            this.loading = false;
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
      }
    }
    else if (this.selectedTab == 1) {
      if ((event.pageIndex > event.previousPageIndex) && (this.lastResCount == this.limit)) {
        this.getSubTags()
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            this.loading = false;
            if (res.data.length > 0) {
              this.lastResCount = res.data[0].DATA.length;
              this.noData = false;
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.subtagPageNumber == 1) {
                if (this.pageSize > this.totalCount) {
                  this.rangeLabel = 1 + ' - ' + this.totalCount;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              if (!this.subtagFilter) {
                this.subtagRespArray = this.subtagRespArray.concat(res.data[0].DATA);
                if (this.subtagRespArray.length == 0) {
                  this.noData = true;
                } else {
                  this.noData = false;
                }
                this.SubTagDataStore.$SubTagData = this.subtagRespArray;
              } else {
                this.subtagFilterArray = this.subtagFilterArray.concat(res.data[0].DATA);
                if (this.subtagFilterArray.length == 0) {
                  this.noData = true;
                } else {
                  this.noData = false;
                }
                this.SubTagDataStore.$SubTagData = this.subtagFilterArray;
              }
              this.dataSource = new MatTableDataSource<any>(this.SubTagDataStore.$SubTagData);
              this.dataSource.paginator = this.paginator.toArray()[1];
              this._subtagData.next(Object.assign({}, this.SubTagDataStore).$SubTagData);
            }
          }, err => {
            this.loading = false;
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
      }
    }
  }

  getCompaniesList(searchValue) {
    this.companyPagenumber++;
    this.limit = 20;
    this.request$ = this.authService.getCompaniesList(this.companyPagenumber, searchValue, this.limit, this.sortingKey, this.sortingOrder);
    return this.request$;
  }

  getCompanies() {
    this.getCompaniesList(this.KEYWORD.value)
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((res: any) => {
        this.loading = false;
        if (res.data.length > 0) {
          this.totalCount = res.data[0].TOTAL_COUNT;
          if (this.companyPagenumber == 1) {
            if (this.pageSize > this.totalCount) {
              this.rangeLabel = 1 + ' - ' + this.totalCount;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.companyRespArray = this.companyRespArray.concat(res.data[0].DATA);
          if (this.companyRespArray.length == 0) {
            this.noData = true;
          } else {
            this.noData = false;
          }
          this.CompanyDataStore.$companyData = this.companyRespArray;
          this.dataSource1.data = this.CompanyDataStore.$companyData;
          this.dataSource1 = new MatTableDataSource<any>(this.CompanyDataStore.$companyData);
          this._companyData.next(Object.assign({}, this.CompanyDataStore).$companyData);
          this.dataSource1.paginator = this.paginator.toArray()[0];
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      })
  }

  getSubTags() {
    this.subtagPageNumber++;
    this.request$ = this.tagService.getSubTagsList(this.subtagPageNumber, this.limit, this.KEYWORD.value);
    return this.request$;
  }

  getSubTagList() {
    this.getSubTags()
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((res: any) => {
        this.loading = false;
        if (res.data.length > 0) {
          this.lastResCount = res.data[0].DATA.length;
          this.noData = false;
          this.totalCount = res.data[0].TOTAL_COUNT;
          if (this.subtagPageNumber == 1) {
            if (this.pageSize > this.totalCount) {
              this.rangeLabel = 1 + ' - ' + this.totalCount;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          if (!this.subtagFilter) {
            this.subtagRespArray = this.subtagRespArray.concat(res.data[0].DATA);
            this.SubTagDataStore.$SubTagData = this.subtagRespArray;
          } else {
            this.subtagFilterArray = [];
            this.subtagFilterArray = this.subtagFilterArray.concat(res.data[0].DATA);
            this.SubTagDataStore.$SubTagData = this.subtagFilterArray;
          }
          this.dataSource = new MatTableDataSource<any>(this.SubTagDataStore.$SubTagData);
          this.dataSource.paginator = this.paginator.toArray()[1];
          this._subtagData.next(Object.assign({}, this.SubTagDataStore).$SubTagData);
        } else {
          this.noData = true;
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      })
  }

  private onFinalize(): void {
    this.request$ = null;
  }

  applyFilter(filterValue: string) {
    if (this.selectedTab == 0) {
      this.startIndex = null;
      this.lastIndex = null;
      this.pageSize = 10;
      this.totalCount = null;
      if (filterValue.length == 0) {
        this.companyPagenumber = 0;
        this.noData = false;
        this.companyFilterArray = [];
        this.dataSource1.data = '';
        this.KEYWORD.setValue('');
        this.companyFilter = false;
        this.getCompanies();
      } else {
        this.companyFilter = true;
        this.companyRespArray = [];
        this.dataSource1.data = [];
        this.companyPagenumber = 0;
        this.noData = false;
        this.KEYWORD.setValue(filterValue)
        this.filterCompany();
      }
    }
    else if (this.selectedTab == 1) {
      this.startIndex = null;
      this.lastIndex = null;
      this.pageSize = 10;
      this.totalCount = null;
      if (filterValue.length == 0) {
        this.subtagPageNumber = 0;
        this.subtagFilterArray = [];
        this.dataSource.data = '';
        this.noData = false;
        this.KEYWORD.setValue('');
        this.subtagFilter = false;
        this.getSubTagList();
      } else {
        this.subtagFilter = true;
        this.subtagRespArray = [];
        this.dataSource.data = [];
        this.subtagPageNumber = 0;
        this.noData = false;
        this.KEYWORD.setValue(filterValue);
        this.getSubTagList();
      }
    }
  }

  onTabChanged(event) {
    this.idArray = [];
    this.selectedTab = event.index;
    if (this.selectedTab == 0) {
      this.rangeLabel = '';
      this.totalCount = '';
      this.subtagFilter = false;
      this.KEYWORD.setValue('');
      this.companyRespArray = [];
      this.companySortedArray = [];
      this.companies = true;
      this.subTags = false;
      this.loading = true;
      this.companyPagenumber = 0;
      this.dataSource1.data = '';
      this.dataSource1 = new MatTableDataSource(this.dataSource1.data);
      this.dataSource1.paginator = this.paginator.toArray()[0];
      this.sortingKey = '';
      this.sortingOrder = '';
      this.compname_asc = false;
      this.compname_desc = false;
      this.getCompanies();
    }
    else if (this.selectedTab == 1) {
      this.rangeLabel = '';
      this.companyFilter = false;
      this.totalCount = '';
      this.KEYWORD.setValue('');
      this.subtagRespArray = [];
      this.companies = false;
      this.subTags = true;
      this.loading = true;
      this.subtagPageNumber = 0;
      this.dataSource.data = '';
      this.dataSource = new MatTableDataSource(this.dataSource.data);
      this.dataSource.paginator = this.paginator.toArray()[1];
      this.sortingKey = '';
      this.sortingOrder = '';
      this.getSubTagList();
    }
  }

  addCompany() {
    const dialogRef = this.dialog.open(AddEditCompanyComponent, {
      width: '610px',
      data: { name: "addcompanies" },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'refresh') {
          this.companyPagenumber = 0;
          this.companyRespArray = [];
          this.dataSource1.data = '';
          this.rangeLabel = 1 + ' - ' + this.pageSize;
          this.getCompanies();
        }
      }
    );
  }

  editCompanies(row) {
    const dialogRef = this.dialog.open(AddEditCompanyComponent, {
      width: '610px',
      data: { row, name: "editcompanies" },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'u-refresh') {
          this.getCompanyById(row['ID']);
          dialogRef.close('refresh');
        }
        if (data === 'd-refresh') {
          this.removeCompanyFromStore(row['ID']);
          dialogRef.close('refresh');
        }
      }
    );
  }

  removeCompanyFromStore(compId) {
    this.CompanyDataStore.$companyData.forEach((t: any, i) => {
      if (t.ID === compId) {
        this.CompanyDataStore.$companyData.splice(i, 1);
      }
    });
    this.totalCount = this.totalCount - 1;
    if (this.totalCount !== 0) {
      if (this.pageSize > this.totalCount) {
        this.pageSize = this.totalCount;
        this.rangeLabel = 1 + ' - ' + this.pageSize;
      }
      if (this.lastIndex > this.totalCount) {
        this.lastIndex = this.totalCount;
        this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
      }
    } else {
      this.noData = true;
    }
    this._companyData.next(Object.assign({}, this.CompanyDataStore).$companyData);
    this.dataSource1 = new MatTableDataSource(this.CompanyDataStore.$companyData);
    this.dataSource1.paginator = this.paginator.toArray()[0];
  }

  getCompanyById(compId) {
    this.authService.getCompanyById(compId).subscribe(res => {
      let userObj = res.data[0];
      this.CompanyDataStore.$companyData.forEach((t: any, i) => {
        if (t.ID === userObj.ID) {
          this.CompanyDataStore.$companyData[i] = userObj;
        }
      });
      this._companyData.next(Object.assign({}, this.CompanyDataStore).$companyData);
      this.updateCompaniesTableDate(userObj.ID);
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  updateCompaniesTableDate(compId) {
    this.$companyData.subscribe((res) => {
      this.companyRespArray = res;
      this.dataSource1.data = res;
      this.dataSource1 = new MatTableDataSource(res);
      this.dataSource1.paginator = this.paginator.toArray()[0];
      // this.dataSource1.data.forEach(element => {
      //   if(compId == element.ID){
      //     if (element['USER_ENABLED'] == 1) {
      //       element['action'] = true;
      //     } else {
      //       element['action'] = false;
      //     }
      //   }
      // });
    })
  }

  approveCompany(row) {
    this.confirmationMsg.title = 'Are you sure you want to approve the company ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: row.ID, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.authService
          .approveCompany(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
              this.getCompanyById(row.ID);
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  rejectCompany(row) {
    this.confirmationMsg.title = 'Are you sure you want to reject the company ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: row.ID, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.authService
          .deleteCompany(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
              this.CompanyDataStore.$companyData.forEach((t: any, i) => {
                if (t.ID === row.ID) {
                  this.CompanyDataStore.$companyData.splice(i, 1);
                }
              });
              this._companyData.next(Object.assign({}, this.CompanyDataStore).$companyData);
              this.$companyData.subscribe((res) => {
                this.companyRespArray = res;
                this.dataSource1.data = res;
                this.dataSource1 = new MatTableDataSource(res);
                this.dataSource1.paginator = this.paginator.toArray()[0];
              })
              this.totalCount = this.totalCount - 1;
              if (this.totalCount !== 0) {
                if (this.pageSize > this.totalCount) {
                  this.pageSize = this.totalCount;
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
                if (this.lastIndex > this.totalCount) {
                  this.lastIndex = this.totalCount;
                  this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                }
              }
              else {
                this.noData = true;
              }
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  selectCompany(value, id, status) {
    let ID = id.toString();
    if (value == true) {
      this.idArray.push(ID);
    } else {
      for (let i = 0; i < this.idArray.length; i++) {
        if (this.idArray[i] == ID) {
          this.idArray.splice(this.idArray.indexOf(ID), 1);
        }
      }
    }
  }

  openMergePopup() {
    const dialogRef = this.dialog.open(MergeCompaniesComponent, {
      width: '1000px',
      height: '-webkit-fill-available',
      disableClose: true,
      data: this.idArray
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'u-refresh') {
          this.companyPagenumber = 0;
          this.companyRespArray = [];
          this.dataSource1.data = '';
          this.rangeLabel = 1 + ' - ' + this.pageSize;
          this.getCompanies();
        }
      }
    );
  }

  filterCompany() {
    this.noData = false;
    this.dataSource1.data = '';
    this.getCompaniesList(this.KEYWORD.value)
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((res: any) => {
        this.companyRespArray = [];
        this.companySortedArray = [];
        this.companyFilterArray = [];
        if (res.data.length > 0) {
          this.totalCount = res.data[0].TOTAL_COUNT;
          if (this.companyPagenumber == 1) {
            if (res.data[0].TOTAL_COUNT < this.pageSize) {
              this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.noData = false;
          this.companyFilterArray = this.companyFilterArray.concat(res.data[0].DATA);
          this.CompanyDataStore.$companyData = this.companyFilterArray;
          this.dataSource1.data = this.CompanyDataStore.$companyData;
          this.dataSource1 = new MatTableDataSource<any>(this.CompanyDataStore.$companyData);
          this._companyData.next(Object.assign({}, this.CompanyDataStore).$companyData);
          this.dataSource1.paginator = this.paginator.toArray()[0];
        } else {
          this.noData = true;
          this.companyFilterArray = [];
          this.dataSource1.data = '';
        }
      })
  }

  addSubTag() {
    const dialogRef = this.dialog.open(AddEditSubtagComponent, {
      width: '610px',
      height: '350px',
      data: { func: "add" },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'add') {
          this.subtagPageNumber = 0;
          this.subtagRespArray = [];
          this.dataSource.data = '';
          this.rangeLabel = 1 + ' - ' + this.pageSize;
          this.getSubTagList();
        }
      }
    );
  }

  editSubTag(element) {
    const dialogRef = this.dialog.open(AddEditSubtagComponent, {
      width: '610px',
      height: '350px',
      data: { element, func: "edit" },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'edit') {
          this.getSubTagById(element['ID'])
        } else if (data === 'remove') {
          this.removeSubTagFromStore(element['ID']);
        }
      }
    );
  }

  getSubTagById(id) {
    this.tagService.getSubTagById(id).subscribe(res => {
      let subTagObj = res.data[0];
      this.SubTagDataStore.$SubTagData.forEach((t: any, i) => {
        if (t.ID === subTagObj.ID) {
          this.SubTagDataStore.$SubTagData[i] = subTagObj;
        }
      });
      this._subtagData.next(Object.assign({}, this.SubTagDataStore).$SubTagData);
      this.updateSubTagTableData(subTagObj['ID']);
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  updateSubTagTableData(id) {
    this.$SubTagData.subscribe((res) => {
      this.subtagRespArray = res;
      this.dataSource.data = res;
      this.dataSource = new MatTableDataSource(res);
      this.dataSource.paginator = this.paginator.toArray()[1];
    });
  }

  removeSubTagFromStore(tagId) {
    this.SubTagDataStore.$SubTagData.forEach((t: any, i) => {
      if (t.ID === tagId) {
        this.SubTagDataStore.$SubTagData.splice(i, 1);
      }
    });
    this.totalCount = this.totalCount - 1;
    if (this.totalCount !== 0) {
      if (this.pageSize > this.totalCount) {
        this.pageSize = this.totalCount;
        this.rangeLabel = 1 + ' - ' + this.pageSize;
      }
      if (this.lastIndex > this.totalCount) {
        this.lastIndex = this.totalCount;
        this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
      }
    } else {
      this.noData = true;
    }
    this._subtagData.next(Object.assign({}, this.SubTagDataStore).$SubTagData);
    this.dataSource = new MatTableDataSource(this.SubTagDataStore.$SubTagData);
    this.dataSource.paginator = this.paginator.toArray()[1];
  }

  sortCompanies(key, order) {
    this.sortingKey = key;
    this.sortingOrder = order;
    this.companyPagenumber = 0;
    if (order != '') {
      if (order == 'ASC') {
        if (key == 'COMPANY_NAME') {
          this.compname_asc = true;
          this.compname_desc = false;
        }
      }
      else if (order == 'DESC') {
        if (key == 'COMPANY_NAME') {
          this.compname_asc = false;
          this.compname_desc = true;
        }
      }
      this.idArray = [];
      this.noData = false;
      this.dataSource1.data = '';
      if (this.companyFilter == true) {
        this.filterCompany();
      }
      else {
        this.noData = false;
        this.getCompaniesList(this.KEYWORD.value)
          .pipe(finalize(() => this.onFinalize()))
          .subscribe((res: any) => {
            this.companyRespArray = [];
            this.companyFilterArray = [];
            this.companySortedArray = [];
            if (res.data.length > 0) {
              this.totalCount = res.data[0].TOTAL_COUNT;
              if (this.companyPagenumber == 1) {
                if (res.data[0].TOTAL_COUNT < this.pageSize) {
                  this.rangeLabel = 1 + ' - ' + res.data[0].TOTAL_COUNT;
                } else {
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
              }
              this.noData = false;
              this.companySortedArray = this.companySortedArray.concat(res.data[0].DATA);
              this.CompanyDataStore.$companyData = this.companySortedArray;
              this.dataSource1.data = this.CompanyDataStore.$companyData;
              this.dataSource1 = new MatTableDataSource<any>(this.CompanyDataStore.$companyData);
              this._companyData.next(Object.assign({}, this.CompanyDataStore).$companyData);
              this.dataSource1.paginator = this.paginator.toArray()[0];
            }
          })
      }
    }
    else if (order == '') {
      this.compname_asc = false;
      this.compname_desc = false;
      if (this.companyFilter == true) {
        this.filterCompany();
      }
      else {
        this.dataSource1.data = [];
        this.companyRespArray = [];
        this.noData = false;
        this.companyPagenumber = 0;
        this.getCompanies();
      }
    }
  }

}