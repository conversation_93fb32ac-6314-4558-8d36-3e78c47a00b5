::ng-deep .mat-form-field-flex > .mat-form-field-infix { padding: 5px 0px 0.4em 0px !important }
:host:ng-deep .mat-form-field-appearance-outline .mat-form-field-label { margin-top:-15px; }
:host:ng-deep label.ng-star-inserted { transform: translateY(-0.59375em) scale(.75) !important;}
:host:ng-deep .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    transform: translateY(-1.1em) scale(.75);
    width: 133.33333%;
}
:host ::ng-deep .mat-input-element {
    position: relative;
    bottom: 5px;
}
:host ::ng-deep .mat-select-value-text {
    position: relative;
    bottom: 2px;
}
:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-subscript-wrapper {
    padding: 0px;
    margin-top: 3px;
}
.input-field {
    width:216px !important;
    margin-left: 8px;
}
.names {
    flex-direction: row;  
    display: flex;
    padding-top: 10px;
    color: #2F3941;
}
.names1 {
    color: #2F3941;  
}
.names_ad {
    flex-direction: row;  
    display: flex;
    grid-gap: 260px;
    color: #2F3941;
}
.toolbar {
    padding-left: 15px;
    height: 84px;
    padding-left: 15px;
    background-color: #F8F9F9;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #2F3941;
}
.search-btn {
    color: solid rgba(47, 57, 65, 0.6);
    background: #F3F3F3;  
    border: 1px solid rgba(47, 57, 65, 0.6);      
}
.input-field_phno {
    width: 558px;
}
.input-field_dept {
    width:270px !important;
    height: 20px !important;
    
}
.lastdivdr {
    width: 560px;
    margin-top: 4px;
}
.remove-btn {
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: #ED2F45;
    border: 1px solid #ED2F45;
    border-radius: 12px;
    box-sizing: border-box;
}
.toolbar-btns {
    grid-gap: 4%;
}
.toolbar2 {
    margin-left: -5px;
    background-color: white;
}
.cancel-btn {
    color: #284255;
    background-color: white;
    border-radius: 14px;
    margin-right: 10px;
}
.update-btn {
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: white;
    background-color: #0088CB;
    border-radius: 12px;
}
.edit-form {
    width: 580px;
    overflow-y: auto;
    overflow-x: hidden;
}
.doc-btn {
    background: #CFD7DF;
    border-radius: 3px;
    width: 35px;
    height: 34px;
    margin-bottom: -8px;
    position: relative;
    background-image: url('../../../assets/images/copy.svg');
    background-repeat: no-repeat;
    background-position: center;
    bottom: 12px;
    left: 11px;
}
.generate-btn {
    color: #5A6F84;
    background-color: #CFD7DF;
    border: 5px ;
    border-radius: 30px;
    margin-top: 15px;
}
.control-container {
    height: 52px;
    width: 540px;
    margin-top: 0px;
    flex-direction: row;
    display: flex;
}
.contents::-webkit-scrollbar {
    display: none;
}
.contents {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.contents {
    margin-left: 5px;
    height: 380px;
    width: 580px;
    overflow-y: scroll;
    overflow-x: hidden;
}
.mat-dialog-actions{
    margin-right: 15px;
}