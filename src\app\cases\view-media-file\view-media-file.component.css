.flex{
    display: flex;
    align-items: center;
    justify-content: center;
}

.header{
    background: #F8F9F9;
    border-radius: 4px 4px 0px 0px;
    width: 100%;
    height: 70px;
}

.head-container{
    height: 70px;
}

.heading{
    color: #000000;
    /* font-weight: 600; */
    font-weight: 500;
    font-size: 16px;
    line-height: 21px;
    padding: 24px 15px 20px ;
}


.close-btn{
    background: #F3F3F3;
    border: 1px solid rgba(47, 57, 65, 0.6);
    margin: 0px 15px 0px 0px;
    position: relative;
}
.close-icon{
    color: rgba(47, 57, 65, 0.6);
    margin-bottom: 5px;
    margin-left: 1px;
}

.media-expsn{
    background: #F8F9F9;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
}

.media-expsn1{
    background: #F8F9F9;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
    height: 50vh;
    -ms-overflow-style: none;
    scrollbar-width: none;
    overflow-y: scroll;
    overflow-x: hidden;
}

.media-expsn1::-webkit-scrollbar {
    display: none;
}

:host ::ng-deep .panel-header>.mat-expansion-indicator:after {
    color: #0088CB;
}

.attribute-container1{
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
    height: 20px;
    padding: 2px 0px;
    width: 190px;
}
.attribute-container2{
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
    height: 20px;
    padding: 2px 0px;
}

.grey-text{
    color: rgba(0, 0, 0, 0.6);
}

.panel-body{
    background: #FFFFFF;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
    width: 395px;
    height: 28px;
    padding: 0px !important;
}

.doc-btn{
    background: rgba(0, 136, 203, 0.25);
    border-radius: 3px 0px 0px 3px;
}
.link-container{
  width: 77%;
}
.doc-link{
    color: #0088CB;
    text-decoration: underline;
    font-size: 12px;
    padding-left: 10px;
}

.flex-end{
    display: flex;
    justify-content: flex-end;
}

.doc-btn,
.icon-btn{
    width: 26px;
    height: 26px;
}