.flex {
    display: flex;
    align-items: center;
    justify-content: center;
}
.bell-btn {
    padding-right: 14px;
    padding-top: 4px;
}
.header-container {
    background: #000000;
    width: 100%;
    height: 84px;
    padding: 5%;
}
.logo {
    width: 102.77px;
    height: 30px;
}
.profile-btn {
    color: #FFFFFF;
    background: #0088CB;
    width: 34px;
    height: 34px;
    text-align: center;
    font-style: normal;
    font-weight: bold;
    font-size: 17px;
    box-sizing: border-box;
    padding-top: 2px;
    line-height: 27px;
}
.common-toolbar {
    border-bottom: 1px solid #D8DCDE;
    border-top: 0;
    border-left: none;
    border-right: none;
}
.heading-container {
    width: 60%;
}
.mat-error {
    color: #ED2F45 !important;
}
table {
    width: 100%;
    border: 1px solid rgba(0,0,0,.12);
    margin-top: 21px;
}
.nams-table {
  height: 500px;
  /* overflow-y: scroll;
  overflow-x: hidden; */
  overflow: scroll;
}
.mat-column-FILE_NAME {
  flex: 0 0 80%;
}
.mat-column-CREATED_DATE {
  flex: 0 0 20%;
}
.mat-row:nth-child(even) {
    background-color: #F8F9F9;
}
.mat-row:nth-child(odd) {
    background-color:#FFFF;
}
.file-history {
    margin-top: 14px;
    margin-left: 36px;
}
.file-upload-main {
    margin-top: 24px;
}
.upload-buttons {
    margin-left: 83%;
    margin-top: 10px;
    margin-bottom: 10px;
    /* margin-right: 8px; */
}
.cancel-button {
    width: 80px;
    color: #5A6F84;
    background-color: #CFD7DF;
    border-radius: 12px;
    height: 35px;
    margin-right: 8px;
}
.browse-file-heading {
    position: relative;
    bottom: 25px;
    margin-left: 127px;
}
.upload-button {
    width: 80px;
    border-radius: 12px;
    background-color: #0088CB;
    color: white;
    height: 35px;
}
th.mat-header-cell:last-of-type {
    padding-left: 20px !important;
    padding-right: 0px !important;
}
.close-icon {
    width: 14px;
    height: 14px;
    position: relative;
    /* bottom: 3px; */
    margin: 0px 10px 0px 10px;
}
.panel-body {
    background: #FFFFFF;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
    width: 347px;
    height: 28px;
    margin: 46px;
    margin-left: 127px;
    position: relative;
    bottom: 27px;
}
.panel-body1 {
    background: #FFFFFF;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
    width: 347px;
    height: 28px;
    margin-left: 23px;
    margin-bottom: 36px;
}
.doc-btn {
    background: rgba(0, 136, 203, 0.25);
    border-radius: 3px 0px 0px 3px;
    width: 26px;
    height: 26px;
}
.link-container {
    width: 87%;
    margin-top: 3px;
    margin-left: 5px;
}
.file-upload {
    margin-top: 20px;
    border: 1px solid rgba(0,0,0,.12);
}
.browse-files {
    margin:24px;
    border: 1px dotted rgba(0,0,0,.12);
}
.options-container {
    width:40%;
}
:host ::ng-deep .mat-select-arrow {
    position: relative;
    top: 4px;
}
.profile-div {
    width: 200px;
    height: 90vh;
    background-color: #F8F9F9;;
    margin-left: 60px;
}
.resend[disabled] {
    opacity: 0.3;
    cursor: not-allowed !important;
}
.profile-div::-webkit-scrollbar {
    display: none;
}
.profile-div {
    -ms-overflow-style: none;
    scrollbar-width: none;
    overflow-x: hidden;
    overflow-y: hidden;
}
.profile-div-condition {
    width: 200px;
    height: 90vh;
    background-color: #F8F9F9;
}
.profile-div-condition::-webkit-scrollbar {
    display: none;
}
.profile-div-condition {
    -ms-overflow-style: none;
    scrollbar-width: none;
    overflow-x: hidden;
    overflow-y: hidden;
}
.pagename {
    width: 40px;
    height: 40px;
    margin-top: 12px;
    margin-left: 18px;
    margin-right: -50px;
    z-index: 1;
}
.buttons {
    margin-top: 25px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}
.hide-icon {
    position: absolute;
    top: 13px;
    left: 12px;
}
.show-icon {
    visibility: hidden;
    top: 13px;
    left: 12px;
}
.namsfiles-button:focus .show-icon , .namsfiles-button:hover .show-icon {
    visibility: hidden;
}
.namsfiles-button:focus .hide-icon , .namsfiles-button:hover .hide-icon {
    visibility: visible;
}
.userprofile:focus .show-icon , .userprofile:hover .show-icon {
    visibility: hidden;
}
.userprofile:focus .hide-icon , .userprofile:hover .hide-icon {
    visibility: visible;
}
.changepswd-button:focus .hide-icon , .changepswd-button:hover .hide-icon {
    visibility: visible;
}
.changepswd-button:focus .show-icon , .changepswd-button:hover .show-icon {
    visibility: hidden;
}
.contents {
    margin-top: 15px;
    margin-left: 10px;
}
.userprofile-content {
    margin-top: 30px;
    margin-left: 10px;
   
}
.complaint-container {
    width: 78% !important;
    height: 88vh;
}

:host ::ng-deep .mat-form-field-flex > .mat-form-field-infix { padding: 0px 0px 0.4em 0px !important; bottom: 0px;}
/* ::ng-deep .mat-form-field-appearance-outline .mat-form-field-label { margin-top:-15px; }
::ng-deep label.ng-star-inserted { transform: translateY(-0.59375em) scale(.75) !important; }
::ng-deep .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    transform: translateY(-1.1em) scale(.75);
    width: 133.33333%;
} */
.names
{
    flex-direction:row;
    display: flex;
    grid-gap: 25px;
    padding-top: 10px;
    font-style: normal;
    font-weight: normal;
    color: #000000;
}
.names-row {
    flex-direction: row;
    display: flex;
    grid-gap: 28px;
    font-style: normal;
    font-weight: normal;
    color: #000000;
}
.control-container {
    margin-top: 0px;
    flex-direction: column;
    display: flex;
}
.input-field {
    width:230px !important;
}
.input-field_title {
    width:80px !important;
}
.input-field_fname {
    width:200px !important;
    padding-left: 0px;
}
.input-field_lastname {
    width:200px !important;
    padding-left: 0px;
}
.input-field_phno {
    width: 529px;
}
.input-field_otp {
    width: 515px;
}
.input-field_dist {
    width:270px !important;
    padding-left: 0px;
}
.names1 {
    color: #000000;
    font-style: normal;
    font-weight: normal;
}
.update-btn {
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: white;
    background-color: #0088CB;
    border-radius: 12px;
}
.resend {
    background-color: white;
    color: #0088CB;
    border: transparent;
}
.toolbar-btns2 {
    /* float: right; */
    padding-top: 10px;
}
.lastdivdr {
    padding-top: 5%;
    width: 520px;
}
.cancel-btn {
    color: #5A6F84;
    background-color: #CFD7DF;
    border-radius: 12px;
    margin-right: 10px;
}
.register-btn {
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: white;
    background-color: #0088CB;
    border-radius: 12px;
}
.myprofile-body {
    flex-direction: row;
    display: flex;
    width: 100%;
    height: 92vh;
}
.userprofile-text {
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    position: relative;
    left: -15px;
}
.changepaswd-text {
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    margin: 0px 15px;
}
.userprofile-button {
    width: 165px;
    border-radius: 4px;   
}
.changepswd-button {
    width: 165px;
    border-radius: 4px; 
}
.phno-varify {
    color:#3ba791;
    font-size: 12px;
    position: relative;
    left: 6px;
    bottom: 3px;
}

                /*MOBILE VIEW*/

@media  screen and (max-width: 1250px) {
    .userprofile-button {
        width: 152px;
    }
    .changepswd-button {
        width: 152px;
    }
    .buttons {
        flex-direction: row;
        margin-left: 10px;
        margin-top: 10px;
    }
    .profile-div {
        width: 100%;
        height: 12vh;
        margin-left: 0px;
    }
    .profile-div::-webkit-scrollbar {
        display: none;
    }
    .profile-div {
        -ms-overflow-style: none;
        scrollbar-width: none;
        overflow-x: hidden;
        overflow-y: hidden;
    }
    .profile-div-condition {
        width: 100%;
        height: 12vh;
        margin-left: 0px;
    }
    .profile-div-condition::-webkit-scrollbar {
        display: none;
    }
    .profile-div-condition {
        -ms-overflow-style: none;
        scrollbar-width: none;
        overflow-x: hidden;
        overflow-y: hidden;
    }
    .myprofile-body {
        width: 100%;
        display: block;
        justify-content: center;
        gap: 5px;
    }
    .complaint-container {
        width: 90% !important;
        height: 100vh;
        padding: 0px 0px 0px 10px ;
    }
    .names-row {
        
        display: block;
        font-style: normal;
        font-weight: normal;
        color: #000000; 
    }
    .input-field {        
        width:98% !important;
    }
    .input-field_dist {
        width:98% !important;
    }
    .names {
        display: block;
        font-style: normal;
        font-weight: normal;
        color: #000000;
    }
    .input-field_title {
        width:98% !important;
    }
    .input-field_fname {
        width:98% !important;
        padding-left: 0px;
    }
    .input-field_lastname {
        width: 98% !important;
        padding-left: 0px;
    }
    .input-field_phno {
        width: 98%;
    }
    .userprofile-button .hide-icon {
        position: absolute;
        top: 13px;
        left: 20px;
    }
    .userprofile-button .show-icon {
        visibility: hidden;
        position: absolute;
        top: 13px;
        left: 20px;
    }
    .userprofile-text {
        font-style: normal;
        font-weight: normal;
        font-size: 14px;
        position: relative;
        left: 0px;
    }
    .changepaswd-text {
        font-style: normal;
        font-weight: normal;
        font-size: 14px;
    }
    .mobview-phverify {
        width: 98%;
    }
    .phno-varify-mob {
        color:#3ba791;
        font-size: 12px;
    }
    .mobview-emailverify {
        width: 98%;
    }
}

.scrollable-content{ 
    height: 71vh;
    overflow-x: hidden;
    overflow-y: scroll;
}
.scrollable-content::-webkit-scrollbar {
    display: none;
}
.scrollable-content {
    -ms-overflow-style: none;
    scrollbar-width: none;
}