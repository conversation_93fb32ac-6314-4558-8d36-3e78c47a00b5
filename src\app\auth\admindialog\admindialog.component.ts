import { Component, HostL<PERSON>ener, Inject, OnInit, Renderer2 } from '@angular/core';
import { <PERSON><PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA, MatDialogConfig } from '@angular/material/dialog';
import { AuthService } from '../../services/auth.service';
import { NotificationService } from '../../services/notification.service';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';
import { colorObj } from 'src/app/shared/color-object';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-admindialog',
  templateUrl: './admindialog.component.html',
  styleUrls: ['./admindialog.component.css'],
})
export class AdmindialogComponent implements OnInit {
  addform: FormGroup;
  value: any;
  confirmationMsg: any = {};
  mobilenopattern = /^((\\+91-?)|0)?[0-9]{10}$/;
  usernamepattern = /^[a-z A-Z]+$/;
  department_name: any[];
  user_role: any[];
  roleID: number;
  deptId: number;
  deptName: string;
  roleName: string;
  roleId;
  emailPattern = environment.emailPatterm;
  userInfo: any;
  userRoleId: number;
  titles: any[];
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private fb: FormBuilder,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private authService: AuthService,
    private notify: NotificationService,
    private renderer: Renderer2,
    private dialogRef: MatDialogRef<AdmindialogComponent>,
    public dialog: MatDialog
  ) {
    this.addform = this.fb.group({
      department_name: ['', [Validators.required]],
      salutation: ['', [Validators.required]],
      user_role: ['', [Validators.required]],
      first_name: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(25), Validators.pattern(this.usernamepattern)]],
      last_name: ['', [Validators.required, Validators.maxLength(25), Validators.pattern(this.usernamepattern)]],
      email: ['', [Validators.required, Validators.pattern(this.emailPattern)]],
      contact: ['', [Validators.required, Validators.pattern(this.mobilenopattern)]],
    });
  }

  ngOnInit(): void {
    this.titles = JSON.parse(window.localStorage.getItem('salutation'));
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.userRoleId = this.userInfo.roleId;
    const roles = JSON.parse(window.localStorage.getItem('role'));
    this.user_role = roles.filter(element => {
      return (element.ADMINISTRATION_VISIBILITY == 1)
    });
    this.roleID = this.data.ROLE_ID;
    this.department_name = JSON.parse(window.localStorage.getItem('departmentType'));
    this.deptId = this.data.DEPARTMENT_TYPE_ID;
    this.addform.controls['department_name'].setValue(this.data.DEPARTMENT_TYPE_NAME);
    this.addform.controls['user_role'].setValue(this.data.ROLE_NAME);
    this.addform.controls['email'].setValue(this.data.EMAIL_ID);
    this.addform.controls['salutation'].setValue(this.data.SALUTATION_ID);
    this.addform.controls['first_name'].setValue(this.data.FIRST_NAME);
    this.addform.controls['last_name'].setValue(this.data.LAST_NAME);
    this.addform.controls['contact'].setValue(this.data.MOBILE);
    this.addform.controls['email'].disable();
    this.addform.controls['contact'].disable();
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  updateUser(model) {
    this.authService.updateUser(model, this.data.ID, this.deptId, this.roleID).subscribe(
      (res) => {
        this.notify.showNotification(
          res.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status] : "success"),
          res.status
        )
        this.dialogRef.close('u-refresh');
      },
      (err) => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      }
    );
  }

  removeUser() {
    this.confirmationMsg.title = 'Are you sure you want to delete the user ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.data.C_ID, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.authService
          .deleteUser(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              this.dialogRef.close('d-refresh');
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  onDeptChange(event) {
    this.deptName = event.option.value;
    this.department_name.forEach(el => {
      if (this.deptName === el.DEPARTMENT_TYPE_NAME) {
        this.deptId = el.ID
      }
    })
  }

  onRoleChange(event) {
    this.roleName = event.option.value;
    this.user_role.forEach(el => {
      if (this.roleName === el.ROLE_NAME) {
        this.roleID = el.ID.toString();
      }
    })
  }

}