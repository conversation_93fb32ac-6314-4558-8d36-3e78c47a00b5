import { ChangeDetector<PERSON><PERSON>, Component, HostL<PERSON>ener, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { ComplaintsService } from '../../services/complaints.service';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { FilterPopupComponent } from '../../filter-popup/filter-popup.component';
import { NotificationService } from '../../services/notification.service';
import { colorObj } from 'src/app/shared/color-object';
import { DatePipe } from '@angular/common';
import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import { FormControl } from '@angular/forms';
import { ReportsService } from 'src/app/services/reports.service';
import { saveAs as importedSaveAs } from "file-saver";
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-admin-dashboard',
  templateUrl: './admin-dashboard.component.html',
  styleUrls: ['./admin-dashboard.component.css']
})

export class AdminDashboardComponent implements OnInit {
  complaintStats: any[];
  pagename: String;
  id;
  userData;
  activeTab;
  public visibility: boolean = false;
  public myLabel: any = '+  Add';
  tabs = [];
  activePage;
  userName: any;
  activitiesList: any = [];
  todayActivities: any = [];
  yesterdayActivities: any = [];
  monthActivities: any = [];
  searchPageNumber = 1;
  lastData: number;
  listLoading: boolean = false;
  text_year = new FormControl('');
  initialValue: any;
  year = "";
  currentYear: number;
  years: any = [];
  // today = new Date();
  // todayDate;
  // yesterday = new Date();
  // yesterdayDate;
  year_value;
  isMonthselected = false;
  from_month = new FormControl('');
  from_year = new FormControl('');
  to_month = new FormControl('');
  to_year = new FormControl('');
  from_month_id: number = 0;
  from_year_id: number = 0;
  to_month_id: number = 0;
  to_year_id: number = 0;
  month = "";
  url;
  public bucketUrl = `${environment.BUCKET_URL}`;
  regdisabled: boolean = false;
  tamsdisabled: boolean = false;
  reechdisabled: boolean = false;
  selectedTab = 0;

  months: any[] = [
    { id: 1, name: "January", viewvalue: "January" },
    { id: 2, name: "February", viewvalue: "February" },
    { id: 3, name: "March", viewvalue: "March" },
    { id: 4, name: "April", viewvalue: "April" },
    { id: 5, name: "May", viewvalue: "May" },
    { id: 6, name: "June", viewvalue: "June" },
    { id: 7, name: "July", viewvalue: "July" },
    { id: 8, name: "August", viewvalue: "August" },
    { id: 9, name: "September", viewvalue: "September" },
    { id: 10, name: "October", viewvalue: "October" },
    { id: 11, name: "November", viewvalue: "November" },
    { id: 12, name: "December", viewvalue: "December" }
  ];
  monthList: any = [];
  yearList: any = [];

  @ViewChild(CdkVirtualScrollViewport) virtualScroll: CdkVirtualScrollViewport;
  innerWidth: number;
  mobile: boolean;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.mobile = true;
    } else {
      this.mobile = false;
    }
  }

  constructor(private cs: ComplaintsService,
    private router: Router,
    public dialog: MatDialog,
    private notify: NotificationService,
    private datePipe: DatePipe,
    private cd: ChangeDetectorRef,
    private reportService: ReportsService
  ) { }

  ngOnInit(): void {
    if (window.innerWidth <= 1250) {
      this.mobile = true;
    } else {
      this.mobile = false;
    }
    this.userData = JSON.parse(window.localStorage.getItem('userInfo'))
    this.userName = this.userData.firstName;
    this.selectYear();
    this.selectActivities(this.searchPageNumber);
    // this.todayDate = this.datePipe.transform(this.today, 'dd/MM/yyyy');
    // this.yesterday.setDate(this.today.getDate() - 1);
    // this.yesterdayDate = this.datePipe.transform(this.yesterday, 'dd/MM/yyyy');
    this.pagename = "Dashboard";
    this.activePage = "Dashboard";
    this.cs.setActivePage(this.activePage);
    let currentDate = new Date();
    this.year_value = currentDate.getFullYear();
    this.displayYears();
    this.displayMonths();
  }

  async onTabChanged(event) {
    this.selectedTab = event.index;
    if (this.selectedTab == 0) {

    }
    else if (this.selectedTab == 1) {

    }
  }

  selectYear() {
    let currentDate = new Date();
    if (currentDate.getMonth() + 1 > 3) {
      this.currentYear = currentDate.getFullYear();
    } else {
      this.currentYear = currentDate.getFullYear() - 1;
    }
    let earliestYear = this.currentYear - 5;
    while (this.currentYear >= earliestYear) {
      let dateOption = {
        "viewValue": this.currentYear + ' ' + '-' + ' ' + (this.currentYear + 1),
        "value": this.currentYear
      };
      this.years.push(dateOption);
      this.currentYear -= 1;
    }
    this.initialValue = this.years[0].viewValue;
    this.selectByYear(this.years[0]);
  }

  selectByYear(event) {
    this.year = event.value;
    this.getComplaintStats(this.year);
    this.isMonthselected = false;
    this.displayMonths();
  }

  selectFromMonth(event) {
    this.month = event.value;
    const monthObj: any[] = this.months.filter(x => x.name === this.month);
    this.from_month_id = monthObj[0]['id'];
    if (this.from_month.value != "" && this.from_year.value != "" && this.to_month.value != "" && this.to_year.value != "") {
      this.isMonthselected = true;
    }
  }

  selectFromYear(event) {
    this.from_year_id = event.value;
    if (this.from_month.value != "" && this.from_year.value != "" && this.to_month.value != "" && this.to_year.value != "") {
      this.isMonthselected = true;
    }
  }

  selectToMonth(event) {
    this.month = event.value;
    const monthObj: any[] = this.months.filter(x => x.name === this.month);
    this.to_month_id = monthObj[0]['id'];
    if (this.from_month.value != "" && this.from_year.value != "" && this.to_month.value != "" && this.to_year.value != "") {
      this.isMonthselected = true;
    }
  }

  selectToYear(event) {
    this.to_year_id = event.value;
    if (this.from_month.value != "" && this.from_year.value != "" && this.to_month.value != "" && this.to_year.value != "") {
      this.isMonthselected = true;
    }
  }

  displayYears() {
    let currentDate = new Date();
    this.currentYear = currentDate.getFullYear();
    let earliestYear = this.currentYear - 5;
    while (this.currentYear >= earliestYear) {
      let yearOption = {
        "viewValue": this.currentYear,
        "value": this.currentYear
      };
      this.yearList.push(yearOption);
      this.currentYear -= 1;
    }
  }

  displayMonths() {
    let currentDate = new Date();
    this.currentYear = currentDate.getFullYear();
    if (Number(this.year) == this.currentYear) {
      this.monthList = [];
      let startMonth = 3;
      let endMonth = currentDate.getMonth() - 1;
      while (this.months[startMonth].name != this.months[endMonth].name) {
        let monthname = {
          "viewValue": this.months[startMonth].name,
          "value": this.months[startMonth].name
        };
        this.monthList.push(monthname);
        if (startMonth == 11)
          startMonth = 0;
        else
          startMonth += 1;
      }
      let monthname = {
        "viewValue": this.months[endMonth].name,
        "value": this.months[endMonth].name
      };
      this.monthList.push(monthname);
    }
    else {
      this.monthList = [];
      let startMonth = 3;
      let endMonth = 2;
      while (this.months[startMonth].name != this.months[endMonth].name) {
        let monthname = {
          "viewValue": this.months[startMonth].name,
          "value": this.months[startMonth].name
        };
        this.monthList.push(monthname);
        if (startMonth == 11)
          startMonth = 0;
        else
          startMonth += 1;
      }
      let monthname = {
        "viewValue": this.months[endMonth].name,
        "value": this.months[endMonth].name
      };
      this.monthList.push(monthname);
    }
  }

  getComplaintStats(year) {
    this.cs.getAdminComplaintStats(year).subscribe((complaintsStatus: any) => {
      this.complaintStats = complaintsStatus.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  onScrollDown(): void {
    this.listLoading = true;
    this.searchPageNumber++;
    this.getNextSet(this.searchPageNumber);
  }

  getNextSet(pageNumber) {
    if (this.lastData == 10) {
      this.cs.getComplaintActivities(pageNumber).subscribe(res => {
        this.listLoading = false;
        this.lastData = res.data.length;
        this.activitiesList = this.activitiesList.concat(res.data);
        this.cd.detectChanges();
        for (let item of res.data) {
          item.date = this.datePipe.transform(item.date, 'dd/MM/yyyy, h:mm a');
          if (item.COMPLAINT.COMPLAINT_NAME != "" && item.COMPLAINT.COMPLAINT_NAME != null) {
            item.COMPLAINT.COMPLAINT_NAME = (item.COMPLAINT.COMPLAINT_NAME).replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').replace(/&amp;/g, ' ');
          }
          for (let val of item.updates) {
            if (val.label == 'Complaint Received On' || val.label == 'Complaint Due date changed to' || val.label == 'Advertiser due date changed to') {
              val.value = this.datePipe.transform(val.value, 'd MMMM, y, EEEE');
            }
            else {
              if (val.value != "" && val.value != null) {
                val.value = (val.value).replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').replace(/&amp;/g, ' ');
              }
            }
          }
        }
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      });
    }
  }

  openFilterDialog() {
    const dialogRef = this.dialog.open(FilterPopupComponent);
    dialogRef.afterClosed().subscribe(result => {
    });
  }

  clickEvent(id) {
    this.tabs.push(id);
    this.activeTab = this.tabs.length - 1;
    this.id = id;
    this.cs.listComplaint(id);
    this.visibility = true;
  }

  closeTab(index) {
    var tabsBeforeIndex = this.tabs.splice(0, index);
    var tabsAfterIndex = this.tabs.splice(index + 1);
    this.tabs = tabsBeforeIndex.concat(tabsAfterIndex);
    return this;
  }

  getColor(status) {
    switch (status) {
      case 'New':
        return '#47d147';
      case 'On Hold':
        return '#00ccff';
      case 'In Progress':
        return '#e6e600';
      case 'Closed':
        return '#ff704d';
    }
  }

  getBGC(status) {
    switch (status) {
      case 'New':
        return '#c6ffb3';
      case 'On Hold':
        return '#ccf2ff';
      case 'In Progress':
        return '#ffffcc';
      case 'Closed':
        return '#ffc2b3';
    }
  }

  selectActivities(pageNumber) {
    this.cs.getComplaintActivities(pageNumber).subscribe((activities: any) => {
      this.activitiesList = activities.data;
      this.lastData = activities.data.length;
      for (let item of this.activitiesList) {
        item.date = this.datePipe.transform(item.date, 'dd/MM/yyyy, h:mm a');
        if (item.COMPLAINT.COMPLAINT_NAME != "" && item.COMPLAINT.COMPLAINT_NAME != null) {
          item.COMPLAINT.COMPLAINT_NAME = (item.COMPLAINT.COMPLAINT_NAME).replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').replace(/&amp;/g, ' ');
        }
        for (let val of item.updates) {
          if (val.label == 'Complaint Received On' || val.label == 'Complaint Due date changed to' || val.label == 'Advertiser due date changed to') {
            val.value = this.datePipe.transform(val.value, 'd MMMM, y, EEEE');
          } else {
            if (val.value != "" && val.value != null) {
              val.value = (val.value).replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').replace(/&amp;/g, ' ');
            }
          }
        }
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  registeredReports() {
    this.regdisabled = true;
    this.reportService.getRegisteredComplaintReports(this.from_month_id, this.from_year_id, this.to_month_id, this.to_year_id).subscribe(res => {
      if (res.data.length > 0) {
        this.url = this.bucketUrl + res.data[0].path;
        this.cs.downloadFile(this.url).subscribe(async data => {
          await importedSaveAs(data, res.data[0].filename);
          this.notify.showNotification(
            'Report downloaded successfully',
            "top",
            (!!colorObj[200] ? colorObj[200] : "success"),
            200
          );
          this.regdisabled = false;
        })
      }
      else {
        this.notify.showNotification(
          res.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status] : "warning"),
          res.status
        )
        this.regdisabled = false;
      }
      this.from_month.setValue("");
      this.from_year.setValue("");
      this.to_month.setValue("");
      this.to_year.setValue("");
      this.isMonthselected = false;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
      this.regdisabled = false;
      this.isMonthselected = false;
    })
  }

  tamsReechReports(section, status) {
    if (section == 'tams') {
      this.tamsdisabled = true;
    }
    else if (section == 'reech') {
      this.reechdisabled = true;
    }
    this.reportService.getTamsReechReports(this.from_month_id, this.from_year_id, this.to_month_id, this.to_year_id, section, status).subscribe((res: any) => {
      if (res.data.length > 0) {
        this.url = this.bucketUrl + res.data[0].path;
        this.cs.downloadFile(this.url).subscribe(async data => {
          await importedSaveAs(data, res.data[0].filename);
          this.notify.showNotification(
            'Report downloaded successfully',
            "top",
            (!!colorObj[200] ? colorObj[200] : "success"),
            200
          );
          this.tamsdisabled = false;
          this.reechdisabled = false;
        })
      }
      else {
        this.notify.showNotification(
          res.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status] : "warning"),
          res.status
        )
        this.tamsdisabled = false;
        this.reechdisabled = false;
      }
      this.from_month.setValue("");
      this.from_year.setValue("");
      this.to_month.setValue("");
      this.to_year.setValue("");
      this.isMonthselected = false;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
      this.tamsdisabled = false;
      this.reechdisabled = false;
      this.isMonthselected = false;
    })
  }

  selectedStatusComplaints(status) {
    this.cs.getStatusComplaints(status);
    this.cs.updateStep('dashboard');
    this.router.navigateByUrl('cases/manage-cases');
  }

}