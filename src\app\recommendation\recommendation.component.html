<div fxLayout="column">
    <div fxLayout="row" style="padding-top: 23px;padding-left:18px;padding-right:18px;background: #F8F9F9;">
        <div style="font-size: 16px;font-weight:600">Recommendations</div>
        <div fxFlex="80%"></div>
        <div class="close-div">
            <button mat-icon-button class="close-btn" mat-dialog-close>
                <mat-icon style="margin-bottom: 2px;">close</mat-icon>
            </button>
        </div>
    </div>
    <mat-divider></mat-divider>
    <div fxLayout="row" style="margin-top: 21px;">
        <div fxFlex="65%">
            <form [formGroup]="recommendationForm">
                <mat-form-field appearance="outline" class="input-field">
                    <input matInput placeholder="Summary of the recommendation..." id="subject" maxlength="500"
                        formControlName="subject" autocomplete="off" required>
                    <mat-error *ngIf="recommendationForm.get('subject').touched && recommendationForm.controls['subject'].errors?.required">
                        Summary is required
                    </mat-error>
                </mat-form-field><br>
                <mat-form-field appearance="outline" class="input-field">
                    <textarea matInput maxlength="15000" rows="11" placeholder="Please describe the recommendation..."
                        formControlName="recommendation" required></textarea>
                    <mat-error *ngIf="recommendationForm.get('recommendation').touched && recommendationForm.controls['recommendation'].errors?.required">
                        Recommendation is required
                    </mat-error>
                </mat-form-field>
                <span style="margin-left: 61%">
                    <button (click)="cancel()" class="cancel-btn" style="background: none !important;border: none;"
                        [disabled]="recommendationForm.invalid">Cancel</button>
                    <button class="theme-blue-button-admin" style="border: none;padding-left: 8px;padding-right: 10px;"
                        (click)="onSubmit(recommendationForm.value)" [disabled]="recommendationForm.invalid">Submit</button>
                </span>
            </form>
        </div>
        <div fxFlex="8%">
            <mat-divider [vertical]="true" style="height: 406px;margin-right: 29px;margin-top: -22px;"></mat-divider>
        </div>
        <div fxFlex="49%" class="recommendations">
            <span style="font-size: 14px;font-weight:600">Recommendation Messages:</span>
            <div *ngFor="let record of records">
                <div [ngClass]="{'recommendation-box': record.PUBLISHED == 0, 'highlighted-recommendation-box': record.PUBLISHED == 1}" fxLayout="row">
                    <div fxFlex="88%">
                        <div style="word-wrap: break-word;">
                            <span class="box-heading">Summary: </span> <span class="box-text">{{record.SUBJECT}}</span>
                        </div>
                        <div style="word-wrap: break-word;">
                            <span class="box-heading">Description: </span>
                            <span class="box-text">{{record.RECOMMENDATION}}</span>
                        </div>
                    </div>
                    <div class="publish-div" fxFlex="10%">
                        <button mat-icon-button class="more-icon" [matMenuTriggerFor]="menu" [disabled]="!isResolution">
                            <mat-icon>more_vert</mat-icon>
                        </button>
                        <mat-menu #menu="matMenu" class="publish-menu" xPosition="before">
                            <div class="publish-option-container">
                                <button mat-menu-item class="option-btn"
                                    (click)="publishOrUnpublishRecommendation(record.PUBLISHED,record.ID,record.COMPLAINT_ID)">
                                    <span class="option-text" *ngIf="record.PUBLISHED == 0">Publish</span>
                                    <span class="option-text" *ngIf="record.PUBLISHED == 1">Unpublish</span>
                                </button>
                                <button mat-menu-item class="option-btn"
                                    (click)="deleteRecommendation(record.ID,record.COMPLAINT_ID)">
                                    <span class="option-text">Delete</span>
                                </button>
                            </div>
                        </mat-menu>
                    </div>
                </div>
                <span style="font-size: 11px;margin-left: 64%;"> - {{record.CREATED_DATE | date: 'dd/MM/yyyy'}}:
                    {{record.CREATED_DATE | date: 'h:mm a'}}</span>
            </div><br>
        </div>
    </div>
</div>