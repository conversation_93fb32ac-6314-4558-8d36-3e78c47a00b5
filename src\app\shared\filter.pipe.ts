import { Pipe, PipeTransform } from '@angular/core';
@Pipe({
  name: 'filter'
})
export class FilterPipe implements PipeTransform {
  transform(items: any[], searchText: string, searchStatus: string): any[] {
    if(!items) return [];
    // if(!searchText && !searchStatus) return items;
    // searchText = searchText.toLowerCase();
    // return items.filter( it => {
    //   return it.company.toLowerCase().includes(searchText) || it.status.toLowerCase().includes(searchStatus); 
    // //   || it.status.toLowerCase().includes(searchStatus);         it.tag.toLowerCase().includes(searchTag);
    // //   return it.president.toLowerCase().includes(searchText) || it.party.toLowerCase().includes(searchText) || it.took_office.toLowerCase().includes(searchText);
    // });
    // if(items.length === 0 || searchText === ''){
    //   return items;
    // }
    
    const complaints = [];
    for(const complaint of items){
      if( complaint['company'] === searchText){
        complaints.push(complaint);
      }
    }
    return complaints;


   }
}