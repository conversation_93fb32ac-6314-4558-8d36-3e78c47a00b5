import { Component, ElementRef, HostListener, Inject, OnInit, ViewChild } from '@angular/core';
import { <PERSON><PERSON>rray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';
import { AuthService } from 'src/app/services/auth.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';
import { AfterViewInit, Renderer2, OnDestroy } from '@angular/core';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-add-edit-company',
  templateUrl: './add-edit-company.component.html',
  styleUrls: ['./add-edit-company.component.scss']
})

export class AddEditCompanyComponent implements OnInit {
  addcompany: FormGroup;
  addCompany: boolean = true;
  confirmationMsg: any = {};
  mobilenopattern = /^((\\+91-?)|0)?[0-9]{10}$/;
  companyNamepattern = /^(?!\s)(?=.*[a-zA-Z0-9])[a-zA-Z0-9 '.£&@€¥$~?!()*-]{1,}$/;
  autoComplete: boolean = false;
  companyList: any[];
  companyId: number = 0;
  complaintId: number = 0;
  emailPattern = environment.emailPatterm;
  regFrom: string = 'company';
  companyInfo: any;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(private fb: FormBuilder,
    private authService: AuthService,
    private renderer: Renderer2,
    private notify: NotificationService,
    private dialogRef: MatDialogRef<AddEditCompanyComponent>,
    public dialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public data: any) {
    this.addcompany = this.fb.group({
      cname: ['', [Validators.required, Validators.pattern(this.companyNamepattern)]],
      caddress: [''],
      contact_info: fb.array([]),
      about_company: [''],
    })
  }

  ngAfterViewInit() {
    setTimeout(() => {
      var elem = this.renderer.selectRootElement('#companyname');
      elem.focus();
    }, 1000);
  }

  ngOnInit(): void {
    // window.addEventListener('scroll', this.scrollEvent, true);
    if (this.data.name == "editcompanies") {
      this.addCompany = false;
      this.authService.getCompanyById(this.data.row['ID']).subscribe(res => {
        this.companyInfo = res.data[0];
        let companyInfo = this.companyInfo['CONTACT_INFO'];
        companyInfo.forEach(() => {
          this.addContactInfo();
        });
        this.addcompany.patchValue({
          'cname': this.companyInfo['COMPANY_NAME'],
          'caddress': this.companyInfo['ADDRESS'],
          'about_company': this.companyInfo['COMPANY_DESCRIPTION'],
          'contact_info': companyInfo
        })
        // this.addcompany.controls['cname'].setValue(this.companyInfo['COMPANY_NAME']);
        // this.addcompany.controls['caddress'].setValue(this.companyInfo['ADDRESS']);
        // this.addcompany.controls['about_company'].setValue(this.companyInfo['COMPANY_DESCRIPTION']);
        // this.addcompany.controls['contact_info'].setValue(this.companyInfo['CONTACT_INFO']);
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      })
    }
    else if (this.data.name == "addcompanies") {
      this.addCompany = true;
      this.addContactInfo();
    } else if (this.data.name === 'email' || this.data.name === 'sysGenCompany' || this.data.name === 'chatbot' || this.data.name === 'whatsapp'
      || this.data.name === 'nams_tams' || this.data.name === 'nams_reech') {
      this.addCompany = true;
      this.autoComplete = true;
      this.complaintId = this.data.row.ID;
      this.addcompany.controls['cname'].setValue(this.data.row['COMPANY_NAME']);
      if (this.data.name === 'sysGenCompany') {
        this.addcompany.controls['cname'].setValue(this.data.row['SUGGESTED_COMPANY_NAME']);
        this.regFrom = 'complaint';
      } else if (this.data.name === 'chatbot') {
        this.regFrom = 'chatbot';
      } else if (this.data.name === 'whatsapp') {
        this.regFrom = 'whatsapp';
      } else if (this.data.name === 'email') {
        this.regFrom = 'email';
      } else if (this.data.name === 'nams_tams') {
        this.regFrom = 'nams_tams';
      } else if (this.data.name === 'nams_reech') {
        this.regFrom = 'nams_reech';
      }
      this.addContactInfo();
    }
    this.addcompany.get('cname')!.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0) {
          this.getCompanyList(value);
        }
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  //   scrollEvent = (event: any): void => {
  //     if(this.auto.panelOpen)
  //       this.auto.updatePosition();
  // };

  addContactInfo() {
    let index = (<FormArray>this.addcompany.get('contact_info')).length;
    if (index < 3) {
      let fg = this.fb.group({
        EMAIL_ID: this.fb.control('', Validators.pattern(this.emailPattern)),
        MOBILE: this.fb.control('', Validators.pattern(this.mobilenopattern))
      });
      (<FormArray>this.addcompany.get('contact_info')).push(fg);
    }
  }

  removeContactInfo(index: number) {
    (<FormArray>this.addcompany.get('contact_info')).removeAt(index);
  }

  getControls() {
    return (this.addcompany.get('contact_info') as FormArray).controls;
  }

  getCompanyList(value) {
    if (!this.addcompany.controls['cname'].hasError('pattern')) {
      this.authService.getCompanies(value.trim()).subscribe(res => {
        this.companyList = res.data;
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
  }

  onSelectionChange(event) {
    this.companyList.forEach(element => {
      if (event.option.value === element.COMPANY_NAME) {
        this.companyId = element.ID;
      }
    });
  }

  register(model) {
    let obj: Object;
    let contactInfo = [];
    let contact_info = model['contact_info'];
    for (let i = 0; i < contact_info.length; i++) {
      if (!!contact_info[i].EMAIL_ID || !!contact_info[i].MOBILE) {
        contactInfo.push(contact_info[i]);
      }
    }
    if (this.data.name == "addcompanies") {
      obj = {
        "COMPANY_NAME": model.cname,
        "ADDRESS": model.caddress,
        "COMPANY_DESCRIPTION": model.about_company,
        "CONTACT_INFO": contactInfo,
        "REG_FROM": this.regFrom,
      }
    } else if (this.data.name === 'email' || this.data.name === 'sysGenCompany' || this.data.name === 'chatbot' || this.data.name === 'whatsapp'
      || this.data.name === 'nams_tams' || this.data.name === 'nams_reech') {
      obj = {
        "COMPANY_NAME": model.cname,
        "COMPANY_DESCRIPTION": model.about_company,
        "ADDRESS": model.caddress,
        "CONTACT_INFO": contactInfo,
        "REG_FROM": this.regFrom,
        "COMPLAINT_ID": this.complaintId,
        "COMPANY_ID": this.companyId
      }
    }
    this.authService.createCompanyByAdmin(obj).subscribe(res => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      this.dialogRef.close('refresh');
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  updateCompany(model) {
    this.authService.updateCompany(model, this.data.row.ID).subscribe(
      (res) => {
        this.notify.showNotification(
          res.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status] : "success"),
          res.status
        )
        this.dialogRef.close('u-refresh');
      },
      (err) => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      }
    );
  }

  removeCompany() {
    this.confirmationMsg.title = 'Are you sure you want to delete the company ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.data.row.ID, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.authService
          .deleteCompany(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              this.dialogRef.close('d-refresh');
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

}