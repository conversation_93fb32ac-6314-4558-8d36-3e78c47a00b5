<div class="common-toolbar-mobile" style="width: 100%; height: auto;" *ngIf="isMobile">
    <app-mobile-header></app-mobile-header>
  </div>
  <span class="dashboard-admin-heading" *ngIf="isMobile">
    <p style="text-align: center;margin-top: 20px;margin-bottom: 3px;"> Welcome {{userName}}!</p>
  </span>
  <div class="dasboard-subheading" *ngIf="isMobile">
    <p style="text-align: center;">Dear ASCI Officers, please move to a larger device for seamlessly accessing all
        functionalities of the system</p>
  </div>
  <div class="dashboard-container" *ngIf="isMobile">
  </div>
  
  <app-icons *ngIf="!isMobile"></app-icons>
  <div class="common-toolbar" fxLayout="row" style="width: 100%; height: 50px;" *ngIf="!isMobile">
    <div class="heading-container">
      <app-heading [pagename]="pagename"></app-heading>
    </div>
    <div class="options-container">
      <app-toolbar-options></app-toolbar-options>
    </div>
  </div>
  
  <app-mat-spinner-overlay overlay="true" *ngIf="loading && !isMobile">
  </app-mat-spinner-overlay>
  
  <mat-toolbar class="toolbar2" fxLayout="row" fxLayoutAlign="end end" *ngIf="!isMobile">
    <div class="header-search">
      <input type="text" name="search" placeholder="Search.." [formControl]="KEYWORD" (keyup)="applyFilter($event.target.value)" style="
          font-style: normal;
          font-weight: normal;
          font-size: 14px; color: #2F3941;" autocomplete="off">
    </div>
    <div>
      <button mat-button *ngIf="internalUser" class="add-btn" (click)="addUser()" 
        style="width: 140px; margin-top: 5px;">
        <mat-icon style="font-size: 16px; padding-top: 3px; margin-left: -5px;">add</mat-icon>
        <span class="bolder">Add New User</span>
      </button>
      <button mat-button *ngIf="externalUser" class="add-btn" (click)="addAdvertiser()"
        style="width: 140px; margin-top: 5px;">
        <mat-icon style="font-size: 16px; padding-top: 3px; margin-left: -5px;">add</mat-icon>
        <span class="bolder">Add New User</span>
      </button>
    </div>
  </mat-toolbar>
  <mat-tab-group style="margin-left: 50px; margin-top: -50px;" class="admin-tabs" *ngIf="!isMobile"
    (selectedTabChange)="onTabChanged($event)">
    <mat-tab label="ASCI Members" class="asci-member">
      <mat-card class="card" *ngIf="!loading">
        <div class="table-scroll">
          <table mat-table class="admin-table" [dataSource]="dataSource1" matSort #table>
            <div>
              <ng-container matColumnDef="first_name">
                <th mat-header-cell *matHeaderCellDef style="width: 3%;"><b>First Name</b>
                  <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!fname_asc && !fname_desc" (click)="sortInternalComplaints('FIRST_NAME', 'ASC')">
                  <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="fname_asc" (click)="sortInternalComplaints('FIRST_NAME', 'DESC')">
                  <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="fname_desc" (click)="sortInternalComplaints('', '')">
                </th>
                <td mat-cell *matCellDef="let element" class="borderleft"> {{element.FIRST_NAME}} </td>
              </ng-container>
  
              <ng-container matColumnDef="last_name">
                <th mat-header-cell *matHeaderCellDef style="width: 3%;"><b>Last Name</b>
                  <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!lname_asc && !lname_desc" (click)="sortInternalComplaints('LAST_NAME', 'ASC')">
                  <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="lname_asc" (click)="sortInternalComplaints('LAST_NAME', 'DESC')">
                  <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="lname_desc" (click)="sortInternalComplaints('', '')">
                </th>
                <td mat-cell *matCellDef="let element"> {{element.LAST_NAME}} </td>
              </ng-container>
  
              <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef style="width: 3%;"><b>Email ID</b>
                  <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!email_asc && !email_desc" (click)="sortInternalComplaints('EMAIL_ID', 'ASC')">
                  <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="email_asc" (click)="sortInternalComplaints('EMAIL_ID', 'DESC')">
                  <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="email_desc" (click)="sortInternalComplaints('', '')">
                </th>
                <td mat-cell *matCellDef="let element"> {{element.EMAIL_ID}} </td>
              </ng-container>
  
              <ng-container matColumnDef="department_name">
                <th mat-header-cell *matHeaderCellDef style="width: 3%;"><b>Department</b>
                  <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!dept_asc && !dept_desc" (click)="sortInternalComplaints('DEPARTMENT_TYPE_NAME', 'ASC')">
                  <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="dept_asc" (click)="sortInternalComplaints('DEPARTMENT_TYPE_NAME', 'DESC')">
                  <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="dept_desc" (click)="sortInternalComplaints('', '')">
                </th>
                <td mat-cell *matCellDef="let element"> {{element.DEPARTMENT_TYPE_NAME}} </td>
              </ng-container>
  
              <ng-container matColumnDef="user_role">
                <th mat-header-cell *matHeaderCellDef  style="width: 3%;"><b>Role</b>
                  <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!role_asc && !role_desc" (click)="sortInternalComplaints('ROLE_NAME', 'ASC')">
                  <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="role_asc" (click)="sortInternalComplaints('ROLE_NAME', 'DESC')">
                  <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="role_desc" (click)="sortInternalComplaints('', '')">
                </th>
                <td mat-cell *matCellDef="let element"> {{element.ROLE_NAME}} </td>
              </ng-container>
  
              <ng-container matColumnDef="contact">
                <th mat-header-cell *matHeaderCellDef style="width: 3%;"><b>Contact no.</b></th>
                <td mat-cell *matCellDef="let element"> {{element.MOBILE}} </td>
              </ng-container>
  
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef  style="width: 3%;"><b>Status</b>
                  <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!status_asc && !status_desc" (click)="sortInternalComplaints('USER_CONFIRMED', 'ASC')">
                  <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="status_asc" (click)="sortInternalComplaints('USER_CONFIRMED', 'DESC')">
                  <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="status_desc" (click)="sortInternalComplaints('', '')">
                </th>
                <td mat-cell *matCellDef="let element"
                  [ngClass]="{'aprv' : 'Confirmed' == element.status, 'pen' : 'Unconfirmed' == element.status }">
                  {{element.status}} </td>
              </ng-container>
  
              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef class="borderright"><b>Action</b></th>
                <td mat-cell *matCellDef="let element;let i = index">
                  <div fxLayout="row" fxLayoutGap="10px">
                    <div fxLayout="column" *ngIf="element.status == 'Confirmed'" style="position: relative; top: 5px;">
                      <mat-slide-toggle (change)="onValChange($event, element.ID, element.C_ID)" [checked]="element.actionVal"
                        class="slider">
                      </mat-slide-toggle>
                      <p *ngIf="element.actionVal==true" style="
                      font-style: normal;
                      font-weight: normal;
                      font-size: 12px;color: rgba(0, 0, 0, 0.6); margin-top: -2px;">Enabled</p>
                      <p *ngIf="element.actionVal==false" style="
                      font-style: normal;
                      font-weight: normal;
                      font-size: 12px;color: rgba(0, 0, 0, 0.6); margin-top: -2px;">Disabled</p>
                    </div>
                    <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                      (click)="approve(element, 'Internal')"
                      style="position: relative; top: 10px; display: flex; align-items: center;">
                      <img src="../../assets/images/Admin-Approve.png" width="15px">
                      <p style="
                      font-style: normal;
                      font-weight: normal;
                      font-size: 12px;color: rgba(4, 165, 133, 0.6);">Approve</p>
                    </div>
                    <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                      (click)="rejectUser(element, 'Internal')"
                      style="position: relative; top: 10px; left: 0px; display: flex; align-items: center">
                      <img src="../../assets/images/reject-red.png" width="15px">
                      <p style="
                      font-style: normal;
                      font-weight: normal;
                      font-size: 12px;color: rgba(237, 47, 69, 0.6);">Reject</p>
                    </div>
                    <div fxLayout="column" fxLayoutGap="3px" (click)="editUser(element)"
                      style="position: relative;top: 10px;  left: 7px;">
                      <img src="../assets/images/edit-icon.svg" width="13px">
                      <p style="
                      font-style: normal;
                      font-weight: normal;
                      font-size: 12px;color: rgba(0, 0, 0, 0.6);">Edit</p>
                    </div>
                  </div>
                </td>
              </ng-container>
  
              <tr mat-header-row *matHeaderRowDef="displayedColumns1; sticky: true"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns1;">
              </tr>
            </div>
          </table>
        </div>
      </mat-card>
      <mat-paginator (page)="changePage($event)" [pageSize]="10" [pageIndex]="0" *ngIf="!noData"></mat-paginator> 
      <span class="label" *ngIf="!noData"> {{rangeLabel}} of {{totalCount}}</span>  
      <div class="text-message" *ngIf="noData">
        <span>
          <br>
          No Data available ....
          <br>
        </span>
      </div>
    </mat-tab>
    <mat-tab label="Intra Industry" class="asci-member">
      <mat-card class="card" *ngIf="!loading">
        <div class="table-scroll">
          <table mat-table class="admin-table" [dataSource]="dataSource2" matSort #table>
            <div>
              <ng-container matColumnDef="first_name">
                <th mat-header-cell *matHeaderCellDef  style="width: 3%;"><b>First Name</b>
                  <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!fname_asc && !fname_desc" (click)="sortExternalComplaints('FIRST_NAME', 'ASC')">
                  <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="fname_asc" (click)="sortExternalComplaints('FIRST_NAME', 'DESC')">
                  <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="fname_desc" (click)="sortExternalComplaints('', '')">
                </th>
                <td mat-cell *matCellDef="let element" class="borderleft"> {{element.FIRST_NAME}} </td>
              </ng-container>
  
              <ng-container matColumnDef="last_name">
                <th mat-header-cell *matHeaderCellDef  style="width: 3%;"><b>Last Name</b>
                  <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!lname_asc && !lname_desc" (click)="sortExternalComplaints('LAST_NAME', 'ASC')">
                  <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="lname_asc" (click)="sortExternalComplaints('LAST_NAME', 'DESC')">
                  <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="lname_desc" (click)="sortExternalComplaints('', '')">
                </th>
                <td mat-cell *matCellDef="let element"> {{element.LAST_NAME}} </td>
              </ng-container>
  
              <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef  style="width: 2%;"><b>Email ID</b>
                  <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!email_asc && !email_desc" (click)="sortExternalComplaints('EMAIL_ID', 'ASC')">
                  <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="email_asc" (click)="sortExternalComplaints('EMAIL_ID', 'DESC')">
                  <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="email_desc" (click)="sortExternalComplaints('', '')">
                </th>
                <td mat-cell *matCellDef="let element"> {{element.EMAIL_ID}} </td>
              </ng-container>
  
              <ng-container matColumnDef="department_name">
                <th mat-header-cell *matHeaderCellDef  style="width: 5%;"><b>Company</b>
                  <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!extcompany_asc && !extcompany_desc" (click)="sortExternalComplaints('COMPANY_NAME', 'ASC')">
                  <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="extcompany_asc" (click)="sortExternalComplaints('COMPANY_NAME', 'DESC')">
                  <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="extcompany_desc" (click)="sortExternalComplaints('', '')">
                </th>
                <td mat-cell *matCellDef="let element"> {{element.COMPANY_NAME}} </td>
              </ng-container>
  
              <ng-container matColumnDef="pro_member">
                <th mat-header-cell *matHeaderCellDef style="width: 3%;"><b>Pro member?</b></th>
                <td mat-cell *matCellDef="let element" style="padding-left: 2.5%;">
                  <mat-checkbox [checked]="element.pro_member"></mat-checkbox>
                </td>
              </ng-container>
  
              <ng-container matColumnDef="contact">
                <th mat-header-cell *matHeaderCellDef style="width: 2%;"><b>Contact no.</b></th>
                <td mat-cell *matCellDef="let element"> {{element.MOBILE}} </td>
              </ng-container>
  
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef style="width: 2%;"><b>Status</b>
                  <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!status_asc && !status_desc" (click)="sortExternalComplaints('USER_CONFIRMED', 'ASC')">
                  <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="status_asc" (click)="sortExternalComplaints('USER_CONFIRMED', 'DESC')">
                  <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="status_desc" (click)="sortExternalComplaints('', '')">
                </th>
                <td mat-cell *matCellDef="let element"
                  [ngClass]="{'aprv' : 'Confirmed' == element.status, 'pen' : 'Unconfirmed' == element.status, 'not-ext' : 'Not exist' == element.status }">
                  {{element.status}} </td>
              </ng-container>
  
              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef class="borderright"><b>Action</b></th>
                <td mat-cell *matCellDef="let element; let i = index">
                  <div fxLayout="row" fxLayoutGap="10px" *ngIf="'Not exist' != element.status">
                    <div fxLayout="column" *ngIf="element.status == 'Confirmed'" style="position: relative;top: 5px;">
                      <mat-slide-toggle (change)="onValChange($event, element.ID, element.C_ID)" [checked]="element.action"
                        class="slider">
                      </mat-slide-toggle>
                      <span class="toggle-text">{{element.action == true ?'Enabled':'Disabled'}}</span>
                    </div>
                    <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                      (click)="approve(element, 'External')"
                      style="position: relative; top: 10px; display: flex; align-items: center;">
                      <img src="../../assets/images/Admin-Approve.png" width="15px">
                      <p style="
                      font-style: normal;
                      font-weight: normal;
                      font-size: 12px;color: rgba(4, 165, 133, 0.6);">Approve</p>
                    </div>
                    <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                      (click)="rejectUser(element, 'External')"
                      style="position: relative; top: 10px; left: 0px; display: flex; align-items: center">
                      <img src="../../assets/images/reject-red.png" width="15px">
                      <p style="
                      font-style: normal;
                      font-weight: normal;
                      font-size: 12px;color: rgba(237, 47, 69, 0.6);">Reject</p>
                    </div>
                    <div fxLayout="column" fxLayoutGap="3px" (click)="editAdvertiser(element)"
                      style="position: relative; left: 5px; top: 10px;">
                      <img src="../assets/images/edit-icon.svg" width="13px">
                      <p style="
                      font-style: normal;
                      font-weight: normal;
                      font-size: 12px;color: rgba(0, 0, 0, 0.6);">Edit</p>
                    </div>
                    <div fxLayout="column" #slide *ngIf="element.status == 'Confirmed'" style="position: relative;top: 5px;">
                      <mat-slide-toggle  [disabled]="(element.action == false)" (change)="onValAdminChange($event, element , i)" [checked]="element.isAdmin"
                        class="slider">
                        <!-- <mat-slide-toggle [disabled]="(element.action == false ||  (isChecked == false && currentIndex == i))" (change)="onValAdminChange($event, element , i)" [checked]="element.isAdmin"
                        class="slider"> -->
                      </mat-slide-toggle>
                      <span class="toggle-text">{{element.isAdmin == true ?'Admin':'Non-Admin'}}</span>
                      <!-- <span class="toggle-text">{{element.isAdmin == true || (isAdmin == true && adminIndex == i) ?'Admin':'Non-Admin'}}</span> -->
                    </div>
                  </div>
                  <div fxLayout="row" style="position: relative;" *ngIf="'Not exist' == element.status">
                    <button class="create_company" (click)="createCompany(element)">Create company</button>
                  </div>
                </td>
              </ng-container>
  
              <tr mat-header-row *matHeaderRowDef="displayedColumns2; sticky: true"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns2;">
              </tr>
            </div>
          </table>
        </div>
      </mat-card>
      <mat-paginator (page)="changePage($event)" [pageSize]="10" [pageIndex]="0" *ngIf="!noData"></mat-paginator> 
      <span class="label" *ngIf="!noData"> {{rangeLabel}} of {{totalCount}}</span> 
      <div class="text-message" *ngIf="noData">
        <span>
          <br>
          No Data available ....
          <br>
        </span>
      </div> 
    </mat-tab>
  </mat-tab-group>