import { AfterViewInit, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import * as d3 from 'd3';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';
import html2canvas from 'html2canvas';

@Component({
  selector: 'app-complaint-type-chart',
  templateUrl: './complaint-type-chart.component.html',
  styleUrls: ['./complaint-type-chart.component.scss']
})
export class ComplaintTypeChartComponent implements OnInit, AfterViewInit, OnDestroy {

  loading: boolean = true;
  data: any[] = [];
  svg: any;
  tooltip: any;
  margin = { top: 20, right: 20, bottom: 60, left: 30 };
  width!: number;
  height!: number;
  @ViewChild('complaint_type', { static: true }) chartContainer!: ElementRef;
  colors = [
    '#F89E1B', // General Public
    '#2E5984', // Industry Member
    '#83159E', // Government Body
    '#35A5DB', // Consumer Org
    '#ED2F45', // Suo Moto
    '#04A585', // Suo Moto NAMS (TAMS)
    '#000000'  // Suo Moto NAMS (Reech)
  ];

  constructor(
    private el: ElementRef,
    private cs: ComplaintsService,
    private notify: NotificationService
  ) { }

  ngOnInit(): void { }

  ngAfterViewInit(): void {
    this.getChartData();
  }

  ngOnDestroy() {
    d3.select('body').selectAll('.chart-tooltip').remove();
    if (this.svg) this.svg.selectAll('*').remove();
  }

  getChartData() {
    this.cs.getMasterData().subscribe(res => {
      this.data = [
        { category: 'General Public', value: 80 },
        { category: 'Industry Member', value: 70 },
        { category: 'Government Body', value: 20 },
        { category: 'Consumer Org.', value: 60 },
        { category: 'Suo Moto', value: 82 },
        { category: 'Suo Moto NAMS (TAMS)', value: 75 },
        { category: 'Suo Moto NAMS (Reech)', value: 95 }
      ];
      this.loading = false;
      this.drawChart();
    }, err => {
      this.loading = false;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  drawChart() {
    const element = this.chartContainer.nativeElement;
    this.width = element.offsetWidth - this.margin.left - this.margin.right;
    let containerHeight = element.offsetHeight;
    if (!containerHeight || containerHeight < 200) {
      containerHeight = Math.round(this.width);
    }
    this.height = containerHeight - this.margin.top - this.margin.bottom;

    d3.select(element).select('svg').remove();
    this.svg = d3.select(element)
      .append('svg')
      .attr('width', '100%')
      .attr('height', this.height + this.margin.top + this.margin.bottom)
      .attr('viewBox', `0 0 ${this.width + this.margin.left + this.margin.right} ${this.height + this.margin.top + this.margin.bottom}`)
      .attr('preserveAspectRatio', 'xMinYMin meet')
      .append('g')
      .attr('transform', `translate(${this.margin.left},${this.margin.top})`);

    const x = d3.scaleBand()
      .domain(this.data.map(d => d.category))
      .range([0, this.width])
      .padding(0.3);

    const y = d3.scaleLinear()
      .domain([0, d3.max(this.data, d => d.value)!])
      .nice()
      .range([this.height, 0]);

    // X-axis styling
    this.svg.append('g')
      .attr('transform', `translate(0,${this.height})`)
      .call(d3.axisBottom(x).tickSize(0))
      .selectAll('text')
      .style('font-size', '10px')
      .style('fill', '#0000008C')
      .style('text-anchor', 'middle')
      .attr('dy', '2em')
      .each(function () {
        const self = d3.select(this);
        const text = self.text();
        const words = text.split(/\s+/);
        self.text('');
        words.forEach((word, i) => {
          self.append('tspan')
            .text(word)
            .attr('x', 0)
            .attr('dy', i === 0 ? 0 : 14); // spacing between lines
        });
      });

    // Remove default X-axis line
    this.svg.selectAll('.domain').attr('stroke', '#e0e0e0');

    // Y-axis styling
    this.svg.append('g')
      .call(d3.axisLeft(y).ticks(5).tickSize(0))
      .selectAll('text')
      .style('font-size', '10px')
      .style('fill', '#0000008C')
      .attr('dx', '-0.5em');
    this.svg.selectAll('.tick line').attr('stroke', '#00000026')
      .attr('stroke-dasharray', '0');
    this.svg.selectAll('.domain').attr('stroke', '#e0e0e0');

    // Tooltip
    if (!d3.select('body').select('.chart-tooltip').node()) {
      this.tooltip = d3.select('body').append('div')
        .attr('class', 'chart-tooltip')
        .style('position', 'absolute')
        .style('padding', '6px 10px')
        .style('background', '#fff')
        .style('border', '1px solid #ccc')
        .style('border-radius', '4px')
        .style('box-shadow', '0px 2px 6px rgba(0,0,0,0.2)')
        .style('font-size', '10px')
        .style('opacity', 0)
        .style('pointer-events', 'none')
        .style('z-index', '9999');
    }
    else {
      this.tooltip = d3.select('body').select('.chart-tooltip');
    }

    // Bars with animation
    this.svg.selectAll('.bar')
      .data(this.data)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('x', (d: any) => x(d.category)!)
      .attr('y', this.height)
      .attr('width', x.bandwidth())
      .attr('height', 0)
      .attr('fill', (d, i) => this.colors[i])
      .on('mouseover', (event: MouseEvent, d: any) => {
        this.tooltip.transition().duration(200).style('opacity', 0.95);
        this.tooltip.html(`<strong>${d.category}</strong>: ${d.value}`)
          .style('left', (event.pageX + 10) + 'px')
          .style('top', (event.pageY - 28) + 'px');
      })
      .on('mousemove', (event: MouseEvent) => {
        this.tooltip
          .style('left', (event.pageX + 10) + 'px')
          .style('top', (event.pageY - 28) + 'px');
      })
      .on('mouseout', () => {
        this.tooltip.transition().duration(200).style('opacity', 0);
      })
      .transition()
      .duration(1000)
      .attr('y', (d: any) => y(d.value))
      .attr('height', (d: any) => this.height - y(d.value));
  }

  exportPNG() {
    html2canvas(this.chartContainer.nativeElement).then(canvas => {
      const link = document.createElement('a');
      link.download = 'chart.png';
      link.href = canvas.toDataURL();
      link.click();
    });
  }

}