<div class="common-toolbar-mobile" style="width: 100%; height: auto;" *ngIf="isMobile">
  <app-mobile-header></app-mobile-header>
</div>
<span class="dashboard-admin-heading" *ngIf="isMobile">
  <p style="text-align: center;margin-top: 20px;margin-bottom: 3px;"> Welcome {{userName}}!</p>
</span>
<div class="dasboard-subheading" *ngIf="isMobile">
  <p style="text-align: center;">Dear ASCI Officers, please move to a larger device for seamlessly accessing all
    functionalities of the system</p>
</div>
<div class="dashboard-container" *ngIf="isMobile">
</div>

<app-icons *ngIf="!isMobile"></app-icons>
<!-- <app-heading [pagename]="pagename"></app-heading> -->
<div class="calendar-container" *ngIf="!isMobile">
  <div class="common-toolbar" fxLayout="row" style="width: 100%;height: 50px;">
    <div class="heading-container">
      <app-heading [pagename]="pagename"></app-heading>
    </div>
    <div class="options-container">
      <app-toolbar-options></app-toolbar-options>
    </div>
  </div>



  <div class="manage">
    <!-- <mat-toolbar class="toolbar1">Calendar
        <span class="fill-remaining-space"></span>
        <button mat-raised-button style="border-radius: 50%; width: 20px;"><mat-icon>search</mat-icon></button>
        <button mat-raised-button style="margin:20px ;border-radius: 50%;color: red;"><mat-icon>notifications_none</mat-icon></button>
        <button mat-raised-button style="border-radius: 45%;color: red;">Admin</button>


    </mat-toolbar> -->
    <!-- <div  class="container1">
        <mat-toolbar class="toolbar">Calendar</mat-toolbar>
      </div> -->
    <mat-divider></mat-divider>



    <mat-toolbar>
      <div class="btn-group">
        <button mat-flat-button style="border-color: aliceblue;" mwlCalendarPreviousView [view]="view"
          [(viewDate)]="viewDate" (viewDateChange)="closeOpenMonthViewDay()">
          <mat-icon>arrow_upward</mat-icon>

        </button>

        <button mat-flat-button style="border-color: aliceblue ;" mwlCalendarNextView [view]="view"
          [(viewDate)]="viewDate" (viewDateChange)="closeOpenMonthViewDay()">
          <!-- Next -->
          <mat-icon>arrow_downward</mat-icon>
        </button>


        <button mat-flat-button style="border-color: aliceblue ;font-weight: 600 !important;">
          <span class="bolder">{{ viewDate | calendarDate:(view + 'ViewTitle'):'en' }}</span>
        </button>
      </div>


      <span class="fill-remaining-space"></span>

      <div>

        <button mat-raised-button class="add-btn" (click)="addUser()"
          style="width: 140px; margin-top: 5px; margin: 5px;">
          <mat-icon style="font-size: 16px; margin-top: 4px; margin-left: -5px;">add</mat-icon>Add New Event
        </button>

        <button mat-raised-button style="margin: 5px;" mwlCalendarToday [(viewDate)]="viewDate">Today</button>
        <!-- <div class="vertical-line" style="height: 45px;"></div> -->
        <!-- <mat-divider [vertical]="true"></mat-divider> -->
        <!-- <mat-divider [vertical]="true" style="position: relative;"></mat-divider> -->
        <button mat-raised-button style="margin: 5px;" (click)="setView(CalendarView.Day)"
          [class.active]="view === CalendarView.Day">Day</button>
        <!-- <div class="vertical-line" style="height: 45px;"></div> -->
        <!-- <mat-divider [vertical]="true"></mat-divider> -->
        <button mat-raised-button style="margin: 5px;" (click)="setView(CalendarView.Week)"
          [class.active]="view === CalendarView.Week">Week</button>

        <button mat-raised-button style="margin: 5px;" (click)="setView(CalendarView.Month)"
          [class.active]="view === CalendarView.Month">Month</button>

        <!-- <button mat-raised-button style="margin: 5px;" >Year</button> -->
      </div>


    </mat-toolbar>

    <br>
    <!-- [viewDate]=whole calendar will be missing -->
    <!-- [events]=events will be missing on dates -->
    <!-- [refresh]="refresh" -->
    <!-- [activeDayIsOpen]=collapsing bar will not be shown -->
    <!-- (dayClicked)=no other days can be clicked if missing"
           (eventClicked)=events cannot be clicked and their info will not be shown
           (eventTimesChanged)=events cannot be changed to other days or dates" -->
    <div [ngSwitch]="view">
      <mwl-calendar-month-view *ngSwitchCase="CalendarView.Month" [viewDate]="viewDate" [events]="events"
        [refresh]="refresh" [activeDayIsOpen]="activeDayIsOpen" (dayClicked)="dayClicked($event.day)"
        (eventClicked)="handleEvent('Clicked', $event.event)" (eventTimesChanged)="eventTimesChanged($event)">
      </mwl-calendar-month-view>
      <!-- "addUser()" -->
      <!-- <mwl-calendar-month-view
          *ngSwitchCase="CalendarView.Month"
          [viewDate]="viewDate"
          [events]="events"
          [refresh]="refresh"
          [activeDayIsOpen]="addUser()"
          (dayClicked)="dayClicked($event.day)"
          (eventClicked)="handleEvent('Clicked', $event.event)"
          (eventTimesChanged)="eventTimesChanged($event)"
        >
        </mwl-calendar-month-view> -->
      <mwl-calendar-week-view *ngSwitchCase="CalendarView.Week" [viewDate]="viewDate" [events]="events"
        [refresh]="refresh" (eventClicked)="handleEvent('Clicked', $event.event)"
        (eventTimesChanged)="eventTimesChanged($event)">
      </mwl-calendar-week-view>
      <mwl-calendar-day-view *ngSwitchCase="CalendarView.Day" [viewDate]="viewDate" [events]="events"
        [refresh]="refresh" (eventClicked)="handleEvent('Clicked', $event.event)"
        (eventTimesChanged)="eventTimesChanged($event)">
      </mwl-calendar-day-view>
    </div>





    <!-- Everything you see below is just for the demo, you don't need to include it in your app -->

    <!--<br /><br /><br />

      <h3>
        Edit events
        <button class="btn btn-primary float-right" (click)="addEvent()">
          Add new
        </button>
        <div class="clearfix"></div>
      </h3>

      <div class="table-responsive">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th>Title</th>
              <th>Primary color</th>
              <th>Secondary color</th>
              <th>Starts at</th>
              <th>Ends at</th>
              <th>Remove</th>
            </tr>
          </thead>

          <tbody>
            <tr *ngFor="let event of events">
              <td>
                <input
                  type="text"
                  class="form-control"
                  [(ngModel)]="event.title"
                  (keyup)="refresh.next()"
                />
              </td>
              <td>
                <input
                  type="color"
                  [(ngModel)]="event.color.primary"
                  (change)="refresh.next()"
                />
              </td>
              <td>
                <input
                  type="color"
                  [(ngModel)]="event.color.secondary"
                  (change)="refresh.next()"
                />
              </td>
              <td>
                <input
                  class="form-control"
                  type="text"
                  mwlFlatpickr
                  [(ngModel)]="event.start"
                  (ngModelChange)="refresh.next()"
                  [altInput]="true"
                  [convertModelValue]="true"
                  [enableTime]="true"
                  dateFormat="Y-m-dTH:i"
                  altFormat="F j, Y H:i"
                  placeholder="Not set"
                />
              </td>
              <td>
                <input
                  class="form-control"
                  type="text"
                  mwlFlatpickr
                  [(ngModel)]="event.end"
                  (ngModelChange)="refresh.next()"
                  [altInput]="true"
                  [convertModelValue]="true"
                  [enableTime]="true"
                  dateFormat="Y-m-dTH:i"
                  altFormat="F j, Y H:i"
                  placeholder="Not set"
                />
              </td>
              <td>
                <button class="btn btn-danger" (click)="deleteEvent(event)">
                  Delete
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div> -->

    <ng-template #modalContent let-close="close">
      <div class="modal-header">
        <h5 class="modal-title">Event action occurred</h5>
        <button type="button" class="close" (click)="close()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div>
          Action:
          <pre>{{ modalData?.action }}</pre>
        </div>
        <div>
          Event:
          <pre>{{ modalData?.event | json }}</pre>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" (click)="close()">
          OK
        </button>
      </div>
    </ng-template>

  </div>
</div>