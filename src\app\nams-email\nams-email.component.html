<form [formGroup]="namsEmailForm">
    <mat-toolbar class="mail-toolbar">
        <div fxLayout="row">
            <div fxLayout="column">
                <div fxFlex="55%">
                    <div fxLayout="row" fxLayoutGap="10px" style="height: 30px; font-size: 13px; margin-bottom: 20px;">
                        <div>
                            <input matInput value="To" class="email-to-button" readonly>
                        </div>
                        <div class="to-field" [ngStyle]="{'color': namsEmailForm.controls['TO_EMAIL_ID'].touched && emailList.length == 0 ? '#ff0000' : 'rgba(0, 0, 0, 0.4)' }">
                            <mat-form-field class="to-field" appearance="standard" style="bottom: 24px;">
                                <mat-chip-list #chipLists aria-label="emailId selection" class="input-control">
                                    <mat-chip *ngFor="let item of emailList; let i=index" [selectable]="true"
                                        [removable]="removable" (removed)="removeEmail(item)"
                                        style="font-size: 13px;">
                                        {{item}}
                                        <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
                                    </mat-chip>
                                    <input matInput [matAutocomplete]="autoUser" [matChipInputFor]="chipLists"
                                        [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                                        [matChipInputAddOnBlur]="true" (matChipInputTokenEnd)="addEmail($event)"
                                        formControlName="TO_EMAIL_ID"
                                        style="word-wrap: break-word; word-break: break-all; padding-left: 2%; color: black;">
                                    <mat-autocomplete #autoUser="matAutocomplete" (optionSelected)="onSelectionChange($event)">
                                        <mat-option *ngFor="let user of internalEmails;" [value]="user.EMAIL_ID" style="font-style: normal; font-weight: normal;">
                                            {{user.EMAIL_ID}}
                                        </mat-option>
                                    </mat-autocomplete>
                                </mat-chip-list>
                            </mat-form-field>
                            <mat-error class="email-error-msg" *ngIf=" namsEmailForm.controls['TO_EMAIL_ID'].touched && emailList.length == 0">
                                Email is required
                            </mat-error>
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="10px" style="height: 30px; font-size: 13px; margin-bottom: 20px;">
                        <div>
                            <input matInput value="Cc" class="email-to-button" readonly>
                        </div>
                        <div class="to-field">
                            <mat-form-field class="to-field" appearance="standard" style="bottom: 24px;color: rgba(0, 0, 0, 0.4);">
                                <mat-chip-list #chipListsForCc aria-label="emailIdCc selection" class="input-control">
                                    <mat-chip *ngFor="let item of emailCcList; let i=index" [selectable]="true"
                                        [removable]="removable" (removed)="removeCcEmail(item)"
                                        style="font-size: 13px;">
                                        {{item}}
                                        <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
                                    </mat-chip>
                                    <input matInput [matAutocomplete]="autoCcUser" [matChipInputFor]="chipListsForCc"
                                        [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                                        [matChipInputAddOnBlur]="true" (matChipInputTokenEnd)="addCcEmail($event)"
                                        formControlName="CC_EMAIL_ID"
                                        style="word-wrap: break-word; word-break: break-all; padding-left: 2%; color: black;">
                                    <mat-autocomplete #autoCcUser="matAutocomplete" (optionSelected)="onSelectionChangeCc($event)">
                                        <mat-option *ngFor="let user of internalEmails;" [value]="user.EMAIL_ID" style="font-style: normal; font-weight: normal;">
                                            {{user.EMAIL_ID}}
                                        </mat-option>
                                    </mat-autocomplete>
                                </mat-chip-list>
                            </mat-form-field>
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="10px" style="height: 30px; font-size: 13px; margin-bottom: 20px;">
                        <div>
                            <input matInput value="Bcc" class="email-to-button" readonly>
                        </div>
                        <div class="to-field">
                            <mat-form-field class="to-field" appearance="standard" style="bottom: 23px;color: rgba(0, 0, 0, 0.4);">
                                <mat-chip-list #chipListsForBcc aria-label="emailIdBcc selection" class="input-control">
                                    <mat-chip *ngFor="let item of emailBccList; let i=index" [selectable]="true"
                                        [removable]="removable" (removed)="removeBccEmail(item)"
                                        style="font-size: 13px;">
                                        {{item}}
                                        <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
                                    </mat-chip>
                                    <input matInput [matAutocomplete]="autoBccUser" [matChipInputFor]="chipListsForBcc"
                                        [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                                        [matChipInputAddOnBlur]="true" (matChipInputTokenEnd)="addBccEmail($event)"
                                        formControlName="BCC_EMAIL_ID"
                                        style="word-wrap: break-word; word-break: break-all; padding-left: 2%; color: black;">
                                    <mat-autocomplete #autoBccUser="matAutocomplete" (optionSelected)="onSelectionChangeBcc($event)">
                                        <mat-option *ngFor="let user of internalEmails;" [value]="user.EMAIL_ID" style="font-style: normal; font-weight: normal;">
                                            {{user.EMAIL_ID}}
                                        </mat-option>
                                    </mat-autocomplete>
                                </mat-chip-list>
                            </mat-form-field>
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="15px" style="height: 30px; font-size: 13px; margin-bottom: 20px;">
                        <div>
                            <input matInput value="Subject" class="email-to-button" readonly>
                        </div>
                        <div class="subject-field">
                            <mat-form-field class="sub-field" appearance="standard" style="width: 205px;">
                                <input matInput formControlName="TITLE_CASEID" style="color: rgb(121, 119, 119); margin-bottom: 0px;" readonly autocomplete="off">
                            </mat-form-field>
                            <mat-form-field class="sub-field" appearance="standard" style="width: 383px;">
                                <input matInput formControlName="TITLE" style="color: black; margin-bottom: 0px;" autocomplete="off">
                            </mat-form-field>
                            <mat-error *ngIf="namsEmailForm.controls['TITLE'].touched && namsEmailForm.controls['TITLE'].errors?.required" style="font-size: 11px; margin-top: -25px;">
                                Subject is required
                            </mat-error>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-left: 8%;">
                <button mat-flat-button class="send-button" (click)="sendEmail(namsEmailForm.value)" [disabled]="uploadFile || this.namsEmailForm.controls['TITLE'].invalid || this.namsEmailForm.controls['BODY'].invalid || emailList.length == 0">
                    <img src="../../assets/images/mail-send.png">
                    <div class="send-text">Send</div>
                </button>
            </div>
            <div style="margin-left: 8%;">
                <button mat-icon-button class="close-btn" mat-dialog-close>
                    <mat-icon style="margin-bottom: 10px;">close</mat-icon>
                </button>
            </div>
        </div>
    </mat-toolbar>
    <mat-divider style="margin-bottom: 6px;"></mat-divider>
    <div fxLayout="row" style="margin-bottom: 15px; margin-left: 14px;">
        <div fxFlex="15%"> 
            <button class="template-btn" (click)="viewTemplate('select')" *ngIf="(!selectTemplate || uploadFile) && (userData.roleId == 1 || userData.roleId == 2 || userData.roleId == 3 || userData.roleId == 6)">
                <span class="bolder">{{!isTemplate? 'Use template': 'Cancel'}}</span> 
            </button>
            <button class="change-btn" (click)="changeTemplate()" *ngIf="(selectTemplate && !isTemplate && !uploadFile) && (userData.roleId == 1 || userData.roleId == 2 || userData.roleId == 3 || userData.roleId == 6)">
                <span class="bolder">Change template</span>
            </button>
        </div> 
        <div fxFlex="40%"></div>
        <button type="button" class="attach_btn" (click)="uploadAttachment()" *ngIf="((isAttachFile && !isTemplate) || selectTemplate) && !uploadFile && (userData.roleId == 1 || userData.roleId == 2 || userData.roleId == 3 || userData.roleId == 6)">
            <img src="../../assets/images/attachment-icon.png">
        </button>
        <button type="button" class="attach_btn1" (click)="uploadAttachment()" *ngIf="((isAttachFile && !isTemplate) || selectTemplate) && (userData.roleId == 4 || userData.roleId == 5 || userData.roleId == 7 || userData.roleId == 8)">
            <img src="../../assets/images/attachment-icon.png">
        </button>
        <div fxFlex="45%" fxLayoutAlign="end end">
            <button class="clear-btn" (click)="viewTemplate('clear')" *ngIf="(selectTemplate && !isTemplate && !uploadFile) && (userData.roleId == 1 || userData.roleId == 2 || userData.roleId == 3 || userData.roleId == 6)">
                <span class="bolder">Clear all</span>
            </button>
            <button class="save-btn" *ngIf="(selectTemplate || !isTemplate) && (userData.roleId == 1 || userData.roleId == 2 || userData.roleId == 3 || userData.roleId == 6)" (click)="saveTemplate(namsEmailForm.value)">
                <span class="bolder">Save as new template</span>
            </button>
        </div> 
    </div>
    <div id="output"  *ngIf="!uploadFile" fxLayout="column"  [ngStyle]="{'border': namsEmailForm.controls['BODY'].touched && namsEmailForm.controls['BODY'].errors?.required ? ' 2.3px solid #ff0000' : '1px solid #ccc;' }">
        <div class="template-file" *ngIf="isTemplate" style="padding-left: 20px;">
            <div fxLayout="row wrap" *ngIf="!selectTemplate" style="padding-top: 20px;">
                <div fxLayout="column" fxLayoutGap="10px" fxFlex="20%" style="padding-bottom: 15px;" *ngFor="let temp of emailTemplates">
                    <div>
                        <img src="../../assets/images/template.png" style="cursor: pointer;" (click)="openTemplate(temp.ID)">
                    </div>
                    <div style="align-content: center;">
                        <div contenteditable="false">{{temp.TITLE}}</div>
                    </div>
                </div>
            </div>
        </div>
        <quill-editor class="textarea-field" *ngIf="!isTemplate || selectTemplate" [ngClass]="{'textarea-field1' : ((userData.roleId == 1 || userData.roleId == 2 || userData.roleId == 3 || userData.roleId == 6) && selectedFiles.length != 0), 'textarea-field2' : ((userData.roleId == 4 || userData.roleId == 5 || userData.roleId == 7 || userData.roleId == 8) && selectedFiles.length != 0)}"
            placeholder="" 
            [modules]="quillConfiguration"
            formControlName="BODY" autocomplete="off" style="text-align:justify; text-justify: inter-word;">
        </quill-editor>
        <!-- ,'red-border-class': namsEmailForm.controls['BODY'].touched && namsEmailForm.controls['BODY'].errors?.required -->
        <!-- <div class="template-file" style="padding-bottom: 10px;">
            <textarea matInput formControlName="BODY" id="output" contenteditable="true" autocomplete="off" style="height: 180px;"></textarea>
        </div> -->
        <div class="footer-content" style="position: relative; padding-left: 0.5rem;" *ngIf="(!isTemplate || selectTemplate) && (userData.roleId == 1 || userData.roleId == 2 || userData.roleId == 3 || userData.roleId == 6)">
            <img src="../../assets/images/Logo-asci.png" style="width: 80px; height: 20px;"><br>
            <span class="address" style="font-size: 10px; font-weight: bolder;">402, A Wing, Aurus Chambers, S.S. Amrutwar Marg, Worli, Mumbai - 400013</span><br>
            <img src="../assets/images/signature-promotion.png" style="height: 30px; cursor: pointer;" (click)="viewAcademyDetails()">
        </div>
        <mat-divider style="margin-bottom: 4px;" *ngIf="selectedFiles.length != 0 && !isTemplate"></mat-divider>
        <div fxLayout="row" class="doc-body1" *ngIf="selectedFiles.length != 0 && !isTemplate">
            <div class="doc-fxrow-container1" fxLayout="row wrap" fxLayoutAlign="flex-start">
                <div fxFlex fxLayout="column" fxLayoutGap="10px">
                    <div fxLayout="row wrap">
                        <div *ngFor="let file of selectedFiles;" style="padding: 5px;" fxLayout="column">
                            <div fxLayout="row" fxLayoutGap="5px"
                                style="vertical-align: middle; align-items: center; border: 1px solid #0088CB; height: 40px; width: 200px; border-radius: 4px; background-color: rgba(0, 136, 203, 0.2);">
                                <div style="height: 50px; width: 50px; border-radius: 4px; position: relative;">
                                    <img src="../../assets/images/img.png" style="margin: 15px 15px;">
                                </div>
                                <div fxLayout="column">
                                    <div style="color: #000000; font-size: 12px; background: transparent; cursor: pointer;" class="doc-caption" (click)="preview(file.path)">
                                        <p mat-line matTooltip="{{file.filename}}">{{file.filename}}</p>
                                    </div>
                                </div>
                                <div style="height: 50px; width: 50px; border-radius: 4px; position: relative; cursor: pointer;" (click)="removeUpload(file)">
                                    <img src="../../assets/images/Trash-icon.svg" style="margin: 18px 0px;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <mat-error class="error-msg" *ngIf="namsEmailForm.controls['BODY'].touched && namsEmailForm.controls['BODY'].errors?.required">
        Body is required
    </mat-error>
    <div *ngIf="uploadFile" style="border: 1px solid #ccc;" class="upload-screen">
        <div class="upload-heading">
            Uploaded documents
        </div>
        <mat-divider style="margin-top: 10px;"></mat-divider>
        <div class="doc-body" fxLayout="row">
            <div class="doc-fxrow-container" fxLayout="row wrap" fxLayoutAlign="flex-start">
                <div fxFlex fxLayout="column" fxLayoutGap="10px">
                    <div fxLayout="row wrap">
                        <div class="add-file-container" *ngIf="(userData.roleId == 4 || userData.roleId == 5 || userData.roleId == 7 || userData.roleId == 8)">
                            <div class="dropzone" fileDragDrop (filesChangeEmiter)="onFileChangeAdvertiser($event)">
                                <div class="addfile-text-wrapper">
                                    <div class="upload-scope-container">
                                        <input type="file" name="file" id="file" (change)="onFileChangeAdvertiser($event)"
                                            accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt,.jpeg,.png,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv"
                                            multiple>
                                        <label class="upload-label" for="file" fxLayout="row" fxLayoutAlign="center center">
                                            <img src="../../assets/images/upload-doc.svg" style="margin-right: 5px;">
                                            <span class="add-textLink" style="font-size: 15px;">Upload new document</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="add-file-container" *ngIf="userData.roleId == 1 || userData.roleId == 2 || userData.roleId == 3 || userData.roleId == 6">
                            <div class="dropzone" fileDragDrop (filesChangeEmiter)="onFileChangeInternal($event)">
                                <div class="addfile-text-wrapper">
                                    <div class="upload-scope-container">
                                        <input type="file" name="file" id="file" (change)="onFileChangeInternal($event)"
                                            accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt,.jpeg,.png,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv"
                                            multiple>
                                        <label class="upload-label" for="file" fxLayout="row" fxLayoutAlign="center center">
                                            <img src="../../assets/images/upload-doc.svg" style="margin-right: 5px;">
                                            <span class="add-textLink" style="font-size: 15px;">Upload new document</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div *ngIf="userData.roleId == 1 || userData.roleId == 2 || userData.roleId == 3 || userData.roleId == 6">
                            <div *ngFor="let file of complainantFiles; let ind = index" fxFlex="30" fxFlex.md="30" fxFlex.sm="20" fxFlex.xs="100"
                                fxLayout="column" style="padding: 5px;">
                                <div fxLayout="row" fxLayoutGap="5px"
                                    style="vertical-align: middle; align-items: center; border: 1px solid #aaa; height: 51px; border-radius: 4px;">
                                    <div style="height: 50px; width: 50px; background: #3A3A3A; border-radius: 4px; position: relative;">
                                        <img src="../assets/images/doc_video.svg" style="margin: 20px 16px;">
                                    </div>
                                    <div fxLayout="column">
                                        <div style="color: #000000; font-size: 12px" class="doc-caption">
                                            <p mat-line matTooltip="{{file.ATTACHMENT_SOURCE_NAME}}">{{file.ATTACHMENT_SOURCE_NAME}}</p>
                                            <br><span style="margin-left: 6px;">{{file.CREATED_DATE| date: 'dd/MM/yyyy h:mm a'}}</span>
                                        </div>
                                    </div>
                                    <div class="removeIcon" style="position: relative; left: 75px;">
                                        <mat-checkbox (change)="selectDoc($event.checked, file, file.ID)" [checked]="attachId.includes(file.ID)"></mat-checkbox>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div *ngFor="let file of advertiserFiles; let ind = index" fxFlex="30" fxFlex.md="30" fxFlex.sm="20" fxFlex.xs="100"
                            fxLayout="column" style="padding: 5px;">
                            <div fxLayout="row" fxLayoutGap="5px"
                                style="vertical-align: middle; align-items: center; border: 1px solid #aaa; height: 51px; border-radius: 4px;">
                                <div style="height: 50px; width: 50px; background: #3A3A3A; border-radius: 4px; position: relative;">
                                    <img src="../assets/images/doc_video.svg" style="margin: 20px 16px;">
                                </div>
                                <div fxLayout="column">
                                    <div style="color: #000000; font-size: 12px" class="doc-caption">
                                        <p mat-line matTooltip="{{file.ATTACHMENT_SOURCE_NAME}}">{{file.ATTACHMENT_SOURCE_NAME}}</p>
                                        <br><span style="margin-left: 6px;">{{file.CREATED_DATE| date: 'dd/MM/yyyy h:mm a'}}</span>
                                    </div>
                                </div>
                                <div class="removeIcon" style="position: relative; left: 75px;">
                                    <mat-checkbox (change)="selectDoc($event.checked, file, file.ID)" [checked]="attachId.includes(file.ID)"></mat-checkbox>
                                </div>
                            </div>
                        </div>
                        <div *ngFor="let file of internalFiles; let ind = index" fxFlex="30" fxFlex.md="30" fxFlex.sm="20" fxFlex.xs="100"
                            fxLayout="column" style="padding: 5px;">
                            <div fxLayout="row" fxLayoutGap="5px"
                                style="vertical-align: middle; align-items: center; border: 1px solid #aaa; height: 51px; border-radius: 4px;">
                                <div style="height: 50px; width: 50px; background: #3A3A3A; border-radius: 4px; position: relative;">
                                    <img src="../assets/images/doc_video.svg" style="margin: 20px 16px;">
                                </div>
                                <div fxLayout="column">
                                    <div style="color: #000000; font-size: 12px" class="doc-caption">
                                        <p mat-line matTooltip="{{file.ATTACHMENT_SOURCE_NAME}}">{{file.ATTACHMENT_SOURCE_NAME}}</p>
                                        <br><span style="margin-left: 6px;">{{file.CREATED_DATE| date: 'dd/MM/yyyy h:mm a'}}</span>
                                    </div>
                                </div>
                                <div class="removeIcon" style="position: relative; left: 75px;">
                                    <!-- <button mat-button matSuffix mat-icon-button aria-label="Clear"
                                        (click)="removeFile(file.ID, file.ATTACHMENT_SOURCE)">
                                        <img src="../assets/images/Trash-icon.svg" style="border: 1px;">
                                    </button> -->
                                    <mat-checkbox (change)="selectDoc($event.checked, file, file.ID)" [checked]="attachId.includes(file.ID)"></mat-checkbox>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <mat-divider style="margin-top: 10px;"></mat-divider>
        <div class="footer" fxLayout="column" fxLayoutAlign="end end">
            <div class="footer-btns" fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="20px">
                <div>
                    <button mat-button class="cancel-btn" (click)="cancelUpload()">Cancel</button>
                </div>
                <div>
                    <button mat-button class="upload-btn" (click)="uploadDocs()">Upload</button>
                </div>
                <!-- <div class="upload-btn" fileDragDrop (filesChangeEmiter)="onFileChange($event)">
                    <div class="flex" style="text-align: center;">
                        <input type="file" name="file" id="file" (change)="onFileChange($event.target.files)" multiple>
                        <label for="file" class="flex" style="text-align: center;padding: 6px 15px;">
                            <span>Upload</span>
                        </label>
                    </div>
                </div> -->
            </div>
        </div>
    </div>
</form>