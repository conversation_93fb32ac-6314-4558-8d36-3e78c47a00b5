<app-icons></app-icons>

<app-mat-spinner-overlay overlay="true" *ngIf="loading">
</app-mat-spinner-overlay>

<div class="inbox-page-container" fxLayout="column" fxLayoutGap="0.5%">

  <div class="common-toolbar" fxLayout="row" style="width: 100%;height: 50px;">
    <div class="heading-container">
      <app-heading [pagename]="pagename"></app-heading>
    </div>
    <div class="options-container">
      <app-toolbar-options></app-toolbar-options>
    </div>
  </div>

  <div class="inbox-body" fxLayout="row" fxLayoutGap="1%">
    <div class="tab-div">
      <mat-tab-group class="tab-group" [(selectedIndex)]="selectedTab" (selectedTabChange)="onTabChanged($event)">
        <mat-tab class="nams-tab">
          <ng-template mat-tab-label>
            <span class="nams-label">NAMS</span>
          </ng-template>

          <div class="select-header" fxLayout="row">
            <div class="select-chkb-container" fxLayoutAlign="start center">
              <mat-checkbox class="select-chkb" [(ngModel)]="select_all" (ngModelChange)="onSelectAll($event)">
                Select all
              </mat-checkbox>
            </div>
            <span style="flex: 1 1 auto;"></span>
            <div class="footer-right-btn" fxLayoutAlign="end center">
              <button mat-stroked-button class="header-btn" (click)="deleteComplaints()">Delete</button>
            </div>
          </div>

          <mat-nav-list class="nams-list">
            <div class="complaint-list" infiniteScroll [infiniteScrollDistance]="2" [infiniteScrollThrottle]="300" 
              (scrolled)="onScrollDown()" [scrollWindow]="false">
              <mat-list-item class="nams-list-container" *ngFor="let nams of namsComplaints">
                <div class="nams-item-container" fxLayout="row" fxLayoutGap="8px">
                  <div class="chkb-container" fxLayoutAlign="start start">
                    <mat-checkbox class="chkb-list" [(ngModel)]="nams.isselected"
                      (change)="selectComplaint($event.checked, nams.ID)"></mat-checkbox>
                  </div>
                  <div fxLayout="column" (click)="getComplaintDetails(nams.ID)" style="width: 290px;">
                    <div class="nams-head-container" fxLayout="row" fxLayoutGap="1px">
                      <!-- <div *ngFor="let source of adSource">
                                              <div class="medium-container" *ngIf="source.ID == nams.ADVERTISEMENT_SOURCE_ID">
                                                  <span class="medium-list">{{source.ADVERTISEMENT_SOURCE_NAME}}</span>
                                              </div>
                                          </div> -->
                      <div class="medium-container">
                        <span class="medium-list">{{nams.MEDIA}}</span>
                      </div>
                      <div class="brand-container">
                        <div class="brand-text" matTooltip="{{nams.BRAND}}">
                          {{nams.BRAND}}
                        </div>
                      </div>
                    </div>
                    <div fxLayout="column" fxLayoutGap="1px">
                      <div class="nams-msg-container">
                        <p class="nams-msg-text" matTooltip="{{nams.COMPLAINT}}">{{nams.COMPLAINT}} </p>
                      </div>
                    </div>
                    <div fxLayoutAlign="end end">
                      <p class="nams-date-container">{{nams.CREATED_DATE | date:'dd MMM yyyy'}}</p>
                    </div>
                  </div>
                </div>
                <mat-divider [inset]="true" class="list-divider"></mat-divider>
              </mat-list-item>
            </div>
          </mat-nav-list>
        </mat-tab>

        <!-- <mat-tab class="chatbot-tab">
          <ng-template mat-tab-label>
            <span class="chatbot-label">CHATBOT</span>
          </ng-template>

          <mat-nav-list class="chatbot-list">
            <div class="complaint-list" infiniteScroll [infiniteScrollDistance]="2" [infiniteScrollThrottle]="300" 
              (scrolled)="onScrollDown()" [scrollWindow]="false">
              <mat-list-item class="chatbot-list-container" *ngFor="let chatbot of chatbotComplaints">
                <div class="chatbot-item-container" fxLayout="row" fxLayoutGap="8px">
                  <div fxLayout="column" (click)="getChatbotDetails(chatbot.ID)" style="width: 310px;">
                    <div class="chatbot-head-container" fxLayout="row">
                      <div *ngFor="let source of adSource">
                        <div class="medium-container" *ngIf="source.ID == chatbot.ADVERTISEMENT_SOURCE_ID">
                          <span class="medium-list">{{source.ADVERTISEMENT_SOURCE_NAME}}</span>
                        </div>
                      </div>
                      <div class="brand-container">
                        <div class="brand-text" matTooltip="{{chatbot.BRAND}}">
                          {{chatbot.BRAND_NAME}}
                        </div>
                      </div>
                    </div>
                    <div fxLayout="column" fxLayoutGap="1px">
                      <div class="nams-msg-container">
                        <p class="nams-msg-text" matTooltip="{{chatbot.COMPLAINT_DESCRIPTION}}">{{chatbot.COMPLAINT_DESCRIPTION}} </p>
                      </div>
                    </div>
                    <div fxLayoutAlign="end end">
                      <p class="nams-date-container" style="right: 232px;">{{chatbot.CREATED_DATE | date:'dd MMM yyyy'}}</p>
                    </div>
                  </div>
                </div>
                <mat-divider [inset]="true" class="list-divider"></mat-divider>
              </mat-list-item>
            </div>
          </mat-nav-list>
        </mat-tab> -->
      </mat-tab-group>
    </div>

    <div class="complaint-container">
      <mat-card class="comp-matcard">

        <div class="mat-card-container" fxLayout="column" fxLayoutGap="1%">
          <div class="mat-card-scroll" fxLayout="column" fxLayoutGap="3%">
            <div class="details-content-container" fxLayout="column" fxLayoutGap="2%">
              <div class="comp-head-container">
                <div class="comp-head">COMPLAINT DETAILS</div>
                <div class="divider-container" style="width: 98%;margin-left: 125px;">
                  <mat-divider class="classfy-head-divider" [inset]="true"></mat-divider>
                </div>
              </div>

              <div class="details-container" fxLayout="row" fxLayoutGap="1%">

                <div class="detail-left-container" fxLayout="column" fxLayoutGap="2px" *ngIf="first">
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Creative ID : </span>
                      <span class="detail-value">{{namsDetails.CREATIVE_ID}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Sender : </span>
                      <span *ngFor="let source of complaintSource">
                        <span *ngIf="source.ID == namsDetails.COMPLAINT_SOURCE_ID">
                          <span *ngFor="let user of userType">
                            <span *ngIf="user.ID == namsDetails.COMPLAINT_TYPE_ID">
                              <span class="detail-value">{{source.COMPLAINT_SOURCE_NAME}},
                                {{user.USER_TYPE_NAME}}</span>
                            </span>
                          </span>
                        </span>
                      </span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Medium : </span>
                      <span class="detail-value" style="text-transform: uppercase;">{{namsDetails.MEDIA}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Brand : </span>
                      <span class="detail-value">{{namsDetails.BRAND}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Complaint description :</span>
                    </p>
                  </div>
                  <div mat-line class="comp-msg-container">
                    <p class="detail-value">{{namsDetails.COMPLAINT}}</p>
                  </div>
                  <div mat-line fxLayout="row">
                    <div class="media-attribute">
                      <p class="detail-attribute">
                        <span style="word-wrap: normal;">
                          Media Link :&nbsp;
                        </span>
                      </p>
                    </div>
                    <div class="media-container">
                      <a  (click)="openNewTab(namsDetails.CREATIVE_HYPERLINK)" class="media-anchor">
                        {{namsDetails.CREATIVE_HYPERLINK}}
                      </a>
                    </div>
                  </div>
                </div>

                <div class="detail-left-container" fxLayout="column" fxLayoutGap="2px" *ngIf="!first">
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Tracking ID : </span>
                      <span class="detail-value">{{chatbotDetails.ID}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Complainant Name : </span>
                      <span class="detail-value">{{chatbotDetails.FIRST_NAME}} {{chatbotDetails.LAST_NAME}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Contact : </span>
                      <span class="detail-value">{{chatbotDetails.MOBILE}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Medium : </span>
                      <span *ngFor="let source of adSource">
                        <span *ngIf="source.ID == chatbotDetails.ADVERTISEMENT_SOURCE_ID">
                          <span class="detail-value">{{source.ADVERTISEMENT_SOURCE_NAME}}</span>
                        </span>
                      </span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Brand : </span>
                      <span class="detail-value">{{chatbotDetails.BRAND_NAME}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Complaint description : </span>
                      <span class="detail-value">{{chatbotDetails.COMPLAINT_DESCRIPTION}}</span>
                    </p>
                  </div>
                  <!-- <div mat-line class="comp-msg-container">
                                        <p class="detail-value">{{chatbotDetails.COMPLAINT_DESCRIPTION}}</p>
                                    </div> -->
                  <div mat-line fxLayout="row">
                    <div class="media-attribute">
                      <p class="detail-attribute">
                        <span style="word-wrap: normal;">
                          Media Link :&nbsp;
                        </span>
                      </p>
                    </div>
                    <div class="media-container" *ngIf="chatbotDetails.ATTACHMENT_SOURCE != ''">
                      <a (click)="openNewTab(docURL)" class="media-anchor">
                        {{showURL}}
                      </a>
                    </div>
                  </div>
                </div>

                <div class="detail-right-container" fxLayout="column" fxLayoutGap="2px" *ngIf="first">
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Advertiser : </span>
                      <span class="detail-value">{{namsDetails.ADVERTISER}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Product Category : </span>
                      <span class="detail-value">{{namsDetails.SUPER_CATEGORY}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Media Outlet : </span>
                      <span class="detail-value">{{namsDetails.MEDIA_OUTLET}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Edition : </span>
                      <span class="detail-value">{{namsDetails.EDITION}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Supplement : </span>
                      <span class="detail-value">{{namsDetails.SUPPLEMENT}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Date : </span>
                      <span class="detail-value">{{namsDetails.CREATED_DATE | date:'dd/MM/yyyy'}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p class="language-text">
                      <span class="detail-attribute">Ad Language : </span>
                      <span class="detail-value">{{namsDetails.AD_LANGUAGE}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Translation Link : </span>
                      <a style="color: #0d6efd;"  [matTooltip]="translation_link" (click)="previewLink(translation_link)">{{translation_link | slice:0: 28}}{{translation_link?.length>29? '..':''}}</a>
                    </p>
                  </div>
                </div>
                <div class="detail-right-container" fxLayout="column" fxLayoutGap="2px" *ngIf="!first">
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Email ID : </span>
                      <span class="detail-value">{{chatbotDetails.EMAIL_ID}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Profession : </span>
                      <span class="detail-value">{{chatbotDetails.PROFESSION_NAME}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Organization Name : </span>
                      <span class="detail-value">{{chatbotDetails.ORGANIZATION_NAME}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Postal Code : </span>
                      <span class="detail-value">{{chatbotDetails.PINCODE}}</span>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Date : </span>
                      <span class="detail-value">{{chatbotDetails.CREATED_DATE | date:'dd/MM/yyyy'}}</span>
                    </p>
                  </div>
                </div>
              </div>

            </div>

            <div class="classfy-content-container" fxLayout="column" fxLayoutGap="2%">
              <div class="classfy-head">
                <div class="comp-head" style="font-weight: 550;">COMPLAINT CLASSIFICATION</div>
                <div class="divider-container">
                  <mat-divider class="classfy-head-divider" [inset]="true"></mat-divider>
                </div>
              </div>
              <div class="classification-container">
                <mat-chip-list>
                  <mat-chip class="comp-chips" for="message">
                    {{!classificationTag ? '-' : classificationTag}}
                  </mat-chip>
                </mat-chip-list>
              </div>
            </div>

            <div class="classfy-container" fxLayout="column" fxLayoutGap="2px">
              <div class="detail-attribute-container">
                <p class="detail-attribute">Related complaints :</p>
              </div>
              <mat-card *ngIf="similarCompLoading"
                style="display: flex; justify-content: center; align-items: center; background: white;">
                <mat-progress-spinner color="primary" mode="indeterminate" diameter="70">
                </mat-progress-spinner>
              </mat-card>
              <div *ngIf="!similarCompLoading">
                <div *ngFor="let similarComp of similarityComplaintList; let in = index;">
                  <div class="related-name-container" fxLayout="row" fxLayoutGap="2%">
                    <h4 for="message">
                      <img src="../assets/images/arrow_circle-icon.svg" />
                      <span class="related-name" *ngIf="!matchFound">{{similarComp?.complaint_identifiation}}</span>
                      <span class="related-name" *ngIf="matchFound">{{similarComp?.prediction}} - {{similarComp?.similarity_score}}% Matching</span>
                    </h4>
                  </div>
                  <div mat-line class="related-detail-container" *ngIf="matchFound">
                    <p>
                      <span class="related-attribute" style="padding-right: 26px;">Case ID : </span>
                      <span class="related-value">{{msgvalue?.complaint_identification}}</span>
                    </p>
                  </div>
                  <div mat-line class="related-detail-container" *ngIf="matchFound">
                    <p>
                      <span class="related-attribute" style="padding-right: 26px;">Company : </span>
                      <span class="related-value">{{msgvalue?.company_name}}</span>
                    </p>
                  </div>
                  <div mat-line class="related-detail-container" *ngIf="matchFound">
                    <p>
                      <span class="related-attribute" style="padding-right: 37px;">Brand : </span>
                      <span class="related-value">{{similarComp?.brand_name}}</span>
                    </p>
                  </div>
                  <div mat-line class="related-detail-container" *ngIf="matchFound">
                    <p>
                      <span class="related-attribute" style="padding-right: 37px;">Product : </span>
                      <span class="related-value">{{similarComp?.product_name}}</span>
                    </p>
                  </div>
                  <div mat-line class="related-detail-container" *ngIf="matchFound">
                    <p>
                      <span class="related-attribute" style="padding-right: 15px;">Description : </span>
                      <span class="related-value" [matTooltip]="similarComp?.complaint_attachment_data">{{similarComp?.complaint_attachment_data | slice:0:150}} ...</span>
                    </p>
                  </div>
                  <button mat-button class="add-btn" (click)="addNewComplaint(similarComp)" *ngIf="matchFound">
                    <mat-icon style="font-size: 15px; padding-top: 3px;">add</mat-icon>Add to this complaint
                  </button>
                </div>
              </div>
            </div>

            <!-- <div class="chips-btn-container" fxLayout="row" fxLayoutGap="10px" *ngIf="matchFound">
              <div class="relcomp-container" *ngIf="extraSimilarComplaints > 0">
                <mat-chip-list>
                  <div fxLayout="row" fxLayoutGap="-15px"> -->
                    <!-- <mat-chip class="person-chips" id="person-chip1" (click)='showFilter(msgvalue.company)'
                      for="message">
                      {{msgvalue?.person1}}
                    </mat-chip>
                    <mat-chip class="person-chips" id="person-chip2" (click)='showFilter(msgvalue.company)'
                      for="message">
                      {{msgvalue?.person2}}
                    </mat-chip> -->
                    <!-- <mat-chip class="person-chips" id="person-chip3" (click)='showFilter(msgvalue.company)'
                      for="message">
                      {{extraSimilarComplaints}}
                    </mat-chip>
                  </div>
                </mat-chip-list>
              </div>
            </div> -->
          </div>

          <div class="footer-fixed" fxLayout="column" fxLayoutGap="1%">
            <div class="footer-divider-container">
              <mat-divider class="footer-divider"></mat-divider>
            </div>
            <div class="footer-content-container" fxLayout="column" fxLayoutGap="5px">
              <div class="comp-footer" fxLayout="row">
                <button mat-stroked-button class="footer-btn" (click)="createComp()">Create complaint</button>
                <span style="flex: 1 1 auto;"></span>
                <div class="footer-right-btn" fxLayoutAlign="end center">
                  <button mat-stroked-button class="footer-btn" (click)="deleteComp()">
                    <img class="delete-icon" src="../assets/images/delete-icon.svg" />Delete
                  </button>
                </div>
              </div> <!-- comp-footer -->
            </div> <!-- footer-content-container -->
          </div><!-- footer-fixed -->
        </div> <!-- mat-card-container -->
      </mat-card>
    </div> <!-- complaint-container -->

  </div> <!-- body -->
</div>
