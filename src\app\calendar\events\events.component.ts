import { Component, OnInit, Input,Inject } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { CalendarComponent } from '../calendar.component';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';

import { AddEventsComponent } from '../add-events/add-events.component';
import { MatDialog } from '@angular/material/dialog';
@Component({
  selector: 'app-events',
  templateUrl: './events.component.html',
  styleUrls: ['./events.component.css']
})
export class EventsComponent implements OnInit {
  @Input() data:string
  receivedRow;
  constructor(public dialog: MatDialog,
    private dialogRef: MatDialogRef<EventsComponent>,
    // @Inject(MAT_DIALOG_DATA) public data: {name: string}) { }
    // @Inject(MAT_DIALOG_DATA) public data: any                      //title related
    ) { 
      // this.receivedRow = data;                                     //title related
    }

  ngOnInit(): void {}

  addUser() {
    const dialogRef = this.dialog.open(AddEventsComponent, {
      width: '70%',
      disableClose: false,
  })
  }

  onClose(){
    
    this.dialog.closeAll();
  }


}
