{"name": "ASCI", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^11.2.14", "@angular/cdk": "~11.2.13", "@angular/common": "~11.2.14", "@angular/compiler": "~11.2.14", "@angular/core": "~11.2.14", "@angular/flex-layout": "^11.0.0-beta.33", "@angular/forms": "~11.2.14", "@angular/material": "~11.2.13", "@angular/material-moment-adapter": "^11.2.13", "@angular/platform-browser": "~11.2.14", "@angular/platform-browser-dynamic": "~11.2.14", "@angular/router": "~11.2.14", "@ng-bootstrap/ng-bootstrap": "^9.1.3", "@syncfusion/ej2-angular-dropdowns": "^19.2.57", "angular-calendar": "^0.28.26", "angular-feather": "^5.0.0", "angular-ng-autocomplete": "^2.0.5", "angularx-flatpickr": "^6.6.0", "classlist.js": "^1.1.20150312", "core-js": "^3.45.0", "d3": "^7.9.0", "date-fns": "^2.22.1", "file-saver": "^2.0.5", "flatpickr": "^4.6.9", "html2canvas": "^1.4.1", "mat-timepicker": "^3.0.0", "ng-multiselect-dropdown": "^0.3.4", "ng2-search-filter": "^0.5.1", "ngx-infinite-scroll": "^10.0.1", "ngx-quill": "^14.3.0", "ngx-smart-popover": "^1.4.0", "quill": "^1.3.6", "rxjs": "~6.6.0", "sass": "^1.61.0", "tslib": "^2.0.0", "web-animations-js": "^2.3.2", "zone.js": "~0.10.2"}, "devDependencies": {"@angular-devkit/build-angular": "~0.1102.14", "@angular/cli": "~11.2.14", "@angular/compiler-cli": "~11.2.14", "@types/file-saver": "^2.0.4", "@types/html2canvas": "^0.5.35", "@types/jasmine": "~3.6.0", "@types/node": "^12.11.1", "codelyzer": "^6.0.0", "jasmine-core": "~3.8.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~5.2.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~4.0.8"}}