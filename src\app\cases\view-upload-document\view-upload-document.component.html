<div class="dialog-body" fxLayout="column" fxLayoutGap="20px">
    <div class="header">
        <mat-toolbar>
            <div class="heading bolder">
                Upload document
            </div>
            <span style="flex: 1 1 auto;"></span>
            <div class="x-container">
                <button mat-icon-button class="close-btn flex" mat-dialog-close>
                    <mat-icon class="close-icon">close</mat-icon>
                </button>
            </div>
          </mat-toolbar>
          <mat-divider></mat-divider>
    </div>

    <div class="body">
        <!-- <mat-tab-group class="tabs">
            <mat-tab label="Computer">                
                content          
            </mat-tab> -->
            <!-- <mat-tab label="Uploaded documents"> -->
                <div class="doc-body" fxLayout="row">
                    <div class="doc-fxrow-container" fxLayout="row wrap" fxLayoutAlign="flex-start">
                        <div fxFlex fxLayout="column" fxLayoutGap="10px">
                            <div fxLayout="row wrap">
                                <div class="add-file-container">
                                    <div class="dropzone" fileDragDrop (filesChangeEmiter)="onFileChange($event)">
                                        <div class="addfile-text-wrapper">
                                            <div class="upload-scope-container">
                                                <input type="file" name="file" id="file" (change)="onFileChange($event)"
                                                    accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt,.jpeg,.png,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv"
                                                    multiple>
                                                <label class="upload-label" for="file" fxLayout="row" fxLayoutAlign="center center">
                                                    <img src="../../assets/images/upload-doc.svg" style="margin-right: 5px;">
                                                    <span class="add-textLink" style="font-size: 15px;">Upload new document</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div *ngFor="let file of internalFiles; let ind = index" fxFlex="30" fxFlex.md="30" fxFlex.sm="20" fxFlex.xs="100"
                                    fxLayout="column" style="padding: 5px;">
                                    <div fxLayout="row" fxLayoutGap="5px"
                                        style="vertical-align: middle; align-items: center; border: 1px solid #aaa; height: 51px; border-radius: 4px;"
                                        *ngIf="file.FIELD_TAB == 'internal'">
                                        <div style="height: 50px; width: 50px; background: #3A3A3A; border-radius: 4px; position: relative;">
                                            <img src="../assets/images/doc_video.svg" style="margin: 20px 16px;">
                                        </div>
                                        <div fxLayout="column">
                                            <div style="color: #000000; font-size: 12px" class="doc-caption">
                                                <p mat-line matTooltip="{{file.ATTACHMENT_SOURCE_NAME}}">{{file.ATTACHMENT_SOURCE_NAME}}</p>
                                                <br><span style="margin-left: 6px;">{{file.CREATED_DATE| date: 'dd/MM/yyyy h:mm a'}}</span>
                                            </div>
                                        </div>
                                        <div class="removeIcon" style="position: relative; left: 75px;">
                                            <!-- <button mat-button matSuffix mat-icon-button aria-label="Clear"
                                                (click)="removeFile(file.ID, file.ATTACHMENT_SOURCE)">
                                                <img src="../assets/images/Trash-icon.svg" style="border: 1px;">
                                            </button> -->
                                            <mat-checkbox (click)="$event.stopPropagation()"
                                            (change)="$event ? selection.toggle(file) : null" [checked]="selection.isSelected(file, event)"></mat-checkbox>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <!-- </mat-tab>
        </mat-tab-group> -->
    </div>        
        
    <div class="footer" fxLayout="column" fxLayoutAlign="end end">
        <div class="footer-btns" fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="20px">
            <div>
                <button mat-button class="cancel-btn large" (click)="cancelUpload()" mat-dialog-close>
                    <span class="bolder">Cancel</span>
                </button>
            </div>
            <div>
                <button mat-button class="upload-btn large flex" (click)="uploadDocs()">
                    <span class="bolder">Upload</span>
                </button>
            </div>
            <!-- <div class="upload-btn large flex" fileDragDrop (filesChangeEmiter)="onFileChange($event)">
                <div class="flex" style="text-align: center;">
                    <input type="file" name="file" id="file" (change)="onFileChange($event.target.files)" multiple>
                    <label for="file" class="flex" style="text-align: center;padding: 6px 15px;">
                        <span>Upload</span>
                    </label>
                </div>
            </div> -->
        </div>
    </div>

</div>