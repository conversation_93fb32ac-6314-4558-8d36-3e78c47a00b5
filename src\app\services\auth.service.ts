import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { EmailValidator } from '@angular/forms';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  public resourceUrl =`${environment.API_BASE_URL}`;

  profileFlag: boolean = true;

  constructor(private http:HttpClient,
    private router: Router) { }

  loginByEmail(body):Observable<any>{
    let url = `${this.resourceUrl}/web/asci/auth/login/email`;
    return this.http.post<any>(url, body, { observe: 'response' });
  }

  signUp(user: any): Observable<any> {
    return this.http.post<any>(`${this.resourceUrl}/web/asci/user/member/register`, user, { observe: 'response' });
  }

  signUpByOtp(otp, cid):Observable<any>{
    let obj = {
      'VERIFY_CODE': otp,
      'C_ID': cid
    }
    return this.http.put<any>(`${this.resourceUrl}/web/asci/user/verify`,obj, {observe: 'response'});
  }

  onForgot(emailId: string): Observable<any> {
    let obj = {
      'USERNAME': emailId
    }
    return this.http.post<any>(`${this.resourceUrl}/web/asci/auth/forgot-password`,obj, { observe: 'response'});
  }

  onReset(code, password, emailId): Observable<any> {
    let obj = {
      'PASSWORD': password,
      'USERNAME': emailId,
      'VERIFY_CODE': code
    }
    return this.http.put<any>(`${this.resourceUrl}/web/asci/auth/reset-password`, obj, { observe: 'response'});
  }

  getOTPByNumber(number): Observable<any> {
    let obj = {
      'USERNAME': number
    }
    return this.http.post<any>(`${this.resourceUrl}/web/asci/auth/login/mobile`, obj, {observe: 'response'});
  }

  loginByOTP(otp, cID, session_token): Observable<any>{
    let obj = {
      'VERIFY_CODE': otp,
      'C_ID': cID
    }
    const headers: any = new HttpHeaders({
      'session-token': encodeURIComponent(session_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/auth/login/verify`,obj, options);
  }

  createUserByAdmin(model): Observable<any>{
    let obj = {
      "EMAIL_ID": model.emailId,
      "FIRST_NAME": model.fname,
      "LAST_NAME": model.lname,
      "PASSWORD": model.password,
      "PHONE_NUMBER": model.phone,
      "SALUTATION_ID":model.salutation,
      "DEPARTMENT_TYPE_ID": model.department,
      "ROLE_ID": model.role
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/user/member/create`, obj, options);
  }

  createAdvertiserByAdmin(model): Observable<any>{
    let obj = {
      "EMAIL_ID": model.emailId,
      "FIRST_NAME": model.fname,
      "LAST_NAME": model.lname,
      "PASSWORD": model.password,
      "PHONE_NUMBER": model.phone,
      "SALUTATION_ID":model.salutation,
      "COMPANY_ID": model.companyId,
      "COMPANY_NAME": model.company,
      "PINCODE": model.pin,
      "ADDRESS": model.address,
      "ROLE_ID": "5"
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/user/consumer/create`, obj, options);
  }

  createCompanyByAdmin(obj): Observable<any>{
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/company/register`, obj, options);
  }

  updateCompany(model, compId): Observable<any>{
    let obj = {
      'ID': compId,
      'COMPANY_NAME': model.cname,
      'ADDRESS': model.caddress,
      'COMPANY_DESCRIPTION': model.about_company,
      "CONTACT_INFO": model.contact_info
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/company/update`, obj, options);
  }

  deleteCompany(ID): Observable<any>{
    let obj = {
      'ID': ID
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/company/delete`, obj, options);
  }

  updateAdvertiser(model, userId, roleID): Observable<any>{
    let obj = {
      'ID': userId,
      'FIRST_NAME': model.first_name,
      'LAST_NAME': model.last_name,
      'PINCODE': model.pin,
      'ADDRESS': model.address,
      'SALUTATION_ID': model.salutation,
      'MEMBER_ROLE_ID': roleID,
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo.access_token),
      'id-token': encodeURIComponent(userInfo.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/user/member/external/update`, obj, options);
  }

  updateIntraIndustryUserByAdmin(model, userId): Observable<any>{
    let obj = {
      'ID': userId,
      'FIRST_NAME': model.first_name,
      'LAST_NAME': model.last_name,
      'PINCODE': model.pin,
      'ADDRESS': model.address,
      'SALUTATION_ID': model.salutation
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/user/industry/member/update`, obj, options);
  }

  updateConsumer(model, userId): Observable<any>{
    let obj = {
      'ID': userId,
      'FIRST_NAME': model.first_name,
      'LAST_NAME': model.last_name,
      'PINCODE': model.postal_code,
      "ORGANIZATION_NAME":model.organization,
      'SALUTATION_ID': model.salutation
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/user/member/consumer-org`, obj, options);
  }

  updateGeneralPublicUser(model, userId): Observable<any>{
    let obj = {
      'ID': userId,
      'FIRST_NAME': model.first_name,
      'LAST_NAME': model.last_name,
      'PINCODE': model.postal_code,
      'SALUTATION_ID': model.salutation
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/user/member/general-public`, obj, options);
  }

  updateGovBodyUser(model, userId): Observable<any>{
    let obj = {
      'ID': userId,
      'FIRST_NAME': model.first_name,
      'LAST_NAME': model.last_name,
      'PINCODE': model.postal_code,
      'SALUTATION_ID': model.salutation
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/user/member/gov-body`, obj, options);
  }

  updateUser(model, userId, deptId, roleID): Observable<any>{
    let obj = {
      'ID': userId,
      'FIRST_NAME': model.first_name,
      'LAST_NAME': model.last_name,
      'DEPARTMENT_TYPE_ID': deptId,
      'ROLE_ID': roleID,
      'SALUTATION_ID': model.salutation
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/user/member/internal/update`, obj, options);
  }

  updateOwnProfile(model, email, deptId, stateId, distId, titleId): Observable<any>{
    let obj = {
      'FIRST_NAME': model.fname,
      'LAST_NAME': model.lname,
      'PINCODE':model.pin,
      'ORGANIZATION_NAME':model.organization,
      'PROFESSION_NAME':model.profession,
      'DEPARTMENT_TYPE_ID': deptId,
      "SALUTATION_ID": titleId,
      "DISTRICT_TYPE_ID": distId,
      "STATE_TYPE_ID": stateId,
      "EMAIL_ID":email
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/user/update`, obj, options);
  }

  deleteUser(C_ID): Observable<any>{
    let obj = {
      'C_ID': C_ID
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/user/member/delete`, obj, options);
  }

  deleteIntraIndustryUserByAdmin(C_ID): Observable<any>{
    let obj = {
      'C_ID': C_ID
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo.access_token),
      'id-token': encodeURIComponent(userInfo.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/user/industry/member/delete`, obj, options);
  }

  enableDisableUser(C_ID, action): Observable<any>{
    let obj = {
      'C_ID': C_ID,
      'ACTION': action
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/user/member/enable-disable`, obj, options);
  }

  enableDisableIntraIndustryUser(C_ID, action): Observable<any>{
    let obj = {
      'C_ID': C_ID,
      'ACTION': action
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo.access_token),
      'id-token': encodeURIComponent(userInfo.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/user/industry/member/enable-disable`, obj, options);
  }

  getInternalMembersList(pageNumber, searchVal, limit, sortKey, sortOrder): Observable<any>{
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/member/internal?PAGE=${pageNumber}&SEARCH_KEY=${searchVal}&limit=${limit}&SORTING_KEY=${sortKey}&SORTING_ORDER=${sortOrder}`, options);
  }

  getExternalMembersList(pageNumber, searchVal, limit, sortKey, sortOrder): Observable<any>{
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/member/external?PAGE=${pageNumber}&SEARCH_KEY=${searchVal}&limit=${limit}&SORTING_KEY=${sortKey}&SORTING_ORDER=${sortOrder}`, options);
  }

  getIntraIndustryMembersList(pageNumber, searchVal, limit, sortKey, sortOrder): Observable<any>{
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo.access_token),
      'id-token': encodeURIComponent(userInfo.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/industry/member?PAGE=${pageNumber}&SEARCH_KEY=${searchVal}&limit=${limit}&SORTING_KEY=${sortKey}&SORTING_ORDER=${sortOrder}`, options);
  }

  getCompaniesList(pageNumber, searchVal, limit, sortKey, sortOrder): Observable<any>{
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/company/list?PAGE=${pageNumber}&SEARCH_KEY=${searchVal}&limit=${limit}&SORTING_KEY=${sortKey}&SORTING_ORDER=${sortOrder}`, options);
  }

  getGeneralPublicUsersList(pageNumber, searchVal, limit, sortKey, sortOrder): Observable<any>{
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/member/general-public?PAGE=${pageNumber}&SEARCH_KEY=${searchVal}&limit=${limit}&SORTING_KEY=${sortKey}&SORTING_ORDER=${sortOrder}`, options);
  }

  getConsumerOrganisationUsersList(pageNumber, searchVal, limit, sortKey, sortOrder): Observable<any>{
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/member/consumer-org?PAGE=${pageNumber}&SEARCH_KEY=${searchVal}&limit=${limit}&SORTING_KEY=${sortKey}&SORTING_ORDER=${sortOrder}`, options);
  }

  getGovBodyUsersList(pageNumber, searchVal, limit, sortKey, sortOrder): Observable<any>{
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/member/gov-body?PAGE=${pageNumber}&SEARCH_KEY=${searchVal}&limit=${limit}&SORTING_KEY=${sortKey}&SORTING_ORDER=${sortOrder}`, options);
  }

  getInternalMemberById(userId){
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/member/internal?ID=${userId}`, options);
  }

  getConsumerMemberById(userId){
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/member/consumer-org?ID=${userId}`, options);
  }

  getGeneralPublicMemberById(userId){
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/member/general-public?ID=${userId}`, options);
  }
  getGovBodyMemberById(userId){
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/member/gov-body?ID=${userId}`, options);
  }

  getExternalMemberById(userId){
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/member/external?ID=${userId}`, options);
  }

  getIntraIndustryMemberDetailsByAdmin(userId){
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo.access_token),
      'id-token': encodeURIComponent(userInfo.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/industry/member?ID=${userId}`, options);
  }

  getCompanyById(compId){
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/company/list?ID=${compId}`, options);
  }

  approveCompany(ID):Observable<any>{
    let obj = {
      'ID': ID
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/company/approve`, obj, options);
  }

  resendOtp(email: string): Observable<any>{
    let obj = {
      'USERNAME': email
    }
    return this.http.post<any>(`${this.resourceUrl}/web/asci/user/resend-otp`, obj, {observe: 'response'});
  }

  logout(): Observable<any>{
    let obj = {
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/auth/logout`, obj, options);
  }

  approveUser(C_ID):Observable<any>{
    let obj = {
      'C_ID': C_ID
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/user/member/approve`, obj, options);
  }

  getMyProfile():Observable<any>{
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/profile`, options);
  }

  getCompanies(keyword):Observable<any>{
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    // const headers: any = new HttpHeaders({
    //   'x-access-token': encodeURIComponent(userInfo?.access_token),
    //   'id-token': encodeURIComponent(userInfo?.id_token),
    // });
    // let options = { headers: headers };
    let key = encodeURIComponent(keyword)
    return this.http.get<any>(`${this.resourceUrl}/web/asci/companies?KEYWORD=${key}`);
  }

  getAttributeCode(attribute): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/verify-attribute?ATTRIBUTE_NAME=${attribute}`, options);
  }

  verifyOTP(val,attribute): Observable<any>
  {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    let obj = {
      'VERIFY_CODE': val,
      'ATTRIBUTE_NAME':attribute
    }
    return this.http.put<any>(`${this.resourceUrl}/web/asci/user/verify-attribute`, obj,options);
  }


  getPhNums(number):Observable<any>{
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/users?MOBILE=${number}`, options);
  }

  changePassword(model):Observable<any>{
    let obj = {
      'OLD_PASSWORD': model.old_pswd,
      'NEW_PASSWORD': model.new_pswd
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/user/change-password`, obj, options);
  }

  getUserDetailsById(id):Observable<any>{
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/details?ID=${id}`, options);
  }

  getOTPForChatbot(number, email): Observable<any> {
    let obj = {
      "PHONE_NUMBER": number,
      "EMAIL_ID": email
    }
    return this.http.post<any>(`${this.resourceUrl}/web/asci/auth/user-check`, obj, { observe: 'response' });
  }

  createUserFromChatbot(model): Observable<any> {
    // let obj = {
    //   "EMAIL_ID": model.emailId,
    //   "FIRST_NAME": model.fname,
    //   "LAST_NAME": model.lname,
    //   "PHONE_NUMBER": model.phone,
    //   "ORGANIZATION_NAME": model.department,
    //   "ROLE_ID": model.role,
    //   "PROFESSION_NAME": model.profession
    // }
    return this.http.post<any>(`${this.resourceUrl}/web/asci/user/consumer/register`, model);
  }

  getCompaniesAll():Observable<any>{
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/companies`, options);
  }

}
