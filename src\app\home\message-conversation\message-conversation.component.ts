import { Component, HostListener, Inject, OnInit, Pipe } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { DomSanitizer } from '@angular/platform-browser';
import { ViewUploadDocumentComponent } from 'src/app/cases/view-upload-document/view-upload-document.component';
import { NamsEmailComponent } from 'src/app/nams-email/nams-email.component';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';

@Component({
  selector: 'app-message-conversation',
  templateUrl: './message-conversation.component.html',
  styleUrls: ['./message-conversation.component.scss']
})
@Pipe({ name: 'safeHtml' })
export class MessageConversationComponent implements OnInit {

  complaint_id;
  case_Id;
  comp_detail;
  company_name;
  brand_name;;
  company_id;
  userDetails = [];
  assigneeDetails = [];
  user_id: number;
  sender_name;
  sender_mail;
  SMSSent: any = [];
  mailsSent: any = [];
  text_message = new FormControl('', Validators.required);
  profile = '';
  selectedFiles = [];
  selectedTab = 0;
  senderMailArray = [];
  name: string;
  senderDetails = [];
  assgIndex;
  userIndex;
  userData: any;
  iniName: string = "";
  iniLname: string = "";
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private matDialog: MatDialog,
    private cs: ComplaintsService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private sanitized: DomSanitizer,
    private notify: NotificationService,
    public dialogRef: MatDialogRef<MessageConversationComponent>,
  ) { }

  safeHTML(unsafe: string) {
    return this.sanitized.bypassSecurityTrustHtml(unsafe);
  }

  ngOnInit(): void {
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
    this.userData = JSON.parse(window.localStorage.getItem('userInfo'));
    this.case_Id = this.data.details.CASE_ID;
    this.comp_detail = (this.data.details.ADVERTISEMENT_DESCRIPTION).replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').replace(/amp;/g, ' ');
    this.company_name = this.data.details.COMPANY_NAME;
    this.brand_name = this.data.details.BRAND_NAME;
    this.company_id = this.data.details.COMPANY_ID;
    this.complaint_id = this.data.details.ID;
    this.getAssigneeUser();
  }

  getAssigneeUser() {
    this.cs.getAssigneeUser(this.complaint_id).subscribe((assignee: any) => {
      this.assigneeDetails = assignee.data;
      if (this.assigneeDetails.length != 0) {
        this.user_id = this.assigneeDetails[0].USER_ID;
        this.sender_name = this.assigneeDetails[0].FIRST_NAME + ' ' + this.assigneeDetails[0].LAST_NAME;
        if (this.assigneeDetails[0].FIRST_NAME != "" && this.assigneeDetails[0].FIRST_NAME != null) {
          this.iniName = this.assigneeDetails[0].FIRST_NAME.charAt(0);
          if (this.assigneeDetails[0].LAST_NAME != "" && this.assigneeDetails[0].LAST_NAME != null) {
            this.iniLname = this.assigneeDetails[0].LAST_NAME.charAt(0);
          }
          this.profile = this.iniName + this.iniLname;
        }
        this.sender_mail = this.assigneeDetails[0].EMAIL_ID;
        this.viewAssigneeMails('', 0);
      }
      this.getEmailUsers();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  getEmailUsers() {
    this.cs.getAllUsers(this.complaint_id, 0, 0, 'system').subscribe((users: any) => {
      this.userDetails = users.data;
      if (this.userDetails.length > 0) {
        if (this.assigneeDetails.length > 0) {
          for (let i = 0; i < this.userDetails.length; i++) {
            if (this.userDetails[i].ID == this.assigneeDetails[0].USER_ID) {
              this.userDetails.splice(i, 1);
            }
          }
        }
      }
      this.removeLoginUser();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  removeLoginUser() {
    let usersDataArray = [];
    if (this.userDetails.length > 0) {
      for (let ud_i = 0; ud_i < this.userDetails.length; ud_i++) {
        if (this.userDetails[ud_i]["ID"] != this.userData.userId && this.userDetails[ud_i]["EMAIL_ID"] != this.userData.emailId) {
          usersDataArray.push(this.userDetails[ud_i]);
        }
      }
    }
    this.userDetails = usersDataArray;
    if (this.userDetails.length != 0 && this.assigneeDetails.length == 0) {
      this.user_id = this.userDetails[0].ID;
      if (this.user_id != 0) {
        this.sender_name = this.userDetails[0].FIRST_NAME + ' ' + this.userDetails[0].LAST_NAME;
        if (this.userDetails[0].FIRST_NAME != "" && this.userDetails[0].FIRST_NAME != null) {
          this.iniName = this.userDetails[0].FIRST_NAME.charAt(0);
          if (this.userDetails[0].LAST_NAME != "" && this.userDetails[0].LAST_NAME != null) {
            this.iniLname = this.userDetails[0].LAST_NAME.charAt(0);
          }
          this.profile = this.iniName + this.iniLname;
        }
      }
      else if (this.user_id == 0) {
        this.sender_name = this.userDetails[0].EMAIL_ID;
      }
      this.sender_mail = this.userDetails[0].EMAIL_ID;
      this.viewMails('', 0);
    }
    if (this.userDetails.length == 0 && this.assigneeDetails.length == 0) {
      this.name = "toNewEmail";
    }
  }

  getSMSUsers() {
    this.cs.getAllUsers(this.complaint_id, 0, 1, 'system').subscribe((users: any) => {
      this.userDetails = users.data;
      this.removeLoginUserSMS();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  removeLoginUserSMS() {
    let usersDataArray = [];
    if (this.userDetails.length > 0) {
      for (let ud_i = 0; ud_i < this.userDetails.length; ud_i++) {
        if (this.userDetails[ud_i]["ID"] != this.userData.userId && this.userDetails[ud_i]["EMAIL_ID"] != this.userData.emailId) {
          usersDataArray.push(this.userDetails[ud_i]);
        }
      }
    }
    this.userDetails = usersDataArray;
    if (this.userDetails.length != 0) {
      this.user_id = this.userDetails[0].ID;
      this.sender_name = this.userDetails[0].FIRST_NAME + ' ' + this.userDetails[0].LAST_NAME;
      if (this.userData.firstName != "" && this.userData.firstName != null) {
        this.iniName = this.userData.firstName.charAt(0);
        if (this.userData.lastName != "" && this.userData.lastName != null) {
          this.iniLname = this.userData.lastName.charAt(0);
        }
        this.profile = this.iniName + this.iniLname;
      }
      this.viewSMS('', 0);
    }
    else {
      this.sender_name = "";
    }
  }

  getShortName(data) {
    if (data.FIRST_NAME != "" && data.FIRST_NAME != null) {
      this.iniName = data.FIRST_NAME.charAt(0);
      if (data.LAST_NAME != "" && data.LAST_NAME != null) {
        this.iniLname = data.LAST_NAME.charAt(0);
      }
      return this.iniName + this.iniLname;
    }
  }

  viewSMS(user, i) {
    this.userIndex = i + 1;
    this.assgIndex = 0;
    if (!!user) {
      this.user_id = user.ID;
      this.sender_name = user.FIRST_NAME + ' ' + user.LAST_NAME;
      if (this.userData.firstName != "" && this.userData.firstName != null) {
        this.iniName = this.userData.firstName.charAt(0);
        if (this.userData.lastName != "" && this.userData.lastName != null) {
          this.iniLname = this.userData.lastName.charAt(0);
        }
        this.profile = this.iniName + this.iniLname;
      }
    }
    this.cs.getSMS(this.complaint_id, 0, this.user_id, 'system').subscribe((sms: any) => {
      this.SMSSent = sms.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  sendSMS(message) {
    this.cs.sendSMS(message, this.complaint_id, 0, this.user_id, this.selectedFiles, 'system').subscribe(res => {
      this.viewSMS('', 0);
      this.text_message.reset();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  refreshChat() {
    this.SMSSent = [];
    this.viewSMS('', 0);
  }

  openUpload() {
    const dialogRef = this.matDialog.open(ViewUploadDocumentComponent, {
      width: '80%',
      height: 'auto',
      data: { ID: this.complaint_id },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        this.selectedFiles = data;
      }
    );
  }

  onTabChanged(event) {
    this.selectedTab = event.index;
    if (this.selectedTab == 0) {
      this.getAssigneeUser();
      this.getEmailUsers();
    }
    else if (this.selectedTab == 1) {
      this.getSMSUsers();
    }
  }

  viewAssigneeMails(user, i) {
    this.assgIndex = i + 1;
    this.userIndex = 0;
    if (!!user) {
      this.user_id = user.USER_ID;
      this.sender_name = user.FIRST_NAME + ' ' + user.LAST_NAME;
      this.sender_mail = user.EMAIL_ID;
    }
    this.cs.getMails(this.complaint_id, 0, this.user_id, '', '', 'system', 'one_to_one').subscribe((mails: any) => {
      this.mailsSent = mails.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
    this.name = "toIntraAssignee";
    this.senderDetails = [];
    this.senderDetails = this.assigneeDetails;
    this.senderMailArray = [];
    this.senderMailArray.push(this.sender_mail);
  }

  viewMails(user, i) {
    this.userIndex = i + 1;
    this.assgIndex = 0;
    if (!!user) {
      this.user_id = user.ID;
      this.sender_mail = user.EMAIL_ID;
      if (this.user_id != 0) {
        this.sender_name = user.FIRST_NAME + ' ' + user.LAST_NAME;
      } else if (this.user_id == 0) {
        this.sender_name = user.EMAIL_ID;
      }
    }
    if (this.user_id != 0) {
      this.cs.getMails(this.complaint_id, 0, this.user_id, '', '', 'system', 'one_to_one').subscribe((mails: any) => {
        this.mailsSent = mails.data;
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
    else if (this.user_id == 0) {
      this.cs.getMails(this.complaint_id, 0, '', this.sender_mail, '', 'system', '').subscribe((mails: any) => {
        this.mailsSent = mails.data;
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
    this.name = "intra";
    this.senderDetails = [];
    this.senderDetails = this.userDetails;
    this.senderMailArray = [];
    this.senderMailArray.push(this.sender_mail);
  }

  sendEmail() {
    const dialogRef = this.matDialog.open(NamsEmailComponent, {
      width: '1500px',
      height: 'auto',
      data: { ID: this.complaint_id, CASE_ID: this.case_Id, CHATBOT_ID: 0, COMP_ID: this.company_id, users: this.senderDetails, mail: this.senderMailArray, name: this.name, call_from: 'system' },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data.state === 'refresh') {
          if (data.name == 'intra' || data.name == 'toNewEmail') {
            this.viewMails('', 0);
            this.getEmailUsers();
          }
          else if (data.name == 'toIntraAssignee') {
            this.viewAssigneeMails('', 0);
            this.getEmailUsers();
          }
        }
      }
    );
  }

  refreshMails() {
    this.mailsSent = [];
    if (this.name == 'intra') {
      this.viewMails('', 0);
    }
    else if (this.name == 'toIntraAssignee') {
      this.viewAssigneeMails('', 0);
    }
  }

  openNewTab(source) {
    window.open(source, 'window 1', '');
  }

  viewAcademyDetails() {
    window.open('https://www.ascionline.in/academy/courses-2/', "_blank");
  }

}