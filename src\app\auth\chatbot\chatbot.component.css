.message {
  border-radius: 50px;
  padding: 10px 20px;
  margin-bottom: 20px;
  position: relative;
  font-size: 15px;
  box-shadow: 20px 20px 35px #d0d0d0;
}

.notification-msg {
  position: absolute;
  top: 62px;
  z-index: 1;
  font-style: normal;
  font-weight: normal;
  font-size: 11px;
  line-height: 15px;
  color: #000000;
  background: #FFF7EA;
  box-shadow: 0px 4px 8px rgb(0 0 0 / 10%);
  width: 348px;
  left: 0px;
  text-align: justify;
  padding: 5px 10px;
}

.time-notification-msg {
  position: absolute;
  top: 62px;
  z-index: 1;
  font-style: normal;
  font-weight: normal;
  font-size: 11px;
  line-height: 15px;
  color: #eb1616;
  background: #FFF7EA;
  box-shadow: 0px 4px 4px rgb(0 0 0 / 1%);
  width: 348px;
  left: 0px;
  text-align: justify;
  padding: 5px 10px;
}

.chatbox-message {
  height: 420px;
  width: 304px;
  margin-top: 6px;
  margin-bottom: 18px;
  overflow-y: scroll;
  overflow-x: hidden;
  pointer-events: auto;
}

::-webkit-scrollbar {
  display: none;
}

.chatbox-message ::-webkit-scrollbar {
  display: none;
}

img.file-upload {
  width: 38px;
  margin-left: 11px;
}

.messagetype {
  width: 227px;
  height: 37px;
  padding-bottom: 4px;
  margin-left: 56px;
  margin-bottom: 13px;
  margin-top: 18px;
  border-radius: 8px;
  border: none;
  font-size: 12px;
  font-style: normal;
  font-weight: normal;
  line-height: 16px;
  padding-left: 19px;
  border: 1px solid #ccc;
  background-color: #F2F3F3;
}

.asci-logo {
  background-color: #FFFFFF;
  box-shadow: 0px 2px 4px rgb(0 0 0 / 10%);
  position: absolute;
  left: 11px;
  bottom: 11px;
  width: 41px;
  height: 28px;
  padding-left: 1px;
  padding-right: 2px;
  padding-bottom: 39px;
  margin-bottom: 2px;
}

.intro-msg {
  text-align: center;
  margin-top: 70px;
  margin-left: 20px;
}

.chatbox-container {
  position: absolute;
  bottom: 91px;
  right: 25px;
  width: 349px;
  z-index: 1;
  background-color: #f9f9f9;
  border-radius: 16px 16px 16px 16px;
}

mat-panel-title {
  margin-top: 18px;
  margin-bottom: 12px;
  font-weight: 500;
  font-size: 12px;
}

.mat-accordion .mat-expansion-panel {
  border-radius: 16px 16px 16px 16px;
}

.mat-expansion-panel-header.mat-expanded,
.mat-expansion-panel-header.mat-expanded:hover,
.mat-expansion-panel-header.mat-expanded:focus {
  background-color: #F89E1B;
  height: 62px;
  border-radius: 16px 16px 0px 0px;
}

.message.to {
  color: #fff;
  display: flex;
  justify-content: flex-end;
  justify-content: space-around;
  margin-left: auto;
  font-style: normal;
  font-weight: normal;
  width: fit-content;
  font-size: 12px;
  line-height: 17px;
  max-width: 196px;
  border-bottom-right-radius: 0;
  background: #F89E1B;
  box-shadow: 0px 4px 10px rgb(25 71 158 / 25%);
  border-radius: 20px 20px 0px 20px;
}

.message.from {
  color: #363636;
  display: flex;
  justify-content: flex-start;
  margin-right: auto;
  max-width: 223px;
  width: fit-content;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  line-height: 17px;
  border-bottom-left-radius: 0;
  background: #FFFFFF;
  border: 1px solid #E1E5E7;
  box-sizing: border-box;
  box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.1);
  border-radius: 20px 20px 20px 0px;
}

.text-ellipsis {
  overflow: hidden;
  width: 170px;
  display: inline-block;
  display: -webkit-inline-box;
  word-break: break-word;
  text-overflow: ellipsis;
  white-space: normal;
  -webkit-box-orient: vertical;
  vertical-align: middle;
  position: relative;
  margin-top: 5px;
  margin-left: 5px;
}

.thumbnail {
  width: 120px;
  height: 75px;
  background: #FFEACB;
  border-radius: 20px;
  border-bottom: 5px;
}

.sendButton {
  background: #F89E1B;
  box-shadow: inset 0px 3px 6px #FED79E;
  border-radius: 8px;
  color: #ffffff;
  margin-left: 10px;
  height: 40px;
  width: 42px;
  padding-left: 0px;
  border: none;
}

.wave-img {
  width: 35px;
  position: relative;
  top: 36px;
  right: 66px;
}

.register-msg {
  position: absolute;
  bottom: 40px;
  right: 131px;
  width: 232px;
  height: 43px;
  padding-top: 11px;
  padding-left: 11px;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 19px;
  color: #253858;
  background: #FFFFFF;
  border-radius: 3px;
}

.register-msg:after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border-top: 7px solid transparent;
  border-left: 12px solid #FFFFFF;
  border-bottom: 7px solid transparent;
  margin-left: 15px;
  margin-top: 4px;
}

.message.from+.from {
  margin-top: 1em;
}

.message.to+.to {
  margin-top: 1em;
}

.message.from+.to {
  margin-top: 2em;
}

.message.to+.from {
  margin-top: 2em;
}

.attachButton {
  margin-top: 17px;
  position: absolute;
  margin-left: 12px;
  padding-left: 7px;
  box-sizing: border-box;
  border-radius: 7px;
  padding-bottom: 3px;
  background: #F8F8F9;
  color: #888C92;
  width: 35px;
  height: 37px;
  border: 1px solid #DFE0E2;
}

.chatbot-img {
  padding: 7px;
  padding-bottom: 15px;
  cursor: pointer;
  position: absolute;
  bottom: 30px;
  pointer-events: auto;
  right: 47px;
  background: #F89E1B;
  border: 1px solid #FFC877;
  box-sizing: border-box;
  box-shadow: 0px 10px 20px rgb(241 148 11 / 20%), inset 0px 3px 6px #fed79e;
  border-radius: 8px;
}

.mat-heading {
  width: 235px;
  margin-bottom: 10px;
  margin-left: 34px;
  font-weight: 600;
  font-size: 14px;
  line-height: 18.62px;
  color: #FFFFFF;
}

.hi-button {
  height: 43px;
  width: 119px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 27px;
  background: #F89E1B;
  border: none;
  box-sizing: border-box;
  box-shadow: 0px 4px 10px rgba(25, 71, 158, 0.25);
  border-radius: 12px;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 19px;
  color: #FFFFFF;
}

.intro-msg2 {
  font-size: 14px;
  width: 184px;
  font-weight: 400;
  line-height: 18.62px;
}

.intro-msg1 {
  font-size: 20px;
  width: 184px;
  font-weight: 400;
  line-height: 33.6px;
}

.message.to .chat-time {
  font-style: normal;
  font-weight: normal;
  font-size: 8px;
  line-height: 11px;
  color: rgba(255, 255, 255, 0.6);
}

.message.from .chat-time {
  font-style: normal;
  font-weight: normal;
  font-size: 8px;
  line-height: 11px;
  color: rgba(0, 0, 0, 0.4);
}

.close-icon {
  pointer-events: auto;
  position: absolute;
  left: 311px;
  bottom: 26px;
  width: 13px;
}

.mat-icon-button {
  line-height: 36px;
}

.prevent-click {
  pointer-events: none;
}

.chatbot-message-send {
  position: absolute;
  pointer-events: auto;
  background: #FFFFFF;
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  left: 0px;
  width: 347px;
  height: 72px;
  bottom: 0px;
  padding: 0px;
}

video.ng-tns-c3-1 {
  width: 74px;
  height: 48px;
}

.objective-button {
  border-radius: 20px;
  position: relative;
}

.objective-button button {
  background: #FFFFFF;
  border: 1px solid #F89E1B;
  box-sizing: border-box;
  box-shadow: 0px 4px 10px rgb(25 71 158 / 25%);
  border-radius: 20px;
  font-style: normal;
  font-weight: bolder;
  font-size: 10px;
  line-height: 12px;
  text-align: center;
  height: 30px;
  color: #F89E1B;
  margin-bottom: 15px;
  min-width: 63px;
  max-width: auto;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding-left: 10px;
  padding-right: 10px;
}

.objective-button1 button {
  font-style: normal;
  font-weight: bolder;
  font-size: 10px;
  background: #FFFFFF;
  border: 1px solid #A73127;
  box-sizing: border-box;
  box-shadow: 0px 4px 10px rgba(25, 71, 158, 0.25);
  border-radius: 20px;
  line-height: 17px;
  min-height: 50px;
  max-height: max-content;
  margin-bottom: 15px;
  text-align: center;
  color: #A73127;
  min-width: 128px;
  max-width: max-content;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.verification-message {
  background: rgba(28, 211, 174, 0.3);
  border-radius: 15px;
  font-style: normal;
  font-weight: 600;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  color: #04A585;
  min-width: 80%;
  max-width: -webkit-min-content;
  max-width: min-content;
  height: auto;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 10px;
  padding-right: 10px;
  position: absolute;
  bottom: 85px;
  z-index: 1;
}

.error-message {
  background: #ffc6c1;
  border-radius: 15px;
  font-style: normal;
  font-weight: 600;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  color: #ad0d0d;
  min-width: 80%;
  max-width: -webkit-min-content;
  max-width: min-content;
  height: auto;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 10px;
  padding-right: 10px;
  position: absolute;
  bottom: 85px;
  z-index: 1;
}

.skip-message {
  background: #cce9f8;
  border-radius: 15px;
  font-style: normal;
  font-weight: 600;
  font-size: 11px;
  line-height: 16px;
  text-align: center;
  color: #0088CB;
  min-width: 80%;
  max-width: -webkit-min-content;
  max-width: min-content;
  height: auto;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 10px;
  padding-right: 10px;
  margin-left: 35px;
  margin-bottom: 20px;
}

.skip-button {
  background: #FFFFFF;
  box-sizing: border-box;
  box-shadow: 0px 4px 10px rgb(25 71 158 / 25%);
  border-radius: 20px;
  font-style: normal;
  font-weight: normal;
  font-size: 11px;
  line-height: 12px;
  border: 1px solid #E1E5E7;
  text-align: center;
  height: 30px;
  color: #363636;
  margin-bottom: 15px;
  min-width: 63px;
  max-width: auto;
  justify-content: center;
  align-items: center;
  padding-left: 10px;
  padding-right: 10px;
}

.objective-button3 button {
  background: #FFFFFF;
  border: 1px solid #F89E1B;
  box-sizing: border-box;
  box-shadow: 0px 4px 10px rgba(25, 71, 158, 0.25);
  border-radius: 20px;
  font-style: normal;
  font-weight: bolder;
  font-size: 10px;
  line-height: 12px;
  text-align: center;
  height: 38px;
  color: #F89E1B;
}

.objective-button1 {
  margin-top: 16px;
}

.objective-button3 {
  margin-top: 27px;
}

.introduction-msg {
  background: #FFFFFF;
  box-shadow: 0px 6px 16px rgb(0 0 0 / 25%);
  border-radius: 12px;
  z-index: 1;
  position: absolute;
  right: 48px;
  bottom: 95px;
  width: 246px;
  height: 121px;
  padding-top: 20px;
  padding-left: 10px;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
}

.mask-group {
  position: absolute;
  bottom: 201px;
  right: 144px;
  width: 50px;
  z-index: 2
}

.chatbot-mask-group {
  position: absolute;
  bottom: 215px;
  right: 150px;
  z-index: 2;
  transform: rotate(-15deg);
}

.introduction-msg-close {
  position: absolute;
  right: 58px;
  cursor: pointer;
  bottom: 195px;
  width: 11px;
  z-index: 2;
}

.introduction-wave-img {
  width: 25px;
  height: 26px;
  position: relative;
  right: 10px;
}

.intro-second-msg {
  margin-left: 34px;
  margin-top: 3px;
}

.intro-first-msg {
  margin-left: 9px;
  margin-left: 11px;
}