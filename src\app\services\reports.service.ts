import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ReportsService {

  public resourceUrl = `${environment.API_BASE_URL}`;

  constructor(private http: HttpClient) { }

  getRegisteredComplaintReports(from_month, from_year, to_month, to_year) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint/report/registered?FROM_MONTH=${from_month}&FROM_YEAR=${from_year}&TO_MONTH=${to_month}&TO_YEAR=${to_year}`, options);
  }

  getTamsReechReports(from_month, from_year, to_month, to_year, section, status) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get(`${this.resourceUrl}/web/asci/complaint/report/nams?FROM_MONTH=${from_month}&FROM_YEAR=${from_year}&TO_MONTH=${to_month}&TO_YEAR=${to_year}&SECTION=${section}&STATUS=${status}`, options);
  }

}