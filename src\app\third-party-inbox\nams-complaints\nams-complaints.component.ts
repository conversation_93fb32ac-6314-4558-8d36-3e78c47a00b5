import { Component, ElementRef, HostListener, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginatorIntl } from '@angular/material/paginator';
import { BehaviorSubject, Observable } from 'rxjs';
import { DatePipe } from '@angular/common';
import { debounceTime, distinctUntilChanged, finalize } from 'rxjs/operators';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';
import { NotificationService } from 'src/app/services/notification.service';
import { ThirdPartyService } from 'src/app/services/third-party.service';
import { colorObj } from 'src/app/shared/color-object';
import { NamsDetailsComponent } from '../nams-details/nams-details.component';
import { MY_FORMATS } from 'src/app/shared/calendarFormat';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter } from 'angular-calendar';
import { MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import moment from 'moment';

@Component({
  selector: 'app-nams-complaints',
  templateUrl: './nams-complaints.component.html',
  styleUrls: ['./nams-complaints.component.css'],
  providers: [
    { provide: MAT_MOMENT_DATE_ADAPTER_OPTIONS, useValue: { useUtc: true } },
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
  ]
})
export class NamsComplaintsComponent implements OnInit {

  pagename: String;
  userInfo: any;
  userName: any;
  loading: boolean = true;
  lastRespSize: number = 0;
  pageNumber: number = 0;
  resArray: Array<any> = [];
  dataSource: any = [];
  filterArray: Array<any> = [];
  sortedArray: Array<any> = [];
  filterKeys: any = [];
  private request$: Observable<any>;
  select_all = false;
  idArray = [];
  advertisers = [];
  medium = [];
  mediumName;
  status;
  num: number = 0;
  count: number = 0;
  pageSize: number = 10;
  DATE_FROM = new FormControl(null);
  DATE_TO = new FormControl(null);
  KEYWORD = new FormControl(null);
  ADVERTISER = new FormControl(null);
  MEDIUM = new FormControl(null);
  isselected: boolean;
  confirmationMsg: any = {};
  namsComplaints = [];

  private _namsData = new BehaviorSubject<any[]>([]);
  private namsDataStore: { $namsData: any[] } = { $namsData: [] };
  readonly $namsData = this._namsData.asObservable();
  count1: number;
  val: unknown[];
  disableAction: boolean = true;
  dat = {};
  statusList = [];
  disableDelete: boolean = true;
  limit: number = 0;
  rangeLabel: any;
  startIndex: number;
  lastIndex: number;
  totalCount: any;
  filterData: { name: string; value: any; }[];
  zeroComplaints: boolean = false;
  keyword: any;
  advertiser: any;
  filter: boolean = false;
  sortingKey: any = '';
  sortingOrder: any = '';
  brand_asc: boolean = false;
  brand_desc: boolean = false;
  status_asc: boolean = false;
  status_desc: boolean = false;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private namsService: ThirdPartyService,
    private notify: NotificationService,
    private datePipe: DatePipe,
    public dialog: MatDialog,
    private el: ElementRef<HTMLElement>) { }

  @ViewChildren(MatPaginator) paginator = new QueryList<MatPaginator>();
  displayedColumns: string[] = ['check', 'BRAND', 'MEDIA', 'advertiser', 'date', 'description', 'status', 'action'];

  ngOnInit(): void {
    this.pagename = "NAMS - TAMS";
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.userName = this.userInfo.firstName;
  }

  ngAfterViewInit() {
    this.getNamsTable();
  }

  getMedium(val) {
    if (val && val.length > 0) {
      this.getComplaintsByMedium(val);
    } else {
      this.pageNumber = 0;
      this.dataSource.data = [];
      this.filterArray = [];
      this.MEDIUM.reset();
      this.startIndex = null;
      this.lastIndex = null;
      this.totalCount = null;
      this.pageSize = 10;
      if (this.ADVERTISER.value == '' && this.KEYWORD.value == '' && this.DATE_FROM.value == '' && this.DATE_TO.value == '') {
        this.getNamsTable();
        this.zeroComplaints = false;
      } else {
        this.zeroComplaints = false;
        this.filter_complaints(1);
      }
    }
  }

  getAdvertiser(val) {
    if (val && val.length > 0) {
      this.getComplaintsByAdvertiser(val);
    } else {
      this.pageNumber = 0;
      this.dataSource.data = [];
      this.filterArray = [];
      this.ADVERTISER.reset();
      this.startIndex = null;
      this.lastIndex = null;
      this.totalCount = null;
      this.pageSize = 10;
      if (this.KEYWORD.value == '' && this.MEDIUM.value == '' && this.DATE_FROM.value == '' && this.DATE_TO.value == '') {
        this.zeroComplaints = false;
        this.getNamsTable();
      } else {
        this.zeroComplaints = false;
        this.filter_complaints(1);
      }
    }
  }

  getKeyword(val) {
    if (val && val.length > 0) {
      this.getComplaintsByKeyword(val);
    } else {
      this.pageNumber = 0;
      this.dataSource.data = [];
      this.filterArray = [];
      this.KEYWORD.reset();
      this.startIndex = null;
      this.lastIndex = null;
      this.totalCount = null;
      this.pageSize = 10;
      if (this.ADVERTISER.value == '' && this.MEDIUM.value == '' && this.DATE_FROM.value == '' && this.DATE_TO.value == '') {
        this.getNamsTable();
        this.zeroComplaints = false;
      } else {
        this.zeroComplaints = false;
        this.filter_complaints(1);
      }
    }
  }

  getComplaintsByAdvertiser(val) {
    this.ADVERTISER.setValue(val);
    this.filter_complaints(1);
  }

  getComplaintsByMedium(val) {
    this.MEDIUM.setValue(val);
    this.filter_complaints(1);
  }

  getComplaintsByKeyword(val) {
    this.KEYWORD.setValue(val);
    this.filter_complaints(1);
  }

  clearFilter() {
    this.dataSource.data = [];
    this.filterArray = [];
    this.sortedArray = [];
    this.resArray = [];
    this.zeroComplaints = false;
    this.DATE_FROM.reset();
    this.DATE_TO.reset();
    this.ADVERTISER.reset();
    this.MEDIUM.reset();
    this.KEYWORD.reset();
    this.filterData = [];
    this.filterKeys = [];
    this.pageNumber = 0;
    this.brand_asc = false;
    this.brand_desc = false;
    this.status_asc = false;
    this.status_desc = false;
    this.filter = false;
    this.sortingKey = '';
    this.sortingOrder = '';
    this.getNamsTable();
  }

  getNamsTable() {
    this.pageNumber++;
    this.getNamsList()
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((namsData: any) => {
        this.loading = false;
        if (namsData.data.length > 0) {
          this.zeroComplaints = false;
          this.totalCount = namsData.data[0].TOTAL_COUNT;
          if (namsData.data) {
            this.resArray = this.resArray.concat(namsData.data[0].DATA);
          }
          if (this.pageNumber == 1) {
            if (this.pageSize > this.totalCount) {
              this.rangeLabel = 1 + ' - ' + this.totalCount;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          if (this.resArray.length == 0) {
            this.zeroComplaints = true;
          } else {
            this.zeroComplaints = false;
          }
          this.namsDataStore.$namsData = this.resArray;
          this.dataSource.data = this.namsDataStore.$namsData;
          this.dataSource = new MatTableDataSource<any>(this.namsDataStore.$namsData);
          this.dataSource.paginator = this.paginator.toArray()[0];
          this.dataSource.data.forEach(element => {
            if (element['REGISTERED'] == 1) {
              element['status'] = 'Confirmed';
            } else if (element['REGISTERED'] == null) {
              element['status'] = 'Unconfirmed';
            }
          })
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      });
  }
  filter_complaints(page) {
    this.startIndex = null;
    this.lastIndex = null;
    this.pageSize = 10;
    this.idArray = [];
    this.namsComplaints = [];
    this.pageNumber = page;
    this.filter = true;
    this.zeroComplaints = false;
    if (this.DATE_FROM.value != '' && this.DATE_TO.value != '') {
      if (this.datePipe.transform(this.DATE_TO.value, 'yyyy-MM-dd HH:mm:ss') < this.datePipe.transform(this.DATE_FROM.value, 'yyyy-MM-dd HH:mm:ss')) {
        this.notify.showNotification(
          "Date-to field should be greater than Date-from field",
          "top",
          "error",
          "any"
        )
        this.resArray = [];
        this.filterArray = [];
        this.dataSource.data = [];
        this.zeroComplaints = true;
        return;
      }
    }
    if (this.KEYWORD.value) {
      this.keyword = this.KEYWORD.value.trim();
    } else {
      this.keyword = '';
    }
    if (this.ADVERTISER.value) {
      this.advertiser = this.ADVERTISER.value.trim();
    } else {
      this.advertiser = '';
    }
    if (this.MEDIUM.value) {
      this.mediumName = this.MEDIUM.value.trim();
    } else {
      this.mediumName = '';
    }
    this.filterData = [
      { "name": "FROM_DATE", "value": this.datePipe.transform(this.DATE_FROM.value, 'yyyy-MM-dd HH:mm:ss') },
      { "name": "TO_DATE", "value": this.datePipe.transform(this.DATE_TO.value, 'yyyy-MM-dd HH:mm:ss') },
      { "name": "SEARCH_KEY", "value": this.keyword },
      { "name": "ADVERTISER", "value": this.advertiser },
      { "name": "MEDIUM", "value": this.mediumName },
      { "name": "PAGE", "value": this.pageNumber },
      { "name": "limit", "value": 20 },
    ]
    let fil = [];
    for (let i = 0; i < this.filterData.length; i++) {
      if (this.filterData[i].value != null) {
        fil.push(this.filterData[i])
      }
    }
    this.dataSource.data = [];
    this.namsService.filterComplaintsNams(fil, this.sortingKey, this.sortingOrder).subscribe((filteredData: any) => {
      this.resArray = [];
      this.filterArray = [];
      this.sortedArray = [];
      if (filteredData.data.length > 0) {
        if (filteredData.data[0].TOTAL_COUNT < this.pageSize) {
          this.rangeLabel = 1 + ' - ' + filteredData.data[0].TOTAL_COUNT;
        } else {
          this.rangeLabel = 1 + ' - ' + this.pageSize;
        }
        this.totalCount = filteredData.data[0].TOTAL_COUNT;
        this.filterArray = this.filterArray.concat(filteredData.data[0].DATA);
        this.namsDataStore.$namsData = this.filterArray;
        this.zeroComplaints = false;
        this.dataSource.data = this.namsDataStore.$namsData;
        this.dataSource = new MatTableDataSource<any>(this.namsDataStore.$namsData);
        this.dataSource.paginator = this.paginator.toArray()[0];
        this.dataSource.data.forEach(element => {
          if (element['REGISTERED'] == 1) {
            element['status'] = 'Confirmed';
          } else if (element['REGISTERED'] == null) {
            element['status'] = 'Unconfirmed';
          }
        })
      } else {
        this.filterArray = [];
        this.resArray = [];
        this.dataSource.data = [];
        this.zeroComplaints = true;
      }
    }, err => {
      this.loading = false;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  changePage(event) {
    this.startIndex = (event.pageIndex * this.pageSize) + 1;
    this.lastIndex = this.startIndex + (this.pageSize - 1);
    if (this.lastIndex > this.totalCount) {
      this.lastIndex = this.totalCount;
    }
    this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
    if (this.resArray.length != 0) {
      this.getNamsTable();
    }
    else if (this.filter == true) {
      this.pageNumber++;
      if (this.KEYWORD.value) {
        this.keyword = this.KEYWORD.value.trim();
      } else {
        this.keyword = '';
      }
      if (this.ADVERTISER.value) {
        this.advertiser = this.ADVERTISER.value.trim();
      } else {
        this.advertiser = '';
      }
      if (this.MEDIUM.value) {
        this.mediumName = this.MEDIUM.value.trim();
      } else {
        this.mediumName = '';
      }
      this.filterData = [
        { "name": "FROM_DATE", "value": this.datePipe.transform(this.DATE_FROM.value, 'yyyy-MM-dd HH:mm:ss') },
        { "name": "TO_DATE", "value": this.datePipe.transform(this.DATE_TO.value, 'yyyy-MM-dd HH:mm:ss') },
        { "name": "SEARCH_KEY", "value": this.keyword },
        { "name": "ADVERTISER", "value": this.advertiser },
        { "name": "MEDIUM", "value": this.mediumName },
        { "name": "PAGE", "value": this.pageNumber },
        { "name": "limit", "value": 20 },
      ]
      let fil = [];
      for (let i = 0; i < this.filterData.length; i++) {
        if (this.filterData[i].value != null) {
          fil.push(this.filterData[i])
        }
      }
      this.namsService.filterComplaintsNams(fil, this.sortingKey, this.sortingOrder).subscribe((filteredData: any) => {
        this.resArray = [];
        if (filteredData.data.length > 0) {
          this.zeroComplaints = false;
          this.totalCount = filteredData.data[0].TOTAL_COUNT;
          if (this.filterArray.length != 0 && this.sortedArray.length == 0) {
            this.filterArray = this.filterArray.concat(filteredData.data[0].DATA);
            this.namsDataStore.$namsData = this.filterArray;
          }
          else if (this.filterArray.length == 0 && this.sortedArray.length != 0) {
            this.sortedArray = this.sortedArray.concat(filteredData.data[0].DATA);
            this.namsDataStore.$namsData = this.sortedArray;
          }
          this.dataSource.data = this.namsDataStore.$namsData;
          this.dataSource = new MatTableDataSource<any>(this.namsDataStore.$namsData);
          this.dataSource.paginator = this.paginator.toArray()[0];
          this.dataSource.data.forEach(element => {
            if (element['REGISTERED'] == 1) {
              element['status'] = 'Confirmed';
            } else if (element['REGISTERED'] == null) {
              element['status'] = 'Unconfirmed';
            }
          })
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      });
    }
    else {
      this.pageNumber++;
      this.filterData = [
        { "name": "PAGE", "value": this.pageNumber }
      ]
      this.namsService.filterComplaintsNams(this.filterData, this.sortingKey, this.sortingOrder).subscribe((sortedData: any) => {
        this.resArray = [];
        this.filterArray = [];
        if (sortedData.data.length > 0) {
          this.zeroComplaints = false;
          this.totalCount = sortedData.data[0].TOTAL_COUNT;
          this.sortedArray = this.sortedArray.concat(sortedData.data[0].DATA);
          this.namsDataStore.$namsData = this.sortedArray;
          this.dataSource.data = this.namsDataStore.$namsData;
          this.dataSource = new MatTableDataSource<any>(this.namsDataStore.$namsData);
          this.dataSource.paginator = this.paginator.toArray()[0];
          this.dataSource.data.forEach(element => {
            if (element['REGISTERED'] == 1) {
              element['status'] = 'Confirmed';
            } else if (element['REGISTERED'] == null) {
              element['status'] = 'Unconfirmed';
            }
          })
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      });
    }
  }

  sortComplaints(key, order) {
    this.sortingKey = key;
    this.sortingOrder = order;
    if (order != '') {
      if (order == 'ASC') {
        if (key == 'BRAND') {
          this.brand_asc = true;
          this.brand_desc = false;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'REGISTERED') {
          this.status_asc = true;
          this.status_desc = false;
          this.brand_asc = false;
          this.brand_desc = false;
        }
      }
      else if (order == 'DESC') {
        if (key == 'BRAND') {
          this.brand_asc = false;
          this.brand_desc = true;
          this.status_asc = false;
          this.status_desc = false;
        }
        else if (key == 'REGISTERED') {
          this.status_asc = false;
          this.status_desc = true;
          this.brand_asc = false;
          this.brand_desc = false;
        }
      }
      this.idArray = [];
      this.namsComplaints = [];
      this.pageNumber = 1;
      this.zeroComplaints = false;
      if (this.KEYWORD.value) {
        this.keyword = this.KEYWORD.value.trim();
      } else {
        this.keyword = '';
      }
      if (this.ADVERTISER.value) {
        this.advertiser = this.ADVERTISER.value.trim();
      } else {
        this.advertiser = '';
      }
      if (this.MEDIUM.value) {
        this.mediumName = this.MEDIUM.value.trim();
      } else {
        this.mediumName = '';
      }
      this.filterData = [
        { "name": "FROM_DATE", "value": this.datePipe.transform(this.DATE_FROM.value, 'yyyy-MM-dd HH:mm:ss') },
        { "name": "TO_DATE", "value": this.datePipe.transform(this.DATE_TO.value, 'yyyy-MM-dd HH:mm:ss') },
        { "name": "SEARCH_KEY", "value": this.keyword },
        { "name": "ADVERTISER", "value": this.advertiser },
        { "name": "MEDIUM", "value": this.mediumName },
        { "name": "PAGE", "value": this.pageNumber },
        { "name": "limit", "value": 20 },
        
      ]
      this.filterKeys = [];
      for (let i = 0; i < this.filterData.length; i++) {
        if (this.filterData[i].value != null) {
          this.filterKeys.push(this.filterData[i])
        }
      }
      this.dataSource.data = [];
      this.namsService.filterComplaintsNams(this.filterKeys, key, order).subscribe((sortedData: any) => {
        this.resArray = [];
        this.filterArray = [];
        this.sortedArray = [];
        if (sortedData.data.length > 0) {
          if (sortedData.data[0].TOTAL_COUNT < this.pageSize) {
            this.rangeLabel = 1 + ' - ' + sortedData.data[0].TOTAL_COUNT;
          } else {
            this.rangeLabel = 1 + ' - ' + this.pageSize;
          }
          this.totalCount = sortedData.data[0].TOTAL_COUNT;
          this.zeroComplaints = false;
          this.sortedArray = this.sortedArray.concat(sortedData.data[0].DATA);
          this.namsDataStore.$namsData = this.sortedArray;
          this.dataSource.data = this.namsDataStore.$namsData;
          this.dataSource = new MatTableDataSource<any>(this.namsDataStore.$namsData);
          this.dataSource.paginator = this.paginator.toArray()[0];
          this.dataSource.data.forEach(element => {
            if (element['REGISTERED'] == 1) {
              element['status'] = 'Confirmed';
            } else if (element['REGISTERED'] == null) {
              element['status'] = 'Unconfirmed';
            }
          })
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      });
    }
    else if (order == '') {
      this.brand_asc = false;
      this.brand_desc = false;
      this.status_asc = false;
      this.status_desc = false;
      if (this.filter == true) {
        this.filter_complaints(1);
      }
      else {
        this.dataSource.data = [];
        this.resArray = [];
        this.sortedArray = [];
        this.zeroComplaints = false;
        this.pageNumber = 0;
        this.getNamsTable();
      }
    }
  }

  getNamsList() {
    this.limit = 20;
    this.request$ = this.namsService.listComplaintsList(this.pageNumber, this.limit);
    return this.request$;
  }

  getComplaintById(compId) {
    this.namsService.getDetails(compId).subscribe(res => {
      let userObj = res.data;
      this.namsDataStore.$namsData.forEach((t: any, i) => {
        if (t.ID === userObj.ID) {
          this.namsDataStore.$namsData[i] = userObj;
        }
      });
      this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
      this.updateNamsTableDate(userObj.ID);
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  updateNamsTableDate(id) {
    this.$namsData.subscribe((res) => {
      if (this.filter == true) {
        this.filterArray = res;
      } else {
        this.resArray = res;
      }
      this.dataSource.data = res;
      this.dataSource = new MatTableDataSource<any>(res);
      this.dataSource.paginator = this.paginator.toArray()[0];
      this.dataSource.data.forEach(element => {
        if (id == element.ID) {
          if (element['REGISTERED'] == 1) {
            element['status'] = 'Confirmed';
          } else if (element['REGISTERED'] == null) {
            element['status'] = 'Unconfirmed';
          }
        }
      });
    });
  }

  private onFinalize(): void {
    this.request$ = null;
  }

  public onScrollDown(): void {
    if (this.lastRespSize == 5) {
      this.getNamsTable();
    }
  }

  applyFilter(filterValue: string) {
    if (!!filterValue) {
      filterValue = filterValue.trim();
      filterValue = filterValue.toLowerCase();
    }
    this.dataSource.filter = filterValue;
  }

  onSelectAll(e: any): void {
    for (let i = 0; i < this.dataSource.data.length; i++) {
      const item = this.dataSource.data[i];
      item.isselected = e;
      if (item.isselected == true) {
        this.idArray.push(item.ID.toString());
      }
      else if (item.isselected == false) {
        for (let i = 0; i < this.idArray.length; i++) {
          if (this.idArray[i] == item.ID) {
            this.idArray[i] = null;
          }
        }
      }
    }
  }

  selectComplaint(value, id, status) {
    let ID = id.toString();
    if (value == true) {
      this.num++;
      this.idArray.push(ID);
      this.dat = { "id": ID, "status": status }
      this.namsComplaints.push(this.dat);
      for (let i = 0; i < this.namsComplaints.length; i++) {
        this.status = this.namsComplaints[0].status;
        if (this.status != this.namsComplaints[i].status || this.status == 'Confirmed') {
          this.disableAction = true;
          this.disableDelete = false;
          if (this.status != this.namsComplaints[i].status) {
            this.notify.showNotification(
              "Please choose the complaints belonging to same category",
              "top",
              "warning",
              0
            )
            this.disableDelete = true;
          }
        } else {
          this.disableAction = false;
          this.disableDelete = true;
        }
      }
      // if (this.num == this.dataSource.data.length) {
      //   this.select_all = true;
      // } else {
      //   this.select_all = false;
      // }
    }
    else if (value == false) {
      this.num--;
      for (let i = 0; i < this.idArray.length; i++) {
        if (this.idArray[i] == ID) {
          this.idArray[i] = null;
        }
      }
      for (let i = 0; i < this.namsComplaints.length; i++) {
        if (this.namsComplaints[i].id == ID) {
          this.namsComplaints.splice(i, 1);
        }
      }
      for (let i = 0; i < this.namsComplaints.length; i++) {
        this.status = this.namsComplaints[0].status;
        if (this.status != this.namsComplaints[i].status || this.status == 'Confirmed') {
          this.disableAction = true;
          this.disableDelete = false;
          if (this.status != this.namsComplaints[i].status) {
            this.disableDelete = true;
          }
        } else {
          this.disableAction = false;
          this.disableDelete = true;
        }
      }
      // if (this.num == this.dataSource.data.length) {
      //   this.select_all = true;
      // } else {
      //   this.select_all = false;
      // }
    }
    if (this.namsComplaints.length == 0) {
      this.disableDelete = true;
      this.disableAction = true;
    }
  }

  complaintDetails(ID, status) {
    const dialogRef = this.dialog.open(NamsDetailsComponent, {
      width: '1500px',
      height: '550px',
      data: { ID, "status": status, "nams": "tams" },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'delete' || data === 'add') {
          this.namsDataStore.$namsData.forEach((t: any, i) => {
            if (t.ID === ID) {
              this.namsDataStore.$namsData.splice(i, 1);
            }
          });
          if (data === 'delete') {
            this.totalCount = this.totalCount - 1;
            if (this.totalCount !== 0) {
              if (this.pageSize > this.totalCount) {
                this.pageSize = this.totalCount;
                this.rangeLabel = 1 + ' - ' + this.pageSize;
              }
              if (this.lastIndex > this.totalCount) {
                this.lastIndex = this.totalCount;
                this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
              }
            } else {
              this.zeroComplaints = true;
            }
          }
          this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
          this.$namsData.subscribe((res) => {
            if (this.filter == true) {
              this.filterArray = res;
            } else {
              this.resArray = res;
            }
            this.dataSource.data = res;
            this.dataSource = new MatTableDataSource(res);
            this.dataSource.paginator = this.paginator.toArray()[0];
          })
        }
        else if (data === 'tams-refresh') {
          this.namsService.getDetails(ID).subscribe((res: any) => {
            let userObj = res.data;
            this.namsDataStore.$namsData.forEach((t: any, i) => {
              if (t.ID === userObj.ID) {
                this.namsDataStore.$namsData[i] = userObj;
              }
            });
            this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
            this.$namsData.subscribe((res) => {
              if (this.filter == true) {
                this.filterArray = res;
              } else {
                this.resArray = res;
              }
              this.dataSource.data = res;
              this.dataSource = new MatTableDataSource(res);
              this.dataSource.paginator = this.paginator.toArray()[0];
              this.dataSource.data.forEach(element => {
                if (userObj.ID == element.ID) {
                  if (element['REGISTERED'] == 1) {
                    element['status'] = 'Confirmed';
                  } else {
                    element['status'] = 'Unconfirmed';
                  }
                }
              });
            })
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
        }
      }
    );
  }

  deleteComplaint(ID) {
    this.idArray = [];
    this.namsComplaints = [];
    this.idArray.push(ID.toString());
    this.confirmationMsg.title = 'Are you sure you want to delete the complaint ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.idArray, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.namsService
          .deleteNamsComplaint(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
              this.namsDataStore.$namsData.forEach((t: any, i) => {
                if (t.ID === ID) {
                  this.namsDataStore.$namsData.splice(i, 1);
                }
              });
              this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
              this.$namsData.subscribe((res) => {
                if (this.filter == true) {
                  this.filterArray = res;
                } else {
                  this.resArray = res;
                }
                this.dataSource = new MatTableDataSource<any>(res);
                this.dataSource.paginator = this.paginator.toArray()[0];
              })
              this.totalCount = this.totalCount - 1;
              if (this.totalCount !== 0) {
                if (this.pageSize > this.totalCount) {
                  this.pageSize = this.totalCount;
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
                if (this.lastIndex > this.totalCount) {
                  this.lastIndex = this.totalCount;
                  this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                }
              } else {
                this.zeroComplaints = true;
              }
              this.idArray = [];
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  bulkDelete() {
    this.confirmationMsg.title = 'Are you sure you want to delete the complaints ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.idArray, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.namsService
          .deleteNamsComplaint(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
              for (let j = 0; j < this.idArray.length; j++) {
                this.namsDataStore.$namsData.forEach((t: any, i) => {
                  if (t.ID == this.idArray[j]) {
                    this.namsDataStore.$namsData.splice(i, 1);
                  }
                });
              }
              this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
              this.$namsData.subscribe((res) => {
                if (this.filter == true) {
                  this.filterArray = res;
                } else {
                  this.resArray = res;
                }
                this.dataSource.data = res;
                this.dataSource = new MatTableDataSource<any>(res);
                this.dataSource.paginator = this.paginator.toArray()[0];
              })
              var filtered = this.idArray.filter(function (el) {
                return el != null;
              });
              this.totalCount = this.totalCount - filtered.length;
              if (this.totalCount !== 0) {
                if (this.pageSize > this.totalCount) {
                  this.pageSize = this.totalCount;
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
                if (this.lastIndex > this.totalCount) {
                  this.lastIndex = this.totalCount;
                  this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                }
              } else {
                this.zeroComplaints = true;
              }
              this.idArray = [];
              this.namsComplaints = [];
              this.disableAction = true;
              this.disableDelete = true;
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  rejectComplaint(ID) {
    this.idArray = [];
    this.namsComplaints = [];
    this.idArray.push(ID.toString());
    this.confirmationMsg.title = 'Are you sure you want to reject the complaint ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.idArray, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.namsService
          .rejectNamsComplaint(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
              this.namsDataStore.$namsData.forEach((t: any, i) => {
                if (t.ID === ID) {
                  this.namsDataStore.$namsData.splice(i, 1);
                }
              });
              this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
              this.$namsData.subscribe((res) => {
                if (this.filter == true) {
                  this.filterArray = res;
                } else {
                  this.resArray = res;
                }
                this.dataSource.data = res;
                this.dataSource = new MatTableDataSource<any>(res);
                this.dataSource.paginator = this.paginator.toArray()[0];
              })
              this.totalCount = this.totalCount - 1;
              if (this.totalCount !== 0) {
                if (this.pageSize > this.totalCount) {
                  this.pageSize = this.totalCount;
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
                if (this.lastIndex > this.totalCount) {
                  this.lastIndex = this.totalCount;
                  this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                }
              } else {
                this.zeroComplaints = true;
              }
              this.idArray = [];
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  bulkReject() {
    this.confirmationMsg.title = 'Are you sure you want to reject the complaints ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.idArray, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.namsService
          .rejectNamsComplaint(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
              for (let j = 0; j < this.idArray.length; j++) {
                this.namsDataStore.$namsData.forEach((t: any, i) => {
                  if (t.ID == this.idArray[j]) {
                    this.namsDataStore.$namsData.splice(i, 1);
                  }
                });
              }
              this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
              this.$namsData.subscribe((res) => {
                if (this.filter == true) {
                  this.filterArray = res;
                } else {
                  this.resArray = res;
                }
                this.dataSource.data = res;
                this.dataSource = new MatTableDataSource<any>(res);
                this.dataSource.paginator = this.paginator.toArray()[0];
              })
              var filtered = this.idArray.filter(function (el) {
                return el != null;
              });
              this.totalCount = this.totalCount - filtered.length;
              if (this.totalCount !== 0) {
                if (this.pageSize > this.totalCount) {
                  this.pageSize = this.totalCount;
                  this.rangeLabel = 1 + ' - ' + this.pageSize;
                }
                if (this.lastIndex > this.totalCount) {
                  this.lastIndex = this.totalCount;
                  this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                }
              } else {
                this.zeroComplaints = true;
              }
              this.idArray = [];
              this.namsComplaints = [];
              this.disableAction = true;
              this.disableDelete = true;
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  shortList(ID) {
    this.namsComplaints = [];
    this.idArray = [];
    this.idArray.push(ID.toString());
    this.confirmationMsg.title = 'Are you sure you want to shortlist the complaint ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.idArray, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.namsService
          .shortlistNamsComplaint(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
              this.getComplaintById(ID);
              this.idArray = [];
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  bulkShortlist() {
    this.confirmationMsg.title = 'Are you sure you want to shortlist the complaints ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.idArray, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.namsService
          .shortlistNamsComplaint(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
              for (let j = 0; j < this.idArray.length; j++) {
                this.namsDataStore.$namsData.forEach((t: any, i) => {
                  if (t.ID == this.idArray[j]) {
                    this.getComplaintById(t.ID);
                  }
                });
              }
              this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
              this.$namsData.subscribe((res) => {
                if (this.filter == true) {
                  this.filterArray = res;
                } else {
                  this.resArray = res;
                }
                this.dataSource.data = res;
                this.dataSource = new MatTableDataSource<any>(res);
                this.dataSource.paginator = this.paginator.toArray()[0];
              })
              this.idArray = [];
              this.namsComplaints = [];
              this.disableAction = true;
              this.disableDelete = true;
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

}