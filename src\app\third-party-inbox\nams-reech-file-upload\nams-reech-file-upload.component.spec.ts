import { ComponentFixture, TestBed } from '@angular/core/testing';

import { NamsReechFileUploadComponent } from './nams-reech-file-upload.component';

describe('NamsReechFileUploadComponent', () => {
  let component: NamsReechFileUploadComponent;
  let fixture: ComponentFixture<NamsReechFileUploadComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ NamsReechFileUploadComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(NamsReechFileUploadComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
