 .timeline-container {
   background-color: #ffffff;
   width: 100%;
   height: 100%;
   margin-bottom: 1%;
   box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
 }

 .time-container {
   width: 95%;
   display: flex;
 }

 .fixed-header {
   background-color: #ffffff !important;
   height: 36px;
   position: fixed;
   opacity: 1 !important;
   width: 100%;
   z-index: 10 !important;
 }

 .scroll-container {
   /* height: 93%; */
   height: 72vh;
   width: 100%;
   overflow-y: scroll;
   overflow-x: hidden;
   position: absolute;
   top: 36px;
 }

 .scroll-container::-webkit-scrollbar {
   display: none;
 }

 .scroll-container {
   -ms-overflow-style: none;
   scrollbar-width: none;
 }

 .timeline {
   width: 100%;
   height: 90%;
   padding-top: 20px;
   margin-left: 20px;
   position: relative;
 }

 .time-enents {
   font-style: normal;
   font-weight: 600;
   font-size: 14px;
   color: #000000;
 }

 .timeline ul {
   list-style: none;
 }

 .timeline ul li {
   margin-bottom: 10px;
 }

 .timeline-content .date {
   font-size: 13px;
   font-weight: 200;
   letter-spacing: 2px;
 }

 @media only screen and (min-width: 768px) {
   .timeline:before {
     content: "";
     position: absolute;
     top: 0;
     left: 32px;
     transform: translateX(-50%);
     width: 4px;
     height: 100%;
     background-color: #D8DCDE;
   }

   .timeline ul li {
     width: 100%;
     position: relative;
     transform: translateX(30px);
   }

   .timeline ul li::after {
     content: "";
     position: absolute;
     height: 16px;
     width: 16px;
     border-radius: 50%;
     background-color: #0088CB;
     border: 4px solid rgba(255, 255, 255, 0.726);
     top: 10px;
     transform: translate(-50%, -50%);
     left: -30px;
   }

   .timeline-content .date {
     position: absolute;
     top: -30px;
   }

   .timeline ul li:hover::before {
     background-color: aqua;
   }
 }

 :host {
   position: fixed;
   left: auto;
   top: 0;
   right: 0;
   bottom: 0;
   width: 100%;
   height: 100%;
   min-width: 50px;
   z-index: 9999 !important;
 }