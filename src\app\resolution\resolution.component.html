<div fxLayout="column">
    <div fxLayout="row" style="padding-top: 23px;padding-left:18px;padding-right:18px;background: #F8F9F9;">
        <div style="font-size: 16px;font-weight:600">Marks as Resolved</div>
        <div fxFlex="76%"></div>
        <div class="close-div">
            <button mat-icon-button class="close-btn" mat-dialog-close>
                <mat-icon style="margin-bottom: 2px;">close</mat-icon>
            </button>
        </div>
    </div>
    <mat-divider></mat-divider>
    <div fxLayout="row" style="margin-top: 21px;">
        <div fxFlex="65%">
            <form [formGroup]="resolutionForm">
                <mat-form-field appearance="outline" class="input-field">
                    <input matInput placeholder="Subject line for remark..." maxlength="500" id="subject"
                        formControlName="subject" autocomplete="off" required>
                    <mat-error *ngIf="resolutionForm.get('subject').touched && resolutionForm.controls['subject'].errors?.required">
                        Subject is required
                    </mat-error>
                </mat-form-field><br>
                <mat-form-field appearance="outline" class="input-field">
                    <textarea matInput maxlength="15000" rows="11" placeholder="Please describe, how it is resolved..."
                        formControlName="resolution" required></textarea>
                    <mat-error *ngIf="resolutionForm.get('resolution').touched && resolutionForm.controls['resolution'].errors?.required">
                        Resolution is required
                    </mat-error>
                </mat-form-field>
                <span style="margin-left: 61%">
                    <button (click)="cancel()" class="cancel-btn" style="background: none !important;border: none;"
                        [disabled]="resolutionForm.invalid">Cancel</button>
                    <button class="theme-blue-button-admin" style="border: none;padding-left: 8px;padding-right: 10px;"
                        (click)="onSubmit(resolutionForm.value)" [disabled]="resolutionForm.invalid">Submit</button>
                </span>
            </form>
        </div>
        <div fxFlex="8%">
            <mat-divider [vertical]="true" style="height: 406px;margin-right: 29px;margin-top: -22px;"></mat-divider>
        </div>
        <div fxFlex="49%" class="resolutions">
            <span style="font-size: 14px;font-weight:600">Resolution Statements:</span>
            <div *ngFor="let record of records">
                <div class="recommendation-box">
                    <div style="word-wrap: break-word;">
                        <span class="box-heading">Subject: </span> <span class="box-text">{{record.SUBJECT}}</span>
                    </div>
                    <div style="word-wrap: break-word;">
                        <span class="box-heading">Description: </span>
                        <span class="box-text">{{record.RESOLUTION}}</span>
                    </div>
                </div>
                <span style="font-size: 11px;margin-left: 68%;"> - {{record.CREATED_DATE | date: 'dd/MM/yyyy'}}:
                    {{record.CREATED_DATE | date: 'h:mm a'}}</span>
            </div><br>
        </div>
    </div>
</div>