import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { MaterialModule } from "../material/material.module";
import { CommonModule } from "@angular/common";
import { ClipboardModule } from '@angular/cdk/clipboard';
import { SharedModule } from "../shared/shared.module";
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatRadioModule } from '@angular/material/radio';
import { HomeRoutingModule } from "./home-routing.module";
import { AdminDashboardComponent } from "./admin-dashboard/admin-dashboard.component";
import { UserDashboardComponent } from "./user-dashboard/user-dashboard.component";
import { CaseModule } from "../cases/cases.module";
import { CreateConfirmDialogComponent } from './create-confirm-dialog/create-confirm-dialog.component';
import { MessageConversationComponent } from './message-conversation/message-conversation.component';
import { InfiniteScrollModule } from "ngx-infinite-scroll";
import { AdvertisementMediumChartComponent } from './admin-dashboard/advertisement-medium-chart/advertisement-medium-chart.component';
import { ComplaintTypeChartComponent } from './admin-dashboard/complaint-type-chart/complaint-type-chart.component';
import { ComplaintSourceChartComponent } from './admin-dashboard/complaint-source-chart/complaint-source-chart.component';
import { ClassificationChartComponent } from './admin-dashboard/classification-chart/classification-chart.component';
import { ChapterGuidelineChartComponent } from './admin-dashboard/chapter-guideline-chart/chapter-guideline-chart.component';
import { ComplaintResolutionStageStatusProcessChartComponent } from './admin-dashboard/complaint-resolution-stage-status-process-chart/complaint-resolution-stage-status-process-chart.component';

@NgModule({
  declarations: [
    AdminDashboardComponent,
    UserDashboardComponent,
    CreateConfirmDialogComponent,
    MessageConversationComponent,
    AdvertisementMediumChartComponent,
    ComplaintTypeChartComponent,
    ComplaintSourceChartComponent,
    ClassificationChartComponent,
    ChapterGuidelineChartComponent,
    ComplaintResolutionStageStatusProcessChartComponent
  ],
  imports: [
    HomeRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    MaterialModule,
    CommonModule,
    ClipboardModule,
    SharedModule,
    FlexLayoutModule,
    CaseModule,
    MatRadioModule,
    InfiniteScrollModule
  ],
  entryComponents: [
    CreateConfirmDialogComponent,
    MessageConversationComponent
  ],
  providers: [],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class HomeModule { }