import { Component, HostListener, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Inject } from '@angular/core';
import { environment } from 'src/environments/environment';
import { saveAs as importedSaveAs } from "file-saver";
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';

@Component({
  selector: 'app-view-media-file',
  templateUrl: './view-media-file.component.html',
  styleUrls: ['./view-media-file.component.scss']
})
export class ViewMediaFileComponent implements OnInit {

  panelOpenState = false;
  media_link = "https://www.youtube.com/watch?v=fYSC8P9n8Hc";
  medium = "Digital media";
  public bucketUrl = `${environment.BUCKET_URL}`;
  platform = "Facebook";
  collapsedHeight: string = '37px';
  adMedium: any;
  adDoc: any;
  url: string;
  imgURL: string;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private cs: ComplaintsService,
    private notify: NotificationService,
    public dialogRef: MatDialogRef<ViewMediaFileComponent>,
  ) { }

  ngOnInit(): void {
    this.adMedium = this.data.adMedium
    this.adDoc = this.data.adDocs
    for (let i = 0; i < this.adMedium.length; i++) {
      this.adMedium[i].docInfo = [];
      this.adDoc.filter(el => {
        if (el.ADVERTISEMENT_MEDIUM_ID == this.adMedium[i].ID && el.ATTACHMENT_SOURCE !== '') {
          this.adMedium[i].docInfo.push({
            url: el.ATTACHMENT_SOURCE,
            name: el.ATTACHMENT_NAME,
            date: el.CREATED_DATE
          });
        }
      })
    }
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  download(doc, source) {
    this.url = this.bucketUrl + source;
    this.cs.downloadFile(this.url).subscribe(data => {
      importedSaveAs(data, doc)
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    });
  }

  preview(doc, source) {
    this.imgURL = this.bucketUrl + source;
    window.open(this.imgURL, 'window 1', '');
  }

}