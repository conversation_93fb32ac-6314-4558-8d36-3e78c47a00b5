import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { MatTooltip } from '@angular/material/tooltip';
import { Directive, HostListener, ElementRef, Input } from '@angular/core';
import { ComplaintsService } from '../services/complaints.service';

@Component({
  selector: 'app-icons',
  templateUrl: './icons.component.html',
  styleUrls: ['./icons.component.css']
})

export class IconsComponent implements OnInit {
  @ViewChild('tooltip') tooltip: MatTooltip;
  isClickedDashboard: boolean = false;
  isClickedAdmin: boolean = false;
  active = false;
  pagename = "";
  activePage: string;
  userInfo: any;

  constructor(private router: Router, public elementRef: ElementRef,
    private cs: ComplaintsService,
    private complaintService: ComplaintsService) { }

  ngOnInit(): void {
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
  }

  administration() {
    this.pagename = 'Administration';
    this.router.navigateByUrl('auth/user-administration');
  }

  userManagement() {
    this.pagename = 'userManagement';
    this.router.navigateByUrl('auth/user-administration');
  }

  memberManagement() {
    this.pagename = 'memberManagement';
    this.router.navigateByUrl('auth/member-administration');
  }

  fieldManagement() {
    this.pagename = 'fieldManagement';
    this.router.navigateByUrl('auth/field-administration');
  }

  dashboard() {
    this.pagename = 'Dashboard';
    this.ngOnInit();
    if (this.userInfo.roleId == 4 || this.userInfo.roleId == 5 || this.userInfo.roleId == 7 || this.userInfo.roleId == 8) {
      this.router.navigateByUrl('home/user-dashboard');
    } else if (this.userInfo.roleId == 1 || this.userInfo.roleId == 2 || this.userInfo.roleId == 3 || this.userInfo.roleId == 6) {
      this.router.navigateByUrl('home/admin-dashboard');
    }
  }

  showIntraAdminPage() {
    this.pagename = 'Administration';
    this.router.navigateByUrl('auth/intra-administration');
  }

  manageCases() {
    this.pagename = 'Manage';
    this.ngOnInit();
    this.complaintService.updateStep('direct');
    this.cs.updateChatbotComplaintId(0);
    this.cs.updateNamsComplaintId = 0;
    this.cs.updateWhatsappComplaintId(0);
    this.router.navigateByUrl('cases/manage-cases');
    this.activePage = this.cs.getActivePage();
  }

  inbox() {
    this.pagename = 'Inbox';
    this.ngOnInit();
    this.router.navigateByUrl('/inbox');
    this.activePage = this.cs.getActivePage();
  }

  nams() {
    this.pagename = 'Third Party Inbox';
    this.router.navigateByUrl('third-party/nams');
  }

  namsReech() {
    this.pagename = 'NAMS - REECH';
    this.router.navigateByUrl('third-party/nams-reech');
  }

  taskTable() {
    this.pagename = 'Tasks';
    this.router.navigateByUrl('tasks/task-table');
    this.activePage = this.cs.getActivePage();
  }

  meeting() {
    this.pagename = 'Meeting';
    this.router.navigateByUrl('/meeting');
    this.activePage = this.cs.getActivePage();
  }

  dataMigration() {
    this.pagename = 'Archive';
    if (this.userInfo.roleId == 1 || this.userInfo.roleId == 2 || this.userInfo.roleId == 3 || this.userInfo.roleId == 6) {
      this.router.navigateByUrl('/archive');
    } else if (this.userInfo.roleId == 4 || this.userInfo.roleId == 5 || this.userInfo.roleId == 7 || this.userInfo.roleId == 8) {
      this.router.navigateByUrl('/archived-data');
    }
    this.activePage = this.cs.getActivePage();
  }

  categorise_complaints() {
    this.pagename = 'Reports';
    this.router.navigate(['/categorised-complaints']);
    this.activePage = this.cs.getActivePage();
  }

  over() {
    this.elementRef.nativeElement.classList.add('show-icon');
    this.elementRef.nativeElement.classList.add('tooltip-span');
  }

  complaint_tracking() {
    this.pagename = 'Reports';
    this.router.navigate(['/complaints-tracking']);
  }

  logout() {
    this.router.navigateByUrl('../auth/');
  }

  activeColor() {
    if (this.isClickedDashboard == true || this.isClickedAdmin == true) {
      return 'white';
    }
    else {
      return '#000000';
    }
  }

  activeBGcolor() {
    if (this.isClickedDashboard == true || this.isClickedAdmin == true) {
      return '#f3b408';
    }
    else {
      return 'white';
    }
  }
}