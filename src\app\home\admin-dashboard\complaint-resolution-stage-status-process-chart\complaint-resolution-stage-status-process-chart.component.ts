import { AfterViewInit, Component, ElementRef, OnInit } from '@angular/core';
import * as d3 from 'd3';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';

@Component({
  selector: 'app-complaint-resolution-stage-status-process-chart',
  templateUrl: './complaint-resolution-stage-status-process-chart.component.html',
  styleUrls: ['./complaint-resolution-stage-status-process-chart.component.scss']
})
export class ComplaintResolutionStageStatusProcessChartComponent implements OnInit, AfterViewInit {

  loading: boolean = true;

  constructor(
    private el: ElementRef,
    private cs: ComplaintsService,
    private notify: NotificationService
  ) { }

  ngOnInit(): void { }

  ngAfterViewInit(): void {
    this.getChartData();
  }

  getChartData() {
    this.cs.getMasterData().subscribe(res => {
      const data = [
        {
          name: "<5",
          value: 19912018
        },
        {
          name: "5-9",
          value: 20501982
        },
        {
          name: "10-14",
          value: 20679786
        },
        {
          name: "15-19",
          value: 21354481
        },
        {
          name: "20-24",
          value: 22604232
        },
        {
          name: "25-29",
          value: 21698010
        },
        {
          name: "30-34",
          value: 21183639
        }
      ];
      this.drawChart(data);
    }, err => {
      this.loading = false;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  drawChart(data: any): void {
    this.loading = false;
    const element = this.el.nativeElement.querySelector('#complaint_resolution_stage_status_process');
    element.innerHTML = '';

    const width = element.clientWidth;
    const height = Math.min(width, 500);
    const radius = width / 2;

    const arc = d3.arc()
      .innerRadius(radius * 0.67)
      .outerRadius(radius - 1);

    const pie = d3.pie()
      .padAngle(1 / radius)
      .sort(null)
      .value(d => d.value);

    const color = d3.scaleOrdinal()
      .domain(data.map(d => d.name))
      .range(d3.quantize(t => d3.interpolateSpectral(t * 0.8 + 0.1), data.length).reverse());

    const svg = d3.create("svg")
      .attr("width", width)
      .attr("height", height)
      .attr("viewBox", [-width / 2, -height / 2, width, height])
      .attr("style", "max-width: 100%; height: auto;");

    svg.append("g")
      .selectAll()
      .data(pie(data))
      .join("path")
      .attr("fill", d => color(d.data.name))
      .attr("d", arc)
      .append("title")
      .text(d => `${d.data.name}: ${d.data.value.toLocaleString()}`);

    svg.append("g")
      .attr("font-family", "sans-serif")
      .attr("font-size", 12)
      .attr("text-anchor", "middle")
      .selectAll()
      .data(pie(data))
      .join("text")
      .attr("transform", d => `translate(${arc.centroid(d)})`)
      .call(text => text.append("tspan")
        .attr("y", "-0.4em")
        .attr("font-weight", "bold")
        .text(d => d.data.name))
      .call(text => text.filter(d => (d.endAngle - d.startAngle) > 0.25).append("tspan")
        .attr("x", 0)
        .attr("y", "0.7em")
        .attr("fill-opacity", 0.7)
        .text(d => d.data.value.toLocaleString("en-US")));

    element.appendChild(svg.node());
  }

}