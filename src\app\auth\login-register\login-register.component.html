<div class="main-container" fxLayout="column" fxLayoutAlign="center center" role="main">
  <div class="content-container">
    <div class="headline-container" fxLayout="column" fxLayoutGap="33px" *ngIf="!isMobileScreen">
      <div>
        <img src="../assets/images/logo-with-title.png"/>
      </div>
      <!-- <div>
        <img src="../assets/images/contest.png" class="contest-img"/>
      </div> -->
    </div>
    <div class="headline-container" *ngIf="isMobileScreen">
      <img src="../assets/images/logo-with-title.png" class="logo-img"/>
    </div>
    <!-- <div class="contest-div2" *ngIf="isMobileScreen">
      <img src="../assets/images/contest.png" class="contest-img"/>
    </div> -->
    <div class="login-box-container" *ngIf="!isMobileScreen" fxLayout="row" fxLayoutAlign="start center">
      <div [@showHide]="showLoginContainer == true ? 'show' : 'hide'" class="login-box" fxLayout="column" fxLayoutAlign="start start">
        <div class="login-container">
          <div class="login-head">Login</div>
          <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5px" style="margin-top: 7px;margin-left: 10px;">
            <span class="login-signup">Not registered yet?</span>
            <span (click)="showHideLoginRegister('register')" class="signup-link">Create Account</span>
          </div>
          <form [formGroup]="loginForm">
            <div class="input-container">
              <input class="theme-input-text" type="text" id="email" placeholder="Email address" formControlName="userName"
                name="username" required/>
            </div>
            <div class="password-error"  style="margin-bottom: -14px;" *ngIf="loginForm.get('userName').touched && loginForm.controls['userName'].errors?.required">
              <small>Email is required</small>
            </div>
            <div class="password-error" style="margin-bottom: -14px;" *ngIf="loginForm.controls['userName'].errors?.pattern">
              <small>Enter Valid Email</small> 
             </div>
            <div fxLayoutAlign="end center">
              <button class="theme-next-button" (click)="showHideLoginRegister('next')" style="margin-right: 9px;position: relative;top: 9px;">Next
              </button>
            </div>
          </form>
          <div fxLayout="row" style="margin-left: 8px;">
            <mat-divider class="divider"></mat-divider><span class="divider-or">Or</span><mat-divider class="divider"></mat-divider>
          </div>
          <div fxLayoutAlign="center center">
            <button class="track-complaint" (click)="showHideLoginRegister('track')">Track your Complaint</button>
          </div>
          <img class="tara-icon" src="../../../assets/images/tara_gif.gif">
        </div>
      </div>
      <div [@showHide]="showLoginContainer1 == true ? 'show' : 'hide'" class="login-box" fxLayout="column" fxLayoutAlign="start start">
        <div class="login-container">
          <div class="login-head">Login</div>
          <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5px" style="margin-top: 7px;margin-left: 10px;">
            <span class="login-signup">Not registered yet?</span>
            <span (click)="showHideLoginRegister('register')" class="signup-link">Create Account</span>
          </div>
          <form [formGroup]="loginForm">
            <div class="input-container">
              <input class="theme-input-text" id="password" placeholder="Password" formControlName="password"
                name="password" [type]="inputType" autocomplete="off"/>
            </div>
            <input type="checkbox" id="checkbox" (click)="hideShowPassword()" style="margin-left: 11px;position: relative;top: 2px;"/>
            <label for="checkbox" *ngIf="passwordCheckbox" style="padding-left:5px;margin-top:7px;transform: none !important;">Hide password</label>
            <label for="checkbox" *ngIf="!passwordCheckbox" style="padding-left:5px;margin-top:7px;transform: none !important;">Show password</label>
            <div fxLayout="row" fxLayoutGap="30px" style="margin-left: 11px;position: relative;top: 9px;">
              <div fxLayoutAlign="start" (click)="showHideLoginRegister('forgot')" style="cursor: pointer; margin-top: 12px; position: relative;
              top: 9px;">
                  <b><u>Forgot password?</u></b>
              </div>
              <div fxLayoutAlign="end">
                <button class="theme-next-button" (click)="onLogin(loginForm)" [disabled]="disabled">Login
                  <i class="fa fa-spinner fa-spin" *ngIf="disabled"></i>
                </button>
              </div>
            </div>
        </form>
          <div fxLayout="row" style="margin-left: 8px;">
            <mat-divider class="divider"></mat-divider><span class="divider-or">Or</span><mat-divider class="divider"></mat-divider>
          </div>
          <div fxLayoutAlign="center center">
          <button class="track-complaint" (click)="showHideLoginRegister('login')">Login with different account</button>
        </div>
        <img  class="tara-icon" style="width:200px; margin-left: 86px;" src="../../../assets/images/tara_gif.gif" />
        </div>
      </div>


      <div [@showHide]="showForgotPasswordVerificationContainer == true ? 'show' : 'hide'" fxLayout="column"
        fxLayoutAlign="start center" class="login-box">
        <div class="login-container" style="margin-left: 13px;">
          <div class="heading-container" fxLayout="column">
            <div fxLayoutAlign="start center">
              <span class="theme-heading" style="margin-left: 21px;margin-top: 2px;">Verify OTP</span>
            </div>
            <div class="theme-text" style="margin-top: 9px;margin-left: 20px;margin-right: 12px;">
              <p>
                We have sent a verification code to your registered mobile number
                please enter that to verify...
              </p>
            </div>
          </div>
          <div class="inputs-container" fxLayout="column" fxLayoutGap="1px" style="padding:0px;margin-left: 9px;
          margin-right: 26px;">
            <div fxLayout="column" fxLayoutGap="6px">
              <form [formGroup]="forgotOtpForm" style="position: relative;bottom: 11px;">
                <div fxLayout="row" fxLayoutGap="10px">
                  <div class="verify-input-container">
                    <input class="theme-input-text" id="forgotEmailOtp" #forgotInput1 type="text" maxlength="1" formControlName="val1"
                      name="verification_code1" (keyup)="onInputEntry($event, forgotInput2)" autocomplete="off" required />
                  </div>
                  <div class="verify-input-container">
                    <input class="theme-input-text" #forgotInput2 type="text" maxlength="1" formControlName="val2"
                      name="verification_code2" (keyup)="onInputEntry($event, forgotInput3)" autocomplete="off" required />
                  </div>
                  <div class="verify-input-container">
                    <input class="theme-input-text" #forgotInput3 type="text" maxlength="1" formControlName="val3"
                      name="verification_code3" (keyup)="onInputEntry($event, forgotInput4)" autocomplete="off" required />
                  </div>
                  <div class="verify-input-container">
                    <input class="theme-input-text" #forgotInput4 type="text" maxlength="1" formControlName="val4"
                      name="verification_code4" (keyup)="onInputEntry($event, forgotInput5)" autocomplete="off" required />
                  </div>
                  <div class="verify-input-container">
                    <input class="theme-input-text" #forgotInput5 type="text" maxlength="1" formControlName="val5"
                      name="verification_code5" (keyup)="onInputEntry($event, forgotInput6)" autocomplete="off" required />
                  </div>
                  <div class="verify-input-container">
                    <input class="theme-input-text" #forgotInput6 type="text" maxlength="1" formControlName="val6"
                      name="verification_code6" autocomplete="off" required />
                  </div>
                </div>
                <div class="input-container" fxLayout ="row" style="min-width: 383px;margin: 0px;">
                  <button class="resend-btn" (click)="resendOtp(resendOtpUsername)" style="cursor:pointer" [disabled]="isExpiryCompleted">Resend OTP</button>
                  <button class="theme-next-button" (click)="verifyForgotPWByOtp(forgotOtpForm.value)" style="margin-left: 186px; position:relative;top: 3px;">
                    Verify
                    <i class="fa fa-spinner fa-spin" *ngIf="disabled"></i>
                  </button>
                </div>
                <span *ngIf="showTime" style="color: #ED2F45;margin-left: 7px; position: relative;bottom: 13px;">{{timeLeft}}</span>
              </form>
              <div fxLayout="row" style="margin-left: 8px;">
                <mat-divider class="divider1"></mat-divider>
              </div>
              <div class="number-change" style="padding-top:6px; position: relative; bottom: 51px; width:396px;" (click)="showHideLoginRegister('change-number')">
                Change Email ID
              </div>
              <img  class="tara-icon" src="../../../assets/images/tara_gif.gif" style="position: relative; bottom: 45px;"/>
            </div>
          </div>
        </div>
      </div>

      <div [@showHide]="showTrackComplaint == true ? 'show' : 'hide'" class="login-box" fxLayout="column"
      fxLayoutAlign="start start">
      <div class="login-container" style="margin-left: 2px;margin-top: 9px;">
        <!-- <mat-progress-spinner class="login-spinner" color="primary" mode="indeterminate" diameter="70" *ngIf="onTrack">
        </mat-progress-spinner> -->
        <div class="forgot-head">
          <div class="theme-heading" fxLayoutAlign="start center">
           Track Your Complaint
          </div>
          <div class="theme-text" style="margin-top: 7px;margin-left: 3px;">
            You can check status of your placed complaints
          </div>
        </div>
        <div class="inputs-container" fxLayout="column" style="padding: 0px 0px 0px 24px;">
          <form [formGroup]="trackComplaintForm" style="margin-bottom: -5px;">
            <div fxLayout="column" fxLayoutGap="8px">
              <div class="input-container" fxLayoutAlign="center center">
                <span matPrefix style="color: darkgray;">+91&nbsp;</span><br>
                <input class="theme-input-text" type="text" id="trackNumber" formControlName="phoneNumber" maxlength="10"
                  minlength="10" name="mobile_number" autocomplete="off" [readonly]="onTrack"/>
              </div>
              <div class="password-error" *ngIf="trackComplaintForm.get('phoneNumber').touched && trackComplaintForm.controls['phoneNumber'].errors?.required">
                <small>Phone number is required</small>
              </div>
              <div class="password-error" *ngIf="trackComplaintForm.controls['phoneNumber'].errors?.pattern">
                <small>Phone number should consist of 10 digits only</small>
              </div>
            </div>
            <div fxLayoutAlign="end center">
              <button class="theme-next-button" (click)="trackNext(trackComplaintForm.value)" style="margin-right: 9px;position: relative;top: 9px;">
                Next
                <i class="fa fa-spinner fa-spin" *ngIf="disabled"></i>
              </button>
            </div>
          </form>
          <div fxLayout="row" style="margin-left: 8px;">
            <mat-divider class="divider"></mat-divider><span class="divider-or">Or</span><mat-divider class="divider"></mat-divider>
          </div>
          <div fxLayoutAlign="center center">
          <button class="back-login" (click)="showHideLoginRegister('login')">Login now</button>
        </div>
        <img  class="tara-icon" src="../../../assets/images/tara_gif.gif" />
        </div>
      </div>
    </div>

    <div [@showHide]="showTrackComplaintVerification == true ? 'show' : 'hide'" class="login-box" fxLayout="column"
    fxLayoutAlign="start start" style="padding-left: 9px;">
    <div class="login-container" style="margin-left: 2px;margin-top: 9px;">
      <div class="forgot-head">
        <div class="theme-heading" fxLayoutAlign="start center">
          Verify Mobile Number
        </div>
        <div class="theme-text" style="margin-top: 7px;margin-left: 3px;">
          We have sent a verification code to your registered mobile number please enter that to verify...
        </div>
      </div>
      <div class="inputs-container" fxLayout="column" style="padding: 0px 0px 0px 24px;">
        <div fxLayout="column" fxLayoutGap="6px">
          <form [formGroup]="trackOtpForm" style="margin-bottom: 0px;">
            <div fxLayout="row" fxLayoutGap="10px">
              <div class="verify-input-container">
                <input class="theme-input-text" id="trackNumberOtp" #trackInput1 type="text" maxlength="1" formControlName="val1"
                  name="verification_code1" (keyup)="onInputEntry($event, trackInput2)" autocomplete="off" required />
              </div>
              <div class="verify-input-container">
                <input class="theme-input-text" #trackInput2 type="text" maxlength="1" formControlName="val2"
                  name="verification_code2" (keyup)="onInputEntry($event, trackInput3)" autocomplete="off" required />
              </div>
              <div class="verify-input-container">
                <input class="theme-input-text" #trackInput3 type="text" maxlength="1" formControlName="val3"
                  name="verification_code3" (keyup)="onInputEntry($event, trackInput4)" autocomplete="off" required />
              </div>
              <div class="verify-input-container">
                <input class="theme-input-text" #trackInput4 type="text" maxlength="1" formControlName="val4"
                  name="verification_code4" (keyup)="onInputEntry($event, trackInput5)" autocomplete="off" required />
              </div>
              <div class="verify-input-container">
                <input class="theme-input-text" #trackInput5 type="text" maxlength="1" formControlName="val5"
                  name="verification_code5" (keyup)="onInputEntry($event, trackInput6)" autocomplete="off" required />
              </div>
              <div class="verify-input-container">
                <input class="theme-input-text" #trackInput6 type="text" maxlength="1" formControlName="val6"
                  name="verification_code6" autocomplete="off" required />
              </div>
            </div>
            <div class="input-container" fxLayout ="row" style="margin: 0px;">
              <button class="resend-btn" (click)="resendOtpForTracking()" [disabled]="isExpiryCompleted">Resend OTP
              </button>
              <button class="theme-next-button" (click)="next(trackOtpForm.value)" [disabled]="disabled" style="margin-left: 184px;">
                Verify
                <i class="fa fa-spinner fa-spin" *ngIf="disabled"></i>
              </button>
            </div>
            <span *ngIf="showTime" style="color: #ED2F45;margin-left: 11px;position: relative;bottom: 12px;">{{timeLeft}}</span>
          </form>
          <div fxLayout="row" style="margin-left: 8px;">
            <mat-divider style="width: 381px;"></mat-divider>
          </div>
          <div class="number-change" style="padding-top: 6px; margin-left: 7px; width: 388px;" (click)="showHideLoginRegister('track-change-number')">
            Change Mobile Number
          </div>
          <img  class="tara-icon" src="../../../assets/images/tara_gif.gif" />
        </div>
      </div>
    </div>
  </div>

      <div [@showHide]="showForgotPasswordContainer == true ? 'show' : 'hide'" class="login-box" fxLayout="column"
        fxLayoutAlign="start start">
        <div class="forgot-container">
          <div class="forgot-head">
            <div class="theme-heading" fxLayoutAlign="start center">
             Reset password
            </div>
            <div class="theme-text" style="margin-top: 7px;margin-left: 3px;">
              Incase you forgot your password
            </div>
          </div>
          <div class="inputs-container" fxLayout="column" style="padding: 10px 27px 0px 27px;">
            <form [formGroup]="forgotForm" style="margin-bottom: -5px;">
              <div fxLayout="column" fxLayoutGap="8px">
                <div class="input-container" fxLayoutAlign="center center">
                  <input class="theme-input-text" type="text" id="forgotEmail" placeholder="Your registered Email Id" formControlName="email"
                    name="email" autocomplete="off"/>
                </div>
                <div class="password-error"  style="margin-bottom: -4px;" *ngIf="forgotForm.get('email').touched && forgotForm.controls['email'].errors?.required">
                  <small>Email is required</small>
                </div>
                <div class="password-error" style="margin-bottom: -4px;" *ngIf="forgotForm.controls['email'].errors?.pattern">
                  <small>Enter Valid Email</small> 
                 </div>
              </div>
              <div fxLayoutAlign="end center" style="margin-right: 9px;position: relative;top: 9px;">
                <button class="theme-next-button" [disabled]= "disabled" (click)="showHideLoginRegister('reset')">
                  Next
                  <i class="fa fa-spinner fa-spin" *ngIf="disabled"></i>
                </button>
              </div>
            </form>
            <div fxLayout="row" style="margin-left: 8px;">
              <mat-divider class="divider"></mat-divider><span class="divider-or">Or</span><mat-divider class="divider"></mat-divider>
            </div>
            <div fxLayoutAlign="center center">
            <button class="back-login" (click)="showHideLoginRegister('login')">Back to login</button>
          </div>
          <img  class="tara-icon" src="../../../assets/images/tara_gif.gif" />
          </div>
        </div>
      </div>

      <div [@showHide]="showResetPasswordContainer == true ? 'show' : 'hide'" fxLayout="column" fxLayoutAlign="start start" class="login-box" style="background: none;">
        <div class="reset-content-container">
          <div class="heading-container" fxLayout="column" style="margin-left: 15px">
            <div fxLayoutAlign="start center">
              <span class="theme-heading" style="margin-left: 19px;margin-top: 11px;">Reset Password</span>
            </div>
            <div class="theme-text" style="margin-top: 9px;margin-left: 20px;margin-right: 12px;">
              <p>
               You can create  your new password now
              </p>
            </div>
          </div>
          <div class="inputs-container" fxLayout="column">
            <form [formGroup]="resetForm">
              <div fxLayout="column" fxLayoutGap="8px">
                <div class="input-container" fxLayoutAlign="center center">
                  <input class="theme-input-text" id="newPassword" placeholder="Enter new password"
                    formControlName="new_password" autocomplete="off"/>
                </div>
                <div class="password-error">
                  <small *ngIf="
                      resetForm.controls['new_password'].errors?.required &&
                      (resetForm.controls['new_password'].dirty ||
                        resetForm.controls['new_password'].touched ||
                        isSubmitDisabled)
                    ">Password is required<br></small>
                    <small *ngIf="
                    !resetForm.controls['new_password'].valid && resetForm.controls['new_password'].dirty
                  ">Password should contain at least one uppercase letter, one lowercase letter, one number and one special character (#?!@$%^&*-) and should be of length 8-15 characters</small>
                </div>

                <div class="input-container" fxLayoutAlign="center center">
                  <input class="theme-input-text" placeholder="Confirm new password"
                    formControlName="confirm_password" autocomplete="off" />
                </div>
                <div class="confirm-password-error">
                  <small  *ngIf="
                      resetForm.controls['confirm_password'].errors?.required &&
                      (resetForm.controls['confirm_password'].dirty ||
                        resetForm.controls['confirm_password'].touched ||
                        isSubmitDisabled)
                    ">Password is required<br></small>
                    <small  *ngIf="
                    !resetForm.controls['confirm_password'].valid && resetForm.controls['confirm_password'].dirty
                  ">Password should contain at least one uppercase letter, one lowercase letter, one number and one special character (#?!@$%^&*-) and should be of length 8-15 characters</small>
                </div>
                <div class="match-error">{{ error }}</div>
                <div class="input-container" fxLayoutAlign="end center">
                  <button class="theme-next-button" (click)="resetPassword()" [disabled]="disabled" style="width: 160px;">
                    Reset Password
                    <i class="fa fa-spinner fa-spin" *ngIf="disabled"></i>
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div [@showHide]="showRegisterContainer == true ? 'show' : 'hide'" class="register-box" fxLayout="column"
        fxLayoutAlign="center center">
        <div class="createuser-content-container">
          <div class="heading-container" fxLayout="row" style="margin-left: 25px">
            <div class="heading" fxLayoutAlign="start center">
              <span class="heading-text heading-font-size bold">Create account</span>
            </div>
          </div>
          <div fxLayout="row" fxLayoutAlign="start center" style="margin: 10px 35px;" fxLayoutGap="5px">
            <span class="theme-text">Already registered?</span>
            <span (click)="showHideLoginRegister('login')" class="login-link">Login now</span>
          </div>
          <div class="inputs-container" fxLayout="column" style="padding-top: 0px;padding-bottom: 0px;">
            <form [formGroup]="signupForm">
              <div class="input-container" fxLayoutAlign="center center">
                <!-- <mat-form-field appearance="outline"> -->
                  <mat-select  class="theme-input-text" placeholder="Who do you represent?" [(ngModel)]="selectedRole" (change)="onChange()" [ngStyle]="{'color':selectedColor1}" formControlName="role" style="line-height: 37px;">
                    <!-- <mat-option hidden value="" disabled selected>Who do you represent?</mat-option> -->
                    <mat-option *ngFor="let role of roles" (click)="getRole(role.ID)" [value]="role.ID" style="color:black">
                      {{role.ROLE_NAME}}
                    </mat-option>
                  </mat-select>
                <!-- </mat-form-field> -->
              </div>
              <div class="password-error" *ngIf="signupForm.get('role').touched && !signupForm.get('role').valid">
                <small>Please select the Role</small>
              </div>
              <div class="input-container" fxLayoutAlign="center center">
                  <mat-select formControlName="salutation" placeholder="Title" style="background-position:right 0.7em top 50%, 0 0; padding-top: 10px;" class="theme-input-text" style="line-height: 37px;">
                    <!-- <option hidden value="" disabled selected style="color: grey !important;">Title</option> -->
                    <mat-option *ngFor="let title of titles" [value]="title.ID" style="color:black;"> {{title.SALUTATION_NAME}}</mat-option>
                  </mat-select>
                  <input class="theme-input-text" type="text" placeholder="First name" formControlName="firstName"
                    name="firstName" style="margin-left: 9px;" autocomplete="off" required />
                </div>
                <div fxLayout="row">
                <div class="password-error" fxFlex="45%">
                  <small *ngIf="signupForm.get('salutation').touched && !signupForm.get('salutation').valid">
                    Please select the title</small>
                </div>
                <div fxFlex="5%"></div>
                <div class="password-error" style="margin-left: 0px;" *ngIf="signupForm.get('firstName').touched && signupForm.controls['firstName'].errors?.required">
                  <small>First Name is required</small>
                </div>
                <div class="password-error" style="margin-left: 0px;" *ngIf="signupForm.controls['firstName'].errors?.pattern||signupForm.controls['firstName'].errors?.minlength || signupForm.controls['firstName'].errors?.maxlength">
                  <small>First Name should be of min length 3 and max length 25 with alphabets only</small>
                </div>
                </div>                   
                  
                <div class="input-container" fxLayoutAlign="center center">
                  <input class="theme-input-text" type="text" placeholder="Last name" formControlName="lastName"
                    name="lastName" autocomplete="off" required />
                </div>
                <div class="password-error" *ngIf="signupForm.get('lastName').touched && signupForm.controls['lastName'].errors?.required">
                  <small>Last Name is required</small>
                </div>
                <div class="password-error" *ngIf="signupForm.controls['lastName'].errors?.pattern || signupForm.controls['firstName'].errors?.maxlength">
                  <small>Last Name should be of  max length 25 with alphabets only</small>
                </div>
              <div class="input-container" fxLayoutAlign="center center">
                <input class="theme-input-text" type="text" placeholder="Phone number" formControlName="phonenumber"
                  name="phonenumber" maxlength="10" autocomplete="off" required />
              </div>
              <div class="password-error" *ngIf="signupForm.get('phonenumber').touched && signupForm.controls['phonenumber'].errors?.required">
                <small>Phone number is required</small>
              </div>
              <div class="password-error" *ngIf="signupForm.controls['phonenumber'].errors?.pattern">
                <small>Phone number should consist of 10 digits</small>
              </div>
              <div class="input-container" fxLayoutAlign="center center">
                <input class="theme-input-text" type="email" placeholder="Email address" formControlName="email" name="email" autocomplete="off"
                  required />
              </div>
              <div class="password-error" *ngIf="signupForm.get('email').touched && signupForm.controls['email'].errors?.required">
                <small>Email is required</small>
              </div>
              <div class="password-error" *ngIf="signupForm.controls['email'].errors?.pattern">
                <small>Enter Valid Email</small>
              </div>
              <div class="input-container" fxLayoutAlign="center center" fxLayout="column">
                <input class="theme-input-text" placeholder="Password" formControlName="password"
                  name="password" autocomplete="disabled" required />
              </div>
              <div class="password-error" *ngIf="signupForm.get('password').touched && signupForm.controls['password'].errors?.required">
               <small>Password is required </small>
              </div>
              <div class="password-error" style="width: 348px;">
              <small
              *ngIf="signupForm.controls['password'].errors?.pattern">
              Password should contain at least one uppercase letter, one lowercase letter, one number and one special character (#?!@$%^&*-) and should be of length 8-15 characters
              </small>
              </div>
              <div class="input-container" fxLayoutAlign="center center">
                <input class="theme-input-text" type="text" placeholder="Your Pin Code" formControlName="pin"
                  name="pin" maxlength="6" autocomplete="off" required/>
              </div>
              <div class="password-error" *ngIf="signupForm.get('pin').touched && signupForm.controls['pin'].errors?.required">
                <small>Pincode is required</small>
              </div>
              <div class="password-error" *ngIf="signupForm.get('pin').touched && signupForm.controls['pin'].errors?.pattern">
                <small>Enter Valid Pin with 6 digits</small>
              </div>
              <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == '4'">
                <mat-select class="theme-input-text" placeholder="Government body name" [(ngModel)]="selectedGovBody" formControlName="govBody" required style="padding-top: 10px;">
                  <!-- <option hidden value="" disabled selected>Government Body Name</option> -->
                  <mat-option *ngFor="let govBody of govDept" [value]="govBody.ID" style="color:black">
                    {{govBody.GOVERNMENT_DEPARTEMENT_NAME}}
                  </mat-option>
                </mat-select>
              </div>
              <div class="password-error" *ngIf="selectedRole == '4' && signupForm.get('govBody').touched && !signupForm.get('govBody').valid">
               <small>Please choose Government Body Name</small>
              </div>
              <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == '4' && selectedGovBody == '10'">
                <input class="theme-input-text" type="text" placeholder="Government Body Name" formControlName="govBodyName"
                  name="govBodyName" autocomplete="off" required/>
              </div>
              <div class="password-error" *ngIf="signupForm.get('govBodyName').touched && signupForm.controls['govBodyName'].errors?.required  && selectedRole == '4' && selectedGovBody == '10'">
                <small>Government Body Name is required</small>
              </div>
              <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == ''">
                <input class="theme-input-text" type="text" placeholder="Company / Organization name"
                  formControlName="companyname" name="companyname" autocomplete="off"/>
              </div>
              <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == '5'">
                <input class="theme-input-text" type="text" placeholder="Company / Organization name"
                  formControlName="companyID" [ngClass]="{'red-border-class': companyNameControl.touched && !companyNameControl.value && selectedRole == '5'}" [formControl]="companyNameControl" name="companyname" [matAutocomplete]="autoCompany"/>
                <mat-autocomplete #autoCompany="matAutocomplete" (optionSelected)="onSelectionChange($event)">
                    <mat-option *ngFor="let company of companyList;" [value]="company.COMPANY_NAME" style="
                    font-style: normal;
                    font-weight: normal;">
                        {{company.COMPANY_NAME}}
                    </mat-option>
                </mat-autocomplete>
              </div>
              <div class="password-error" *ngIf="companyNameControl.touched && !companyNameControl.value && selectedRole == '5'">
                <small>Please choose Company Name</small>
               </div>
              <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == '8'">
                <input class="theme-input-text" type="text" placeholder="Consumer Organization / Industry Name"
                  formControlName="companyname" name="companyname" autocomplete="off" required/>
              </div>
              <div class="password-error" *ngIf="signupForm.get('companyname').touched && selectedRole == '8' && !signupForm.get('companyname').valid">
                <small>Consumer Organization is required</small>
               </div>
               <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == '7'">
                <input class="theme-input-text" type="text" placeholder="Organization Name"
                  formControlName="companyname" name="companyname" autocomplete="off"/>
              </div>
              <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == '7'">
                <input class="theme-input-text" type="text" placeholder="Profession Name"
                  formControlName="professionName" name="professionName" autocomplete="off"/>
              </div>
              <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == '5'">
                <input class="theme-input-text" type="text" placeholder="Your address" formControlName="address"
                  name="address" autocomplete="off" required/>
              </div>
              <div class="input-container" fxLayoutAlign="center center" *ngIf=" selectedRole == '8'">
                <input class="theme-input-text" type="text" placeholder="Your address" formControlName="address"
                  name="address" autocomplete="off" required/>
              </div>
              <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == ''">
                <input class="theme-input-text" type="text" placeholder="Your address" formControlName="address"
                  name="address" autocomplete="off"/>
              </div>
              <div class="password-error" *ngIf="signupForm.get('address').touched && signupForm.controls['address'].errors?.required && (selectedRole == '5' || selectedRole == '8')">
                <small>Address is required</small>
               </div>
              <div class="input-container" fxLayoutAlign="end center">
                <button class="theme-next-button" [disabled]="isRegisterDisabled" (click)="showHideLoginRegister('registerVerify')">
                  Next
                  <i class="fa fa-spinner fa-spin" *ngIf="isRegisterDisabled"></i>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div [@showHide]="showVerificationContainer == true ? 'show' : 'hide'" fxLayout="column"
        fxLayoutAlign="start center" class="login-box">
        <div class="login-container" style="margin-left: 19px;">
          <div class="heading-container" fxLayout="column">
            <div fxLayoutAlign="start center">
              <span class="theme-heading" style="margin-left: 21px;margin-top: 2px;">Verify Yourself</span>
            </div>
            <div class="theme-text" style="margin-top: 9px;margin-left: 20px;margin-right: 12px;">
              <p>
                We have sent a verification code to your registered mobile
                number, please enter that to verify...
              </p>
            </div>
          </div>
          <div class="inputs-container" fxLayout="column" fxLayoutGap="1px" style="padding:0px;margin-left: 9px;
          margin-right: 26px;">
            <div fxLayout="column" fxLayoutGap="6px">
              <form [formGroup]="signupOtpForm" style="position: relative;bottom: 2px; margin-bottom: 15px;">
                <div fxLayout="row" fxLayoutGap="10px">
                  <div class="verify-input-container">
                    <input class="theme-input-text" type="text" id="signupOtp" #input1 maxlength="1" formControlName="val1"
                      name="verification_code1" (keyup)="onInputEntry($event, input2)" autocomplete="off" required />
                  </div>
                  <div class="verify-input-container">
                    <input class="theme-input-text" type="text" #input2 maxlength="1" formControlName="val2"
                      name="verification_code2" (keyup)="onInputEntry($event, input3)" autocomplete="off" required />
                  </div>
                  <div class="verify-input-container">
                    <input class="theme-input-text" #input3 type="text" maxlength="1" formControlName="val3"
                      name="verification_code3" (keyup)="onInputEntry($event, input4)" autocomplete="off" required />
                  </div>
                  <div class="verify-input-container">
                    <input class="theme-input-text" #input4 type="text" maxlength="1" formControlName="val4"
                      name="verification_code4" (keyup)="onInputEntry($event, input5)" autocomplete="off" required />
                  </div>
                  <div class="verify-input-container">
                    <input class="theme-input-text" #input5 type="text" maxlength="1" formControlName="val5"
                      name="verification_code5" (keyup)="onInputEntry($event, input6)" autocomplete="off" required />
                  </div>
                  <div class="verify-input-container">
                    <input class="theme-input-text" #input6 type="text" maxlength="1" formControlName="val6"
                      name="verification_code6" autocomplete="off" required />
                  </div>
                </div>
                <div class="input-container" fxLayout ="row" style="min-width: 383px;">
                  <button class="resend-btn" style="margin-left: -5px;" (click)="resendOtp(resendOtpUsername)" [disabled]="isExpiryCompleted">Resend OTP</button>
                  <button class="theme-next-button" (click)="verifySignUpByOtp(signupOtpForm.value)" [disabled]="disabled" style="margin-left: 186px; position:relative;top: 2px;z-index: 1">
                    Verify
                    <i class="fa fa-spinner fa-spin" *ngIf="disabled"></i>
                  </button>
                </div>
                <span *ngIf="showTime" style="color: #ED2F45;margin-left: 13px;position: relative;bottom: 18px;">{{timeLeft}}</span>
              </form>
              <!-- <div class="number-change" style="padding-top:6px;position: relative;bottom: 17px;left: 9px;" (click)="showHideLoginRegister('register')">
                Change Registration Details
              </div> -->
              <img  class="tara-icon" src="../../../assets/images/tara_gif.gif" style="position: relative; bottom: 22px;"/>
            </div>
          </div>
        </div>
      </div>

    </div>

    <div class="login-box-container" *ngIf="isMobileScreen" fxLayout="row" fxLayoutAlign="center center">
      <!-- <div class="contest-div1">
        <img src="../assets/images/contest.png" class="contest-img"/>
      </div> -->
      <div [@showHide]="showTrackComplaint == true ? 'show' : 'hide'" class="mobile-login-box" fxLayout="column"
      fxLayoutAlign="start start"  *ngIf="!showTrackComplaintVerification && !showRegisterContainer">
        <div class="login-container">
          <div class="login-head">Login</div>
          <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5px" style="padding: 7px 0 0 10px;">
            <span class="login-signup">Not registered yet?</span>
            <span class="signup-link" (click)="showHideLoginRegister('register')">Create Account</span>
          </div>
          <div class="inputs-container" fxLayout="column" fxLayoutAlign="center center" fxLayoutGap="10px">
            <form [formGroup]="trackComplaintForm">
              <div fxLayout="column" fxLayoutGap="8px">
                <div class="input-container" fxLayoutAlign="center center">
                  <input class="theme-input-text" type="text" id="mobile_no" formControlName="phoneNumber" maxlength="10"
                    name="mobile_number" autocomplete="off" placeholder="Mobile number"/>
                </div>
                <div class="password-error" *ngIf="trackComplaintForm.get('phoneNumber').touched && trackComplaintForm.controls['phoneNumber'].errors?.required">
                  <small>Phone number is required</small>
                </div>
                <div class="password-error" *ngIf="trackComplaintForm.controls['phoneNumber'].errors?.pattern">
                  <small>Phone number should consist of 10 digits only</small>
                </div>
              </div>
              <div fxLayoutAlign="end center">
                <button class="theme-next-button" (click)="trackNext(trackComplaintForm.value)" [disabled]= "disabled" style="margin-right: 9px;position: relative;top: 9px;">
                  Next
                  <i class="fa fa-spinner fa-spin" *ngIf="disabled"></i>
                </button>
              </div>
            </form>
            <!-- <div fxLayoutAlign="center center">
              <button class="back-login" (click)="showHideLoginRegister('login')" style="width: 93%;">Login now</button>
            </div> -->
            <div fxLayoutAlign="center center">
              <img  class="tara-icon" src="../../../assets/images/tara_gif.gif" />
            </div>
          </div>
        </div>
      </div>

      <div [@showHide]="showTrackComplaintVerification == true ? 'show' : 'hide'" class="mobile-login-box" fxLayout="column"
      fxLayoutAlign="start start"  *ngIf="isMobileScreen">
        <div class="login-container" fxLayout="column" fxLayoutGap="20px">
          <div class="forgot-head">
            <div class="theme-heading" fxLayoutAlign="start center">
              Verify Mobile Number
            </div>
            <div class="theme-text" style="margin-top: 7px;margin-left: 3px;">
              We have sent a verification code to your registered mobile number please enter that to verify...
            </div>
          </div>
          <div class="inputs-container" fxLayout="column" fxLayoutGap="10px" style="padding: 0px 0px 0px 2px;">
            <div fxLayout="column" fxLayoutGap="1px">
              <form [formGroup]="trackOtpForm" style="margin-bottom: 0px;">
                <div fxLayout="row" fxLayoutAlign="space-between center" class="verify-inputs-container">
                  <div class="verify-input-container">
                    <input class="theme-input-text" id="trackNumberOtpMobile"  #trackInput1 type="text" maxlength="1" formControlName="val1"
                      name="verification_code1" (keyup)="onInputEntry($event, trackInput2)" autocomplete="off" required />
                  </div>
                  <div class="verify-input-container">
                    <input class="theme-input-text" #trackInput2 type="text" maxlength="1" formControlName="val2"
                      name="verification_code2" (keyup)="onInputEntry($event, trackInput3)" autocomplete="off" required />
                  </div>
                  <div class="verify-input-container">
                    <input class="theme-input-text" #trackInput3 type="text" maxlength="1" formControlName="val3"
                      name="verification_code3" (keyup)="onInputEntry($event, trackInput4)" autocomplete="off" required />
                  </div>
                  <div class="verify-input-container">
                    <input class="theme-input-text" #trackInput4 type="text" maxlength="1" formControlName="val4"
                      name="verification_code4" (keyup)="onInputEntry($event, trackInput5)" autocomplete="off" required />
                  </div>
                  <div class="verify-input-container">
                    <input class="theme-input-text" #trackInput5 type="text" maxlength="1" formControlName="val5"
                      name="verification_code5" (keyup)="onInputEntry($event, trackInput6)" autocomplete="off" required />
                  </div>
                  <div class="verify-input-container">
                    <input class="theme-input-text" #trackInput6 type="text" maxlength="1" formControlName="val6"
                      name="verification_code6" autocomplete="off" required />
                  </div>
                </div>
                <div class="btn-container" fxLayout="row" style="margin: 0px;">
                  <div fxLayout="column" fxLayoutAlign="center center">
                    <button class="resend-btn" (click)="resendOtpForTracking()" [disabled]="isExpiryCompleted">Resend OTP
                    </button>
                    <span *ngIf="showTime" style="color: #ED2F45;">{{timeLeft}}</span>
                  </div>
                  <span style="flex: 1 1 auto;"></span>
                  <button class="theme-next-button" (click)="next(trackOtpForm.value)" [disabled]="disabled">
                    Login
                    <i class="fa fa-spinner fa-spin" *ngIf="disabled"></i>
                  </button>
                </div>
                <!-- <span *ngIf="showTime" style="color: #ED2F45;margin-left: 8px;position: relative;bottom: 32px;">{{timeLeft}}</span> -->
              </form>
              <div class="number-change" (click)="showHideLoginRegister('track')">
                Change Mobile Number
              </div>
              <div fxLayoutAlign="center center">
                <img class="tara-icon" src="../../../assets/images/tara_gif.gif" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div [@showHide]="showRegisterContainer == true ? 'show' : 'hide'" class="register-box" fxLayout="column"
       fxLayoutAlign="center center">
      <div class="createuser-content-container">
        <div class="heading-container" fxLayout="row" style="margin-left: 25px">
          <div class="heading" fxLayoutAlign="start center">
            <span class="heading-text heading-font-size bold">Create account</span>
          </div>
        </div>
        <div fxLayout="row" fxLayoutAlign="start center" style="margin: 10px 35px;" fxLayoutGap="5px">
          <span class="theme-text">Already registered?</span>
          <span (click)="openMobileLogin()" class="login-link">Login now</span>
        </div>
        <div class="inputs-container" fxLayout="column" style="padding-top: 0px;padding-bottom: 0px;">
          <form [formGroup]="signupForm">
            <div class="input-container" fxLayoutAlign="center center">
              <!-- <mat-form-field appearance="outline"> -->
                <mat-select  class="theme-input-text" [(ngModel)]="selectedRole" (change)="onChange()" [ngStyle]="{'color':selectedColor1}" formControlName="role" style="line-height: 37px;">
                  <mat-option hidden value="" disabled selected>Who do you represent?</mat-option>
                  <mat-option *ngFor="let role of roles" [value]="role.ID" style="color:black">
                    {{role.ROLE_NAME}}
                  </mat-option>
                </mat-select>
              <!-- </mat-form-field> -->
            </div>
            <div class="password-error" *ngIf="signupForm.get('role').touched && !signupForm.get('role').valid">
              <small>Please select the Role</small>
            </div>
            <div class="input-container" fxLayoutAlign="center center">
                <mat-select formControlName="salutation" placeholder="Title" style="background-position:right 0.7em top 50%, 0 0; padding-top: 10px;" class="theme-input-text" style="line-height: 37px;">
                  <!-- <option hidden value="" disabled selected style="color: grey !important;">Title</option> -->
                  <mat-option *ngFor="let title of titles" [value]="title.ID" style="color:black;"> {{title.SALUTATION_NAME}}</mat-option>
                </mat-select>
                <input class="theme-input-text" type="text" placeholder="First name" formControlName="firstName"
                  name="firstName" style="margin-left: 9px;" autocomplete="off" required />
              </div>
              <div fxLayout="row">
                <div class="password-error" fxFlex="45%">
                  <small *ngIf="signupForm.get('salutation').touched && !signupForm.get('salutation').valid">
                    Please select the title</small>
                </div>
                <div fxFlex="5%"></div>
                <div class="password-error" style="margin-left: 0px;" *ngIf="signupForm.get('firstName').touched && signupForm.controls['firstName'].errors?.required">
                  <small>First Name is required</small>
                </div>
                <div class="password-error" style="margin-left: 0px;" *ngIf="signupForm.controls['firstName'].errors?.pattern||signupForm.controls['firstName'].errors?.minlength || signupForm.controls['firstName'].errors?.maxlength">
                  <small>First Name should be of min length 3 and max length 25 with alphabets only</small>
                </div>
                </div>    
              <div class="input-container" fxLayoutAlign="center center">
                <input class="theme-input-text" type="text" placeholder="Last name" formControlName="lastName"
                  name="lastName" autocomplete="off" required />
              </div>
              <div class="password-error" *ngIf="signupForm.get('lastName').touched && signupForm.controls['lastName'].errors?.required">
                <small>Last Name is required</small>
              </div>
              <div class="password-error" *ngIf="signupForm.controls['lastName'].errors?.pattern || signupForm.controls['firstName'].errors?.maxlength">
                <small>Last Name should be of  max length 25 with alphabets only</small>
              </div>
            <div class="input-container" fxLayoutAlign="center center">
              <input class="theme-input-text" type="text" placeholder="Phone number" formControlName="phonenumber"
                name="phonenumber" maxlength="10" autocomplete="off" required />
            </div>
            <div class="password-error" *ngIf="signupForm.get('phonenumber').touched && signupForm.controls['phonenumber'].errors?.required">
              <small>Phone number is required</small>
            </div>
            <div class="password-error" *ngIf="signupForm.controls['phonenumber'].errors?.pattern">
              <small>Phone number should consist of 10 digits</small>
            </div>
            <div class="input-container" fxLayoutAlign="center center">
              <input class="theme-input-text" type="email" placeholder="Email address" formControlName="email" name="email" autocomplete="off"
                required />
            </div>
            <div class="password-error" *ngIf="signupForm.get('email').touched && signupForm.controls['email'].errors?.required">
              <small>Email is required</small>
            </div>
            <div class="password-error" *ngIf="signupForm.controls['email'].errors?.pattern">
              <small>Enter Valid Email</small>
            </div>
            <div class="input-container" fxLayoutAlign="center center" fxLayout="column">
              <input class="theme-input-text" placeholder="Password" formControlName="password"
                name="password" autocomplete="disabled" required />
            </div>
            <div class="password-error" *ngIf="signupForm.get('password').touched && signupForm.controls['password'].errors?.required">
              <small>Password is required </small>
             </div>
             <div class="password-error" style="width: 348px;">
             <small *ngIf="signupForm.controls['password'].errors?.pattern">
             Password should contain at least one uppercase letter, one lowercase letter, one number and one special character (#?!@$%^&*-) and should be of length 8-15 characters
             </small>
             </div>
            <div class="input-container" fxLayoutAlign="center center">
              <input class="theme-input-text" type="text" placeholder="Your Pin Code" formControlName="pin"
                name="pin" maxlength="6" autocomplete="off" required/>
            </div>
            <div class="password-error" *ngIf="signupForm.get('pin').touched && signupForm.controls['pin'].errors?.required">
              <small>Pincode is required</small>
            </div>
            <div class="password-error" *ngIf="signupForm.get('pin').touched && signupForm.controls['pin'].errors?.pattern">
              <small>Enter Valid Pin with 6 digits</small>
            </div>
            <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == '4'">
              <mat-select class="theme-input-text" placeholder="Government body name" [(ngModel)]="selectedGovBody" formControlName="govBody" required style="padding-top: 10px;">
                <!-- <option hidden value="" disabled selected>Government Body Name</option> -->
                <mat-option *ngFor="let govBody of govDept" [value]="govBody.ID" style="color:black">
                  {{govBody.GOVERNMENT_DEPARTEMENT_NAME}}
                </mat-option>
              </mat-select>
            </div>
            <div class="password-error" *ngIf="selectedRole == '4' && signupForm.get('govBody').touched && !signupForm.get('govBody').valid">
             <small>Please choose Government Body Name</small>
            </div>
            <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == '4' && selectedGovBody == '10'">
              <input class="theme-input-text" type="text" placeholder="Government Body Name" formControlName="govBodyName"
                name="govBodyName" autocomplete="off" required/>
            </div>
            <div class="password-error" *ngIf="signupForm.get('govBodyName').touched && signupForm.controls['govBodyName'].errors?.required  && selectedRole == '4' && selectedGovBody == '10'">
              <small>Government Body Name is required</small>
            </div>
            <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == ''">
              <input class="theme-input-text" type="text" placeholder="Company / Organization name"
                formControlName="companyname" name="companyname" autocomplete="off"/>
            </div>
            <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == '5'">
              <input class="theme-input-text" type="text" placeholder="Company / Organization name"
                formControlName="companyID" [ngClass]="{'red-border-class': companyNameControl.touched && !companyNameControl.value && selectedRole == '5'}" [formControl]="companyNameControl" name="companyname" [matAutocomplete]="autoCompany"/>
              <mat-autocomplete #autoCompany="matAutocomplete" (optionSelected)="onSelectionChange($event)">
                  <mat-option *ngFor="let company of companyList;" [value]="company.COMPANY_NAME" style="
                  font-style: normal;
                  font-weight: normal;">
                      {{company.COMPANY_NAME}}
                  </mat-option>
              </mat-autocomplete>
            </div>
            <div class="password-error" *ngIf="companyNameControl.touched && !companyNameControl.value && selectedRole == '5'">
              <small>Please choose Company Name</small>
             </div>
            <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == '8'">
              <input class="theme-input-text" type="text" placeholder="Consumer Organization / Industry Name"
                formControlName="companyname" name="companyname" autocomplete="off" required/>
            </div>
            <div class="password-error" *ngIf="signupForm.get('companyname').touched && selectedRole == '8' && !signupForm.get('companyname').valid">
              <small>Consumer Organization is required</small>
             </div>
             <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == '7'">
              <input class="theme-input-text" type="text" placeholder="Organization Name"
                formControlName="companyname" name="companyname" autocomplete="off"/>
            </div>
            <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == '7'">
              <input class="theme-input-text" type="text" placeholder="Profession Name"
                formControlName="professionName" name="professionName" autocomplete="off"/>
            </div>
            <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == '5'">
              <input class="theme-input-text" type="text" placeholder="Your address" formControlName="address"
                name="address" autocomplete="off" required/>
            </div>
            <div class="input-container" fxLayoutAlign="center center" *ngIf=" selectedRole == '8'">
              <input class="theme-input-text" type="text" placeholder="Your address" formControlName="address"
                name="address" autocomplete="off" required/>
            </div>
            <div class="input-container" fxLayoutAlign="center center" *ngIf="selectedRole == ''">
              <input class="theme-input-text" type="text" placeholder="Your address" formControlName="address"
                name="address" autocomplete="off"/>
            </div>
            <div class="password-error" *ngIf="signupForm.get('address').touched && signupForm.controls['address'].errors?.required && (selectedRole == '5' || selectedRole == '8')">
              <small>Address is required</small>
             </div>
            <div class="input-container" fxLayoutAlign="end center">
              <button class="theme-next-button" [disabled]="isRegisterDisabled" (click)="showHideLoginRegister('registerVerify')">
                Next
                <i class="fa fa-spinner fa-spin" *ngIf="isRegisterDisabled"></i>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div [@showHide]="showVerificationContainer == true ? 'show' : 'hide'" fxLayout="column"
    fxLayoutAlign="start center" class="mobile-register-box">
    <div class="login-container">
      <div class="heading-container" fxLayout="column">
        <div fxLayoutAlign="start center">
          <span class="theme-heading" style="margin-left: 21px;margin-top: 2px;">Verify Yourself</span>
        </div>
        <div class="theme-text" style="margin-top: 9px;margin-left: 20px;margin-right: 12px;">
          <p>
            We have sent a verification code to your registered mobile
            number, please enter that to verify...
          </p>
        </div>
      </div>
       <div class="inputs-container" fxLayout="column" fxLayoutGap="10px" style="padding: 0px 0px 0px 2px;">
        <div fxLayout="column" fxLayoutGap="6px">
          <form [formGroup]="signupOtpForm">
            <div fxLayout="row" fxLayoutAlign="space-between center" class="verify-inputs-container" style="padding: 0 10px;">
              <div class="verify-input-container">
                <input class="theme-input-text" type="text" id="signupOtpMobile" #input1 maxlength="1" formControlName="val1"
                  name="verification_code1" (keyup)="onInputEntry($event, input2)" autocomplete="off" required />
              </div>
              <div class="verify-input-container">
                <input class="theme-input-text" type="text" #input2 maxlength="1" formControlName="val2"
                  name="verification_code2" (keyup)="onInputEntry($event, input3)" autocomplete="off" required />
              </div>
              <div class="verify-input-container">
                <input class="theme-input-text" #input3 type="text" maxlength="1" formControlName="val3"
                  name="verification_code3" (keyup)="onInputEntry($event, input4)" autocomplete="off" required />
              </div>
              <div class="verify-input-container">
                <input class="theme-input-text" #input4 type="text" maxlength="1" formControlName="val4"
                  name="verification_code4" (keyup)="onInputEntry($event, input5)" autocomplete="off" required />
              </div>
              <div class="verify-input-container">
                <input class="theme-input-text" #input5 type="text" maxlength="1" formControlName="val5"
                  name="verification_code5" (keyup)="onInputEntry($event, input6)" autocomplete="off" required />
              </div>
              <div class="verify-input-container">
                <input class="theme-input-text" #input6 type="text" maxlength="1" formControlName="val6"
                  name="verification_code6" autocomplete="off" required />
              </div>
            </div>
            <!-- <div class="input-container verify-min-width" fxLayout ="row" style="min-width: 383px;margin-top: 12px;margin-left: 11px;padding: 0px;"> -->
              <div class="input-container verify-min-width" fxLayout ="row" style="margin-top: 12px;margin-left: 11px;padding: 0px;">
              <div fxLayout="column" fxLayoutAlign="center center">
                <button class="resend-btn" (click)="resendOtp(resendOtpUsername)" [disabled]="isExpiryCompleted">Resend OTP
                  <i class="fa fa-spinner fa-spin" *ngIf="disabled"></i>
                </button>
                <span *ngIf="showTime" style="color: #ED2F45;">{{timeLeft}}</span>
              </div>
              <span style="flex: 1 1 auto;"></span>
              <button class="theme-next-button" (click)="verifySignUpByOtp(signupOtpForm.value)" [disabled]="disabled" style="z-index: 1;">
                Verify
                <i class="fa fa-spinner fa-spin" *ngIf="disabled"></i>
              </button>
            </div>
            <!-- <span *ngIf="showTime" style="color: #ED2F45;margin-left: 19px;position: relative;bottom: 15px;">{{timeLeft}}</span> -->
          </form>
          <!-- <div class="number-change" style="padding-top:6px;position: relative;bottom: 17px;left: 9px;" (click)="showHideLoginRegister('register')">
            Change Registration Details
          </div> -->
          <div fxLayoutAlign="center center">
            <img  class="tara-icon" src="../../../assets/images/tara_gif.gif"/>
          </div>
        </div>
      </div>
    </div>
  </div>

    </div>

    <div *ngIf="isMobileScreen && !showRegisterContainer" class="chatbot-container" style="z-index: 3!important;">
        <app-chatbot></app-chatbot>
    </div>

  </div>
</div>
<app-chatbot *ngIf="!isMobileScreen"></app-chatbot>
<div class="background-image-container"></div>
