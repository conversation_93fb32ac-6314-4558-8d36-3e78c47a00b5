<div class="complaint-container">
    <div class="mat-card-container" fxLayout="column" fxLayoutGap="1%">
        <div class="mat-card-scroll" fxLayout="column" fxLayoutGap="3%">
            <div class="details-content-container" fxLayout="column">
                <div class="comp-head-container" fxLayout="row" fxLayoutGap="2%">
                    <div fxFlex="16%">
                        <div class="comp-head" style="padding-top: 5px;" *ngIf="compDetails.FTC == 0">CCC COMPLAINT</div>
                        <div class="comp-head" style="padding-top: 5px;" *ngIf="compDetails.FTC == 1">FTC COMPLAINT</div>
                    </div>
                    <div fxFlex="80%"></div>
                    <div class="close-div" style="margin-top: -24px;" fxFlex="4%">
                        <button mat-icon-button class="close-btn" mat-dialog-close>
                            <mat-icon style="margin-bottom: 4px;">close</mat-icon>
                        </button>
                    </div>
                </div>
                <div class="top-divider-container">
                    <mat-divider class="classfy-head-divider"></mat-divider>
                </div>
                <div style="background-color:#F8F9F9;">
                    <div class="contents-scroll" *ngIf="compDetails.length == 0" style="height: 450px;">
                        <div class="details-container">
                            <p style="font-size: 15px; color: rgb(121, 120, 120);">No details exist...</p>
                        </div>
                    </div>
                    <div class="contents-scroll" *ngIf="compDetails.length != 0">
                        <div class="details-container" fxLayout="column" fxLayoutGap="1%">
                            <div class="classfy-head">
                                <div class="comp-head" style="font-weight: 550;">COMPLAINT DETAILS</div>
                                <div class="divider-container" style="width: 94%;margin-left: 72px;">
                                    <mat-divider class="classfy-head-divider" [inset]="true"></mat-divider>
                                </div>
                            </div>
                            <div style="margin-left: 20px; padding-top: 15px;">
                                <div fxLayout="row" fxLayoutGap="220px">
                                    <div mat-line style="width:300px">
                                        <p>
                                            <span class="detail-attribute">Case ID : </span>
                                            {{compDetails.CASE_ID}}
                                        </p>
                                    </div>
                                    <div mat-line>
                                        <p>
                                            <span class="detail-attribute">Tracking ID : </span>
                                            {{compDetails.ID}}
                                        </p>
                                    </div>
                                </div>
                                <div fxLayout="row" fxLayoutGap="220px">
                                    <div mat-line style="width:300px"
                                        *ngIf="compDetails.BRAND_NAME != null && compDetails.BRAND_NAME != ''">
                                        <p>
                                            <span class="detail-attribute">Product : </span>
                                            {{compDetails.BRAND_NAME}}
                                        </p>
                                    </div>
                                    <div mat-line style="width:300px"
                                        *ngIf="compDetails.BRAND_NAME == null || compDetails.BRAND_NAME == ''">
                                        <p>
                                            <span class="detail-attribute">Company : </span>
                                            {{compDetails.COMPANY_NAME}}
                                        </p>
                                    </div>
                                    <div mat-line *ngIf="compDetails.BRAND_NAME != null && compDetails.BRAND_NAME != ''">
                                        <p>
                                            <span class="detail-attribute">Company : </span>
                                            {{compDetails.COMPANY_NAME}}
                                        </p>
                                    </div>
                                </div>
                                <div fxLayout="row" fxLayoutGap="220px">
                                    <div mat-line style="width:300px">
                                        <p>
                                            <span class="detail-attribute">Contact : </span>
                                            {{compDetails.MOBILE}}
                                        </p>
                                    </div>
                                    <div mat-line>
                                        <p>
                                            <span class="detail-attribute">Email Id : </span>
                                            {{compDetails.EMAIL_ID}}
                                        </p>
                                    </div>
                                </div>
                                <div fxLayout="row" fxLayoutGap="220px"
                                    *ngFor="let comp of company_details; let i=index;">
                                    <div mat-line style="width: 300px;">
                                        <p>
                                            <span class="detail-attribute">Advertiser company {{i+1}} : </span>
                                            {{comp.COMPANY_NAME}}
                                        </p>
                                    </div>
                                    <div mat-line>
                                        <p>
                                            <span class="detail-attribute">Advertiser company {{i+1}} <br> email :
                                            </span>
                                            {{!!comp.EMAIL_ID ? comp.EMAIL_ID : 'Not available'}}
                                        </p>
                                    </div>
                                </div>
                                <div *ngFor="let source of detail_adsource; let i=index;">
                                    <div fxLayout="row" fxLayoutGap="220px">
                                        <div mat-line style="width: 300px;" *ngIf="source.ADVERTISEMENT_SOURCE_ID != 0">
                                            <p>
                                                <span class="detail-attribute" *ngIf="detail_adsource.length > 1">Media source {{i+1}} : </span>
                                                <span class="detail-attribute" *ngIf="detail_adsource.length == 1">Media source : </span>
                                                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 1">Television</span>
                                                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 2">Radio</span>
                                                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3">Digital Media</span>
                                                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 4">Hoardings</span>
                                                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 5">Print</span>
                                                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 6">Promotional Material</span>
                                                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 7">Packaging</span>
                                                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 8">SMS</span>
                                                <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 9">Others</span>
                                            </p>
                                        </div>
                                        <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3" style="margin-left: 100px;">
                                            <p>
                                                <span class="detail-attribute">Platform name : </span>
                                                <span *ngIf="source.PLATFORM_ID  == 1">Facebook</span>
                                                <span *ngIf="source.PLATFORM_ID  == 2">Instagram</span>
                                                <span *ngIf="source.PLATFORM_ID  == 3">YouTube</span>
                                                <span *ngIf="source.PLATFORM_ID  == 4">Twitter</span>
                                                <span *ngIf="source.PLATFORM_ID  == 5">LinkedIn</span>
                                                <span *ngIf="source.PLATFORM_ID  == 6">Website</span>
                                                <span *ngIf="source.PLATFORM_ID  == 7">Google Ad</span>
                                                <span *ngIf="source.PLATFORM_ID  == 8">Mobile App</span>
                                                <span *ngIf="source.PLATFORM_ID  == 9">Others</span>
                                            </p>
                                        </div>
                                        <div mat-line
                                            *ngIf="source.ADVERTISEMENT_SOURCE_ID== 1 || source.ADVERTISEMENT_SOURCE_ID == 2"
                                            style="margin-left: 100px;">
                                            <p>
                                                <span class="detail-attribute">Channel name : </span>
                                                {{source.SOURCE_NAME}}
                                            </p>
                                        </div>
                                        <div mat-line
                                            *ngIf="source.ADVERTISEMENT_SOURCE_ID == 4 || source.ADVERTISEMENT_SOURCE_ID == 6 "
                                            style="margin-left: 100px;">
                                            <p>
                                                <span class="detail-attribute">Place : </span>
                                                {{source.SOURCE_PLACE}}
                                            </p>
                                        </div>
                                        <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 5"
                                            style="margin-left: 100px;">
                                            <p>
                                                <span class="detail-attribute">Print source : </span>
                                                {{source.SOURCE_NAME}}
                                            </p>
                                        </div>
                                        <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 7"
                                            style="margin-left: 100px;">
                                            <p>
                                                <span class="detail-attribute">MFD/PKD Date : </span>
                                                {{source.DATE| date:'dd/MM/yyyy'}}
                                            </p>
                                        </div>
                                        <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 8"
                                            style="margin-left: 100px;">
                                            <p>
                                                <span class="detail-attribute">Sender : </span>
                                                {{source.SOURCE_NAME}}
                                            </p>
                                        </div>
                                        <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 9"
                                            style="margin-left: 100px;">
                                            <p>
                                                <span class="detail-attribute">Source : </span>
                                                {{source.SOURCE_NAME}}
                                            </p>
                                        </div>
                                        <div mat-line
                                            *ngIf="source.ADVERTISEMENT_SOURCE_ID != 0 && !source.ADVERTISEMENT_SOURCE_ID"
                                            style="margin-left: 100px;">
                                            <p>
                                                <span class="detail-attribute">Source : </span>
                                            </p>
                                        </div>
                                    </div>
                                    <div fxLayout="row" fxLayoutGap="220px" *ngIf="complaint_source_id != 7 && complaint_source_id != 8">
                                        <div mat-line style="width: 300px;" *ngIf="source.SOURCE_URL != '' && source.SOURCE_URL != null">
                                            <p>
                                                <span class="detail-attribute" *ngIf="detail_adsource.length > 1">Media
                                                    URL {{i+1}} : </span>
                                                <span class="detail-attribute" *ngIf="detail_adsource.length == 1">Media
                                                    URL : </span>
                                                <a style="font-size: 13px; cursor: pointer;"
                                                    (click)="previewLink(source.SOURCE_URL)" class="media-anchor"
                                                    matTooltip="{{source.SOURCE_URL}}">{{source.SOURCE_URL | slice:0:
                                                    35}}{{source.SOURCE_URL.length>36? '..':''}} </a>
                                            </p>
                                        </div>
                                        <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3 && source.PLATFORM_ID == 9" style="margin-left: 100px;">
                                            <p>
                                                <span class="detail-attribute">Source : </span>
                                                {{source.SOURCE_PLACE}}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div fxLayout="row" fxLayoutGap="220px">
                                    <div mat-line style="width: 300px;">
                                        <p>
                                            <span class="detail-attribute">Created date : </span>
                                            {{compDetails.CREATED_DATE | date:'dd/MM/yyyy'}}
                                        </p>
                                    </div>
                                    <div mat-line>
                                        <p>
                                            <span class="detail-attribute">Time : </span>
                                            {{compDetails.CREATED_DATE | date:'shortTime'}}
                                        </p>
                                    </div>
                                </div>
                                <div fxLayout="row" fxLayoutGap="220px">
                                    <div mat-line style="width: 300px;">
                                        <p>
                                            <span class="detail-attribute">Complaint via : </span>
                                            {{compDetails.COMPLAINT_SOURCE_NAME}}
                                        </p>
                                    </div>
                                    <div mat-line>
                                        <p>
                                            <span class="detail-attribute">Registered date : </span>
                                            {{compDetails.REGISTERED_DATE | date:'dd/MM/yyyy'}}
                                        </p>
                                    </div>
                                </div>
                                <div fxLayout="row" fxLayoutGap="220px">
                                    <div mat-line style="width: 300px;" *ngIf="detail_addate">
                                        <p>
                                            <span class="detail-attribute">Advertisement seen date : </span>
                                            {{detail_addate| date:'dd/MM/yyyy'}}
                                        </p>
                                    </div>
                                    <div mat-line style="width: 300px;" *ngIf="seen_date">
                                        <p>
                                            <span class="detail-attribute">Advertisement seen date : </span>
                                            {{seen_date| date:'dd/MM/yyyy'}}
                                        </p>
                                    </div>
                                </div>
                                <div fxLayout="row" fxLayoutGap="220px">
                                    <div mat-line *ngIf="complaint_source_id != 7 && complaint_source_id != 8">
                                        <p *ngIf="noOfDocs != 0">
                                            <span class="detail-attribute">Media file : </span>
                                            <a style="font-size: 13px; cursor: pointer;" (click)="openNewTab()"
                                                class="media-anchor">{{noOfDocs}} {{noOfDocs > 1 ? 'files': 'file'}}
                                                <img src="../../assets/images/media_download.svg"
                                                    style="position: relative;left:8px;bottom:1px"></a>
                                        </p>
                                    </div>
                                    <div mat-line style="width: 300px;" *ngIf="complaint_source_id == 7">
                                        <p>
                                            <span class="detail-attribute">Translation hyperlink : </span>
                                            <a style="font-size: 13px; cursor: pointer;"
                                                (click)="previewLink(translation_hyper)" class="media-anchor"
                                                matTooltip="{{translation_hyper}}">{{translation_hyper | slice:0:
                                                22}}{{longText5}}</a>
                                        </p>
                                    </div>
                                    <div mat-line *ngIf="complaint_source_id == 7">
                                        <p>
                                            <span class="detail-attribute">Creative Id : </span>
                                            {{creative_id}}
                                        </p>
                                    </div>
                                    <div mat-line style="width: 300px;" *ngIf="complaint_source_id == 8">
                                        <p>
                                            <span class="detail-attribute">Influencer name : </span>
                                            {{influencer_name}}
                                        </p>
                                    </div>
                                    <div mat-line *ngIf="complaint_source_id == 8">
                                        <p>
                                            <span class="detail-attribute">Engagements : </span>
                                            {{engagements}}
                                        </p>
                                    </div>
                                </div>
                                <div fxLayout="row" fxLayoutGap="220px" *ngIf="complaint_source_id == 8">
                                    <div mat-line style="width: 300px;" *ngIf="influencer_contact != null && influencer_contact != ''">
                                        <p>
                                            <span class="detail-attribute">Influencer contact no. : </span>
                                            {{influencer_contact}}
                                        </p>
                                    </div>
                                    <div mat-line *ngIf="influencer_email != null && influencer_email != ''">
                                        <p>
                                            <span class="detail-attribute">Influencer email : </span>
                                            {{influencer_email}}
                                        </p>
                                    </div>
                                </div>
                                <div fxLayout="row" fxLayoutGap="220px" *ngIf="complaint_source_id == 7">
                                    <div mat-line style="width:300px">
                                        <p>
                                            <span class="detail-attribute">Media outlet : </span>
                                            {{media_outlet}}
                                        </p>
                                    </div>
                                    <div mat-line>
                                        <p>
                                            <span class="detail-attribute">Media : </span>
                                            {{media}}
                                        </p>
                                    </div>
                                </div>
                                <div fxLayout="row" fxLayoutGap="220px" *ngIf="complaint_source_id == 8">
                                    <div mat-line style="width:300px">
                                        <p>
                                            <span class="detail-attribute">Publication URL : </span>
                                            <a style="font-size: 13px; cursor: pointer;"
                                                (click)="previewLink(publication_url)" class="media-anchor"
                                                matTooltip="{{publication_url}}">{{publication_url | slice:0:
                                                26}}{{publication_url.length>27? '..':''}}</a>
                                        </p>
                                    </div>
                                    <div mat-line>
                                        <p>
                                            <span class="detail-attribute">Profile URL : </span>
                                            <a style="font-size: 13px; cursor: pointer;"
                                                (click)="previewLink(profile_url)" class="media-anchor"
                                                matTooltip="{{profile_url}}">{{profile_url | slice:0:
                                                24}}{{profile_url.length>25? '..':''}}</a>
                                        </p>
                                    </div>
                                </div>
                                <div fxLayout="row" fxLayoutGap="220px" *ngIf="complaint_source_id == 7">
                                    <div mat-line style="width:300px" *ngIf="suppliment != null">
                                        <p>
                                            <span class="detail-attribute">Supplement : </span>
                                            {{suppliment}}
                                        </p>
                                    </div>
                                    <div mat-line *ngIf="edition != null">
                                        <p>
                                            <span class="detail-attribute">Edition : </span>
                                            {{edition}}
                                        </p>
                                    </div>
                                </div>
                                <div fxLayout="row" fxLayoutGap="220px" *ngIf="complaint_source_id == 7">
                                    <div mat-line style="width:300px">
                                        <p>
                                            <span class="detail-attribute">Ad language : </span>
                                            {{ad_language}}
                                        </p>
                                    </div>
                                    <div mat-line>
                                        <p matTooltip="{{super_category}}">
                                            <span class="detail-attribute">Product category : </span>
                                            {{super_category | slice:0:20}}{{longText2}}
                                        </p>
                                    </div>
                                </div>
                                <div fxLayout="row" fxLayoutGap="220px" *ngIf="complaint_source_id == 7 && duration">
                                    <div mat-line style="width:300px">
                                        <p>
                                            <span class="detail-attribute">Duration : </span>
                                            {{duration}}
                                        </p>
                                    </div>
                                </div>
                                <div fxLayout="row" fxLayoutGap="220px"
                                    *ngIf="complaint_source_id == 7 || complaint_source_id == 8">
                                    <div mat-line style="width:300px" *ngIf="noOfDocs != 0">
                                        <p>
                                            <span class="detail-attribute">Media file : </span>
                                            <a style="font-size: 13px; cursor: pointer;" (click)="openNewTab()"
                                                class="media-anchor">{{noOfDocs}} {{noOfDocs > 1 ? 'files': 'file'}}
                                                <img src="../../assets/images/media_download.svg"
                                                    style="position: relative;left:8px;bottom:1px"></a>
                                        </p>
                                    </div>
                                    <div mat-line *ngIf="noOfDocs != 0">
                                        <p *ngIf="detail_link" style="margin-left: 101px;">
                                            <span class="detail-attribute">Media URL : </span>
                                            <a style="font-size: 13px; cursor: pointer;"
                                                (click)="previewLink(detail_link)" class="media-anchor"
                                                matTooltip="{{detail_link}}">{{detail_link | slice:0:
                                                28}}{{longText4}}</a>
                                        </p>
                                    </div>
                                    <div mat-line *ngIf="noOfDocs == 0">
                                        <p *ngIf="detail_link">
                                            <span class="detail-attribute">Media URL : </span>
                                            <a style="font-size: 13px; cursor: pointer;"
                                                (click)="previewLink(detail_link)" class="media-anchor"
                                                matTooltip="{{detail_link}}">{{detail_link | slice:0:
                                                28}}{{longText4}}</a>
                                        </p>
                                    </div>
                                </div>
                                <div mat-line *ngIf="complaint_source_id == 7 && transcription">
                                    <p>
                                        <span class="detail-attribute">Transcription :</span>
                                    </p>
                                </div>
                                <div mat-line class="comp-msg-container" *ngIf="complaint_source_id == 7 && transcription">
                                    <p class="comp-msg" [innerHTML]="safeHTML(transcription)">
                                        {{transcription}}
                                    </p>
                                </div>
                                <div mat-line *ngIf="detail_advert">
                                    <p>
                                        <span class="detail-attribute">Advertisement description :</span>
                                    </p>
                                </div>
                                <div mat-line class="comp-msg-container" *ngIf="detail_advert">
                                    <p class="comp-msg" [innerHTML]="safeHTML(detail_advert)">
                                        {{detail_advert}}
                                    </p>
                                </div>
                                <div mat-line>
                                    <p>
                                        <span class="detail-attribute">Objectionable frames :</span>
                                    </p>
                                </div>
                                <div mat-line class="comp-msg-container">
                                    <p class="comp-msg" [innerHTML]="safeHTML(detail_complaint)">
                                        {{detail_complaint}}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="details-container" fxLayout="column" fxLayoutGap="2%">
                            <div class="classfy-head">
                                <div class="comp-head" style="font-weight: 550;">ATTACHMENTS / DOCUMENTS</div>
                                <div class="divider-container" style="width: 88%;margin-left: 125px;">
                                    <mat-divider class="classfy-head-divider" [inset]="true"></mat-divider>
                                </div>
                            </div>
                            <div *ngIf="noOfDocs == 0 && (detail_link == '' || detail_link == null)">
                                No documents
                            </div>
                            <div class="attachment-box"
                                *ngIf="noOfDocs == 0 && (detail_link != '' && detail_link != null)">
                                <div class="media-container" style="width:96%" fxLayout="row" fxLayoutGap="4%">
                                    <div>
                                        <img src="../assets/images/Link-blue.png" style="margin-top: 8px;" />
                                    </div>
                                    <div fxFlex="75%" matTooltip="{{detail_link}}">
                                        <a (click)="previewLink(detail_link)" class="media-anchor1">
                                            {{detail_link}}
                                        </a>
                                    </div>
                                    <div>
                                        <a (click)="previewLink(detail_link)">
                                            <img src="../assets/images/visit.png" style="margin-top: 10px;" />
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div *ngIf="noOfDocs != 0" fxLayout="row wrap">
                                <div class="attachment-box" *ngFor="let doc of adDocs">
                                    <div class="media-container" style="width:100%" fxLayout="row" fxLayoutGap="4%">
                                        <div *ngIf="doc.TYPE_OF_DOCUMENT != null && (doc.TYPE_OF_DOCUMENT).split('/')[0] == 'image'">
                                            <img src="../../assets/images/img.png" style="margin-top: 5px;" />
                                        </div>
                                        <div *ngIf="doc.TYPE_OF_DOCUMENT != null && (doc.TYPE_OF_DOCUMENT).split('/')[0] == 'video'">
                                            <img src="../../assets/images/vid.png" style="margin-top: 5px;" />
                                        </div>
                                        <div
                                            *ngIf="doc.TYPE_OF_DOCUMENT != null && ((doc.TYPE_OF_DOCUMENT).split('/')[0] == 'application' || (doc.TYPE_OF_DOCUMENT).split('/')[0] == 'text' || doc.TYPE_OF_DOCUMENT == 'pdf')">
                                            <img src="../../assets/images/docs.png" style="margin-top: 5px;" />
                                        </div>
                                        <div *ngIf="doc.TYPE_OF_DOCUMENT != null && (doc.TYPE_OF_DOCUMENT).split('/')[0] == 'audio'">
                                            <img src="../../assets/images/audio.png" style="margin-top: 5px;" />
                                        </div>
                                        <div matTooltip="{{doc.ATTACHMENT_NAME}}" fxFlex="65%">
                                            <div style="margin-top: 5px;">{{doc.ATTACHMENT_NAME | slice:
                                                0:20}}{{doc.ATTACHMENT_NAME.length>21? '..': ''}}
                                            </div>
                                        </div>
                                        <div style="cursor: pointer;">
                                            <img src="../../assets/images/Down.png"
                                                (click)="download(doc.ATTACHMENT_NAME, doc.ATTACHMENT_SOURCE)"
                                                style="margin-top: 5px;">
                                        </div>
                                        <div style="cursor: pointer;">
                                            <img src="../../assets/images/eye.png"
                                                (click)="preview(doc.ATTACHMENT_SOURCE)" style="margin-top: 7px;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>