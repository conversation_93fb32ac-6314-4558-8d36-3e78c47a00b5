import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class TasksService {

  public resourceUrl = `${environment.API_BASE_URL}`;

  constructor(private http: HttpClient) { }

  getTasktableData(pageNumber,limit) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/task?PAGE=${pageNumber}&limit=${limit}`, options);
  }

  getTaskDataById(id,limit): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/task?COMPLAINT_ID=${id}&limit=${limit}`, options);
  }

  getTaskDataByTaskId(id): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/task?ID=${id}`, options);
  }

  getAssigneList(key) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/assignee?KEYWORD=${key}`, options);
  }

  getComplaintsList(value) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint/list?QUERY=${value}`, options);
  }

  createTask(record): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/task/create`, record, options);
  }

  getTaskTimeline(id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/task/timeline?TASK_ID=${id}`, options);
  }

  deleteTask(id): Observable<any> {
    let obj = {
      'ID': id
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/task/delete`, obj, options);
  }

  updateTask(record): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/task/update`, record, options);
  }

  getTaskComments(id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/task/comment?TASK_ID=${id}`, options);
  }

  createComment(data, TASK_ID, COMPLAINT_ID, CREATED_BY): Observable<any> {
    let obj = {
      "TASK_ID": TASK_ID,
      "COMPLAINT_ID": COMPLAINT_ID,
      "COMMENTS": data,
      "CREATED_BY": CREATED_BY
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/task/create-comment`, obj, options);
  }

  deleteComment(id): Observable<any> {
    let obj = {
      'ID': id
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/task/delete-comment`, obj, options);
  }

  updateComment(ID, comment): Observable<any> {
    let obj = {
      'ID': ID,
      'COMMENTS': comment
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/task/update-comment`, obj, options);
  }

  getTaskDocuments(id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/task/document?TASK_ID=${id}`, options);
  }

  filterTask(filter) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let query = "?";
    for (let i = 0; i < filter.length; i++) {
      query = query + filter[i].name + "=" + filter[i].value
      if (i < filter.length - 1) {
        query = query + "&"
      }
    }
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/task${query}`, options);
  }

  attachTaskDocuments(files, task_id, comp_id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    const formData: any = new FormData();
    const file: Array<File> = files;
    for (let i = 0; i < files.length; i++) {
      formData.append("docs", files[i]);
    }
    return this.http.post<any>(`${this.resourceUrl}/web/asci/task/attach-document?ID=${comp_id}&TASK_ID=${task_id}`, formData, options);
  }

  deleteTaskDocuments(id, ATTACHMENT_SOURCE): Observable<any> {
    let obj = {
      'ID': id,
      'KEY': ATTACHMENT_SOURCE
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/task/delete-document`, obj, options);
  }

  saveTaskDocToDB(body:any){
      let options = { headers: this.getHeaders() };
      return this.http.post<any>(`${this.resourceUrl}/web/asci/task/save-to-db`,body, options).toPromise();
  }

  getHeaders(){
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    return headers;
  }

}
