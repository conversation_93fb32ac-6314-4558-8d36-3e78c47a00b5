import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MarkInvalidComponent } from './mark-invalid.component';

describe('MarkInvalidComponent', () => {
  let component: MarkInvalidComponent;
  let fixture: ComponentFixture<MarkInvalidComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ MarkInvalidComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(MarkInvalidComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
