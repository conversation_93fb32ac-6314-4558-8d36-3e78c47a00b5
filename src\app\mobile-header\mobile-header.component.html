<div class="header-container" fxLayout="row">
    <div class="headline-container" fxLayout="row" fxLayoutAlign="start center">
        <img class="logo" src="../assets/images/logo-with-title.png" />
    </div>
    <span style="flex: 1 1 auto;"></span>
    <div fxLayout="row" fxLayoutAlign="end center">
        <img class="bell-btn" src="../assets/images/bell-mobile.png" (click)="viewNotifications()" *ngIf="roleId == 4 || roleId == 5 || roleId == 7 || roleId == 8"/>
        <button [matMenuTriggerFor]="admin" mat-mini-fab class="flex profile-btn">{{roleName}}</button>
    </div>
    <mat-menu #admin="matMenu" class="admin-menu">
        <div class="admin-option-container">
          <button mat-menu-item routerLink="/my-profile" class="option-btn" (click)="viewProfile(true)" *ngIf="roleId == 4 || roleId == 5 || roleId == 7 || roleId == 8">
            <span class="option-text">My profile</span>
          </button>
          <mat-divider class="option-divider" *ngIf="roleId == 4 || roleId == 5 || roleId == 7 || roleId == 8"></mat-divider>
          <button mat-menu-item class="option-btn" (click)='logout()'>
            <span class="option-text">Log out</span>
          </button>
        </div>
      </mat-menu>
</div>