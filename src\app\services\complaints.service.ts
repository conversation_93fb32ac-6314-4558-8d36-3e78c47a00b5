import { Injectable } from '@angular/core';
import { HttpClient, HttpEvent, HttpEventType, HttpHeaders } from '@angular/common/http';
import { Complaints } from '../model/complaints';
import { BehaviorSubject, Observable } from 'rxjs';
import { Subject } from 'rxjs';
import { environment } from 'src/environments/environment';
import { tap } from 'rxjs/operators';

export class Message {
  constructor(public author: string, public content: string, public time: Date, public type: String) { }
}

@Injectable({
  providedIn: 'root'
})

export class ComplaintsService {
  time: Date;
  conversation = new Subject<Message[]>();
  listComplaint$: Observable<any>;
  private listId = new Subject<any>();
  imageError: string;
  isImageSaved: boolean;
  cardImageBase64: string;
  format;
  url;
  activePage = "Dashboard";

  getComplaint$: Observable<any>;
  companyFilter$: Observable<any>;
  private companyName = new Subject<any>();
  private company = new Subject<any>();
  public resourceUrl = `${environment.API_BASE_URL}`;
  public bucketUrl = `${environment.BUCKET_URL}`;
  isFTCComplaint: Subject<any> = new BehaviorSubject<any>({ isFTC: 0 });
  complainantID: Subject<any> = new BehaviorSubject<any>({ complainantID: 0 });

  //Search Filter
  private filter_subject = new Subject<any>();

  //sample
  msg: string;

  count: number;
  company_count = [];

  invokeRefreshEvent: Subject<any> = new Subject();
  callManageCaseComponent() {
    this.invokeRefreshEvent.next("refreshCompList")
  }

  complaintData: any[] = [];
  complaintRegisterDataSource: BehaviorSubject<Array<any>> = new BehaviorSubject([]);
  complaintRegisterData = this.complaintRegisterDataSource.asObservable();

  complaintRegister(stepData: any, index) {
    if (Object.keys(stepData).length === 0 && stepData.constructor === Object) {
      this.complaintData.splice(index, 1);
    } else {
      this.complaintData[index] = stepData;
    }
    this.complaintRegisterDataSource.next(Object.assign([], this.complaintData))
  }

  private buttonName = new BehaviorSubject('Admin');
  currentButtonName = this.buttonName.asObservable();

  updateButtonName(name: string) {
    this.buttonName.next(name)
  }

  private editableForm = new BehaviorSubject('');
  currentEditableForm = this.editableForm.asObservable();

  updateEditableForm(name: string) {
    this.editableForm.next(name)
  }

  private complaintStatus = new BehaviorSubject('');
  currentComplaintStatus = this.complaintStatus.asObservable();

  getStatusComplaints(name: string) {
    this.complaintStatus.next(name)
  }

  private blankComplaintId = new BehaviorSubject(0);
  currentBlankComplaintId = this.blankComplaintId.asObservable();

  // updateBlankComplaintId(ID: number) {
  //   this.blankComplaintId.next(ID);
  // }

  public get getBlankId(): any {
    return this.blankComplaintId.getValue()
  }

  public set updateBlankComplaintId(ID: any) {
    this.blankComplaintId.next(ID)
  }

  private step = new BehaviorSubject('direct');
  currentStep = this.step.asObservable();

  updateStep(step: string) {
    this.step.next(step)
  }

  private complaintTimeline = new BehaviorSubject(0);
  currentComplaintTimeline = this.complaintTimeline.asObservable();

  getTimeline(id: number) {
    this.complaintTimeline.next(id)
  }

  private chatbotComplaintId = new BehaviorSubject(0);
  currentChatbotComplaintId = this.chatbotComplaintId.asObservable();

  updateChatbotComplaintId(chatbotComplaintId: number) {
    this.chatbotComplaintId.next(chatbotComplaintId)
  }

  private whatsappComplaintId = new BehaviorSubject(0);
  currentWhatsappComplaintId = this.whatsappComplaintId.asObservable();

  updateWhatsappComplaintId(whatsappComplaintId: number) {
    this.whatsappComplaintId.next(whatsappComplaintId)
  }

  private whatsappComplaintObj = new BehaviorSubject({});
  currentWhatsappComplaintObj = this.whatsappComplaintObj.asObservable();

  updateWhatsappComplaintObj(whatsappComplaintObj: Object) {
    this.whatsappComplaintObj.next(whatsappComplaintObj)
  }

  private emailComplaintId = new BehaviorSubject(0);
  currentEmailComplaintId = this.emailComplaintId.asObservable();

  updateEmailComplaintId(emailComplaintId: number) {
    this.emailComplaintId.next(emailComplaintId)
  }

  private sysComplaintId = new BehaviorSubject(0);
  currentSysComplaintId = this.sysComplaintId.asObservable();

  updateSysComplaintId(sysComplaintId: number) {
    this.sysComplaintId.next(sysComplaintId)
  }

  public complaintTypeId = new BehaviorSubject(0);
  currentComplaintTypeId = this.complaintTypeId.asObservable();

  public get getComplaintType(): any {
    return this.complaintTypeId.getValue()
  }

  updateComplaintType(ID: any) {
    this.complaintTypeId.next(ID)
  }

  private namsComplaintId = new BehaviorSubject(0);
  currentNamsComplaintId = this.namsComplaintId.asObservable();

  // updateNamsComplaiublicntId(namsComplaintId: number) {
  //   this.namsComplaintId.next(namsComplaintId)
  // }

  public get getNamsBlankId(): any {
    return this.namsComplaintId.getValue()
  }

  public set updateNamsComplaintId(ID: any) {
    this.namsComplaintId.next(ID)
  }

  public _subject = new BehaviorSubject<any>('');

  emit<T>(data: T) {
    this._subject.next(data);
  }

  on<T>(): Observable<T> {
    return this._subject.asObservable();
  }


  // sendFilterEvent()
  // {
  //   this.filter_subject.next();
  // }

  // getFilterEvent():Observable<any>{
  //   return this.filter_subject.asObservable();
  // }

  baseURL: string = "http://localhost:3000/complaints";

  constructor(private http: HttpClient) {
    this.listComplaint$ = this.listId.asObservable();
    // this.companyFilter$ = this.companyName.asObservable();
    this.getComplaint$ = this.company.asObservable();
  }

  setMsg(data, count) {
    // this.filter_subject.next();
    // this.company.next(data);
    this.msg = data;
    this.count = count;
    // this.companyName.next(data);
  }
  // getMsg():Observable<any>{
  //   return this.filter_subject.asObservable();

  getMsg() {
    this.company_count[0] = this.msg;
    this.company_count[1] = this.count;
    // return this.msg;
    return this.company_count;
  }

  getComplaintByCompany(company: string): Observable<Complaints> {
    const url = `${this.baseURL}/${company}`;
    return this.http.get<Complaints>(url);
  }

  getWhatsappComplaints(pageNumber): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/whatsapp?PAGE=${pageNumber}`, options);
  }

  deleteWhatsappComplaint(id): Observable<any> {
    let obj = {
      "ID": id
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/whatsapp/complaint/delete`, obj, options);
  }

  getMergeCompaniesInfo(idArray) {
    let obj = {
      "ID": idArray
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/merge/companies-info`, obj, options);
  }

  mergeCompanies(obj): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/merge/companies`, obj, options);
  }

  getComplaints(CLASSIFICATION_ID, BRAND_NAME, COMPLAINT_STATUS_ID, SOURCE_ID, SIMILAR_CASE_ID, SEARCH_KEY, pageNumber, ftc, registered): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint?CLASSIFICATION_ID=${CLASSIFICATION_ID}&COMPLAINT_STATUS_ID=${COMPLAINT_STATUS_ID}&COMPLAINT_SOURCE_ID=${SOURCE_ID}&BRAND_NAME=${BRAND_NAME}&SIMILAR_CASE_ID=${SIMILAR_CASE_ID}&SEARCH_KEY=${SEARCH_KEY}&PAGE=${pageNumber}&REGISTERED=${registered}&FTC=${ftc}`, options);
  }

  getFTCComplaints(CLASSIFICATION_ID, BRAND_NAME, COMPLAINT_STATUS_ID, SOURCE_ID, SEARCH_KEY, pageNumber): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint/FTC?CLASSIFICATION_ID=${CLASSIFICATION_ID}&COMPLAINT_STATUS_ID=${COMPLAINT_STATUS_ID}&COMPLAINT_SOURCE_ID=${SOURCE_ID}&BRAND_NAME=${BRAND_NAME}&SEARCH_KEY=${SEARCH_KEY}&PAGE=${pageNumber}`, options);
  }

  getSysGenComplaints(CLASSIFICATION_ID, BRAND_NAME, COMPLAINT_STATUS_ID, SOURCE_ID, SEARCH_KEY, pageNumber, ftc, registered): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint?CLASSIFICATION_ID=${CLASSIFICATION_ID}&COMPLAINT_STATUS_ID=${COMPLAINT_STATUS_ID}&COMPLAINT_SOURCE_ID=${SOURCE_ID}&BRAND_NAME=${BRAND_NAME}&SEARCH_KEY=${SEARCH_KEY}&PAGE=${pageNumber}&REGISTERED=${registered}`, options);
  }


  getDocumentsByComplaint(ID): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint/document?COMPLAINT_ID=${ID}`, options);
  }

  listComplaint(id) {
    this.listId.next(id);
  }

  getComplaint(id: number): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint?ID=${id}`, options);
  }

  getWhatsappComplaint(id: number): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/whatsapp?ID=${id}`, options);
  }

  getMailComplaint(id: number): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/emails?ID=${id}`, options);
  }

  getMailComplaints(page): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/emails?PAGE_TOKEN=${page}`, options);
  }

  getFTCComplaint(id: number): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint?ID=${id}`, options);
  }

  saveFileSourceToDB(body: any) {
    let options = { headers: this.getHeaders() };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/complaint/save-to-db`, body, options).toPromise();
  }

  saveChatbotFileToDB(body: any) {
    let options = { headers: this.getHeaders() };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/chatbot/save-to-db`, body, options).toPromise();
  }

  getHeaders() {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    return headers;
  }

  getAssigneeList(key, email_id): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/assignee?KEYWORD=${key}&EMAIL_KEY=${email_id}`, options);
  }

  deleteComplaint(id): Observable<any> {
    let obj = {
      "ID": id
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/complaint/delete`, obj, options);
  }

  autoComplete(value): Observable<any> {
    return;
  }

  messageMap = {
    "Hi": "Hello",
    "Let's Start": "Should we proceed to register a complaint?",
    "Yes": "Whom do you represent?",
    "No": "Sorry,currently I only register new complaints,so if you have any other query,please write to us at: <EMAIL>",
    "Want to change number": "Please share me your mobile number",
    "I am complaining as an individual": "Please share ur mobile number",
    "I am complaining as a business/company/sole trader about the advertising of a competitor": "Please share ur mobile number",
    "I am complaining as a consumer interest body": "Please share ur mobile number",
    "I am complaining as a government body/regulator": "Please share ur mobile number",
    "Who are you?": "I'm Angular Bot",
    "How are you?": "Couldn't be better!!",
    "Verified Successfully": "Where have you seen this ad?",
    "Digital Media": "Please specify the platform",
    "default": "I can't understand. Can you please repeat?"
  }

  getBotAnswer(msg: any) {
    if (typeof (msg) == 'string') {
      const userMessage = new Message('user', msg, this.time = new Date(Date.now()), 'String');
      this.conversation.next([userMessage]);
      const botMessage = new Message('bot', this.getBotMessage(msg), this.time = new Date(Date.now()), 'String');
      setTimeout(() => {
        this.conversation.next([botMessage]);
      }, 1000);
    }

    else {
      const file = msg.target.files && msg.target.files[0];
      if (file) {
        var reader = new FileReader();
        reader.readAsDataURL(file);
        if (file.type.indexOf('image') > -1) {
          this.format = 'Image';
        } else if (file.type.indexOf('video') > -1) {
          this.format = 'Video';
        }
        reader.onload = (event) => {
          this.url = (<FileReader>event.target).result;
          if (this.url.includes('video') || this.url.includes('image')) {
            const userMessage = new Message('user', this.url, this.time = new Date(Date.now()), this.format);
            this.conversation.next([userMessage]);
            const botMessage = new Message('bot', 'Got it', this.time = new Date(Date.now()), 'String');
            setTimeout(() => {
              this.conversation.next([botMessage]);
            }, 1000);
          }
          else {
            alert("Only Images and videos are allowed")
          }
        }
      }
    }
  }

  getBotMessage(question: any) {
    if (!isNaN(question) && (question.length >= 10 && question.length <= 15)) {
      let answer = "I have sent you an OTP on your above mobile number,please enter to verify";
      return answer
    } else if (!isNaN(question) && question.length == 6) {
      let answer = "Verified Successfully"
      return answer
    }
    else {
      let answer = this.messageMap[question];
      return answer || this.messageMap['default'];
    }
  }

  getMasterData(): Observable<any> {
    return this.http.get<any>(`${this.resourceUrl}/web/asci/masters-data`, {});
  }

  createUserByComplaint(model, roleId): Observable<any> {
    let obj = {
      "EMAIL_ID": model.email,
      "FIRST_NAME": model.first_name,
      "LAST_NAME": model.last_name,
      "PHONE_NUMBER": model.phone_number.toString(),
      "COMPANY_ID": model.companyID,
      "PINCODE": model.postal_code,
      "ORGANIZATION_NAME": model.organization,
      "GOVERNMENT_DEPARTMENT_ID": model.govt_body,
      "GOVERNMENT_DEPARTMENT_NAME": model.govt_body_name,
      "ADDRESS": model.address,
      "ROLE_ID": roleId.toString(),
      "USER_TYPE_ID": "1",
      "DEPARTMENT_TYPE_ID": "2",
      "CONTACT_METHOD_TYPE_ID": model.contact_method,
      "STATE_TYPE_ID": model.state,
      "DISTRICT_TYPE_ID": model.district,
      "CITY": model.city,
      "USER_AGENT": "Agent",
      "PROFESSION_NAME": model.profession,
      "SALUTATION_ID": model.salutation
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/user/consumer/create`, obj, options);
  }

  createBlankComplaint(selectedUserTypeID, currentUserId, createdUserId, is_FTC, createdDate): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    let obj = {
      "USER_TYPE_ID": selectedUserTypeID,
      "USER_ID": createdUserId,
      "CREATED_BY_USER_ID": currentUserId,
      "FTC": is_FTC,
      "CREATED_DATE": createdDate
    }
    return this.http.post<any>(`${this.resourceUrl}/web/asci/complaint`, obj, options);
  }

  deleteBlankComplaint(id): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    let obj = {
      "ID": id
    }
    return this.http.put<any>(`${this.resourceUrl}/web/asci/complaint/blank/delete`, obj, options);
  }

  deleteDraftComplaint(id): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    let obj = {
      "ID": id
    }
    return this.http.put<any>(`${this.resourceUrl}/web/asci/complaint/draft/delete`, obj, options);
  }

  createComplaint(obj): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/complaint`, obj, options);
  }

  updateComplaint(obj): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/complaint/update`, obj, options);
  }

  migrateOldComplaint(obj): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/complaint/migrate`, obj, options);
  }

  addRecommendation(id, data): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    let obj = {
      "COMPLAINT_ID": id,
      "SUBJECT": data.subject,
      "RECOMMENDATION": data.recommendation
    };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/complaint/recommendation`, obj, options);
  }

  getRecommendationsById(id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint/recommendation?COMPLAINT_ID=${id}`, options);
  }

  addResolution(id, data): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    let obj = {
      "COMPLAINT_ID": id,
      "SUBJECT": data.subject,
      "RESOLUTION": data.resolution
    };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/complaint/resolution`, obj, options);
  }

  getResolutionsById(id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint/resolution?COMPLAINT_ID=${id}`, options);
  }

  publishRecommendation(isPublished, id, complaint_id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    let obj = {
      "COMPLAINT_ID": complaint_id,
      "RECOMMENDATION_ID": id,
      "PUBLISH": isPublished,
    };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/complaint/recommendation/publish`, obj, options);
  }

  deleteRecommendation(id, complaint_id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    let obj = {
      "COMPLAINT_ID": complaint_id,
      "RECOMMENDATION_ID": id
    };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/complaint/recommendation/delete`, obj, options);
  }

  getComplaintTimeline(id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint/timeline?ID=${id}`, options);
  }

  getClaimsDocuments(id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/claim/document?ID=${id}`, options);
  }

  getSimilarComplaints(parentId, caseId) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint/similar?PARENT_ID=${parentId}&CASE_ID=${caseId}`, options);
  }

  getAdvertiser(id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/users/advertiser?COMPANY_ID=${id}`, options);
  }

  downloadFile(url): Observable<Blob> {
    return this.http.get(url,
      { responseType: 'blob' });
  }

  getComplaintActivities(pageNumber) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint/activity-timeline?PAGE=${pageNumber}`, options);
  }

  setActivePage(data) {
    this.activePage = data;
  }
  getActivePage() {
    return this.activePage;
  }

  getUserComplaints(year): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/complaint?YEAR=${year}`, options);
  }

  getUserComplaintById(id): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/complaint?ID=${id}`, options);
  }

  getUserChatbotComplaints(year): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/chatbot/complaint?YEAR=${year}`, options);
  }

  getUserChatbotComplaintById(id): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/chatbot/complaint?ID=${id}`, options);
  }

  getAdvertiserComplaints(year): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/complaint/received?YEAR=${year}`, options);
  }

  getAdvertiserComplaintById(id): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/complaint/received?ID=${id}`, options);
  }

  getAdminComplaintStats(year): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint/stats?YEAR=${year}`, options);
  }

  getCompanyComplaintStats(year): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/company/complaint/stats?YEAR=${year}`, options);
  }

  getUserComplaintStats(year): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/user/complaint/stats?YEAR=${year}`, options);
  }

  getInboxComplaintStats(): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint/inbox/stats`, options);
  }

  uploadComplaintAttachments(files, complaintId, section) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    const formData: any = new FormData();
    const file: Array<File> = files;
    for (let i = 0; i < files.length; i++) {
      formData.append("docs", files[i]);
    }
    return this.http.post<any>(`${this.resourceUrl}/web/asci/complaint/attach-document?ID=${complaintId}&SECTION=${section}`, formData, options);
  }

  deleteComplaintDocuments(ID, ATTACHMENT_SOURCE): Observable<any> {
    let obj = {
      'ID': ID,
      'KEY': ATTACHMENT_SOURCE
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/complaint/delete-document`, obj, options);
  }

  sendEmail(record, COMP_ID, USER_ID, USER_EMAIL) {
    let obj = {
      "COMPLAINT_ID": COMP_ID,
      "FROM_USER_ID": USER_ID,
      "FROM_EMAIL_ID": USER_EMAIL,
      "TO_EMAIL_ID": record.TO_EMAIL_ID,
      "TITLE": record.TITLE,
      "BODY": record.BODY
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/claim/send-message`, obj, options);
  }

  getComplaintClassification(compDesc, comp_source_id, row_id) {
    let obj = {
      "COMPLAINT_DESCRIPTION": compDesc,
      "COMPLAINT_SOURCE_ID": comp_source_id,
      "ROW_ID": row_id
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/ml/complaint/classification`, obj, options);
  }

  getSimilarityCheck(classificationTag, desc, hyperLinik, brand, product, company, compSourceId, id, creativeId): Observable<any> {
    if (!!desc) {
      desc = desc.replace(/<[^>]+>/g, '');
    }
    let obj = {
      "COMPLAINT_DESCRIPTION": !!desc ? desc : '',
      "BRAND_NAME": brand,
      "CLASSIFICATION_TAG": classificationTag,
      "PRODUCT_NAME": product,
      "COMPANY_NAME": company,
      "COMPLAINT_SOURCE_ID": compSourceId,
      "COMPLAINT_ID": id,
      "CREATIVE_ID": creativeId
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/ml/complaint/similarity-check`, obj, options);
  }

  addNewComplaint(similarityData, complaint_attachment_data, compSourceId, id) {
    const d = new Date();
    let year = d.getFullYear();
    let month = d.getMonth();
    let obj = {
      "COMPANY_NAME": !!similarityData.company_name ? similarityData.company_name : '',
      "BRAND_NAME": !!similarityData.brand_name ? similarityData.brand_name : '',
      "PRODUCT_NAME": !!similarityData.product_name ? similarityData.product_name : '',
      "COMPLAINT_IDENTIFICATION": similarityData.complaint_identification,
      "COMPLAINT_SOURCE_ID": compSourceId,
      "ROW_ID": id
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/ml/complaint/add`, obj, options);
  }

  registerComplaint(id, ml_data): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    let obj = {
      ID: id,
      ML_DATA: ml_data
    }
    return this.http.put<any>(`${this.resourceUrl}/web/asci/complaint/register`, obj, options);
  }

  getAllAdvertisers(id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/company/user/member?COMPANY_ID=${id}`, options);
  }

  getAllCompanyEmails(id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/company/list?ID=${id}`, options);
  }

  getAllComplainants(case_id, id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint/user/requester?CASE_ID=${case_id}&ID=${id}`, options);
  }

  getChatbotComplainants(id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/chatbot/complaint/user-info?ID=${id}`, options);
  }

  getEmailTemplates(id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/conversation/email/template?ID=${id}`, options);
  }

  getMails(comp_id, chatbot_comp_id, user_id, email_id, company_id, call_from, call_for) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/conversation/email?COMPLAINT_ID=${comp_id}&CHATBOT_COMPLAINT_ID=${chatbot_comp_id}&USER_ID=${user_id}&EMAIL_ID=${email_id}&COMPANY_ID=${company_id}&CALL_FROM=${call_from}&CALL_FOR=${call_for}`, options);
  }

  sendEmails(record, COMP_ID, CHATBOT_COMP_ID, TO_EMAIL_ID, CC_EMAIL_ID, BCC_EMAIL_ID, ATTACHMENTS, CALL_FROM) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    var ascilogo = `${this.bucketUrl}branding/ascilogo.png`;
    var asciSignature = `${this.bucketUrl}branding/signature-promotion.png`;
    let message = "";
    if (userInfo.roleId == 1 || userInfo.roleId == 2 || userInfo.roleId == 3 || userInfo.roleId == 6) {
      message = record.BODY + `<br><img src="${ascilogo}" width="184" height="63"><br><span style="font-weight: bolder;">402, A Wing, Aurus Chambers, S.S. Amrutwar Marg, Worli, Mumbai - 400013</span><br><br><a href="https://www.ascionline.in/academy/courses-2/" target="_blank"><img src="${asciSignature}" width="630" height="110"></a>`;
    }
    else {
      message = record.BODY;
    }
    let obj = {
      "COMPLAINT_ID": COMP_ID,
      "CHATBOT_COMPLAINT_ID": CHATBOT_COMP_ID,
      "TO_EMAIL_ID": record.TO_EMAIL_ID,
      "TO_USER_ID": TO_EMAIL_ID,
      "CC": record.CC_EMAIL_ID,
      "CC_USER_ID": CC_EMAIL_ID,
      "BCC": record.BCC_EMAIL_ID,
      "BCC_USER_ID": BCC_EMAIL_ID,
      "TITLE": record.TITLE_CASEID + record.TITLE,
      "SUBJECT": record.TITLE_CASEID + record.TITLE,
      "BODY": message,
      "ATTACHMENTS": ATTACHMENTS,
      "CALL_FROM": CALL_FROM
    }
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/conversation/email`, obj, options);
  }

  getSMS(comp_id, chatbot_comp_id, user_id, call_from) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/conversation/sms?COMPLAINT_ID=${comp_id}&CHATBOT_COMPLAINT_ID=${chatbot_comp_id}&USER_ID=${user_id}&CALL_FROM=${call_from}`, options);
  }

  sendSMS(MESSAGE, COMP_ID, CHATBOT_COMP_ID, USER_ID, ATTACHMENTS, CALL_FROM) {
    let obj = {
      "COMPLAINT_ID": COMP_ID,
      "CHATBOT_COMPLAINT_ID": CHATBOT_COMP_ID,
      "TO_USER_ID": USER_ID,
      "BODY": MESSAGE,
      "ATTACHMENTS": ATTACHMENTS,
      "CALL_FROM": CALL_FROM
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/conversation/sms`, obj, options);
  }

  getAllUsers(id, chatbot_id, sms, call_from) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/conversation/user?COMPLAINT_ID=${id}&CHATBOT_COMPLAINT_ID=${chatbot_id}&SMS=${sms}&CALL_FROM=${call_from}`, options);
  }

  createEmailTemplate(record) {
    // var ascilogo = "https://aw-asci-complaints-records.s3.ap-south-1.amazonaws.com/ascilogo.png";
    let obj = {
      "TITLE": record.TITLE,
      "SUBJECT": record.TITLE,
      "BODY": record.BODY,
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/conversation/email/template`, obj, options);
  }

  getAssigneeUser(id) {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint/assignee?COMPLAINT_ID=${id}`, options);
  }

  shareInternalDocuments(id, advertiser, complainant) {
    let obj = {
      "ID": id,
      "ADVERTISER": advertiser,
      "COMPLAINANT": complainant
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/complaint/doc/update-visibility`, obj, options);
  }

  markInvalid(id, record) {
    let obj = {
      "ID": id,
      "MESSAGE": `*` + record.reason + `*:` + ` ` + record.solution
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.post<any>(`${this.resourceUrl}/web/asci/whatsapp/send`, obj, options);
  }

  editSystemComplaint(cond, id): Observable<any> {
    let obj = {
      "ID": id,
      "EDITABLE": cond
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/complaint/editable-status/update`, obj, options);
  }

  editChatbotComplaint(cond, id): Observable<any> {
    let obj = {
      "ID": id,
      "EDITABLE": cond
    }
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.put<any>(`${this.resourceUrl}/web/asci/chatbot/complaint/editable-status/update`, obj, options);
  }

  getArchivedComplaints(pageNumber, keyword, comp_medium, comp_status, resolution, compliance, stage, process): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint/archived?PAGE=${pageNumber}&SEARCH_KEY=${keyword}&COMPLAINT_STATUS_ID=${comp_status}&COMPLAINT_SOURCE_ID=${comp_medium}&RESOLUTION_ID=${resolution}&COMPLIANCE_STATUS_ID=${compliance}&STAGE_ID=${stage}&PROCESS_ID=${process}`, options);
  }

  getArchivedComplaintDetails(id): Observable<any> {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    const headers: any = new HttpHeaders({
      'x-access-token': encodeURIComponent(userInfo?.access_token),
      'id-token': encodeURIComponent(userInfo?.id_token),
    });
    let options = { headers: headers };
    return this.http.get<any>(`${this.resourceUrl}/web/asci/complaint/archived?ID=${id}`, options);
  }

}