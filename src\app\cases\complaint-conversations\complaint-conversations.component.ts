import { Component, HostListener, Inject, OnInit, Pipe } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { NamsEmailComponent } from 'src/app/nams-email/nams-email.component';
import { ViewUploadDocumentComponent } from '../view-upload-document/view-upload-document.component';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { FormControl, Validators } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';
@Component({
  selector: 'app-complaint-conversations',
  templateUrl: './complaint-conversations.component.html',
  styleUrls: ['./complaint-conversations.component.scss'],
})
@Pipe({ name: 'safeHtml' })
export class ComplaintConversationsComponent implements OnInit {

  complaint_id;
  chatbot_complaint_id;
  call_from: string = "";
  case_Id;
  comp_detail;
  company_name;
  brand_name;
  advertiser_name;
  advertiserDetails = [];
  complainantDetails = [];
  companyEmails = [];
  internalEmails = [];
  company_id;
  advertiserList: boolean;
  complainantsList: boolean;
  selectedTab = 0;
  comp_id: number;
  user_id: number;
  mailsSent: any = [];
  SMSSent: any = [];
  sender_name;
  searchFilter = [];
  text_message = new FormControl('', Validators.required);
  profile = '';
  sender_mail;
  allAdvMembers: boolean = false;
  allCompMailMembers: boolean = false;
  allComplainantMails: boolean = false;
  senderMailArray = [];
  selectedFiles = [];
  name: string;
  senderDetails = [];
  disableSend: boolean = false;
  advIndex;
  companyIndex;
  internalIndex;
  userData: any;
  iniName: string = "";
  iniLname: string = "";
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private matDialog: MatDialog,
    public dialogRef: MatDialogRef<ComplaintConversationsComponent>,
    private cs: ComplaintsService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private sanitized: DomSanitizer,
    private notify: NotificationService) { }

  safeHTML(unsafe: string) {
    return this.sanitized.bypassSecurityTrustHtml(unsafe);
  }

  ngOnInit(): void {
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
    this.userData = JSON.parse(window.localStorage.getItem('userInfo'));
    if (this.data.from == 'manage-cases') {
      this.case_Id = this.data.details[0].data.CASE_ID;
      this.comp_detail = (this.data.details[0].data.ADVERTISEMENT_DESCRIPTION).replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').replace(/amp;/g, ' ');
      this.company_name = this.data.details[0].data.COMPANY_NAME;
      this.brand_name = this.data.details[0].data.BRAND_NAME;
      this.advertiser_name = this.data.advertiser;
      this.company_id = this.data.details[0].data.COMPANY_ID;
      this.complaint_id = this.data.details[0].data.ID;
      this.chatbot_complaint_id = 0;
      this.call_from = "system";
      this.getAdvertisers();
    }
    else if (this.data.from == 'inbox-system') {
      this.case_Id = this.data.details.CASE_ID;
      this.comp_detail = (this.data.details.ADVERTISEMENT_DESCRIPTION).replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').replace(/amp;/g, ' ');
      this.company_name = this.data.details.COMPANY_NAME;
      this.brand_name = this.data.details.BRAND_NAME;
      this.company_id = this.data.details.COMPANY_ID;
      this.complaint_id = this.data.details.ID;
      this.chatbot_complaint_id = 0;
      this.call_from = "system";
      this.getComplainants();
    }
    else if (this.data.from == 'inbox-chatbot') {
      this.case_Id = this.data.details.CASE_ID;
      this.comp_detail = (this.data.details.COMPLAINT_DESCRIPTION).replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').replace(/amp;/g, ' ');
      this.company_name = this.data.details.COMPANY_NAME;
      this.brand_name = this.data.details.BRAND_NAME;
      this.company_id = this.data.details.COMPANY_ID;
      this.chatbot_complaint_id = this.data.details.ID;
      this.complaint_id = 0;
      this.call_from = "chatbot";
      this.getComplainants();
    }
  }

  getShortName(data) {
    if (data.FIRST_NAME != "" && data.FIRST_NAME != null) {
      this.iniName = data.FIRST_NAME.charAt(0);
      if (data.LAST_NAME != "" && data.LAST_NAME != null) {
        this.iniLname = data.LAST_NAME.charAt(0);
      }
      return this.iniName + this.iniLname;
    }
  }

  sendEmail() {
    const dialogRef = this.matDialog.open(NamsEmailComponent, {
      width: '1500px',
      height: 'auto',
      data: { ID: this.complaint_id, CASE_ID: this.case_Id, CHATBOT_ID: this.chatbot_complaint_id, COMP_ID: this.company_id, toSender: this.senderDetails, mail: this.senderMailArray, name: this.name, call_from: this.call_from },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data.state === 'refresh') {
          if (data.name == 'toCompanyMail') {
            this.viewCompanyMails('', 0);
            this.getEmailsForInternal();
          }
          else if (data.name == 'toAllCompMails') {
            this.viewAllCompanyMails();
            this.getEmailsForInternal();
          }
          else if (data.name == 'toAdvertiser') {
            this.viewMails('', 0);
            this.getEmailsForInternal();
          }
          else if (data.name == 'toAllAdvertisers') {
            this.viewAllAdvMail();
            this.getEmailsForInternal();
          }
          else if (data.name == 'toComplainant') {
            this.viewMails('', 0);
          }
          else if (data.name == 'toInternal' || data.name == 'toNewEmail') {
            this.viewInternalMails('', 0);
            this.getEmailsForInternal();
          }
        }
      }
    );
  }

  sendEmailToComp() {
    const dialogRef = this.matDialog.open(NamsEmailComponent, {
      width: '1500px',
      height: 'auto',
      data: { ID: this.complaint_id, CASE_ID: this.case_Id, CHATBOT_ID: this.chatbot_complaint_id, COMP_ID: this.company_id, complainants: this.complainantDetails, mail: this.sender_mail, name: 'toComplainant', call_from: this.call_from },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data.name == 'toComplainant') {
          this.viewMails('', 0);
        }
      }
    );
  }

  viewAllAdvMail() {
    this.cs.getMails(this.complaint_id, this.chatbot_complaint_id, '', '', this.company_id, this.call_from, 'intra_emails').subscribe((mails: any) => {
      this.mailsSent = mails.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
    this.allAdvMembers = true;
    this.allCompMailMembers = false;
    this.sender_name = "All company members";
    this.name = "toAllAdvertisers";
    this.senderDetails = [];
    this.senderDetails = this.advertiserDetails;
    this.senderMailArray = [];
    for (let i = 0; i < this.advertiserDetails.length; i++) {
      this.senderMailArray.push(this.advertiserDetails[i].EMAIL_ID);
    }
    this.advIndex = 0;
    this.companyIndex = 0;
    this.internalIndex = 0;
  }

  openUpload() {
    let id = 0;
    if (this.data.from == 'inbox-chatbot') {
      id = this.chatbot_complaint_id;
    }
    else {
      id = this.complaint_id;
    }
    const dialogRef = this.matDialog.open(ViewUploadDocumentComponent, {
      width: '80%',
      height: 'auto',
      data: { ID: id },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        this.selectedFiles = data;
      }
    );
  }

  onTabChanged(event) {
    if (this.data.from == 'manage-cases') {
      this.selectedTab = event.index;
      this.getAdvertisers();
    }
    else if (this.data.from == 'inbox-system' || this.data.from == 'inbox-chatbot') {
      this.selectedTab = event.index;
      this.getComplainants();
    }
  }

  getAdvertisers() {
    this.advertiserList = true;
    this.complainantsList = false;
    this.getAllAdvertisers();
    this.getAllCompanyEmails();
  }

  getComplainants() {
    this.complainantsList = true;
    this.advertiserList = false;
    if (this.data.from == 'manage-cases') {
      this.getAllComplainants();
    }
    else if (this.data.from == 'inbox-system') {
      this.getComplainantsInboxSystem();
    }
    else if (this.data.from == 'inbox-chatbot') {
      this.getComplainantsInboxChatbot();
    }
  }

  getAllAdvertisers() {
    this.cs.getAllAdvertisers(this.company_id).subscribe((advertisers: any) => {
      this.advertiserDetails = advertisers.data;
      if (this.advertiserDetails.length != 0) {
        if (this.selectedTab == 0) {
          this.user_id = this.advertiserDetails[0].USER_ID;
          this.sender_name = this.advertiserDetails[0].FIRST_NAME + ' ' + this.advertiserDetails[0].LAST_NAME;
          this.sender_mail = this.advertiserDetails[0].EMAIL_ID;
          this.viewMails('', 0);
        }
        else if (this.selectedTab == 1) {
          this.user_id = this.advertiserDetails[0].USER_ID;
          this.sender_name = this.advertiserDetails[0].FIRST_NAME + ' ' + this.advertiserDetails[0].LAST_NAME;
          if (this.advertiserDetails[0].FIRST_NAME != "" && this.advertiserDetails[0].FIRST_NAME != null) {
            this.iniName = this.advertiserDetails[0].FIRST_NAME.charAt(0);
            if (this.advertiserDetails[0].LAST_NAME != "" && this.advertiserDetails[0].LAST_NAME != null) {
              this.iniLname = this.advertiserDetails[0].LAST_NAME.charAt(0);
            }
            this.profile = this.iniName + this.iniLname;
          }
          this.viewSMS('', 0);
        }
      }
      this.getEmailsForInternal();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  getAllCompanyEmails() {
    this.cs.getAllCompanyEmails(this.company_id).subscribe((companymails: any) => {
      this.companyEmails = companymails.data[0].CONTACT_INFO;
      if (this.companyEmails.length != 0 && this.advertiserDetails.length == 0) {
        if (this.selectedTab == 0) {
          for (let i = 0; i < this.companyEmails.length; i++) {
            if (this.companyEmails[i].EMAIL_ID != '' && this.companyEmails[i].EMAIL_ID != null) {
              this.sender_name = this.companyEmails[i].EMAIL_ID;
              this.sender_mail = this.companyEmails[i].EMAIL_ID;
              this.viewCompanyMails('', 0);
              return;
            }
          }
        }
        // else if (this.selectedTab == 1) {
        //   this.sender_name = this.companyEmails[0].EMAIL_ID;
        //   this.profile = "?";
        //   this.viewSMSCompMail('');
        // }
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  getAllComplainants() {
    this.cs.getAllComplainants(this.case_Id, '').subscribe((complainants: any) => {
      this.complainantDetails = complainants.data;
      if (this.complainantDetails.length != 0) {
        if (this.selectedTab == 0) {
          this.user_id = this.complainantDetails[0].USER_ID;
          this.sender_name = this.complainantDetails[0].FIRST_NAME + ' ' + this.complainantDetails[0].LAST_NAME;
          this.sender_mail = this.complainantDetails[0].EMAIL_ID;
          if (this.sender_mail == '' || this.sender_mail == null) {
            this.disableSend = true;
          }
          this.viewMails('', 0);
        }
        else if (this.selectedTab == 1) {
          this.user_id = this.complainantDetails[0].USER_ID;
          this.sender_name = this.complainantDetails[0].FIRST_NAME + ' ' + this.complainantDetails[0].LAST_NAME;
          if (this.complainantDetails[0].FIRST_NAME != "" && this.complainantDetails[0].FIRST_NAME != null) {
            this.iniName = this.complainantDetails[0].FIRST_NAME.charAt(0);
            if (this.complainantDetails[0].LAST_NAME != "" && this.complainantDetails[0].LAST_NAME != null) {
              this.iniLname = this.complainantDetails[0].LAST_NAME.charAt(0);
            }
            this.profile = this.iniName + this.iniLname;
          }
          this.viewSMS('', 0);
        }
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  getComplainantsInboxSystem() {
    this.cs.getAllComplainants('', this.complaint_id).subscribe((complainants: any) => {
      this.complainantDetails = complainants.data;
      if (this.complainantDetails.length != 0) {
        if (this.selectedTab == 0) {
          this.user_id = this.complainantDetails[0].USER_ID;
          this.sender_name = this.complainantDetails[0].FIRST_NAME + ' ' + this.complainantDetails[0].LAST_NAME;
          this.sender_mail = this.complainantDetails[0].EMAIL_ID;
          if (this.sender_mail == '' || this.sender_mail == null) {
            this.disableSend = true;
          }
          this.viewMails('', 0);
        }
        else if (this.selectedTab == 1) {
          this.user_id = this.complainantDetails[0].USER_ID;
          this.sender_name = this.complainantDetails[0].FIRST_NAME + ' ' + this.complainantDetails[0].LAST_NAME;
          if (this.complainantDetails[0].FIRST_NAME != "" && this.complainantDetails[0].FIRST_NAME != null) {
            this.iniName = this.complainantDetails[0].FIRST_NAME.charAt(0);
            if (this.complainantDetails[0].LAST_NAME != "" && this.complainantDetails[0].LAST_NAME != null) {
              this.iniLname = this.complainantDetails[0].LAST_NAME.charAt(0);
            }
            this.profile = this.iniName + this.iniLname;
          }
          this.viewSMS('', 0);
        }
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  getComplainantsInboxChatbot() {
    this.cs.getChatbotComplainants(this.chatbot_complaint_id).subscribe((complainants: any) => {
      this.complainantDetails = complainants.data;
      if (this.complainantDetails.length != 0) {
        if (this.selectedTab == 0) {
          this.user_id = this.complainantDetails[0].USER_ID;
          this.sender_name = this.complainantDetails[0].FIRST_NAME + ' ' + this.complainantDetails[0].LAST_NAME;
          this.sender_mail = this.complainantDetails[0].EMAIL_ID;
          if (this.sender_mail == '' || this.sender_mail == null) {
            this.disableSend = true;
          }
          this.viewMails('', 0);
        }
        else if (this.selectedTab == 1) {
          this.user_id = this.complainantDetails[0].USER_ID;
          this.sender_name = this.complainantDetails[0].FIRST_NAME + ' ' + this.complainantDetails[0].LAST_NAME;
          if (this.complainantDetails[0].FIRST_NAME != "" && this.complainantDetails[0].FIRST_NAME != null) {
            this.iniName = this.complainantDetails[0].FIRST_NAME.charAt(0);
            if (this.complainantDetails[0].LAST_NAME != "" && this.complainantDetails[0].LAST_NAME != null) {
              this.iniLname = this.complainantDetails[0].LAST_NAME.charAt(0);
            }
            this.profile = this.iniName + this.iniLname;
          }
          this.viewSMS('', 0);
        }
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  viewAllComplainantMails() {
    this.allComplainantMails = true;
    this.cs.getMails(this.complaint_id, this.chatbot_complaint_id, this.user_id, '', '', this.call_from, 'all_emails').subscribe((mails: any) => {
      this.mailsSent = mails.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
    this.name = "toComplainant";
    this.sender_name = "All complainant mails";
  }

  viewMails(user, i) {
    this.advIndex = i + 1;
    this.companyIndex = 0;
    this.internalIndex = 0;
    if (!!user) {
      this.user_id = user.USER_ID;
      this.sender_name = user.FIRST_NAME + ' ' + user.LAST_NAME;
      this.sender_mail = user.EMAIL_ID;
      if (this.sender_mail == '' || this.sender_mail == null) {
        this.disableSend = true;
      }
    }
    this.cs.getMails(this.complaint_id, this.chatbot_complaint_id, this.user_id, '', '', this.call_from, 'one_to_one').subscribe((mails: any) => {
      this.mailsSent = mails.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
    if (this.complainantsList == true) {
      this.name = "toComplainant";
      this.allComplainantMails = false;
    }
    else if (this.advertiserList == true) {
      this.allAdvMembers = false;
      this.allCompMailMembers = false;
      this.name = "toAdvertiser";
      this.senderDetails = [];
      this.senderDetails = this.advertiserDetails;
      this.senderMailArray = [];
      this.senderMailArray.push(this.sender_mail);
    }
  }

  viewCompanyMails(user, i) {
    this.companyIndex = i + 1;
    this.advIndex = 0;
    this.internalIndex = 0;
    if (!!user) {
      this.sender_name = user.EMAIL_ID;
      this.sender_mail = user.EMAIL_ID;
    }
    this.cs.getMails(this.complaint_id, this.chatbot_complaint_id, '', this.sender_mail, '', this.call_from, '').subscribe((mails: any) => {
      this.mailsSent = mails.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
    this.allCompMailMembers = false;
    this.allAdvMembers = false;
    this.name = "toCompanyMail";
    this.senderDetails = [];
    this.senderDetails = this.companyEmails;
    this.senderMailArray = [];
    this.senderMailArray.push(this.sender_mail);
  }

  viewAllCompanyMails() {
    this.cs.getMails(this.complaint_id, this.chatbot_complaint_id, '', '', this.company_id, this.call_from, 'company_emails').subscribe((mails: any) => {
      this.mailsSent = mails.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
    this.allCompMailMembers = true;
    this.allAdvMembers = false;
    this.sender_name = "All company mails";
    this.name = "toAllCompMails";
    this.senderDetails = [];
    for (let i = 0; i < this.companyEmails.length; i++) {
      if (this.companyEmails[i].EMAIL_ID != '' && this.companyEmails[i].EMAIL_ID != null) {
        this.senderDetails.push(this.companyEmails[i]);
      }
    }
    this.senderMailArray = [];
    for (let i = 0; i < this.companyEmails.length; i++) {
      if (this.companyEmails[i].EMAIL_ID != '' && this.companyEmails[i].EMAIL_ID != null) {
        this.senderMailArray.push(this.companyEmails[i].EMAIL_ID);
      }
    }
    this.advIndex = 0;
    this.companyIndex = 0;
    this.internalIndex = 0;
  }

  getEmailsForInternal() {
    this.cs.getAllUsers(this.complaint_id, this.chatbot_complaint_id, 0, this.call_from).subscribe((users: any) => {
      this.internalEmails = users.data;
      this.removeAdvDuplicates();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  removeAdvDuplicates() {
    let advertisersDataObj = {};
    let usersDataArray = [];
    if (this.internalEmails.length > 0) {
      if (!!this.advertiserDetails && this.advertiserDetails.length > 0) {
        for (let ad_i = 0; ad_i < this.advertiserDetails.length; ad_i++) {
          advertisersDataObj[this.advertiserDetails[ad_i]["USER_ID"]] = this.advertiserDetails[ad_i];
        }
      }
      for (let ud_i = 0; ud_i < this.internalEmails.length; ud_i++) {
        if (!advertisersDataObj[this.internalEmails[ud_i]["ID"]]) {
          usersDataArray.push(this.internalEmails[ud_i]);
        }
      }
    }
    this.internalEmails = usersDataArray;
    this.removeCompanyDuplicates();
  }

  removeCompanyDuplicates() {
    let advertisersDataObj = {};
    let usersDataArray = [];
    if (this.internalEmails.length > 0) {
      if (!!this.companyEmails && this.companyEmails.length > 0) {
        for (let ad_i = 0; ad_i < this.companyEmails.length; ad_i++) {
          advertisersDataObj[this.companyEmails[ad_i]["EMAIL_ID"]] = this.companyEmails[ad_i];
        }
      }
      for (let ud_i = 0; ud_i < this.internalEmails.length; ud_i++) {
        if (!advertisersDataObj[this.internalEmails[ud_i]["EMAIL_ID"]]) {
          usersDataArray.push(this.internalEmails[ud_i]);
        }
      }
    }
    this.internalEmails = usersDataArray;
    this.removeLoginUser();
  }

  removeLoginUser() {
    let usersDataArray = [];
    if (this.internalEmails.length > 0) {
      for (let ud_i = 0; ud_i < this.internalEmails.length; ud_i++) {
        if (this.internalEmails[ud_i]["ID"] != this.userData.userId && this.internalEmails[ud_i]["EMAIL_ID"] != this.userData.emailId) {
          usersDataArray.push(this.internalEmails[ud_i]);
        }
      }
    }
    this.internalEmails = usersDataArray;
    if (this.internalEmails.length != 0 && this.advertiserDetails.length == 0 && this.companyEmails.length == 0) {
      if (this.selectedTab == 0) {
        this.user_id = this.internalEmails[0].ID;
        if (this.user_id != 0) {
          this.sender_name = this.internalEmails[0].FIRST_NAME + ' ' + this.internalEmails[0].LAST_NAME;
          if (this.internalEmails[0].FIRST_NAME != "" && this.internalEmails[0].FIRST_NAME != null) {
            this.iniName = this.internalEmails[0].FIRST_NAME.charAt(0);
            if (this.internalEmails[0].LAST_NAME != "" && this.internalEmails[0].LAST_NAME != null) {
              this.iniLname = this.internalEmails[0].LAST_NAME.charAt(0);
            }
            this.profile = this.iniName + this.iniLname;
          }
        }
        else if (this.user_id == 0) {
          this.sender_name = this.internalEmails[0].EMAIL_ID;
        }
        this.sender_mail = this.internalEmails[0].EMAIL_ID;
        this.viewInternalMails('', 0);
      }
      else if (this.selectedTab == 1) {
        this.sender_name = '';
        this.SMSSent = [];
      }
    }
    if (this.internalEmails.length == 0 && this.advertiserDetails.length == 0 && this.companyEmails.length == 0) {
      this.name = "toNewEmail";
      this.sender_name = '';
    }
  }

  viewInternalMails(user, i) {
    this.internalIndex = i + 1;
    this.advIndex = 0;
    this.companyIndex = 0;
    if (!!user) {
      this.user_id = user.ID;
      if (this.user_id != 0) {
        this.sender_name = user.FIRST_NAME + ' ' + user.LAST_NAME;
      } else if (this.user_id == 0) {
        this.sender_name = user.EMAIL_ID;
      }
      this.sender_mail = user.EMAIL_ID;
      if (this.sender_mail == '' || this.sender_mail == null) {
        this.disableSend = true;
      }
    }
    if (this.user_id != 0) {
      this.cs.getMails(this.complaint_id, this.chatbot_complaint_id, this.user_id, '', '', this.call_from, 'one_to_one').subscribe((mails: any) => {
        this.mailsSent = mails.data;
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
    else if (this.user_id == 0) {
      this.cs.getMails(this.complaint_id, this.chatbot_complaint_id, '', this.sender_mail, '', this.call_from, '').subscribe((mails: any) => {
        this.mailsSent = mails.data;
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
    this.allCompMailMembers = false;
    this.allAdvMembers = false;
    this.name = "toInternal";
    this.senderDetails = [];
    this.senderDetails = this.internalEmails;
    this.senderMailArray = [];
    this.senderMailArray.push(this.sender_mail);
  }

  refreshAllMails(key) {
    this.mailsSent = [];
    if (key == 'advertiserScreen') {
      if (this.name == 'toAllAdvertisers') {
        this.viewAllAdvMail();
      }
      else if (this.name == 'toAllCompMails') {
        this.viewAllCompanyMails();
      }
    }
    else if (key == 'complainantScreen') {
      if (this.name == 'toComplainant') {
        this.viewAllComplainantMails();
      }
    }
  }

  refreshMails(key) {
    this.mailsSent = [];
    if (key == 'advertiserScreen') {
      if (this.name == 'toAdvertiser') {
        this.viewMails('', 0);
      }
      else if (this.name == 'toCompanyMail') {
        this.viewCompanyMails('', 0);
      }
      else if (this.name == 'toInternal') {
        this.viewInternalMails('', 0);
      }
    }
    else if (key == 'complainantScreen') {
      if (this.name == 'toComplainant') {
        this.viewMails('', 0);
      }
    }
  }

  refreshChat() {
    this.SMSSent = [];
    this.viewSMS('', 0);
  }

  applyFilter(val) {
    val = val.trim().toLowerCase();
    let fil = [];
    if (this.advertiserList == true) {
      this.searchFilter = [];
      this.searchFilter = this.advertiserDetails;
    } else if (this.complainantsList == true) {
      this.searchFilter = [];
      this.searchFilter = this.complainantDetails;
    }
    this.searchFilter.forEach(x => {
      let firstName = x.FIRST_NAME;
      firstName = firstName.trim().toLowerCase();
      let lastName = x.LAST_NAME;
      lastName = lastName.trim().toLowerCase();
      let a = 0;
      for (let i = 0; i < val.length; i++) {
        if (firstName[i] == val[i] || lastName[i] == val[i]) {
          a = a + 1;
        }
        if (a == val.length) {
          fil.push(x);
        }
      }
    });
    this.searchFilter = fil;
  }

  viewSMS(user, i) {
    this.advIndex = i + 1;
    this.companyIndex = 0;
    this.internalIndex = 0;
    if (!!user) {
      this.user_id = user.USER_ID;
      this.sender_name = user.FIRST_NAME + ' ' + user.LAST_NAME;
      if (user.FIRST_NAME != "" && user.FIRST_NAME != null) {
        this.iniName = user.FIRST_NAME.charAt(0);
        if (user.LAST_NAME != "" && user.LAST_NAME != null) {
          this.iniLname = user.LAST_NAME.charAt(0);
        }
        this.profile = this.iniName + this.iniLname;
      }
    }
    this.cs.getSMS(this.complaint_id, this.chatbot_complaint_id, this.user_id, this.call_from).subscribe((sms: any) => {
      this.SMSSent = sms.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  // viewSMSCompMail(user) {
  //   if (!!user) {
  //     this.sender_name = user.EMAIL_ID;
  //     this.profile = "?";
  //   }
  //   this.cs.getSMS(this.complaint_id, this.sender_name).subscribe((sms: any) => {
  //     this.SMSSent = sms.data;
  //   })
  // }

  sendSMS(message) {
    this.cs.sendSMS(message, this.complaint_id, this.chatbot_complaint_id, this.user_id, this.selectedFiles, this.call_from).subscribe(res => {
      this.viewSMS('', 0);
      this.text_message.reset();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  openNewTab(source) {
    window.open(source, 'window 1', '');
  }

  viewAcademyDetails() {
    window.open('https://www.ascionline.in/academy/courses-2/', "_blank");
  }

}