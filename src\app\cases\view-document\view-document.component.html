<div class="document-contents">
  <div class="document-body">
    <div fxLayout="row">
      <mat-toolbar class="toolbar">
        <div fxFlex="35%" fxLayoutAlign="start">
          <h2 mat-dialog-title fxLayoutAlign="start" style="font-size: 16px;">
            Documents of claims
          </h2>
        </div>
        <div fxFlex="60%"></div>
        <div fxFlex="2%" fxLayoutAlign="end">
          <mat-dialog-actions mat-dialog-close style="position: relative; top: -10px; left: 15px;">
            <button mat-icon-button class="search-btn" mat-dialog-close>
              <mat-icon style="margin-bottom: 4px;">close</mat-icon>
            </button>
          </mat-dialog-actions>
        </div>
      </mat-toolbar>
    </div>

    <div class="document-container" fxLayout="column" fxLayoutGap="10px">
      <div class="no-docs" *ngIf="claimsDocuments.length == 0">
        No documents uploaded...
      </div>
      <mat-accordion multi *ngIf="claimsDocuments.length != 0">
        <mat-expansion-panel style="margin: 0% 2% 0% 2%;" class="outer-row-container"
          *ngFor="let doc of claimsDocuments; let in = index;">
          <mat-expansion-panel-header class="extension-header">
            <mat-panel-title>
              <div style="padding: 1% 0% 2% 0%;">
                <div>
                  <mat-label style="color: #000000; font-size: 13px; font-weight: 600;"> {{doc?.DOC_NAME}}
                  </mat-label>
                </div>
              </div>
            </mat-panel-title>
          </mat-expansion-panel-header>
          <div fxLayout="column" fxLayoutGap="20px">
            <div>
              <div style="border: 1px solid #CFD7DF; width: 400px; height: 28px; border-radius: 4px;">
                <div matPrefix fxLayout="row">
                  <div class="file-img">
                    <img src="../../assets/images/File.png" class="fileimge">
                  </div>
                  <div class="pdf-text" fxFlex="70%" matTooltip="{{doc.ATTACHMENT_NAME}}" style="cursor: pointer;"> &nbsp;
                    {{doc?.ATTACHMENT_NAME |slice:0:40}}{{doc.ATTACHMENT_NAME.length>41? '..':''}}
                  </div>
                  <div>
                    <img src="../../assets/images/eye.svg" (click)="preview(doc.ATTACHMENT_NAME, doc.ATTACHMENT_SOURCE)"
                      style="width:18px;margin-left: 21px;cursor: pointer;">
                    <button class="dwnld-btn" (click)="download(doc.ATTACHMENT_NAME, doc.ATTACHMENT_SOURCE)"><u><img
                          src="../../assets/images/download.svg" style="width:18px;margin-left: 13px;"></u></button>
                  </div>
                </div>
              </div>
            </div>
            <div class="document-info-container" fxLayout="row" fxLayoutGap="80px">
              <div class="attribute-container width50">
                <p>
                  <span class="grey-text">Type of document</span><br> {{doc?.DOC_TYPE}}
                </p>
              </div>
              <div class="attribute-container width50">
                <p>
                  <span class="grey-text">Annexure no.</span><br> {{doc?.ANNEXURE_NO}}
                </p>
              </div>
            </div>
            <div class="date-container">
                <span class="grey-text">Date</span><br>
                {{doc?.UPDATED_DATE | date:'dd/MM/yyyy hh:mm a' }}
            </div>
          </div>
        </mat-expansion-panel>
      </mat-accordion>
    </div>
  </div>
</div>
