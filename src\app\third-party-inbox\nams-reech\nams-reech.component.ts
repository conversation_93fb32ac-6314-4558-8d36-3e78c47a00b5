import { Component, ElementRef, HostListener, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { BehaviorSubject, Observable } from 'rxjs';
import { debounceTime, distinctUntilChanged, finalize } from 'rxjs/operators';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';
import { NotificationService } from 'src/app/services/notification.service';
import { ThirdPartyService } from 'src/app/services/third-party.service';
import { colorObj } from 'src/app/shared/color-object';
import { NamsDetailsComponent } from '../nams-details/nams-details.component';
import { NamsReechFileUploadComponent } from '../nams-reech-file-upload/nams-reech-file-upload.component';
import { ReechDeleteReasonComponent } from '../reech-delete-reason/reech-delete-reason.component';

@Component({
  selector: 'app-nams-reech',
  templateUrl: './nams-reech.component.html',
  styleUrls: ['./nams-reech.component.css']
})
export class NamsReechComponent implements OnInit {

  pagename: String;
  userInfo: any;
  userName: any;
  loading: boolean = true;
  pageNumber: number = 0;
  fbPageNumber: number = 0;
  resArray: Array<any> = [];
  fbResArray: Array<any> = [];
  dataSource: any = [];
  dataSource1: any = [];
  private request$: Observable<any>;
  INFLUENCER = new FormControl(null);
  UPLOADED_BY = new FormControl(null);
  // NETWORK = new FormControl(null);
  BRAND = new FormControl(null);
  select_all = false;
  idArray = [];
  instaNum: number = 0;
  fbNum: number = 0;
  isselected: boolean;
  confirmationMsg: any = {};
  dat = {};
  reechInstaComplaints = [];
  reechFbComplaints = [];
  networks = [];
  influencers = [];
  status;
  zeroComplaints: boolean = false;
  disableAction: boolean = true;
  disableDelete: boolean = true;
  influencer_asc: boolean = false;
  influencer_desc: boolean = false;
  brand_asc: boolean = false;
  brand_desc: boolean = false;
  sortingKey: any = '';
  sortingOrder: any = '';
  pageSize: number = 10;
  instagram: boolean;
  facebook: boolean;
  selectedNetwork = 'Instagram';

  private _namsData = new BehaviorSubject<any[]>([]);
  private namsDataStore: { $namsData: any[] } = { $namsData: [] };
  readonly $namsData = this._namsData.asObservable();
  private _namsFbData = new BehaviorSubject<any[]>([]);
  private namsFbDataStore: { $namsFbData: any[] } = { $namsFbData: [] };
  readonly $namsFbData = this._namsFbData.asObservable();
  totalCount: any;
  startIndex: number;
  lastIndex: number;
  rangeLabel: string;
  limit: number = 0;
  filterData: { name: string; value: any; }[];
  instaFilterArray: any[];
  fbFilterArray: any[];
  instaSortedArray: Array<any> = [];
  fbSortedArray: Array<any> = [];
  filterKeys: any = [];
  influencerName: any;
  uploadedBy: any;
  networkName: any;
  brandName: any;
  network: string;
  upload_key;
  selectedTab;
  instaFilter: boolean = false;
  fbFilter: boolean = false;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private namsService: ThirdPartyService,
    private notify: NotificationService,
    public dialog: MatDialog, private el: ElementRef<HTMLElement>) { }

  @ViewChildren(MatPaginator) paginator = new QueryList<MatPaginator>();
  displayedColumns: string[] = ['check', 'network', 'publication_image_url', 'publication_url', 'publication_content', 'INFLUENCER_NAME', 'publication_date', 'uploaded_by', 'engagements', 'action'];
  displayedColumns1: string[] = ['check', 'network', 'publication_image_url', 'publication_url', 'publication_content', 'BRAND_NAME', 'publication_date', 'uploaded_by', 'engagements', 'action'];

  ngOnInit(): void {
    this.pagename = "NAMS - REECH";
    this.selectedTab = 0;
    this.network = "instagram";
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.userName = this.userInfo.firstName;
  }

  ngAfterViewInit() {
    if (this.selectedTab == 0) {
      this.getReechInstagramTable();
    } else if (this.selectedTab == 1) {
      this.getReechFacebookTable();
    }
  }

  onTabChanged(event) {
    this.idArray = [];
    this.selectedTab = event.index;
    if (this.selectedTab == 0) {
      this.network = "instagram";
      this.selectedNetwork = "Instagram";
      this.rangeLabel = '';
      this.totalCount = '';
      this.INFLUENCER.setValue('');
      this.UPLOADED_BY.setValue('');
      this.resArray = [];
      this.instaSortedArray = [];
      this.instagram = true;
      this.facebook = false;
      this.loading = true;
      this.pageNumber = 0;
      this.dataSource.data = '';
      this.dataSource = new MatTableDataSource(this.dataSource.data);
      this.dataSource.paginator = this.paginator.toArray()[0];
      this.sortingKey = '';
      this.sortingOrder = '';
      this.influencer_asc = false;
      this.influencer_desc = false;
      this.getReechInstagramTable();
    }
    else if (this.selectedTab == 1) {
      this.network = "facebook";
      this.selectedNetwork = "Facebook";
      this.rangeLabel = '';
      this.totalCount = '';
      this.BRAND.setValue('');
      this.UPLOADED_BY.setValue('');
      this.fbResArray = [];
      this.fbSortedArray = [];
      this.instagram = false;
      this.facebook = true;
      this.loading = true;
      this.fbPageNumber = 0;
      this.dataSource1.data = '';
      this.dataSource1 = new MatTableDataSource(this.dataSource1.data);
      this.dataSource1.paginator = this.paginator.toArray()[1];
      this.sortingKey = '';
      this.sortingOrder = '';
      this.brand_asc = false;
      this.brand_desc = false;
      this.getReechFacebookTable();
    }
  }

  getBrandName(value) {
    if (value && value.length > 0) {
      this.getComplaintsByBrandName(value);
    } else {
      this.fbPageNumber = 0;
      this.dataSource1.data = [];
      this.fbFilterArray = [];
      this.BRAND.reset();
      this.startIndex = null;
      this.lastIndex = null;
      this.totalCount = null;
      this.pageSize = 10;
      if (this.UPLOADED_BY.value == '') {
        this.zeroComplaints = false;
        this.getReechFacebookTable();
      } else {
        this.zeroComplaints = false;
        this.filter_complaints(1);
      }
    }
  }

  getInfluencer(value) {
    if (value && value.length > 0) {
      this.getComplaintsByInfluencerName(value);
    } else {
      this.pageNumber = 0;
      this.dataSource.data = [];
      this.instaFilterArray = [];
      this.INFLUENCER.reset();
      this.startIndex = null;
      this.lastIndex = null;
      this.totalCount = null;
      this.pageSize = 10;
      if (this.UPLOADED_BY.value == '') {
        this.zeroComplaints = false;
        this.getReechInstagramTable();
      } else {
        this.zeroComplaints = false;
        this.filter_complaints(1);
      }
    }
  }

  getUploadedBy(value) {
    if (value && value.length > 0) {
      this.getComplaintsByUploadedBy(value);
    }
    else {
      this.UPLOADED_BY.reset();
      this.startIndex = null;
      this.lastIndex = null;
      this.totalCount = null;
      this.pageSize = 10;
      if (this.selectedTab == 0) {
        this.pageNumber = 0;
        this.dataSource.data = [];
        this.instaFilterArray = [];
        if (this.INFLUENCER.value == '') {
          this.zeroComplaints = false;
          this.getReechInstagramTable();
        } else {
          this.zeroComplaints = false;
          this.filter_complaints(1);
        }
      }
      else if (this.selectedTab == 1) {
        this.fbPageNumber = 0;
        this.dataSource1.data = [];
        this.fbFilterArray = [];
        if (this.BRAND.value == '') {
          this.zeroComplaints = false;
          this.getReechFacebookTable();
        } else {
          this.zeroComplaints = false;
          this.filter_complaints(1);
        }
      }
    }
  }

  // getNetwork(value) {
  //   if (this.selectedTab == 0) {
  //     this.getComplaintsByNetwork(value);
  //     if (value == "instagram") {
  //       this.selectedNetwork = 'Instagram';
  //     }
  //     else if (value == "youtube") {
  //       this.selectedNetwork = 'Youtube';
  //     }
  //   }
  //   if (this.selectedTab == 1) {
  //     this.getComplaintsByNetwork(value);
  //     if (value == "facebook") {
  //       this.selectedNetwork = 'Facebook';
  //     }
  //   }
  // }

  // getComplaintsByNetwork(val) {
  //   this.NETWORK.setValue(val);
  //   this.filter_complaints(1);
  // }

  getComplaintsByUploadedBy(val) {
    this.UPLOADED_BY.setValue(val)
    this.filter_complaints(1);
  }

  getComplaintsByInfluencerName(val) {
    this.INFLUENCER.setValue(val)
    this.filter_complaints(1);
  }

  getComplaintsByBrandName(val) {
    this.BRAND.setValue(val)
    this.filter_complaints(1);
  }

  clearFilter() {
    if (this.selectedTab == 0) {
      this.dataSource.data = [];
      this.instaFilterArray = [];
      this.instaSortedArray = [];
      this.resArray = [];
      this.totalCount = null;
      this.zeroComplaints = false;
      this.INFLUENCER.reset();
      this.UPLOADED_BY.reset();
      this.filterData = [];
      this.filterKeys = [];
      this.pageNumber = 0;
      this.influencer_asc = false;
      this.influencer_desc = false;
      this.instaFilter = false;
      this.sortingKey = '';
      this.sortingOrder = '';
      this.getReechInstagramTable();
    }
    else if (this.selectedTab == 1) {
      this.dataSource1.data = [];
      this.fbFilterArray = [];
      this.fbSortedArray = [];
      this.fbResArray = [];
      this.totalCount = null;
      this.zeroComplaints = false;
      this.BRAND.reset();
      this.UPLOADED_BY.reset();
      this.filterData = [];
      this.filterKeys = [];
      this.fbPageNumber = 0;
      this.brand_asc = false;
      this.brand_desc = false;
      this.fbFilter = false;
      this.sortingKey = '';
      this.sortingOrder = '';
      this.getReechFacebookTable();
    }
  }

  getReechInstagramTable() {
    this.getInstagramList()
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((namsData: any) => {
        this.loading = false;
        if (namsData.data.length > 0) {
          this.totalCount = namsData.data[0].TOTAL_COUNT;
          if (this.pageNumber == 1) {
            if (this.pageSize > this.totalCount) {
              this.rangeLabel = 1 + ' - ' + this.totalCount;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.resArray = this.resArray.concat(namsData.data[0].DATA);
          if (this.resArray.length == 0) {
            this.zeroComplaints = true;
          } else {
            this.zeroComplaints = false;
          }
          this.namsDataStore.$namsData = this.resArray;
          this.dataSource.data = this.namsDataStore.$namsData;
          this.dataSource = new MatTableDataSource<any>(this.namsDataStore.$namsData);
          this.dataSource.paginator = this.paginator.toArray()[0];
          this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
          this.dataSource.data.forEach(element => {
            if (element['REGISTERED'] == 1) {
              element['status'] = 'Confirmed';
            } else if (element['REGISTERED'] == null) {
              element['status'] = 'Unconfirmed';
            }
          })
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      });
  }

  getReechFacebookTable() {
    this.getFacebookList()
      .pipe(finalize(() => this.onFinalize()))
      .subscribe((res: any) => {
        this.loading = false;
        if (res.data.length > 0) {
          this.totalCount = res.data[0].TOTAL_COUNT;
          if (this.fbPageNumber == 1) {
            if (this.pageSize > this.totalCount) {
              this.rangeLabel = 1 + ' - ' + this.totalCount;
            } else {
              this.rangeLabel = 1 + ' - ' + this.pageSize;
            }
          }
          this.fbResArray = this.fbResArray.concat(res.data[0].DATA);
          if (this.fbResArray.length == 0) {
            this.zeroComplaints = true;
          } else {
            this.zeroComplaints = false;
          }
          this.namsFbDataStore.$namsFbData = this.fbResArray;
          this.dataSource1.data = this.namsFbDataStore.$namsFbData;
          this.dataSource1 = new MatTableDataSource<any>(this.namsFbDataStore.$namsFbData);
          this.dataSource1.paginator = this.paginator.toArray()[1];
          this._namsFbData.next(Object.assign({}, this.namsFbDataStore).$namsFbData);
          this.dataSource1.data.forEach(element => {
            if (element['REGISTERED'] == 1) {
              element['status'] = 'Confirmed';
            } else if (element['REGISTERED'] == null) {
              element['status'] = 'Unconfirmed';
            }
          })
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      })
  }

  changePage(event) {
    this.startIndex = (event.pageIndex * this.pageSize) + 1;
    this.lastIndex = this.startIndex + (this.pageSize - 1);
    if (this.lastIndex > this.totalCount) {
      this.lastIndex = this.totalCount;
    }
    this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
    if (this.selectedTab == 0) {
      if (this.resArray.length != 0) {
        this.getReechInstagramTable();
      }
      else if (this.instaFilter == true) {
        this.pageNumber++;
        if (this.INFLUENCER.value) {
          this.influencerName = this.INFLUENCER.value.trim();
        } else {
          this.influencerName = '';
        }
        if (this.UPLOADED_BY.value) {
          this.uploadedBy = this.UPLOADED_BY.value.trim();
        } else {
          this.uploadedBy = '';
        }
        this.networkName = "instagram";
        this.filterData = [
          { "name": "INFLUENCER_NAME", "value": this.influencerName },
          { "name": "NETWORK", "value": this.networkName },
          { "name": "UPLOADED_BY", "value": this.uploadedBy },
          { "name": "PAGE", "value": this.pageNumber },
          { "name": "limit", "value": 20 },
        ]
        let fil = [];
        for (let i = 0; i < this.filterData.length; i++) {
          if (this.filterData[i].value != null) {
            fil.push(this.filterData[i])
          }
        }
        this.namsService.filterComplaintsReech(fil, this.sortingKey, this.sortingOrder).subscribe((filteredData: any) => {
          this.resArray = [];
          if (filteredData.data.length > 0) {
            this.zeroComplaints = false;
            this.totalCount = filteredData.data[0].TOTAL_COUNT;
            if (this.instaFilterArray.length != 0 && this.instaSortedArray.length == 0) {
              this.instaFilterArray = this.instaFilterArray.concat(filteredData.data[0].DATA);
              this.namsDataStore.$namsData = this.instaFilterArray;
            }
            else if (this.instaFilterArray.length == 0 && this.instaSortedArray.length != 0) {
              this.instaSortedArray = this.instaSortedArray.concat(filteredData.data[0].DATA);
              this.namsDataStore.$namsData = this.instaSortedArray;
            }
            this.dataSource.data = this.namsDataStore.$namsData;
            this.dataSource = new MatTableDataSource<any>(this.namsDataStore.$namsData);
            this.dataSource.paginator = this.paginator.toArray()[0];
            this.dataSource.data.forEach(element => {
              if (element['REGISTERED'] == 1) {
                element['status'] = 'Confirmed';
              } else if (element['REGISTERED'] == null) {
                element['status'] = 'Unconfirmed';
              }
            })
          }
        }, err => {
          this.loading = false;
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        });
      }
      else {
        this.pageNumber++;
        this.filterData = [
          { "name": "PAGE", "value": this.pageNumber }
        ]
        this.namsService.filterComplaintsReech(this.filterData, this.sortingKey, this.sortingOrder).subscribe((sortedData: any) => {
          this.resArray = [];
          this.instaFilterArray = [];
          if (sortedData.data.length > 0) {
            this.zeroComplaints = false;
            this.totalCount = sortedData.data[0].TOTAL_COUNT;
            this.instaSortedArray = this.instaSortedArray.concat(sortedData.data[0].DATA);
            this.namsDataStore.$namsData = this.instaSortedArray;
            this.dataSource.data = this.namsDataStore.$namsData;
            this.dataSource = new MatTableDataSource<any>(this.namsDataStore.$namsData);
            this.dataSource.paginator = this.paginator.toArray()[0];
            this.dataSource.data.forEach(element => {
              if (element['REGISTERED'] == 1) {
                element['status'] = 'Confirmed';
              } else if (element['REGISTERED'] == null) {
                element['status'] = 'Unconfirmed';
              }
            })
          }
        }, err => {
          this.loading = false;
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        });
      }
    }
    else if (this.selectedTab == 1) {
      if (this.fbResArray.length != 0) {
        this.getReechFacebookTable();
      }
      else if (this.fbFilter == true) {
        this.fbPageNumber++;
        if (this.BRAND.value) {
          this.brandName = this.BRAND.value.trim();
        } else {
          this.brandName = '';
        }
        if (this.UPLOADED_BY.value) {
          this.uploadedBy = this.UPLOADED_BY.value.trim();
        } else {
          this.uploadedBy = '';
        }
        this.networkName = "facebook";
        this.filterData = [
          { "name": "BRAND_NAME", "value": this.brandName },
          { "name": "NETWORK", "value": this.networkName },
          { "name": "UPLOADED_BY", "value": this.uploadedBy },
          { "name": "PAGE", "value": this.fbPageNumber },
          { "name": "limit", "value": 20 },
        ]
        let fil = [];
        for (let i = 0; i < this.filterData.length; i++) {
          if (this.filterData[i].value != null) {
            fil.push(this.filterData[i])
          }
        }
        this.namsService.filterComplaintsReech(fil, this.sortingKey, this.sortingOrder).subscribe((filteredData: any) => {
          this.fbResArray = [];
          if (filteredData.data.length > 0) {
            this.zeroComplaints = false;
            this.totalCount = filteredData.data[0].TOTAL_COUNT;
            if (this.fbFilterArray.length != 0 && this.fbSortedArray.length == 0) {
              this.fbFilterArray = this.fbFilterArray.concat(filteredData.data[0].DATA);
              this.namsFbDataStore.$namsFbData = this.fbFilterArray;
            }
            else if (this.fbFilterArray.length == 0 && this.fbSortedArray.length != 0) {
              this.fbSortedArray = this.fbSortedArray.concat(filteredData.data[0].DATA);
              this.namsFbDataStore.$namsFbData = this.fbSortedArray;
            }
            this.dataSource1.data = this.namsFbDataStore.$namsFbData;
            this.dataSource1 = new MatTableDataSource<any>(this.namsFbDataStore.$namsFbData);
            this.dataSource1.paginator = this.paginator.toArray()[1];
            this.dataSource1.data.forEach(element => {
              if (element['REGISTERED'] == 1) {
                element['status'] = 'Confirmed';
              } else if (element['REGISTERED'] == null) {
                element['status'] = 'Unconfirmed';
              }
            })
          }
        }, err => {
          this.loading = false;
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        });
      }
      else {
        this.fbPageNumber++;
        this.filterData = [
          { "name": "PAGE", "value": this.fbPageNumber }
        ]
        this.namsService.filterComplaintsReech(this.filterData, this.sortingKey, this.sortingOrder).subscribe((sortedData: any) => {
          this.fbResArray = [];
          this.fbFilterArray = [];
          if (sortedData.data.length > 0) {
            this.zeroComplaints = false;
            this.totalCount = sortedData.data[0].TOTAL_COUNT;
            this.fbSortedArray = this.fbSortedArray.concat(sortedData.data[0].DATA);
            this.namsFbDataStore.$namsFbData = this.fbSortedArray;
            this.dataSource1.data = this.namsFbDataStore.$namsFbData;
            this.dataSource1 = new MatTableDataSource<any>(this.namsFbDataStore.$namsFbData);
            this.dataSource1.paginator = this.paginator.toArray()[1];
            this.dataSource1.data.forEach(element => {
              if (element['REGISTERED'] == 1) {
                element['status'] = 'Confirmed';
              } else if (element['REGISTERED'] == null) {
                element['status'] = 'Unconfirmed';
              }
            })
          }
        }, err => {
          this.loading = false;
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        });
      }
    }
  }

  filter_complaints(page) {
    if (this.selectedTab == 0) {
      this.startIndex = null;
      this.lastIndex = null;
      this.pageSize = 10;
      this.instaFilter = true;
      this.pageNumber = page;
      this.zeroComplaints = false;
      if (this.INFLUENCER.value) {
        this.influencerName = this.INFLUENCER.value.trim();
      } else {
        this.influencerName = '';
      }
      if (this.UPLOADED_BY.value) {
        this.uploadedBy = this.UPLOADED_BY.value.trim();
      } else {
        this.uploadedBy = '';
      }
      this.networkName = "instagram";
      this.filterData = [
        { "name": "INFLUENCER_NAME", "value": this.influencerName },
        { "name": "NETWORK", "value": this.networkName },
        { "name": "UPLOADED_BY", "value": this.uploadedBy },
        { "name": "PAGE", "value": this.pageNumber },
        { "name": "limit", "value": 20 },
      ]
      let fil = [];
      for (let i = 0; i < this.filterData.length; i++) {
        if (this.filterData[i].value != null) {
          fil.push(this.filterData[i])
        }
      }
      this.dataSource.data = [];
      this.namsService.filterComplaintsReech(fil, this.sortingKey, this.sortingOrder).subscribe((filteredData: any) => {
        this.resArray = [];
        this.instaFilterArray = [];
        this.instaSortedArray = [];
        if (filteredData.data.length > 0) {
          this.zeroComplaints = false;
          if (filteredData.data[0].TOTAL_COUNT < this.pageSize) {
            this.rangeLabel = 1 + ' - ' + filteredData.data[0].TOTAL_COUNT;
          } else {
            this.rangeLabel = 1 + ' - ' + this.pageSize;
          }
          this.totalCount = filteredData.data[0].TOTAL_COUNT;
          this.instaFilterArray = this.instaFilterArray.concat(filteredData.data[0].DATA);
          this.namsDataStore.$namsData = this.instaFilterArray;
          this.dataSource.data = this.namsDataStore.$namsData;
          this.dataSource = new MatTableDataSource<any>(this.namsDataStore.$namsData);
          this.dataSource.paginator = this.paginator.toArray()[0];
          this.dataSource.data.forEach(element => {
            if (element['REGISTERED'] == 1) {
              element['status'] = 'Confirmed';
            } else if (element['REGISTERED'] == null) {
              element['status'] = 'Unconfirmed';
            }
          })
        } else {
          this.instaFilterArray = [];
          this.resArray = [];
          this.dataSource.data = [];
          this.zeroComplaints = true;
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      });
    }
    else if (this.selectedTab == 1) {
      this.startIndex = null;
      this.lastIndex = null;
      this.pageSize = 10;
      this.fbFilter = true;
      this.fbPageNumber = page;
      this.zeroComplaints = false;
      if (this.BRAND.value) {
        this.brandName = this.BRAND.value.trim();
      } else {
        this.brandName = '';
      }
      if (this.UPLOADED_BY.value) {
        this.uploadedBy = this.UPLOADED_BY.value.trim();
      } else {
        this.uploadedBy = '';
      }
      this.networkName = "facebook";
      this.filterData = [
        { "name": "BRAND_NAME", "value": this.brandName },
        { "name": "NETWORK", "value": this.networkName },
        { "name": "UPLOADED_BY", "value": this.uploadedBy },
        { "name": "PAGE", "value": this.fbPageNumber },
        { "name": "limit", "value": 20 },
      ]
      let fil = [];
      for (let i = 0; i < this.filterData.length; i++) {
        if (this.filterData[i].value != null) {
          fil.push(this.filterData[i])
        }
      }
      this.dataSource1.data = [];
      this.namsService.filterComplaintsReech(fil, this.sortingKey, this.sortingOrder).subscribe((filteredData: any) => {
        this.fbResArray = [];
        this.fbFilterArray = [];
        this.fbSortedArray = [];
        if (filteredData.data.length > 0) {
          this.zeroComplaints = false;
          if (filteredData.data[0].TOTAL_COUNT < this.pageSize) {
            this.rangeLabel = 1 + ' - ' + filteredData.data[0].TOTAL_COUNT;
          } else {
            this.rangeLabel = 1 + ' - ' + this.pageSize;
          }
          this.totalCount = filteredData.data[0].TOTAL_COUNT;
          this.fbFilterArray = this.fbFilterArray.concat(filteredData.data[0].DATA);
          this.namsFbDataStore.$namsFbData = this.fbFilterArray;
          this.dataSource1.data = this.namsFbDataStore.$namsFbData;
          this.dataSource1 = new MatTableDataSource<any>(this.namsFbDataStore.$namsFbData);
          this.dataSource1.paginator = this.paginator.toArray()[1];
          this.dataSource1.data.forEach(element => {
            if (element['REGISTERED'] == 1) {
              element['status'] = 'Confirmed';
            } else if (element['REGISTERED'] == null) {
              element['status'] = 'Unconfirmed';
            }
          })
        } else {
          this.fbFilterArray = [];
          this.fbResArray = [];
          this.dataSource1.data = [];
          this.zeroComplaints = true;
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      });
    }
  }

  sortInstaComplaints(key, order) {
    this.sortingKey = key;
    this.sortingOrder = order;
    this.totalCount = null;
    if (order != '') {
      if (order == 'ASC') {
        this.influencer_asc = true;
        this.influencer_desc = false;
      }
      else if (order == 'DESC') {
        this.influencer_asc = false;
        this.influencer_desc = true;
      }
      this.idArray = [];
      this.pageNumber = 1;
      this.zeroComplaints = false;
      if (this.INFLUENCER.value) {
        this.influencerName = this.INFLUENCER.value.trim();
      } else {
        this.influencerName = '';
      }
      if (this.UPLOADED_BY.value) {
        this.uploadedBy = this.UPLOADED_BY.value.trim();
      } else {
        this.uploadedBy = '';
      }
      this.networkName = "instagram";
      this.filterData = [
        { "name": "INFLUENCER_NAME", "value": this.influencerName },
        { "name": "NETWORK", "value": this.networkName },
        { "name": "UPLOADED_BY", "value": this.uploadedBy },
        { "name": "PAGE", "value": this.pageNumber },
        { "name": "limit", "value": 20 },
      ]
      this.filterKeys = [];
      for (let i = 0; i < this.filterData.length; i++) {
        if (this.filterData[i].value != null) {
          this.filterKeys.push(this.filterData[i])
        }
      }
      this.dataSource.data = [];
      this.namsService.filterComplaintsReech(this.filterKeys, key, order).subscribe((sortedData: any) => {
        this.resArray = [];
        this.instaFilterArray = [];
        this.instaSortedArray = [];
        if (sortedData.data.length > 0) {
          if (sortedData.data[0].TOTAL_COUNT < this.pageSize) {
            this.rangeLabel = 1 + ' - ' + sortedData.data[0].TOTAL_COUNT;
          } else {
            this.rangeLabel = 1 + ' - ' + this.pageSize;
          }
          this.totalCount = sortedData.data[0].TOTAL_COUNT;
          this.zeroComplaints = false;
          this.instaSortedArray = this.instaSortedArray.concat(sortedData.data[0].DATA);
          this.namsDataStore.$namsData = this.instaSortedArray;
          this.dataSource.data = this.namsDataStore.$namsData;
          this.dataSource = new MatTableDataSource<any>(this.namsDataStore.$namsData);
          this.dataSource.paginator = this.paginator.toArray()[0];
          this.dataSource.data.forEach(element => {
            if (element['REGISTERED'] == 1) {
              element['status'] = 'Confirmed';
            } else if (element['REGISTERED'] == null) {
              element['status'] = 'Unconfirmed';
            }
          })
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      });
    }
    else if (order == '') {
      this.influencer_asc = false;
      this.influencer_desc = false;
      if (this.instaFilter == true) {
        this.filter_complaints(1);
      }
      else {
        this.dataSource.data = [];
        this.resArray = [];
        this.instaSortedArray = [];
        this.zeroComplaints = false;
        this.pageNumber = 0;
        this.getReechInstagramTable();
      }
    }
  }

  sortFbComplaints(key, order) {
    this.sortingKey = key;
    this.sortingOrder = order;
    this.totalCount = null;
    if (order != '') {
      if (order == 'ASC') {
        this.brand_asc = true;
        this.brand_desc = false;
      }
      else if (order == 'DESC') {
        this.brand_asc = false;
        this.brand_desc = true;
      }
      this.idArray = [];
      this.fbPageNumber = 0;
      this.zeroComplaints = false;
      if (this.BRAND.value) {
        this.brandName = this.BRAND.value.trim();
      } else {
        this.brandName = '';
      }
      if (this.UPLOADED_BY.value) {
        this.uploadedBy = this.UPLOADED_BY.value.trim();
      } else {
        this.uploadedBy = '';
      }
      this.networkName = "facebook";
      this.filterData = [
        { "name": "BRAND_NAME", "value": this.brandName },
        { "name": "NETWORK", "value": this.networkName },
        { "name": "UPLOADED_BY", "value": this.uploadedBy },
        { "name": "PAGE", "value": this.fbPageNumber },
        { "name": "limit", "value": 20 }
      ]
      this.filterKeys = [];
      for (let i = 0; i < this.filterData.length; i++) {
        if (this.filterData[i].value != null) {
          this.filterKeys.push(this.filterData[i])
        }
      }
      this.dataSource1.data = [];
      this.namsService.filterComplaintsReech(this.filterKeys, key, order).subscribe((sortedData: any) => {
        this.fbResArray = [];
        this.fbFilterArray = [];
        this.fbSortedArray = [];
        if (sortedData.data.length > 0) {
          if (sortedData.data[0].TOTAL_COUNT < this.pageSize) {
            this.rangeLabel = 1 + ' - ' + sortedData.data[0].TOTAL_COUNT;
          } else {
            this.rangeLabel = 1 + ' - ' + this.pageSize;
          }
          this.totalCount = sortedData.data[0].TOTAL_COUNT;
          this.zeroComplaints = false;
          this.fbSortedArray = this.fbSortedArray.concat(sortedData.data[0].DATA);
          this.namsFbDataStore.$namsFbData = this.fbSortedArray;
          this.dataSource1.data = this.namsFbDataStore.$namsFbData;
          this.dataSource1 = new MatTableDataSource<any>(this.namsFbDataStore.$namsFbData);
          this.dataSource1.paginator = this.paginator.toArray()[1];
          this.dataSource1.data.forEach(element => {
            if (element['REGISTERED'] == 1) {
              element['status'] = 'Confirmed';
            } else if (element['REGISTERED'] == null) {
              element['status'] = 'Unconfirmed';
            }
          })
        }
      }, err => {
        this.loading = false;
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      });
    }
    else if (order == '') {
      this.brand_asc = false;
      this.brand_desc = false;
      if (this.fbFilter == true) {
        this.filter_complaints(1);
      }
      else {
        this.dataSource1.data = [];
        this.fbResArray = [];
        this.fbSortedArray = [];
        this.zeroComplaints = false;
        this.fbPageNumber = 0;
        this.getReechFacebookTable();
      }
    }
  }

  getInstagramList() {
    this.pageNumber++;
    this.limit = 20;
    this.request$ = this.namsService.listReechComplaintsList(this.pageNumber, this.limit, this.network);
    return this.request$;
  }

  getFacebookList() {
    this.fbPageNumber++;
    this.limit = 20;
    this.request$ = this.namsService.listReechComplaintsList(this.fbPageNumber, this.limit, this.network);
    return this.request$;
  }

  getComplaintById(compId) {
    if (this.selectedTab == 0) {
      this.namsService.getReechDetails(compId).subscribe(res => {
        let userObj = res.data;
        this.namsDataStore.$namsData.forEach((t: any, i) => {
          if (t.ID === userObj.ID) {
            this.namsDataStore.$namsData[i] = userObj;
          }
        });
        this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
        this.updateInstaTableDate(userObj.ID);
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      })
    }
    else if (this.selectedTab == 1) {
      this.namsService.getReechDetails(compId).subscribe(res => {
        let userObj = res.data;
        this.namsFbDataStore.$namsFbData.forEach((t: any, i) => {
          if (t.ID === userObj.ID) {
            this.namsFbDataStore.$namsFbData[i] = userObj;
          }
        });
        this._namsFbData.next(Object.assign({}, this.namsFbDataStore).$namsFbData);
        this.updateFbTableDate(userObj.ID);
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      })
    }
  }

  updateInstaTableDate(id) {
    this.$namsData.subscribe((res) => {
      if (this.instaFilter == true) {
        this.instaFilterArray = res;
      } else {
        this.resArray = res;
      }
      this.dataSource.data = res;
      this.dataSource = new MatTableDataSource<any>(res);
      this.dataSource.paginator = this.paginator.toArray()[0];
      this.dataSource.data.forEach(element => {
        if (id == element.ID) {
          if (element['REGISTERED'] == 1) {
            element['status'] = 'Confirmed';
          } else if (element['REGISTERED'] == null) {
            element['status'] = 'Unconfirmed';
          }
        }
      });
    });
  }

  updateFbTableDate(id) {
    this.$namsFbData.subscribe((res) => {
      if (this.fbFilter == true) {
        this.fbFilterArray = res;
      } else {
        this.fbResArray = res;
      }
      this.dataSource1.data = res;
      this.dataSource1 = new MatTableDataSource<any>(res);
      this.dataSource1.paginator = this.paginator.toArray()[1];
      this.dataSource1.data.forEach(element => {
        if (id == element.ID) {
          if (element['REGISTERED'] == 1) {
            element['status'] = 'Confirmed';
          } else if (element['REGISTERED'] == null) {
            element['status'] = 'Unconfirmed';
          }
        }
      });
    });
  }

  private onFinalize(): void {
    this.request$ = null;
  }

  onSelectAll(e: any): void {
    if (this.selectedTab == 0) {
      for (let i = 0; i < this.dataSource.data.length; i++) {
        const item = this.dataSource.data[i];
        item.isselected = e;
        if (item.isselected == true) {
          this.idArray.push(item.ID.toString());
        }
        else if (item.isselected == false) {
          for (let i = 0; i < this.idArray.length; i++) {
            if (this.idArray[i] == item.ID) {
              this.idArray[i] = null;
            }
          }
        }
      }
    }
    else if (this.selectedTab == 1) {
      for (let i = 0; i < this.dataSource1.data.length; i++) {
        const item = this.dataSource1.data[i];
        item.isselected = e;
        if (item.isselected == true) {
          this.idArray.push(item.ID.toString());
        }
        else if (item.isselected == false) {
          for (let i = 0; i < this.idArray.length; i++) {
            if (this.idArray[i] == item.ID) {
              this.idArray[i] = null;
            }
          }
        }
      }
    }
  }

  selectInstaComplaint(value, id, status) {
    let ID = id.toString();
    if (value == true) {
      this.instaNum++;
      this.idArray.push(ID);
      this.dat = { "id": ID, "status": status }
      this.reechInstaComplaints.push(this.dat);
      for (let i = 0; i < this.reechInstaComplaints.length; i++) {
        this.status = this.reechInstaComplaints[0].status;
        if (this.status != this.reechInstaComplaints[i].status || this.status == 'Confirmed') {
          this.disableAction = true;
          this.disableDelete = false;
          if (this.status != this.reechInstaComplaints[i].status) {
            this.notify.showNotification(
              "Please choose the complaints belonging to same category",
              "top",
              "warning",
              0
            )
            this.disableDelete = true;
          }
        } else {
          this.disableAction = false;
          this.disableDelete = true;
        }
      }
      if (this.instaNum == this.dataSource.data.length) {
        this.select_all = true;
      } else {
        this.select_all = false;
      }
    }
    else if (value == false) {
      this.instaNum--;
      for (let i = 0; i < this.idArray.length; i++) {
        if (this.idArray[i] == ID) {
          this.idArray[i] = null;
        }
      }
      for (let i = 0; i < this.reechInstaComplaints.length; i++) {
        if (this.reechInstaComplaints[i].id == ID) {
          this.reechInstaComplaints.splice(i, 1);
        }
      }
      for (let i = 0; i < this.reechInstaComplaints.length; i++) {
        this.status = this.reechInstaComplaints[0].status;
        if (this.status != this.reechInstaComplaints[i].status || this.status == 'Confirmed') {
          this.disableAction = true;
          this.disableDelete = false;
          if (this.status != this.reechInstaComplaints[i].status) {
            this.disableDelete = true;
          }
        } else {
          this.disableAction = false;
          this.disableDelete = true;
        }
      }
      if (this.instaNum == this.dataSource.data.length) {
        this.select_all = true;
      } else {
        this.select_all = false;
      }
    }
    if (this.reechInstaComplaints.length == 0) {
      this.disableDelete = true;
      this.disableAction = true;
    }
  }

  selectFbComplaint(value, id, status) {
    let ID = id.toString();
    if (value == true) {
      this.fbNum++;
      this.idArray.push(ID);
      this.dat = { "id": ID, "status": status }
      this.reechFbComplaints.push(this.dat);
      for (let i = 0; i < this.reechFbComplaints.length; i++) {
        this.status = this.reechFbComplaints[0].status;
        if (this.status != this.reechFbComplaints[i].status || this.status == 'Confirmed') {
          this.disableAction = true;
          this.disableDelete = false;
          if (this.status != this.reechFbComplaints[i].status) {
            this.notify.showNotification(
              "Please choose the complaints belonging to same category",
              "top",
              "warning",
              0
            )
            this.disableDelete = true;
          }
        } else {
          this.disableAction = false;
          this.disableDelete = true;
        }
      }
      if (this.fbNum == this.dataSource1.data.length) {
        this.select_all = true;
      } else {
        this.select_all = false;
      }
    }
    else if (value == false) {
      this.fbNum--;
      for (let i = 0; i < this.idArray.length; i++) {
        if (this.idArray[i] == ID) {
          this.idArray[i] = null;
        }
      }
      for (let i = 0; i < this.reechFbComplaints.length; i++) {
        if (this.reechFbComplaints[i].id == ID) {
          this.reechFbComplaints.splice(i, 1);
        }
      }
      for (let i = 0; i < this.reechFbComplaints.length; i++) {
        this.status = this.reechFbComplaints[0].status;
        if (this.status != this.reechFbComplaints[i].status || this.status == 'Confirmed') {
          this.disableAction = true;
          this.disableDelete = false;
          if (this.status != this.reechFbComplaints[i].status) {
            this.disableDelete = true;
          }
        } else {
          this.disableAction = false;
          this.disableDelete = true;
        }
      }
      if (this.fbNum == this.dataSource1.data.length) {
        this.select_all = true;
      } else {
        this.select_all = false;
      }
    }
    if (this.reechFbComplaints.length == 0) {
      this.disableDelete = true;
      this.disableAction = true;
    }
  }

  instaComplaintDetails(ID, status) {
    const dialogRef = this.dialog.open(NamsDetailsComponent, {
      width: '1500px',
      height: '550px',
      data: { ID, "status": status, "nams": 'reech' },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'delete' || data === 'add') {
          this.namsDataStore.$namsData.forEach((t: any, i) => {
            if (t.ID === ID) {
              this.namsDataStore.$namsData.splice(i, 1);
            }
          });
          if (data === 'delete') {
            this.totalCount = this.totalCount - 1;
            if (this.totalCount !== 0) {
              if (this.pageSize > this.totalCount) {
                this.pageSize = this.totalCount;
                this.rangeLabel = 1 + ' - ' + this.pageSize;
              }
              if (this.lastIndex > this.totalCount) {
                this.lastIndex = this.totalCount;
                this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
              }
            } else {
              this.zeroComplaints = true;
            }
          }
          this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
          this.$namsData.subscribe((res) => {
            if (this.instaFilter == true) {
              this.instaFilterArray = res;
            } else {
              this.resArray = res;
            }
            this.dataSource.data = res;
            this.dataSource = new MatTableDataSource(res);
            this.dataSource.paginator = this.paginator.toArray()[0];
          })
        }
        else if (data === 'reech-refresh') {
          this.namsService.getReechDetails(ID).subscribe((res: any) => {
            let userObj = res.data;
            this.namsDataStore.$namsData.forEach((t: any, i) => {
              if (t.ID === userObj.ID) {
                this.namsDataStore.$namsData[i] = userObj;
              }
            });
            this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
            this.$namsData.subscribe((res) => {
              if (this.instaFilter == true) {
                this.instaFilterArray = res;
              } else {
                this.resArray = res;
              }
              this.dataSource.data = res;
              this.dataSource = new MatTableDataSource(res);
              this.dataSource.paginator = this.paginator.toArray()[0];
              this.dataSource.data.forEach(element => {
                if (userObj.ID == element.ID) {
                  if (element['REGISTERED'] == 1) {
                    element['status'] = 'Confirmed';
                  } else {
                    element['status'] = 'Unconfirmed';
                  }
                }
              });
            })
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
        }
      }
    );
  }

  fbComplaintDetails(ID, status) {
    const dialogRef = this.dialog.open(NamsDetailsComponent, {
      width: '1500px',
      height: '550px',
      data: { ID, "status": status, "nams": 'reech' },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data === 'delete' || data === 'add') {
          this.namsFbDataStore.$namsFbData.forEach((t: any, i) => {
            if (t.ID === ID) {
              this.namsFbDataStore.$namsFbData.splice(i, 1);
            }
          });
          if (data === 'delete') {
            this.totalCount = this.totalCount - 1;
            if (this.totalCount !== 0) {
              if (this.pageSize > this.totalCount) {
                this.pageSize = this.totalCount;
                this.rangeLabel = 1 + ' - ' + this.pageSize;
              }
              if (this.lastIndex > this.totalCount) {
                this.lastIndex = this.totalCount;
                this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
              }
            } else {
              this.zeroComplaints = true;
            }
          }
          this._namsFbData.next(Object.assign({}, this.namsFbDataStore).$namsFbData);
          this.$namsFbData.subscribe((res) => {
            if (this.fbFilter == true) {
              this.fbFilterArray = res;
            } else {
              this.fbResArray = res;
            }
            this.dataSource1.data = res;
            this.dataSource1 = new MatTableDataSource(res);
            this.dataSource1.paginator = this.paginator.toArray()[1];
          })
        }
        else if (data === 'reech-refresh') {
          this.namsService.getReechDetails(ID).subscribe((res: any) => {
            let userObj = res.data;
            this.namsFbDataStore.$namsFbData.forEach((t: any, i) => {
              if (t.ID === userObj.ID) {
                this.namsFbDataStore.$namsFbData[i] = userObj;
              }
            });
            this._namsFbData.next(Object.assign({}, this.namsFbDataStore).$namsFbData);
            this.$namsFbData.subscribe((res) => {
              if (this.fbFilter == true) {
                this.fbFilterArray = res;
              } else {
                this.fbResArray = res;
              }
              this.dataSource1.data = res;
              this.dataSource1 = new MatTableDataSource(res);
              this.dataSource1.paginator = this.paginator.toArray()[1];
              this.dataSource1.data.forEach(element => {
                if (userObj.ID == element.ID) {
                  if (element['REGISTERED'] == 1) {
                    element['status'] = 'Confirmed';
                  } else {
                    element['status'] = 'Unconfirmed';
                  }
                }
              });
            })
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          })
        }
      }
    );
  }

  deleteComplaint(ID, key) {
    this.idArray = [];
    this.reechInstaComplaints = [];
    this.reechFbComplaints = [];
    this.idArray.push(ID.toString());
    const dialogRef = this.dialog.open(ReechDeleteReasonComponent, {
      data: { id: this.idArray, button: "Delete" },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.namsService
          .deleteReechComplaint(dialogResult.id, dialogResult.reason)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
              if (key == "Instagram") {
                this.namsDataStore.$namsData.forEach((t: any, i) => {
                  if (t.ID === ID) {
                    this.namsDataStore.$namsData.splice(i, 1);
                  }
                });
                this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
                this.$namsData.subscribe((res) => {
                  if (this.instaFilter == true) {
                    this.instaFilterArray = res;
                  } else {
                    this.resArray = res;
                  }
                  this.dataSource.data = res;
                  this.dataSource = new MatTableDataSource<any>(res);
                  this.dataSource.paginator = this.paginator.toArray()[0];
                })
                this.totalCount = this.totalCount - 1;
                if (this.totalCount !== 0) {
                  if (this.pageSize > this.totalCount) {
                    this.pageSize = this.totalCount;
                    this.rangeLabel = 1 + ' - ' + this.pageSize;
                  }
                  if (this.lastIndex > this.totalCount) {
                    this.lastIndex = this.totalCount;
                    this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                  }
                } else {
                  this.zeroComplaints = true;
                }
                this.idArray = [];
              }
              else if (key == "Facebook") {
                this.namsFbDataStore.$namsFbData.forEach((t: any, i) => {
                  if (t.ID === ID) {
                    this.namsFbDataStore.$namsFbData.splice(i, 1);
                  }
                });
                this._namsFbData.next(Object.assign({}, this.namsFbDataStore).$namsFbData);
                this.$namsFbData.subscribe((res) => {
                  if (this.fbFilter == true) {
                    this.fbFilterArray = res;
                  } else {
                    this.fbResArray = res;
                  }
                  this.dataSource1.data = res;
                  this.dataSource1 = new MatTableDataSource<any>(res);
                  this.dataSource1.paginator = this.paginator.toArray()[1];
                })
                this.totalCount = this.totalCount - 1;
                if (this.totalCount !== 0) {
                  if (this.pageSize > this.totalCount) {
                    this.pageSize = this.totalCount;
                    this.rangeLabel = 1 + ' - ' + this.pageSize;
                  }
                  if (this.lastIndex > this.totalCount) {
                    this.lastIndex = this.totalCount;
                    this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                  }
                } else {
                  this.zeroComplaints = true;
                }
                this.idArray = [];
              }
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  bulkDelete() {
    const dialogRef = this.dialog.open(ReechDeleteReasonComponent, {
      data: { id: this.idArray, button: "Delete" },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.namsService
          .deleteReechComplaint(dialogResult.id, dialogResult.reason)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
              if (this.instagram == true) {
                for (let j = 0; j < this.idArray.length; j++) {
                  this.namsDataStore.$namsData.forEach((t: any, i) => {
                    if (t.ID == this.idArray[j]) {
                      this.namsDataStore.$namsData.splice(i, 1);
                    }
                  });
                }
                this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
                this.$namsData.subscribe((res) => {
                  if (this.instaFilter == true) {
                    this.instaFilterArray = res;
                  } else {
                    this.resArray = res;
                  }
                  this.dataSource.data = res;
                  this.dataSource = new MatTableDataSource<any>(res);
                  this.dataSource.paginator = this.paginator.toArray()[0];
                })
                var filtered = this.idArray.filter(function (el) {
                  return el != null;
                });
                this.totalCount = this.totalCount - filtered.length;
                if (this.totalCount !== 0) {
                  if (this.pageSize > this.totalCount) {
                    this.pageSize = this.totalCount;
                    this.rangeLabel = 1 + ' - ' + this.pageSize;
                  }
                  if (this.lastIndex > this.totalCount) {
                    this.lastIndex = this.totalCount;
                    this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                  }
                } else {
                  this.zeroComplaints = true;
                }
                this.idArray = [];
                this.reechInstaComplaints = [];
                this.disableAction = true;
                this.disableDelete = true;
              }
              else if (this.facebook == true) {
                for (let j = 0; j < this.idArray.length; j++) {
                  this.namsFbDataStore.$namsFbData.forEach((t: any, i) => {
                    if (t.ID == this.idArray[j]) {
                      this.namsFbDataStore.$namsFbData.splice(i, 1);
                    }
                  });
                }
                this._namsFbData.next(Object.assign({}, this.namsFbDataStore).$namsFbData);
                this.$namsFbData.subscribe((res) => {
                  if (this.fbFilter == true) {
                    this.fbFilterArray = res;
                  } else {
                    this.fbResArray = res;
                  }
                  this.dataSource1.data = res;
                  this.dataSource1 = new MatTableDataSource<any>(res);
                  this.dataSource1.paginator = this.paginator.toArray()[1];
                })
                var filtered = this.idArray.filter(function (el) {
                  return el != null;
                });
                this.totalCount = this.totalCount - filtered.length;
                if (this.totalCount !== 0) {
                  if (this.pageSize > this.totalCount) {
                    this.pageSize = this.totalCount;
                    this.rangeLabel = 1 + ' - ' + this.pageSize;
                  }
                  if (this.lastIndex > this.totalCount) {
                    this.lastIndex = this.totalCount;
                    this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                  }
                } else {
                  this.zeroComplaints = true;
                }
                this.idArray = [];
                this.reechFbComplaints = [];
                this.disableAction = true;
                this.disableDelete = true;
              }
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  rejectComplaint(ID, key) {
    this.idArray = [];
    this.reechInstaComplaints = [];
    this.reechFbComplaints = [];
    this.idArray.push(ID.toString());
    const dialogRef = this.dialog.open(ReechDeleteReasonComponent, {
      data: { id: this.idArray, button: "Reject" },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.namsService
          .rejectReechComplaint(dialogResult.id, dialogResult.reason)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
              if (key == "Instagram") {
                this.namsDataStore.$namsData.forEach((t: any, i) => {
                  if (t.ID === ID) {
                    this.namsDataStore.$namsData.splice(i, 1);
                  }
                });
                this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
                this.$namsData.subscribe((res) => {
                  if (this.instaFilter == true) {
                    this.instaFilterArray = res;
                  } else {
                    this.resArray = res;
                  }
                  this.dataSource.data = res;
                  this.dataSource = new MatTableDataSource<any>(res);
                  this.dataSource.paginator = this.paginator.toArray()[0];
                })
                this.totalCount = this.totalCount - 1;
                if (this.totalCount !== 0) {
                  if (this.pageSize > this.totalCount) {
                    this.pageSize = this.totalCount;
                    this.rangeLabel = 1 + ' - ' + this.pageSize;
                  }
                  if (this.lastIndex > this.totalCount) {
                    this.lastIndex = this.totalCount;
                    this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                  }
                } else {
                  this.zeroComplaints = true;
                }
                this.idArray = [];
              }
              else if (key == "Facebook") {
                this.namsFbDataStore.$namsFbData.forEach((t: any, i) => {
                  if (t.ID === ID) {
                    this.namsFbDataStore.$namsFbData.splice(i, 1);
                  }
                });
                this._namsFbData.next(Object.assign({}, this.namsFbDataStore).$namsFbData);
                this.$namsFbData.subscribe((res) => {
                  if (this.fbFilter == true) {
                    this.fbFilterArray = res;
                  } else {
                    this.fbResArray = res;
                  }
                  this.dataSource1.data = res;
                  this.dataSource1 = new MatTableDataSource<any>(res);
                  this.dataSource1.paginator = this.paginator.toArray()[1];
                })
                this.totalCount = this.totalCount - 1;
                if (this.totalCount !== 0) {
                  if (this.pageSize > this.totalCount) {
                    this.pageSize = this.totalCount;
                    this.rangeLabel = 1 + ' - ' + this.pageSize;
                  }
                  if (this.lastIndex > this.totalCount) {
                    this.lastIndex = this.totalCount;
                    this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                  }
                } else {
                  this.zeroComplaints = true;
                }
                this.idArray = [];
              }
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  bulkReject() {
    const dialogRef = this.dialog.open(ReechDeleteReasonComponent, {
      data: { id: this.idArray, button: "Reject" },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.namsService
          .rejectReechComplaint(dialogResult.id, dialogResult.reason)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
              if (this.instagram == true) {
                for (let j = 0; j < this.idArray.length; j++) {
                  this.namsDataStore.$namsData.forEach((t: any, i) => {
                    if (t.ID == this.idArray[j]) {
                      this.namsDataStore.$namsData.splice(i, 1);
                    }
                  });
                }
                this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
                this.$namsData.subscribe((res) => {
                  if (this.instaFilter == true) {
                    this.instaFilterArray = res;
                  } else {
                    this.resArray = res;
                  }
                  this.dataSource.data = res;
                  this.dataSource = new MatTableDataSource<any>(res);
                  this.dataSource.paginator = this.paginator.toArray()[0];
                })
                var filtered = this.idArray.filter(function (el) {
                  return el != null;
                });
                this.totalCount = this.totalCount - filtered.length;
                if (this.totalCount !== 0) {
                  if (this.pageSize > this.totalCount) {
                    this.pageSize = this.totalCount;
                    this.rangeLabel = 1 + ' - ' + this.pageSize;
                  }
                  if (this.lastIndex > this.totalCount) {
                    this.lastIndex = this.totalCount;
                    this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                  }
                } else {
                  this.zeroComplaints = true;
                }
                this.idArray = [];
                this.reechInstaComplaints = [];
                this.disableAction = true;
                this.disableDelete = true;
              }
              else if (this.facebook == true) {
                for (let j = 0; j < this.idArray.length; j++) {
                  this.namsFbDataStore.$namsFbData.forEach((t: any, i) => {
                    if (t.ID == this.idArray[j]) {
                      this.namsFbDataStore.$namsFbData.splice(i, 1);
                    }
                  });
                }
                this._namsFbData.next(Object.assign({}, this.namsFbDataStore).$namsFbData);
                this.$namsFbData.subscribe((res) => {
                  if (this.fbFilter == true) {
                    this.fbFilterArray = res;
                  } else {
                    this.fbResArray = res;
                  }
                  this.dataSource1.data = res;
                  this.dataSource1 = new MatTableDataSource<any>(res);
                  this.dataSource1.paginator = this.paginator.toArray()[1];
                })
                var filtered = this.idArray.filter(function (el) {
                  return el != null;
                });
                this.totalCount = this.totalCount - filtered.length;
                if (this.totalCount !== 0) {
                  if (this.pageSize > this.totalCount) {
                    this.pageSize = this.totalCount;
                    this.rangeLabel = 1 + ' - ' + this.pageSize;
                  }
                  if (this.lastIndex > this.totalCount) {
                    this.lastIndex = this.totalCount;
                    this.rangeLabel = this.startIndex + ' - ' + this.lastIndex;
                  }
                } else {
                  this.zeroComplaints = true;
                }
                this.idArray = [];
                this.reechFbComplaints = [];
                this.disableAction = true;
                this.disableDelete = true;
              }
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  shortList(ID) {
    this.idArray = [];
    this.reechInstaComplaints = [];
    this.reechFbComplaints = [];
    this.idArray.push(ID.toString());
    this.confirmationMsg.title = 'Are you sure you want to shortlist the complaint ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.idArray, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.namsService
          .shortlistReechComplaint(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
              this.getComplaintById(ID);
              this.idArray = [];
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  bulkShortlist() {
    this.confirmationMsg.title = 'Are you sure you want to shortlist the complaints ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.idArray, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.namsService
          .shortlistReechComplaint(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              dialogRef.close('refresh');
              if (this.instagram == true) {
                for (let j = 0; j < this.idArray.length; j++) {
                  this.namsDataStore.$namsData.forEach((t: any, i) => {
                    if (t.ID == this.idArray[j]) {
                      this.getComplaintById(t.ID);
                    }
                  });
                }
                this._namsData.next(Object.assign({}, this.namsDataStore).$namsData);
                this.$namsData.subscribe((res) => {
                  if (this.instaFilter == true) {
                    this.instaFilterArray = res;
                  } else {
                    this.resArray = res;
                  }
                  this.dataSource.data = res;
                  this.dataSource = new MatTableDataSource<any>(res);
                  this.dataSource.paginator = this.paginator.toArray()[0];
                })
                this.idArray = [];
                this.reechInstaComplaints = [];
                this.disableAction = true;
                this.disableDelete = true;
              }
              else if (this.facebook == true) {
                for (let j = 0; j < this.idArray.length; j++) {
                  this.namsFbDataStore.$namsFbData.forEach((t: any, i) => {
                    if (t.ID == this.idArray[j]) {
                      this.getComplaintById(t.ID);
                    }
                  });
                }
                this._namsFbData.next(Object.assign({}, this.namsFbDataStore).$namsFbData);
                this.$namsFbData.subscribe((res) => {
                  if (this.fbFilter == true) {
                    this.fbFilterArray = res;
                  } else {
                    this.fbResArray = res;
                  }
                  this.dataSource1.data = res;
                  this.dataSource1 = new MatTableDataSource<any>(res);
                  this.dataSource1.paginator = this.paginator.toArray()[1];
                })
                this.idArray = [];
                this.reechFbComplaints = [];
                this.disableAction = true;
                this.disableDelete = true;
              }
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  upload() {
    if (this.selectedTab == 0) {
      this.upload_key = "instagram";
    }
    else if (this.selectedTab == 1) {
      this.upload_key = "facebook";
    }
    const dialogRef = this.dialog.open(NamsReechFileUploadComponent, {
      width: '1100px',
      height: '700px',
      position: {
        top: '0px',
        left: '19vw'
      },
      data: { key: this.upload_key },
      disableClose: true
    });
  }

  openUrl(row, url) {
    if (url.indexOf("https") == -1) {
      window.open("https://" + url, "_blank");
    } else {
      window.open(url, "_blank");
    }
  }

}