import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RecommendationResolutionDetailsComponent } from './recommendation-resolution-details.component';

describe('RecommendationResolutionDetailsComponent', () => {
  let component: RecommendationResolutionDetailsComponent;
  let fixture: ComponentFixture<RecommendationResolutionDetailsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ RecommendationResolutionDetailsComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(RecommendationResolutionDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
