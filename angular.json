{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"web": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/web", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "allowedCommonJsDependencies": ["chart.js", "core-js", "raf", "xlsx", "deps/pdfmake", "@babel/runtime"], "aot": true, "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.css"], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "aot": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "12mb", "maximumError": "16mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "50kb"}]}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "aot": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "12mb", "maximumError": "16mb"}, {"type": "anyComponentStyle", "maximumWarning": "12mb", "maximumError": "16mb"}]}, "qa": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.qa.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "aot": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "12mb", "maximumError": "16mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "50kb"}]}, "uat": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.uat.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "aot": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "12mb", "maximumError": "16mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "50kb"}]}, "stg": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stg.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "aot": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "12mb", "maximumError": "16mb"}, {"type": "anyComponentStyle", "maximumWarning": "12mb", "maximumError": "16mb"}]}, "prod": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "aot": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "12mb", "maximumError": "16mb"}, {"type": "anyComponentStyle", "maximumWarning": "12mb", "maximumError": "16mb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "web:build"}, "configurations": {"production": {"browserTarget": "web:build:production"}, "dev": {"browserTarget": "web:build:dev"}, "qa": {"browserTarget": "web:build:qa"}, "uat": {"browserTarget": "web:build:uat"}, "stg": {"browserTarget": "web:build:stg"}, "prod": {"browserTarget": "web:build:prod"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "web:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "web:serve"}, "configurations": {"production": {"devServerTarget": "web:serve:production"}, "qa": {"devServerTarget": "web:serve:qa"}, "uat": {"devServerTarget": "web:serve:uat"}, "stg": {"devServerTarget": "web:serve:stg"}, "prod": {"devServerTarget": "web:serve:prod"}}}}}}, "defaultProject": "web", "cli": {"analytics": "6551ea1f-b37e-4a1e-b608-0e4e127a156a"}}