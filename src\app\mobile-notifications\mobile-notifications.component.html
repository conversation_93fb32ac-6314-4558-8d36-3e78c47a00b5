
<div class="header-container" fxLayout="row" *ngIf="mobile">
    <div class="headline-container" fxLayout="row" fxLayoutAlign="start center" style="color: #fff;font-size: 20px;">
        <img  src="../assets/images/mobile-x.png" style="padding-right: 10px; padding-top: 3px;" (click)="viewDashboard()"/>
        Notifications
    </div>
    <span style="flex: 1 1 auto;"></span>
    <div fxLayout="row" fxLayoutAlign="end center">
        <img class="bell-btn" src="../assets/images/bell-mobile.png" (click)="viewNotifications()" *ngIf="roleId == 4 || roleId == 5 || roleId == 7 || roleId == 8"/>
        <button [matMenuTriggerFor]="admin" mat-mini-fab class="flex profile-btn">{{roleName}}</button>
    </div>
    <mat-menu #admin="matMenu" class="admin-menu">
        <div class="admin-option-container">
          <button mat-menu-item class="option-btn" (click)="viewProfile(true)" *ngIf="roleId == 4 || roleId == 5 || roleId == 7 || roleId == 8">
            <span class="option-text">My profile</span>
          </button>
          <mat-divider class="option-divider" *ngIf="roleId == 4 || roleId == 5 || roleId == 7 || roleId == 8"></mat-divider>
          <button mat-menu-item class="option-btn" (click)='logout()'>
            <span class="option-text">Log out</span>
          </button>
        </div>
      </mat-menu>
</div>
<div class="manage" *ngIf="mobile">
    <div class="head-container">
        <div class="comp-head">
            <h3 matSubheader class="head-text">
                <img src="/assets/images/Vector.png" /> Activities on complaints
            </h3>
        </div>
        <span class="head-spacer"></span>
    </div>
    <div>
        <form class="header-search">
            <!-- <input type="text" name="search" [matAutocomplete]="auto" [formControl]="stateCtrl" placeholder="Search by PO#" (keyup)="applyFilter($event.target.value)"> -->
            <!-- <mat-autocomplete #auto="matAutocomplete" (optionSelected)="applyFilter($event.option.value)"> -->
            <mat-form-field appearance="outline" class="header-search" style="width: 80%;">
                <mat-select placeholder="Case ID" [(value)]="selectedID"
                    (selectionChange)="selectActivities($event.value)">
                    <mat-option *ngFor="let comp of storeUserComp" [value]="comp.ID">
                        {{comp.CASE_ID}}
                    </mat-option>
                </mat-select>
            </mat-form-field>
            <!-- </mat-autocomplete> -->
        </form>
    </div>
    <div class="dashboard-complaint-list">
        <mat-list-item *ngFor="let item of complaintsList">
            <div class="list-item-container" *ngFor="let complaint of item.updates" style="margin-top: 5px;">
                <div class="item-head-container" fxLayout="column">
                    <div class="message" fxLayout="row">
                        <div class="dot" *ngIf="complaint.label == 'Status moved to' && complaint.value == 'New'"
                            style="min-width: 12px; margin-top: 4px; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #0088CB;">
                        </div>
                        <div class="dot" *ngIf="complaint.label == 'Status moved to' && complaint.value == 'In Progress'"
                            style="min-width: 12px; margin-top: 4px; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #F89E1B;">
                        </div>
                        <div class="dot" *ngIf="complaint.label == 'Status moved to' && complaint.value == 'On Hold'"
                            style="min-width: 12px; margin-top: 4px; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #ED2F45;">
                        </div>
                        <div class="dot" *ngIf="complaint.label == 'Status moved to' && complaint.value == 'Out of remit/Outside ASCI Purview'"
                            style="min-width: 12px; margin-top: 4px; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #000000;">
                        </div>
                        <div class="dot" *ngIf="complaint.label == 'Status moved to' && complaint.value == 'Non-Issue'"
                            style="min-width: 12px; margin-top: 4px; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #000000;">
                        </div>
                        <div class="dot" *ngIf="complaint.label == 'Status moved to' && complaint.value == 'Closed'"
                            style="min-width: 12px; margin-top: 4px; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #000000;">
                        </div>
                        <div class="dot" *ngIf="complaint.label == 'Status moved to' && complaint.value == 'Resolution'"
                            style="min-width: 12px; margin-top: 4px; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #04A585;">
                        </div>
                        <div class="dot" *ngIf="complaint.label == 'Status moved to' && complaint.value == 'Sub-Judice'"
                            style="min-width: 12px; margin-top: 4px; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid #000000;">
                        </div>
                        <div class="dot" *ngIf="complaint.label != 'Status moved to'"
                            style="min-width: 12px; margin-top: 4px; margin-right: 2%; height: 12px;border-radius: 100px; border: 3px solid darkgray;">
                        </div>
                        <div fxLayout="row">
                            {{complaint.label}} {{complaint.value}}
                        </div>
                    </div>
                    <div fxLayout="row" fxLayoutGap="20px" style="margin-top: 2%; margin-left: 5%;">
                        <div>
                            <p style="color: #92A2B1;"> {{item.date}} </p>
                        </div>
                    </div>
                </div>
            </div>
            <mat-divider></mat-divider>
        </mat-list-item>
    </div>
</div>
