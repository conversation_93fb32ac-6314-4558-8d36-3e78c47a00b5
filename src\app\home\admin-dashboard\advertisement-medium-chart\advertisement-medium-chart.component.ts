import { AfterViewInit, Component, ElementRef, OnInit } from '@angular/core';
/* Observable MQ - D3 Charts */
import * as d3 from 'd3';
import html2canvas from 'html2canvas';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { colorObj } from 'src/app/shared/color-object';

@Component({
  selector: 'app-advertisement-medium-chart',
  templateUrl: './advertisement-medium-chart.component.html',
  styleUrls: ['./advertisement-medium-chart.component.scss']
})
export class AdvertisementMediumChartComponent implements OnInit, AfterViewInit {

  root: any;
  loading: boolean = true;

  constructor(
    private el: ElementRef,
    private cs: ComplaintsService,
    private notify: NotificationService
  ) { }

  ngOnInit(): void { }

  ngAfterViewInit(): void {
    // Logic to be executed after <PERSON><PERSON> has fully initialized the component's view.
    // This is used for tasks that require access to the rendered DOM elements or child components
    // queried with @ViewChild or @ViewChildren.
    this.getChartData();
  }

  getChartData() {
    this.cs.getMasterData().subscribe(res => {
      const data = {
        name: 'root',
        children: [
          {
            name: 'A',
            children: [
              { name: 'A1', value: 5 },
              { name: 'A2', value: 3 },
              { name: 'A3', value: 2 }
            ]
          },
          {
            name: 'B',
            children: [
              { name: 'B1', value: 4 },
              { name: 'B2', value: 6 }
            ]
          },
          {
            name: 'C',
            children: [
              {
                name: 'C1',
                children: [
                  { "name": "AgglomerativeCluster", "value": 5 },
                  { "name": "CommunityStructure", "value": 8 }
                ]
              }
            ]
          }
        ]
      };
      this.drawChart(data);
    }, err => {
      this.loading = false;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  drawChart(data: any): void {
    this.loading = false;
    const element = this.el.nativeElement.querySelector('#advertisement_medium');
    element.innerHTML = '';

    const width = element.clientWidth;
    const radius = width / 2;

    const tooltip = d3.select(element)
      .append('div')
      .attr('class', 'sunburst-tooltip')
      .style('opacity', 0);

    const svg = d3.select(element)
      .append('svg')
      .attr('viewBox', `0 0 ${width} ${width}`)
      .attr('preserveAspectRatio', 'xMidYMid meet')
      .append('g')
      .attr('transform', `translate(${radius},${radius})`);

    const partition = d3.partition().size([2 * Math.PI, radius]);

    this.root = d3.hierarchy(data)
      .sum(d => d.value)
      .sort((a, b) => b.value - a.value);

    partition(this.root);
    this.root.each(d => d.current = d);

    const arc = d3.arc()
      .startAngle(d => d.x0)
      .endAngle(d => d.x1)
      .innerRadius(d => d.y0)
      .outerRadius(d => d.y1);

    const color = d3.scaleOrdinal(d3.schemeCategory10);

    const gPaths = svg.append('g');

    const path = gPaths.selectAll('path')
      .data(this.root.descendants().slice(1))
      .enter()
      .append('path')
      .attr('fill', d => color((d.children ? d : d.parent).data.name))
      .attr('d', d => arc(d.current))
      .on('click', (event, d) => {
        event.stopPropagation();
        this.zoom(d, svg, arc, path, label);
      })
      .on('mouseover', (event, d) => {
        tooltip.transition().duration(200).style('opacity', 0.9);
        tooltip.html(`${d.data.name}: ${d.value}`)
          .style('left', `${event.offsetX + 10}px`)
          .style('top', `${event.offsetY + 10}px`);
      })
      .on('mouseout', () => {
        tooltip.transition().duration(300).style('opacity', 0);
      });

    const label = svg.append('g')
      .attr('pointer-events', 'none')
      .attr('text-anchor', 'middle')
      .selectAll('text')
      .data(this.root.descendants().slice(1))
      .enter().append('text')
      .attr('dy', '0.35em')
      .attr('fill', '#000')
      .style('font-size', '10px')
      .attr('transform', d => labelTransform(d.current))
      .style('opacity', d => +labelVisible(d.current))
      .text(d => `${d.data.name}: ${d.value}`);

    this.zoom(this.root, svg, arc, path, label);
    updateBreadcrumbs(this.root);

    svg.on('click', () => this.zoom(this.root, svg, arc, path, label));

    function labelVisible(d) {
      return d.y1 <= radius && d.y0 >= 0 && (d.x1 - d.x0) > 0.03;
    }

    function labelTransform(d) {
      const x = (d.x0 + d.x1) / 2 * 180 / Math.PI;
      const y = (d.y0 + d.y1) / 2;
      return `rotate(${x - 90}) translate(${y},0) rotate(${x < 180 ? 0 : 180})`;
    }

    function updateBreadcrumbs(d) {
      const breadcrumb = d.ancestors().reverse().map(n => n.data.name).join(' > ');
      const bcEl = document.getElementById('breadcrumb');
      if (bcEl) bcEl.innerText = breadcrumb;
    }
  }

  zoom(p, svg, arc, path, label) {
    this.root.each(d => d.target = {
      x0: Math.max(0, Math.min(1, (d.x0 - p.x0) / (p.x1 - p.x0))) * 2 * Math.PI,
      x1: Math.max(0, Math.min(1, (d.x1 - p.x0) / (p.x1 - p.x0))) * 2 * Math.PI,
      y0: Math.max(0, d.y0 - p.y0),
      y1: Math.max(0, d.y1 - p.y0)
    });

    const t = svg.transition().duration(750).ease(d3.easeCubicInOut);

    path.transition(t)
      .tween('data', d => {
        const i = d3.interpolate(d.current, d.target);
        return t => {
          d.current = i(t);
        };
      })
      .attrTween('d', d => () => arc(d.current));

    label.transition(t)
      .attr('transform', d => {
        const x = (d.current.x0 + d.current.x1) / 2 * 180 / Math.PI;
        const y = (d.current.y0 + d.current.y1) / 2;
        return `rotate(${x - 90}) translate(${y},0) rotate(${x < 180 ? 0 : 180})`;
      })
      .style('opacity', d => +((d.current.y1 <= arc.outerRadius()(d) && d.current.y0 >= 0 && (d.current.x1 - d.current.x0) > 0.03)));

    const breadcrumb = p.ancestors().reverse().map(d => d.data.name).join(' > ');
    const bcEl = document.getElementById('breadcrumb');
    if (bcEl) bcEl.innerText = breadcrumb;
  }

  exportChart(): void {
    const svgElement = this.el.nativeElement.querySelector('svg');
    const serializer = new XMLSerializer();
    const source = serializer.serializeToString(svgElement);
    const blob = new Blob([source], { type: 'image/svg+xml;charset=utf-8' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = 'sunburst-chart.svg';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  exportPNG(): void {
    const element = this.el.nativeElement.querySelector('#advertisement_medium');
    html2canvas(element).then(canvas => {
      const link = document.createElement('a');
      link.href = canvas.toDataURL('image/png');
      link.download = 'sunburst-chart.png';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  }

}