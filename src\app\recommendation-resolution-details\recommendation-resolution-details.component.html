<div fxLayout="column">
    <div fxLayout="row" style="padding-top: 23px;padding-left:18px;padding-right:18px;background: #F8F9F9;">
        <div style="font-size: 16px;font-weight:600" *ngIf="type == 'recommendation'">Recommendations</div>
        <div style="font-size: 16px;font-weight:600" *ngIf="type == 'resolution'">Resolutions</div>
        <div fxFlex="67%"></div>
        <div class="close-div">
            <button mat-icon-button class="close-btn" mat-dialog-close>
                <mat-icon style="margin-bottom: 2px;">close</mat-icon>
            </button>
        </div>
    </div>
    <mat-divider></mat-divider>
    <div style="margin-top: 21px;">
        <div fxFlex="94" class="recommendations">
            <span style="font-size: 14px;font-weight:600"  *ngIf="type == 'recommendation'">Recommendation Messages:</span>
            <span style="font-size: 14px;font-weight:600"  *ngIf="type == 'resolution'">Resolution Statements:</span>
            <div class="box">
            <div *ngFor="let record of records">
                <div class="recommendation-box">
                    <div style="word-wrap: break-word;">
                        <span class="box-heading" *ngIf="type == 'recommendation'">Summary: </span> 
                        <span class="box-heading" *ngIf="type == 'resolution'">Subject: </span> 
                        <span class="box-text">{{record.SUBJECT}}</span>
                    </div> 
                    <div style="word-wrap: break-word;">
                        <span class="box-heading">Description: </span>
                         <span class="box-text" *ngIf="type == 'recommendation'">{{record.RECOMMENDATION}}</span>
                         <span class="box-text" *ngIf="type == 'resolution'">{{record.RESOLUTION}}</span>
                    </div>
                </div>
                <span style="font-size: 11px;margin-left: 69%;"> - {{record.CREATED_DATE | date: 'dd/MM/yyyy'}}: {{record.CREATED_DATE | date: 'h:mm a'}}</span>
            </div><br>
            </div>
        </div>
    </div>
</div>
