.flex {
    display: flex;
    align-items: center;
    justify-content: center;
}
.email-screen {
    height: 90vh;
}
.detail-header-container {
    background-color: #F8F9F9;
    width: 100%;
    padding: 7px 20px;
}
.black-text {
    font-size: 14px;
    color: #000000;
}
.grey-text {
    color: #92A2B1;
    font-size: 12px;
    line-height: 16px;
    font-weight: normal;
}
.close-div {
    position: relative;
    bottom: 99vh;
    left: 894px;
    z-index: 2;
}
.close-btn {
    color: solid rgba(47, 57, 65, 0.6);
    background: #F3F3F3;
    border: 1px solid rgba(47, 57, 65, 0.6);
}
.email-inbox {
    padding: 1%;
    height: 80vh;
}
.inbox-body {
    background: #F8F9F9;
    border-radius: 4px 4px 0px 0px;
    padding: 10px;
    height: 77vh;
}
.space {
    padding: 10px 0px; 
}
.inbox-head {
    color: #92A2B1;
    font-weight: normal;
    font-size: 12px;
    line-height: 16px;
}
.individuals-chat {
    overflow-y: scroll;
    overflow-x: hidden;
    width: 100%;
    height: 86%;
}
.individuals-chat::-webkit-scrollbar {
    display: none;
}
.individuals-chat {
    -ms-overflow-style: none;
    scrollbar-width: none; 
}
.chat-list {
    min-height: 90%;
}
.scroll::-webkit-scrollbar {
    display: none;
}       
.scroll { 
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.blue-icon {
    color:#0088CB;
    width: 32px;
    height: 32px;
    background: #CCE7F5;
    border: 1px solid #FFFFFF;
    border-radius: 50px;
    padding: 5px;
}
.small {
    font-size: 12px;
    line-height: 14px;
    font-weight: normal;
}
.chat-msg {
    padding: 5px;
}
.email-body {
    width: 70%;
    height: 60%;
    padding: 10px 20px;
}
.members {
    height:26px;
}
.emails-header {
    padding: 1%;
    border-bottom: 1px solid #D8DCDE;
}
.create-btn {
    color: #0088CB;
    background: #FFFFFF;
    border: 1px solid #0088CB;
    border-radius: 12px;
    height: auto;
    padding: 5px 17px;
    padding-bottom: 5px;
}
.create-btn[disabled] {
    color: #92A2B1;
    background: #F8F9F9;
    border: 1px solid #92A2B1;
    border-radius: 12px;
    height: auto;
    padding: 5px 17px;
    padding-bottom: 5px;
}
.title {
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
}
.email-container {
    height: 58vh;
    overflow-y: scroll;
    overflow-x: hidden;
}
.refresh-btn {
    color: #5A6F84;
    background: #CFD7DF;
    border-radius: 12px;
    height: 30px;
}
.body-box {
    background: #F8F9F9;
    border-radius: 4px;
    padding: 0px 5px 7px;
}
.content-box {
    padding: 20px 10px 10px;
}
.chat-container {
    height: 57vh;
    overflow-y: scroll;
    overflow-x: hidden;
}
.name-box1 {
    color: #0088CB;
    background: #FFFFFF;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
    border-radius: 50px;
    font-weight: 600;
    width: 28px;
    height: 28px;
    padding: 2.5% 1% 1% 1%;
}
.white-box {
    background: #FFFFFF;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    padding: 10px 5px;
}
.hover:hover {
    cursor: pointer;
    background-color: rgb(238, 238, 238);
}
.msg-text {
    font-size: 12px;
    line-height: 19px;
    font-weight: normal;
}
.msg-text1 {
    font-size: 12px;
    line-height: 19px;
    font-weight: normal;
    height: 110px;
    overflow-x: hidden;
    overflow-y: scroll;
    outline: none;
    resize: none;
    margin-bottom: 10px;
}
:host ::ng-deep .msg-text1 p {
    margin-bottom: 0px !important;
}
::-webkit-scrollbar {
    width: 5px;
}
::-webkit-scrollbar-thumb {
    background: #ccc
}
.ellipsis {
    overflow: hidden;
    width: 380px;
    display: inline-block;
    display: -webkit-inline-box;
    word-break: break-word;
    text-overflow: ellipsis;
    white-space: normal;
    -webkit-box-orient: vertical;
    position: relative;
}
.ellipsis1 {
    overflow: hidden;
    width: 390px;
    display: inline-block;
    display: -webkit-inline-box;
    word-break: break-word;
    text-overflow: ellipsis;
    white-space: normal;
    -webkit-box-orient: vertical;
    position: relative;
}
:host ::ng-deep .ellipsis,.ellipsis1 p {
    margin-bottom: 0px !important;
}
.media-anchor {
    color: #0088CB;
    font-size: 12px;
    word-break: break-all;
    cursor: pointer;
}
.blue-text {
    color: #0088CB;
}
.name-box {
    color: #0088CB;
    background: #FFFFFF;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
    border-radius: 50px;
    font-weight: 600;
    width: 28px;
    height: 28px;
    padding: 1%;
}
.type-box {
    background: #FFFFFF;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    padding: 10px;
}
.attach-btn {
    background: #F8F8F9;
    color: #888C92;
    border: 1px solid #DFE0E2;
    box-sizing: border-box;
    border-radius: 7px;
    width: 39px;
    height: 39px;
    text-align: center;
}
.attach-btn[disabled] {
    opacity: 0.4;
} 
.msg-box {
    background: #FFFFFF;
    border: 1px solid #DFE0E2;
    box-sizing: border-box;
    border-radius: 8px;
    width: 100%;
    height: 38px;
    padding: 10px 20px;
}
.send-btn {
    background: #0088CB;
    box-shadow: inset 0px 3px 6px rgba(212, 241, 255, 0.2);
    border-radius: 8px;
    width: 39px !important;
    height: 39px;
    padding-right: 10px;
}
.send-btn[disabled] {
    opacity: 0.4;
}
.chat-item-selected {
    background-color: rgba(63,81,181,.15);
}
.no-mails {
    font-family: 'Segoe UI';
    font-style: italic;
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
    color: #92A2B1;
    margin-left: 230px;
}