.common-toolbar {
    border-bottom: 1px solid #D8DCDE;
    border-top: 0;
    border-left: none;
    border-right: none;
}

.heading-container {
    padding: 18px;
    align-items: center;
    vertical-align: middle;
    width: 60%;
}

.float-btn {
    z-index: 1;
    background-color: rgb(255, 255, 255);
    border: 1px solid rgb(223, 223, 223);
    border-radius: 15px;
}

.options-container {
    width: 40%;
}

.float-right-container {
    position: absolute;
    top: 5px;
    right: 10px;
    display: flex;
    flex-direction: row;
    gap: 20px;
}

.message {
    font-size: 14px;
    color: #000000;
}

.right-search-btn {
    color: gray;
    border: 1px solid gray;
}

.dashboard-container {
    width: 100%;
    height: 90%;
}

.intradashboard-container {
    width: 95%;
    margin-left: 5%;
}

.manage {
    width: 23%;
    height: 91%;
}

.dashboard-admin-heading {
    font-weight: 600;
    font-size: 18px;
    line-height: 23.94px;
    color: #ED2F45;
}

.dashboard-admin {
    width: 77%;
    height: 100%;
    border-left: 1px solid rgb(245, 245, 245);
}

.dasboard-subheading {
    font-weight: 400;
    font-size: 14px;
    line-height: 18.62px;
}

.list-time {
    font-weight: 400;
    font-size: 12px;
    line-height: 15.96px;
    color: #92A2B1;
}

.dashboard-complaint-list {
    overflow-y: scroll;
    overflow-x: hidden;
    width: 100%;
    height: 86%;
}

.dashboard-complaint-list::-webkit-scrollbar {
    display: none;
}

.dashboard-complaint-list {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.item-head-container:hover {
    cursor: pointer;
}

:host ::ng-deep .panel-header>.mat-expansion-indicator::after {
    color: #0088CB;
}

.head-container {
    display: flex;
    flex-direction: row;
    gap: 20px;
    text-align: center;
}

.comp-head {
    padding-top: 5px;
}

.head-text {
    padding: 15px 9px 6px 1px;
    font-weight: 500;
    font-size: 16px;
    line-height: 21.28px;
}

.head-spacer {
    flex: 1 1 auto;
}

.list-item-container::-webkit-scrollbar {
    display: none;
}

.list-item-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.list-item-container {
    width: 100%;
    height: auto !important;
    padding: 1px 3px 1px 3px;
}

.list-item-container:hover {
    background-color: rgb(235, 235, 235);
}

.item-content-container {
    display: flex;
    flex-direction: row;
    gap: 10px;
}

.name-container,
.date-container {
    font-size: small;
    color: rgb(85, 84, 84);
}

.item-icon {
    font-size: medium;
}

.status-chip {
    height: 3px !important;
}

.comp-status {
    min-height: 22px !important;
    width: auto;
    font-size: smaller;
    border-width: 1px;
    border-style: solid;
    border-radius: 5px;
    padding: 0px 7px 0px 7px;
}

.complaint-display-head {
    margin: 5px 0px 5px 15px;
}

.tab-title-container {
    font-size: small;
    height: 40px;
    width: 100px;
}

:host ::ng-deep .mat-tab-group.mat-tab-group.mat-primary .mat-ink-bar {
    background-color: none;
    border-bottom: none;
}

.host::ng-deep .mat-tab-header,
.mat-tab-nav-bar {
    border-bottom: 0;
}

.item-head-container {
    padding-top: 10px;
    font-weight: 400;
    font-size: 13px;
    line-height: 17.29px;
}

.status {
    width: 60px;
}

.tab {
    width: 1000px;
    height: auto;
}

.toolbar {
    background-color: white;
    height: 9%;
    width: 100%;
}

.adm-head {
    color: #2f3941;
    font-size: 16px;
    font-weight: 500;
    line-height: 21.28px;
}

.toolbar-btns {
    display: flex;
    flex-direction: row;
    grid-gap: 10px;
}

.search-btn {
    color: gray;
    border: 1px solid gray;
    border-radius: 15px;
}

.bell-btn {
    color: crimson;
    border: 1px solid gray;
    border-radius: 15px;
}

.admin-btn {
    color: white;
    background-color: #0088CB;
    border-radius: 15px;
}

.option-btn {
    line-height: 25%;
    height: 550%;
}

.option-text {
    line-height: 200%;
    padding-left: 8px;
}

.media-anchor {
    color: #0088CB;
    word-break: break-all;
}

.recommendation-container,
.resolution-container {
    position: relative;
    bottom: 8px;
}

.add-button {
    width: auto;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    border-radius: 13px;
    background-color: #CFD7DF;
    color: #5A6F84;
    padding: 0px 15px 0px 15px;
}

.delete-button {
    width: auto;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    background: #FFFFFF;
    color: #5A6F84;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 12px;
    text-align: center;
    padding: 0px 15px 0px 15px;
}

.save-button {
    width: auto;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    background: #FFFFFF;
    border: 1px solid #0088CB;
    color: #0088CB;
    box-sizing: border-box;
    border-radius: 12px;
    text-align: center;
    margin-left: 1%;
    padding: 0px 15px 0px 15px;
}

.msg-button {
    width: auto;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    border-radius: 13px;
    background-color: #0088CB;
    color: #FFFFFF;
    padding: 0px 15px 0px 15px;
    margin: 1% 2% 1% 2%;
}

.conversation-btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    z-index: 10;
    border-top: 0;
    border-left: none;
    border-right: none;
    right: 1%;
    top: 19%;
}

.header-search {
    width: 98%;
    margin-top: 10px;
}

.header-search ::placeholder {
    color: #92A2B1;
}

::ng-deep .mat-form-field-flex>.mat-form-field-infix {
    padding: 0.4em 0px !important;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-label {
    margin-top: -5px;
    margin-bottom: -5px;
}

::ng-deep label.ng-star-inserted {
    transform: translateY(-0.59375em) scale(.75) !important;
}

.box {
    background: #F8F9F9;
    height: 89%;
}

.toolbar1 {
    padding: 0% 2% 0% 2%;
    background: #F8F9F9;
}

.card-row {
    padding: 2% 0% 3% 2%;
}

.card-row1 {
    padding: 2% 0% 2% 2%;
}

.card {
    width: 24%;
    margin-right: 1%;
    border: 0.5px solid #D8DCDE;
    box-sizing: border-box;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    cursor: pointer;
    padding: 2% 0% 2% 0%;
    height: 86px;
}

.card:hover {
    border: 0.5px solid #0088CB;
}

.digit {
    font-weight: bold;
    font-size: 24px;
    font-weight: 500;
    line-height: 20px;
    color: #000000;
}

.category {
    margin-top: 3%;
    font-size: 11px;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: #92A2B1;
}

.category1 {
    margin-top: 3%;
    font-size: 11px;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: #92A2B1;
}

.category2 {
    margin-top: 3%;
    font-size: 8px;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: #92A2B1;
}

.complaint-table::-webkit-scrollbar {
    display: none;
}

.complaint-table {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.complaint-table {
    margin: 0% 2% 0% 2%;
    border-left: 1px solid #D8DCDE;
    border-right: 1px solid #D8DCDE;
    border-top: 1px solid #D8DCDE;
    border-radius: 5px;
    background-color: white;
}

.complaint-table1::-webkit-scrollbar {
    display: none;
}

.complaint-table1 {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.complaint-table1 {
    border-left: 1px solid #D8DCDE;
    border-right: 1px solid #D8DCDE;
    border-top: 1px solid #D8DCDE;
    /* height: 241px;
    overflow-y: scroll;
    overflow-x: hidden; */
    border-radius: 5px;
    background-color: white;
}

mat-paginator {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    background: white;
    box-sizing: border-box;
    border-left: 1px solid #D8DCDE;
    border-right: 1px solid #D8DCDE;
    border-bottom: 1px solid #D8DCDE;
}

.mat-expansion-panel-spacing {
    margin: 16px;
}

mat-expansion-panel {
    border: none;
    box-shadow: none;
}

.text-message {
    font-size: 25px;
    color: #524f56;
    text-align: center;
    padding-top: 5%;
}

.mat-header-cell {
    background-color: #F8F9F9;
    font-weight: 400;
    color: #000000;
}

.mat-header-row {
    height: 40px;
}

.mat-row {
    height: 40px;
    cursor: pointer;
}

.mat-column-caseID {
    font-size: 13px;
}

.mat-column-brandname {
    font-size: 13px;
}

.mat-column-case {
    font-size: 13px;
}

.mat-column-status {
    font-size: 13px;
}

.status-button {
    cursor: pointer;
}

.documenttab {
    height: 210px;
}

.documenttab1 {
    height: 240px;
}

.docs_attached {
    height: 235px;
    padding-top: 5px;
    overflow-y: scroll;
    overflow-x: hidden;
}

.docs_attached_by::-webkit-scrollbar {
    display: none;
}

.docs_attached_by {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.docs_attached_by {
    height: 285px;
    padding-top: 5px;
    overflow-y: scroll;
    overflow-x: hidden;
    margin-bottom: 10px;
}

.doc-caption {
    background-color: rgb(255, 255, 255);
    height: fit-content;
    padding: 13px;
}

.doc-caption>p {
    height: 18px;
    width: 150px;
    padding: 0;
    overflow: hidden;
    position: relative;
    display: inline-block;
    margin: 0 5px 0 5px;
    font-size: 12px;
    text-decoration: none;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #000;
    font-weight: 700;
}

.doc-caption1 {
    background-color: rgb(255, 255, 255);
    height: fit-content;
}

.doc-caption1>p {
    height: 15px;
    width: 150px;
    padding: 0;
    overflow: hidden;
    position: relative;
    display: inline-block;
    margin: 0 5px 0 5px;
    font-size: 12px;
    text-decoration: none;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #000;
    font-weight: 700;
}

::-webkit-scrollbar {
    width: 0px;
}

.divider {
    width: 320px !important;
}

.status-option-container {
    background: #FFFFFF;
    border: 1px solid #D8DCDE;
    box-sizing: border-box;
    box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.option {
    margin-top: 10%;
}

.mat-column-duedate {
    font-size: 13px;
}

.mat-column-stage {
    font-size: 13px;
}

.prog {
    color: #F89E1B;
}

.new {
    color: #0088CB;
}

.hold {
    color: #ED2F45;
}

.resolve {
    color: #04A585;
}

.close {
    color: #000000;
}

.Out-of-remit {
    color: #000000;
}

.invalid {
    color: #000000;
}

.urgent {
    color: #FF4242;
}

.high {
    color: #F29100;
}

.mid {
    color: #F89E1B;
}

.low {
    color: #2ED9FF;
}

.comp_details {
    width: 96%;
    margin: 0% 2% 2% 2%;
    border: 0.5px solid #D8DCDE;
    box-sizing: border-box;
    box-shadow: none;
    border-radius: 4px;
    padding: 0%;
    height: 340px;
}

.details-header {
    background: #F8F9F9;
    height: 50px;
    padding: 13px;
}

.details-header1 {
    background: #F8F9F9;
    height: max-content;
    padding: 10px 13px;
}

.details-container::-webkit-scrollbar {
    display: none;
}

.details-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.details-container {
    margin: 20px 0px 5px 15px;
    height: 255px;
    overflow-x: hidden;
    overflow-y: scroll;
}

.detail-attribute {
    color: #92A2B1;
    width: 30%;
    font-size: 14px;
}

.detail-values {
    font-size: 14px;
    width: 70%;
    overflow-wrap: break-word;
}

.vertical-line {
    width: 1px;
    background-color: #D8DCDE;
    height: 289px;
}

.vertical-line1 {
    width: 1px;
    background-color: #D8DCDE;
    height: 254px;
}

.tabs-container::-webkit-scrollbar {
    display: none;
}

.tabs-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.tabs-container {
    padding: 5px 0px 10px 1px;
    height: 100%;
}

.mat-tab-label,
.mat-tab-link {
    color: rgba(0, 0, 0, .87);
}

:host ::ng-deep .detail-subtab .mat-tab-label,
.mat-tab-link {
    min-width: 0 !important;
    padding: 10px 15px 10px 15px !important;
    border: 1px solid #D8DCDE;
    border-radius: 3px 3px 0px 0px;
    opacity: 0.6 !important;
    background-color: #F8F9F9 !important;
    height: 44px;
}

:host ::ng-deep .detail-subtab .mat-tab-label-active {
    background-color: #ffffff !important;
    opacity: 1 !important;
    color: black;
}

:host ::ng-deep .detail-subtab .mat-ink-bar {
    display: none !important;
}

.detail-subtab {
    height: 100%;
    padding-left: 1%;
}

:host ::ng-deep .detail-subtab1 .mat-tab-label,
.mat-tab-link {
    padding: 10px 15px 10px 15px !important;
    border: 1px solid #D8DCDE;
    border-radius: 3px 3px 0px 0px;
    opacity: 1 !important;
    color: #000000;
    background-color: #F8F9F9 !important;
    height: 44px;
    min-width: 120px;
}

:host ::ng-deep .detail-subtab1 .mat-tab-label-active {
    background-color: #0088CB !important;
    opacity: 1 !important;
    color: #ffffff;
}

:host ::ng-deep .detail-subtab1 .mat-ink-bar {
    display: none !important;
}

.detail-subtab1 {
    height: 100%;
    width: 100%;
    margin-top: 10px;
}

.comp-msg-container {
    height: auto;
    width: 90%;
    word-wrap: break-word;
    word-break: normal;
}

.comp-msg {
    word-break: break-word;
    line-height: 16px;
}

.comp-detail-container::-webkit-scrollbar {
    display: none;
}

.comp-detail-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.comp-detail-container {
    width: 100%;
    height: 195px;
    overflow-x: hidden;
    overflow-y: scroll;
}

.doc-body::-webkit-scrollbar {
    display: none;
}

.doc-body {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.doc-body {
    width: 100%;
    height: 195px;
    overflow-x: hidden;
    overflow-y: scroll;
}

.claims-tab::-webkit-scrollbar {
    display: none;
}

.claims-tab {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.claims-tab {
    width: 100%;
    height: 195px;
    overflow-x: hidden;
    overflow-y: scroll;
}

.add-btn {
    color: #5A6F84;
    background-color: #CFD7DF;
    border-radius: 12px;
    font-size: 14px;
    justify-content: center;
    align-items: center;
    margin-top: 3%;
}

.file-container {
    background: #FFFFFF;
    width: 250px;
    height: auto;
    font-size: 12px;
    color: #2F3941;
}

.file-container1 {
    background: #FFFFFF;
    border: 1px solid #D8DCDE;
    box-sizing: border-box;
    border-radius: 4px;
    max-width: 300px;
    height: 43px;
    font-size: 12px;
    color: #2F3941;
}

.doc-wrapper {
    margin: 12px;
}

.no-timeline {
    width: 100%;
    padding-top: 15px;
    padding-left: 10px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.time-container::-webkit-scrollbar {
    display: none;
}

.time-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.time-container {
    width: 100%;
    height: 195px;
    overflow-x: hidden;
    overflow-y: scroll;
}

.time-container-user {
    width: 100%;
    height: 225px;
    overflow-y: scroll;
    overflow-x: hidden;
}

.time-container-user::-webkit-scrollbar {
    display: none;
}

.time-container-user {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.timeline {
    width: 100%;
    margin-left: 20px;
    position: relative;
    padding-top: 10px;
}

.time-enents {
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: #000000;
}

.timeline ul {
    list-style: none;
    margin-right: 20px;
}

.timeline ul li {
    padding-top: 5px;
    margin-bottom: 10px;
}

.timeline-content .date {
    font-size: 13px;
    font-weight: 200;
    letter-spacing: 2px;
}

@media only screen and (min-width: 768px) {
    .timeline:before {
        content: "";
        position: absolute;
        top: 0;
        transform: translateX(-50%);
        width: 4px;
        height: 100%;
        background-color: #D8DCDE;
    }

    .timeline ul {
        list-style: none;
        margin-right: 20px;
    }

    .timeline ul li {
        width: 90%;
        position: relative;
        transform: translateX(30px);
    }

    .timeline ul li::after {
        content: "";
        position: absolute;
        height: 16px;
        width: 16px;
        border-radius: 50%;
        background-color: #0088CB;
        border: 4px solid rgba(255, 255, 255, 0.726);
        top: 15px;
        transform: translate(-50%, -50%);
        left: -30px;
    }

    .timeline-content .date {
        position: absolute;
        top: -30px;
    }

    .timeline ul li:hover::before {
        background-color: aqua;
    }
}

.head-row {
    padding: 0% 2% 0% 2%;
    height: 50px;
}

.comp-heading {
    margin-top: 2%;
    font-size: 15px;
    font-weight: 500;
    color: #000000;
}

.example-full-width {
    margin-top: 1%;
    width: 130px;
    margin-left: 6%;
}

:host::ng-deep .mat-select-placeholder {
    color: #000000;
}

:host ::ng-deep .mat-select-trigger {
    height: 18px;
}

.table-heading {
    padding: 0% 0% 1% 2%;
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.5px;
    color: #000000;
}

.tables-div::-webkit-scrollbar {
    display: none;
}

.tables-div {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.tables-div {
    height: 320px;
    overflow-y: scroll;
    overflow-x: hidden;
}

.comp_details-adv {
    width: 96%;
    /* height: 350px; */
    margin: 0% 2% 0.5% 2%;
    border: 0.5px solid #D8DCDE;
    box-sizing: border-box;
    box-shadow: none;
    border-radius: 4px;
    padding: 0%;
}

.detail-data::-webkit-scrollbar {
    display: none;
}

.detail-data {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.detail-data {
    height: 195px;
    overflow-y: scroll;
    overflow-x: hidden;
}

.detail-data1::-webkit-scrollbar {
    display: none;
}

.detail-data1 {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.detail-data1 {
    height: 245px;
    overflow-y: scroll;
    overflow-x: hidden;
}

::ng-deep .multiline-tooltip {
    white-space: pre-line !important;
    color: #FFFFFF;
    background-color: #3A3A3A;
    font-size: small;
    border: 1px solid #D8DCDE;
    box-sizing: border-box;
    box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.ellipsis {
    overflow: hidden;
    width: 180px;
    display: inline-block;
    display: -webkit-inline-box;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    vertical-align: middle;
    position: relative;
    margin-top: 6px;
    max-height: 1.5rem;
}

:host ::ng-deep ul {
    margin-bottom: 0px !important;
}

:host ::ng-deep h1 {
    margin-bottom: 0px !important;
}

:host ::ng-deep h2 {
    margin-bottom: 0px !important;
}

:host ::ng-deep h3 {
    margin-bottom: 0px !important;
}

:host ::ng-deep h4 {
    margin-bottom: 0px !important;
}

:host ::ng-deep .ellipsis p {
    margin-bottom: 0px !important;
}

:host ::ng-deep .ellipsis1 p {
    margin-bottom: 0px !important;
}

:host ::ng-deep .ellipsis2 p {
    margin-bottom: 0px !important;
}

.ellipsis1 {
    overflow: hidden;
    width: 180px;
    display: inline-block;
    display: -webkit-inline-box;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    vertical-align: middle;
    position: relative;
    max-height: 1.5rem;
}

@media only screen and (max-width: 1250px) {
    .manage {
        visibility: hidden;
        max-height: 0;
        width: 0;
        margin-left: 0%;
    }

    .dashboard-admin-heading {
        font-size: 18px;
    }

    .comp_details-adv .table {
        font-size: 16px;
    }

    .mat-paginator {
        display: none;
    }

    .dasboard-subheading {
        padding-top: 2px;
    }

    .create-button-mobile-blue {
        background-color: #0088CB;
        border-radius: 21px;
        color: white;
        padding-left: 14px;
        padding-right: 14px;
    }

    .comp_details-adv {
        width: 80%;
        margin-left: 15px;
        margin-right: 15px;
    }

    .dashboard-admin {
        width: 100%;
        margin-left: 0%;
    }

    .create-button {
        visibility: hidden;
    }

    .create-button-mobile {
        background-color: #ffffffff;
        padding: 11px 15px 10px 15px;
        float: left;
        box-shadow: 0px -4px 8px rgb(0 0 0 / 10%);
        width: 100%;
        text-align: center;
        position: fixed;
        bottom: 0px;
    }

    .card-row1 {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-around;
        padding-bottom: 18px;
    }

    .box {
        padding-bottom: 39px;
        height: auto;
    }

    .card-row1 .card {
        flex: 0 0 42%;
        margin-bottom: 2%;
        padding-right: 4%;
        padding-left: 4%;
        padding-top: 15px;
    }

    .detail-attribute {
        color: #000000;
        width: 30%;
        font-size: 14px;
    }

    .documenttab {
        height: 240px;
        display: flex;
        flex-direction: column;
    }

    .note-mob {
        margin-left: 34px;
        position: relative;
        bottom: 20px;
        font-size: 13px;
        font-style: normal;
        font-weight: normal;
        margin-right: 4pc;
        color: rgba(0, 0, 0, 0.6);
    }

    .note {
        font-size: 12px;
        font-weight: 400;
        white-space: normal;
        padding-left: 23px;
    }

    .divider-mob {
        display: none;
    }

    .toolbar1 {
        background: #fff;
        padding: 2% 2% 0% 2%;
    }

    .category, .category1, .category2 {
        margin-top: 0px;
    }

    .tables-div {
        max-height: 0;
    }

    .case-detail-box {
        background: #FFFFFF;
        border: 1px solid #D8DCDE;
        margin: 17px;
        border-radius: 4px;
    }

    .flex {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .bell-btn {
        padding-right: 14px;
        width: 39px;
        height: 26px;
    }

    .header-container {
        background: #000000;
        width: 100%;
        height: 84px;
        padding: 5%;
    }

    .logo {
        width: 102.77px;
        height: 30px;
    }

    .profile-btn {
        color: #FFFFFF;
        background: #0088CB;
        width: 34px;
        height: 34px;
        text-align: center;
        font-style: normal;
        font-weight: bold;
        font-size: 17px;
        box-sizing: border-box;
        padding-top: 2px;
        line-height: 27px;
    }

    .attach_doc {
        display: flex;
        flex-direction: row;
        align-self: flex-end;
        justify-self: flex-end;
        padding: 10px;
    }
}

@media only screen and (max-width: 350px) {
    .doc-caption1 {
        background-color: rgb(255, 255, 255);
        height: fit-content;
        width: 95px;
        overflow: hidden;
        position: relative;
        display: inline-block;
        text-decoration: none;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .doc-caption1>p {
        height: 15px;
        width: 114px;
        padding: 0;
        overflow: hidden;
        position: relative;
        display: inline-block;
        margin: 0 5px 0 5px;
        font-size: 12px;
        text-decoration: none;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #000;
        font-weight: 700;
    }
}

.doc-fxrow-container {
    width: 100%;
    margin-top: 2%;
}

.mat-card-doc {
    padding: 0px;
    width: fit-content;
    height: fit-content;
}

.doc-icon-container {
    background-color: #3a3a3a;
    height: 85px;
    width: 226px;
    border-radius: 5px;
    display: flex;
    justify-content: center;
}

.doc-icon-container>span {
    color: rgb(168, 168, 168);
}

.doc-image-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.action-buttons {
    border: 1px solid gainsboro;
    position: relative;
    bottom: 17px;
    left: 122px;
    background: white;
    width: 104px;
    height: 64px;
    padding-left: 13px;
    padding-top: 10px;
    z-index: 1;
}

.option-btn {
    line-height: 25%;
    height: 550%;
}

.option-text {
    line-height: 200%;
    padding-left: 8px;
}

.dropzone {
    height: 120px;
    display: table;
    width: 120px;
    border: 1px dashed #aaa;
    border-radius: 15px;
    margin-left: 10px;
    margin-top: 5px;
}

.addfile-text-wrapper {
    width: calc(30% - 6px);
    height: 126px;
    display: table-cell;
    vertical-align: middle;
}

.upload-scope-container {
    height: max-content;
    width: 100%;
    text-align: center;
}

input[type="file"] {
    display: none;
}

.upload-label {
    color: rgb(170, 170, 170);
}

.upload-label>span {
    font-size: small;
    font-weight: normal;
    padding-top: 5px;
}

.no-claims {
    color: rgb(160, 160, 156);
    margin-top: 10px;
    margin-left: 10px;
    font-size: 15px;
}

.intra-expansion {
    border: 1px solid #D8DCDE;
    padding: 0% 2% 0% 2%;
}

.panel-title {
    color: #000000;
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
}

:host ::ng-deep .panel-header>.mat-expansion-indicator:after {
    color: #0088CB;
}

.raised-container {
    width: 65%;
}

.raised-container1 {
    width: 100%;
}

.head-container1 {
    padding: 5px 0px;
}

.head-container1>div {
    min-height: 20px;
    max-height: max-content;
}

.arrow-img-icon {
    vertical-align: super !important;
}

.intra-h3 {
    color: #000000;
    font-weight: normal;
    font-size: 14px;
    line-height: 19px;
}

.challenge-container {
    border: 1px solid #D8DCDE;
    border-radius: 4px;
    padding: 10px;
}

.attribute-container {
    padding: 10px 0px 0px;
}

.panel-title {
    color: #000000;
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
}

:host ::ng-deep .panel-header>.mat-expansion-indicator:after {
    color: #0088CB;
}

.grey-text {
    color: #92A2B1;
    font-size: 14px;
    line-height: 19px;
}

.violated-container {
    width: 37%;
}

.violated-container1 {
    width: 100%;
}

.chapters-container {
    padding: 10px;
    overflow-wrap: anywhere;
    padding-top: 0px;
}

.chapter-text {
    color: #000000;
    font-weight: normal;
    font-size: 14px;
    margin-bottom: 11px;
    line-height: 19px;
    width: 192px;
    text-align: -webkit-auto;
}

.flex {
    display: flex;
    align-items: center;
    justify-content: center;
}

.circle-icon {
    font-size: 4px;
    vertical-align: sub;
    height: 16px;
    margin-top: 8px;
    bottom: 6px;
    position: relative;
}

.width50 {
    width: 50%;
}

.violated-popups {
    color: #0088CB;
    text-align: center;
    flex-direction: row;
    width: auto;
    height: 30px;
    background: #FFFFFF;
    border: 1px solid #0088CB;
    box-sizing: border-box;
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
    justify-content: center;
    align-items: center;
    padding: 0px 16px;
}

:host ::ng-deep .detail-subtab .mat-tab-label .mat-tab-label-content {
    font-weight: 500;
}

:host ::ng-deep .detail-subtab1 .mat-tab-label .mat-tab-label-content {
    font-weight: 500;
}

.list-item-container1::-webkit-scrollbar {
    display: none;
}

.list-item-container1 {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.list-item-container1 {
    width: 100%;
    height: auto !important;
    padding: 1px 3px 1px 3px;
}

.list-item-container1:hover {
    background-color: rgb(235, 235, 235);
    cursor: pointer;
}

.left-panel::-webkit-scrollbar {
    display: none;
}

.left-panel {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.left-panel {
    width: 100%;
    height: 195px;
    overflow-x: hidden;
    overflow-y: scroll;
}

.message {
    font-size: 14px;
    color: #000000;
}

.item-icon1 {
    margin-right: -7%;
    font-size: 15px;
}

.ellipsis_desc {
    overflow: hidden;
    display: inline-block;
    display: -webkit-inline-box;
    text-overflow: ellipsis;
    -webkit-line-clamp: 3;
    white-space: normal;
    -webkit-box-orient: vertical;
    vertical-align: middle;
    position: relative;
    height: auto !important;
}

.item-head-container1 {
    padding-top: 10px;
    padding-left: 10px;
    font-weight: 300;
    font-size: 13px;
    width: 260px;
    text-align: justify;
}

.divider1 {
    border-right: 1px solid #D8DCDE;
    height: 195px;
}

.right-panel::-webkit-scrollbar {
    display: none;
}

.right-panel {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.right-panel {
    width: 100%;
    height: 195px;
    overflow-x: hidden;
    overflow-y: scroll;
    padding-top: 10px;
}