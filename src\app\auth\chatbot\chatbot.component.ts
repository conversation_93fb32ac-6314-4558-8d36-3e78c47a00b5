import { Component, ElementRef, ViewChild, On<PERSON>nit, HostListener } from '@angular/core';
import { MatAccordion } from '@angular/material/expansion';
import { trigger, transition, style, animate, state } from '@angular/animations';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { AuthService } from 'src/app/services/auth.service';
import { environment } from 'src/environments/environment';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ThirdPartyService } from 'src/app/services/third-party.service';
import { UploadService } from 'src/app/services/upload.service';
import moment from 'moment';
import { colorObj } from 'src/app/shared/color-object';
import { NotificationService } from 'src/app/services/notification.service';

let chats = {
  'greeting': {
    text: 'Hi There! I am Tara, I am here to assist you today.',
    step: 'greeting',
    options: null,
    author: 'bot',
    action: false,
    skipOption: false,
    information: null,
    end: false
  },
  'proceed_complaint_registration': {
    text: 'Should we proceed to register a complaint?',
    step: 'proceed_complaint_registration',
    options: [
      { value: 1, name: 'Yes' },
      { value: 2, name: 'No' }
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'cancel_complaint_registration': {
    text: 'Sorry, currently I register new complaints only, so if you have any other queries, please write to <NAME_EMAIL>',
    step: 'cancel_complaint_registration',
    options: null,
    author: 'bot',
    action: false,
    skipOption: false,
    information: null,
    end: true
  },
  'proceed_information_collection': {
    text: 'In order to proceed, I need some information about you, is that okay?',
    step: 'proceed_information_collection',
    options: [
      { value: 1, name: 'Yes' },
      { value: 2, name: 'No' }
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: 'You would be required to provide details of the advertisement through a link or file upload or a detailed description of the ad and where you saw it. We request you to keep this information handy.',
    end: false
  },
  'cancel_information_collection': {
    text: 'Sorry, I cannot move further to register the complaint without receiving details from you. Would you like to continue to complete the registration process?',
    step: 'cancel_information_collection',
    options: [
      { value: 1, name: 'Yes' },
      { value: 2, name: 'No' }
    ],
    author: 'bot',
    action: false,
    skipOption: false,
    information: null,
    end: false
  },
  'exit_information_collection': {
    text: 'Thanks for reaching out to us. Have a good day.',
    step: 'exit_information_collection',
    options: null,
    author: 'bot',
    action: false,
    skipOption: false,
    information: null,
    end: true
  },
  'user_type': {
    text: 'Whom do you represent ?',
    step: 'user_type',
    options1: [
      { value: 1, name: 'General Public / Consumer', ID: 7 },
      { value: 2, name: 'Business / Company / Sole Trader / Industry Body', ID: 5 }
    ],
    options2: [
      { value: 3, name: 'Consumer Organisation', ID: 8 },
      { value: 4, name: 'Government Regulator', ID: 4 }
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'different_user_type': {
    text: 'Request you to signup through the ASCI CMS portal to register your complaint. This will help you to share/upload more documents along with a detailed complaint.',
    step: 'different_user_type',
    options: null,
    author: 'bot',
    action: false,
    skipOption: false,
    information: null,
    end: true
  },
  'user_contact': {
    text: 'Please share your contact number, so that we can keep you posted on your complaint',
    step: 'user_contact',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'invalid_user': {
    text: 'This number is already a registered user, kindly login with your credentials to lodge a fresh complaint.',
    step: 'invalid_user',
    options: null,
    author: 'error',
    action: false,
    skipOption: false,
    information: null,
    end: false
  },
  'invalid_phone_number': {
    text: 'Please enter a valid mobile number',
    step: 'invalid_phone_number',
    options: null,
    author: 'error',
    action: false,
    skipOption: false,
    information: null,
    end: false
  },
  'user_otp': {
    text: 'I have sent you an OTP on your above mobile number, please enter to verify.',
    step: 'user_otp',
    options: [
      { value: 1, name: 'Resend OTP' }
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'user_validation_otp': {
    text: 'I have sent you an OTP on your above mobile number, please enter to verify.',
    step: 'user_validation_otp',
    options: [
      { value: 1, name: 'Resend OTP' }
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'change_number': {
    text: 'Do you want to change your mobile number?',
    step: 'change_number',
    options: [
      { value: 1, name: 'Yes' },
      { value: 2, name: 'No' }
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'change_email': {
    text: 'Do you want to change your email id?',
    step: 'change_email',
    options: [
      { value: 1, name: 'Yes' },
      { value: 2, name: 'No' }
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'verify_user_confirmation': {
    text: 'Congrats!! You\'r verified. We have sent you an OTP on your above mobile number, please enter to authenticate and proceed with complaint registration. ',
    step: 'verify_user_confirmation',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'incorrect_otp': {
    text: 'Entered OTP is not correct, please resend OTP',
    step: 'incorrect_otp',
    options: [
      { value: 1, name: 'Resend OTP' },
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'incorrect_verify_otp': {
    text: 'Entered OTP is not correct, please enter the correct OTP or resend OTP',
    step: 'incorrect_verify_otp',
    options: [
      { value: 1, name: 'Resend OTP' },
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'exceeds_otp_limit': {
    text: null,
    step: 'exceeds_otp_limit',
    options: null,
    author: 'bot',
    action: false,
    skipOption: false,
    information: 'Your session has been timed-out, as you have failed in entering OTP.',
    end: true
  },
  'user_name': {
    text: 'What is your name?',
    step: 'user_name',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'user_email': {
    text: 'Please share your email address, so that we can correspond with you on your complaint.',
    step: 'user_email',
    options: null,
    author: 'bot',
    action: true,
    skipOption: true,
    information: null,
    end: false
  },
  'invalid_email': {
    text: 'Please share a valid email id.',
    step: 'invalid_email',
    options: null,
    author: 'error',
    action: false,
    skipOption: false,
    information: null,
    end: false
  },
  'user_postal': {
    text: 'Please share your postal code',
    step: 'user_postal',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'invalid_postal': {
    text: 'Please enter valid postal code',
    step: 'invalid_postal',
    options: null,
    author: 'error',
    action: false,
    skipOption: false,
    information: null,
    end: false
  },
  'user_profession': {
    text: 'What is your profession?',
    step: 'user_profession',
    options: null,
    author: 'bot',
    action: true,
    skipOption: true,
    information: null,
    end: false
  },
  'user_organization': {
    text: 'What is your organization name?',
    step: 'user_organization',
    options: null,
    author: 'bot',
    action: true,
    skipOption: true,
    information: null,
    end: false
  },
  'unconfirmed_user': {
    text: 'The user is not confirmed, please verify your account to move on.',
    step: 'unconfirmed_user',
    options: null,
    author: 'error',
    action: false,
    skipOption: false,
    information: null,
    end: false
  },
  'error_user_creation': {
    text: 'Error occurred in user registration!! Please try again.',
    step: 'error_user_creation',
    options: null,
    author: 'error',
    action: false,
    skipOption: false,
    information: null,
    end: false
  },
  'complaint_details': {
    text: 'Now I would like to know about the advertisement you want to register a complaint against.',
    step: 'complaint_details',
    options: null,
    author: 'bot',
    action: false,
    skipOption: false,
    information: null,
    end: false
  },
  'complaint_desc': {
    text: 'Please specify the name of the brand or product you wish to raise a complaint against.',
    step: 'complaint_desc',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'adv_medium': {
    text: 'Where did you see the advertisement?',
    step: 'adv_medium',
    options1: [
      { value: 1, name: 'Television', ID: 1 },
      { value: 2, name: 'Digital media', ID: 3 },
      { value: 3, name: 'Radio', ID: 2 }
    ],
    options2: [
      { value: 4, name: 'Print', ID: 5 },
      { value: 5, name: 'Promotional material', ID: 6 },
      { value: 6, name: 'SMS', ID: 8 }
    ],
    options3: [
      { value: 7, name: 'Hoarding', ID: 4 },
      { value: 8, name: 'Packaging', ID: 7 },
      { value: 9, name: 'Others', ID: 9 }
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'adv_platform': {
    text: 'Please select the platform',
    step: 'adv_platform',
    options1: [
      { value: 1, name: 'Facebook', ID: 1 },
      { value: 2, name: 'Instagram', ID: 2 },
      { value: 3, name: 'YouTube', ID: 3 }
    ],
    options2: [
      { value: 4, name: 'LinkedIn', ID: 5 },
      { value: 5, name: 'Website', ID: 6 },
      { value: 6, name: 'Google Ad', ID: 7 }
    ],
    options3: [
      { value: 7, name: 'Twitter', ID: 4 },
      { value: 8, name: 'Mobile App', ID: 8 },
      { value: 9, name: 'Others', ID: 9 }
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'adv_platform_other': {
    text: 'Please give the name of platform',
    step: 'adv_platform_other',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'digital_media_date': {
    text: 'When did you see the advertisement, specify the date? (Date format: DD/MM/YYYY)',
    step: 'digital_media_date',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'digital_media_time': {
    text: 'Can you please specify the time (Time format: HH:MM:SS)',
    step: 'digital_media_time',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'invalid_date': {
    text: 'Invalid date / format',
    step: 'invalid_date',
    options: null,
    author: 'error',
    action: false,
    skipOption: false,
    information: null,
    end: false
  },
  'invalid_time': {
    text: 'Invalid time / format',
    step: 'invalid_time',
    options: null,
    author: 'error',
    action: false,
    skipOption: false,
    information: null,
    end: false
  },
  'adv_medium_other': {
    text: 'Please specify the source',
    step: 'adv_medium_other',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'adv_medium_other_date': {
    text: 'Please specify the date on which the ad was seen (Date format: DD/MM/YYYY)',
    step: 'adv_medium_other_date',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'have_link': {
    text: 'Do you have a link of the advertisement?',
    step: 'have_link',
    options: [
      { value: 1, name: 'Yes' },
      { value: 2, name: 'No' }
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'submit_link': {
    text: 'Please upload the link.',
    step: 'submit_link',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'have_documents': {
    text: 'Do you have any details / reference / documents of the advertisement?',
    step: 'have_documents',
    options: [
      { value: 1, name: 'Yes' },
      { value: 2, name: 'No' }
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'submit_documents': {
    text: 'Can you please upload the same. (Note: You can select a max of 10 files at one go and max size 10 MB)',
    step: 'submit_documents',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'document_format_error': {
    text: 'The document format is not supporting.',
    step: 'document_format_error',
    options: null,
    author: 'error',
    action: false,
    skipOption: false,
    information: null,
    end: false
  },
  'document_size_error': {
    text: 'Max size 35MB and Max 10 files allowed.',
    step: 'document_size_error',
    options: null,
    author: 'error',
    action: false,
    skipOption: false,
    information: null,
    end: false
  },
  'adv_date_television': {
    text: 'When did you see the advertisement, specify the date? (Date format: DD/MM/YYYY)',
    step: 'adv_date_television',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'adv_time': {
    text: 'Can you please specify the time (Time format: HH:MM:SS)',
    step: 'adv_time',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'adv_date_radio': {
    text: 'When did you hear the advertisement, specify the date? (Date format: DD/MM/YYYY)',
    step: 'adv_date_radio',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'adv_channel_tv': {
    text: 'Can you please specify the channel name?',
    step: 'adv_channel_tv',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'adv_station_radio': {
    text: 'Can you please specify the name of the radio station?',
    step: 'adv_station_radio',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'adv_printed_medium': {
    text: 'Where have you seen the advertisement?',
    step: 'adv_printed_medium',
    options: [
      { value: 1, name: 'Newspaper', ID: 1 },
      { value: 2, name: 'Magazine', ID: 2 }
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'adv_printed_edition': {
    text: 'Please specify the publication name and city edition',
    step: 'adv_printed_edition',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'adv_publication_date': {
    text: 'Please specify the publication date (Date format: DD/MM/YYYY)',
    step: 'adv_publication_date',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'type_promotional': {
    text: 'Specify the type of promotional material',
    step: 'type_promotional',
    options1: [
      { value: 1, name: 'Pamphlet', ID: 1 },
      { value: 2, name: 'Brochures', ID: 2 }
    ],
    options2: [
      { value: 3, name: 'Hard bills', ID: 3 },
      { value: 4, name: 'POS', ID: 4 }
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'place_promotional_material': {
    text: 'Please specify the place where you saw the promotional material.',
    step: 'place_promotional_material',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'date_promotional_material': {
    text: 'When did you see the advertisement, specify the date? (Date format: DD/MM/YYYY)',
    step: 'date_promotional_material',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'snapshot_promotional_material': {
    text: 'Can you share a copy / snapshot of the Promotional Material. (Note: You can select a max of 10 files at one go and max size 10 MB)',
    step: 'snapshot_promotional_material',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'sms_sender': {
    text: 'Please specify the sender',
    step: 'sms_sender',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'sms_screenshot': {
    text: 'Can you please provide me with a screenshot of the message. (Note: You can select a max of 10 files at one go and max size 10 MB)',
    step: 'sms_screenshot',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'sms_details': {
    text: 'Please provide the date of message (Date format: DD/MM/YYYY)',
    step: 'sms_details',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'hoarding_place': {
    text: 'Can you name the place where you saw the hoarding?',
    step: 'hoarding_place',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'hoarding_snapshot': {
    text: 'Share snapshot of the hoarding? (Note: You can select a max of 10 files at one go and max size 10 MB)',
    step: 'hoarding_snapshot',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'hoarding_date': {
    text: 'Date on which you saw the hoarding (Date format: DD/MM/YYYY)',
    step: 'hoarding_date',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'package_snapshot': {
    text: 'Can you share copy / snapshots of the front and back of pack. (Note: You can select a max of 10 files at one go and max size 10 MB)',
    step: 'package_snapshot',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'package_mfg_details': {
    text: 'Share the manufacturing date of product. (Date format: DD/MM/YYYY)',
    step: 'package_mfg_details',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'digital_media_desc': {
    text: 'Please write a short description about the advertisement.',
    step: 'digital_media_desc',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'adv_claims': {
    text: 'What content in the advertisement did you find objectionable? Please provide specific details.',
    step: 'adv_claims',
    options: null,
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'complaint_suggestions': {
    text: 'According to you the advertisement is',
    step: 'complaint_suggestions',
    options1: [
      { value: 3, name: 'Harmful Products and Situations', ID: 3 }
    ],
    options2: [
      { value: 4, name: 'Indecent/Offensive', ID: 4 },
      { value: 6, name: 'Misleading/Dishonest', ID: 6 }
    ],
    options3: [
      { value: 7, name: 'Unfair to Competition', ID: 7 },
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  // 'error_complaint_creation': {
  //   text: 'Error occurred in complaint creation!! Please try again.',
  //   step: 'error_complaint_creation',
  //   options: null,
  //   author: 'error',
  //   action: false,
  //   skipOption: false,
  //   information: null,
  //   end: false
  // },
  'thank_you_msg': {
    text: 'Thank you for registering your complaint with me.',
    step: 'thank_you_msg',
    options: null,
    author: 'bot',
    action: false,
    skipOption: false,
    information: null,
    end: false
  },
  'asci_res_msg': {
    text: 'ASCI will reach out to you as soon as possible with updates on your complaint.',
    step: 'asci_res_msg',
    options: null,
    author: 'bot',
    action: false,
    skipOption: false,
    information: null,
    end: false
  },
  'another_complaint': {
    text: 'Would you like to register an another complaint?',
    step: 'another_complaint',
    options: [
      { value: 1, name: 'Yes' },
      { value: 2, name: 'No' }
    ],
    author: 'bot',
    action: true,
    skipOption: false,
    information: null,
    end: false
  },
  'end_msg': {
    text: 'Thank you for being a vigilant consumer. Have a great day.',
    step: 'end_msg',
    options: null,
    author: 'bot',
    action: false,
    skipOption: false,
    information: null,
    end: true
  },
}

@Component({
  selector: 'app-chatbot',
  templateUrl: './chatbot.component.html',
  styleUrls: ['./chatbot.component.css'],
  animations: [
    trigger('simpleFadeAnimation', [
      state('in', style({ opacity: 1 })),
      transition(':enter', [
        style({ opacity: 0 }),
        animate("150ms 0.2s")
      ])
    ]),
    trigger('simpleFadeAnimation1', [
      state('in', style({ opacity: 1 })),
      transition(':enter', [
        style({ opacity: 0 }),
        animate("150ms 0.8s ease-out")
      ])
    ]),

    trigger('simpleFadeAnimation2', [
      state('void', style({ opacity: 1 })),
      state('*', style({ opacity: 0 })),
      transition(':enter', animate('150ms 4s ease-out')),
      transition(':leave', animate('150ms 4s ease-out')),
    ]),
    trigger('simpleFadeAnimation3', [
      transition('void => *', [
        style({ opacity: 0 }),
        animate(800)
      ]),
      transition('* => void', [
        animate(800, style({ transform: 'translateY(-100%)' }))
      ])
    ]),
  ],
})

export class ChatbotComponent implements OnInit {
  @ViewChild('scrollMe') private myScrollContainer: ElementRef;
  @ViewChild(MatAccordion) accordion?: MatAccordion;
  @ViewChild('msgs') textMessage: ElementRef;
  public bucketUrl = `${environment.BUCKET_URL}`;

  currentStepId = 'greeting';
  currentStep = {};
  displayContent = [];
  disableInput = true;
  disableUpload = true;
  infoHidden: boolean = true;
  emailPattern = environment.emailPatterm;
  mobilenopattern = /^[0-9]{10}$/;
  pincodepattern = /^[0-9]{6}$/;
  text_message = new FormControl('');
  phone_num: number;
  userToken = [];
  complaintForm: FormGroup;
  userForm: FormGroup;
  C_ID;
  NAME_OF_THE_USER = '';
  otpCount = 0;
  validationOtpCount = 0;
  COMP_ID;
  docSize;
  successNotification;
  errorNotification;
  fileTypes: any = [];
  hideNotification: boolean = true;
  warningMsg;
  interval;
  expiryTime;
  showTime: boolean = false;
  isExpiryCompleted: boolean;
  timeLeft: string;
  longText: string;
  skipMessage: boolean = false;
  timePattern = /^(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/;
  datePattern = /^(0[0-9]|1[0-9]|2[0-9]|3[0-1])[/](0[0-9]|1[0-2])[/]\d{4}$/;
  otp: number;

  fileList: File[] = [];
  listOfFiles: any[] = [];
  public files: any[] = [];

  ads: any[];
  hover: boolean;
  panelOpenState: boolean = false;
  hidden: boolean = false;
  hiddenIntroMsg: boolean = true;
  showObjectiveMessage: boolean = false;
  showObjectiveMessage1: boolean = false;
  showObjectiveMessage2: boolean = false;
  showObjectiveMessage3: boolean = false;
  showMediaMessage: boolean = false;
  docURL: string;

  constructor(
    private cs: ComplaintsService,
    private authService: AuthService,
    private thirdPartyService: ThirdPartyService,
    private uploadService: UploadService,
    private fb: FormBuilder,
    private notify: NotificationService) {
    this.complaintForm = this.fb.group({
      ID: [''],
      ADVERTISEMENT_SOURCE_ID: [''],
      COMPLAINT_SOURCE_ID: '1',
      ATTACHMENT_TYPE_ID: [''],
      USER_ID: [''],
      BRAND_NAME: [''],
      ADVERTISOR_NAME: [''],
      SOURCE_NAME: [''],
      SOURCE_DETAILS: [''],
      SOURCE_PLACE: [''],
      PLATFORM_ID: [''],
      P_M_SOURCE_ID: [''],
      PRINT_SOURCE_ID: [''],
      SOURCE_URL: [''],
      ADVERTISEMENT_DESCRIPTION: [''],
      CLASSIFICATION_ID: [''],
      COMPLAINT_DESCRIPTION: [''],
      DATE: [''],
      TIME: ['']
    });
    this.userForm = this.fb.group({
      EMAIL_ID: ['', Validators.pattern(this.emailPattern)],
      FIRST_NAME: [''],
      LAST_NAME: [''],
      PHONE_NUMBER: ['', Validators.pattern(this.mobilenopattern)],
      ORGANIZATION_NAME: [''],
      ROLE_ID: [''],
      PROFESSION_NAME: [''],
      PINCODE: ['', Validators.pattern(this.pincodepattern)]
    });
  }

  ngOnInit(): void {
    this.ads = JSON.parse(window.localStorage.getItem('advertisementSource'));
    if (this.panelOpenState == false) {
      setTimeout(() => {
        this.hiddenIntroMsg = false
      }, 5000)
    }
  }

  enterMsg() {
    setTimeout(() => {
      this.textMessage.nativeElement.focus();
    }, 0)
  }

  enterOption() {
    setTimeout(() => {
      this.textMessage.nativeElement.blur();
    }, 0)
  }

  askQuestion() {
    let chat = chats[this.currentStepId];
    if (!!chat) {
      this.currentStep = chat;
      let chatRecord = this.currentStep;
      chatRecord['time'] = new Date();
      this.displayContent.push(chatRecord);
      if (this.currentStep['end'] == false) {
        this.appendQuestion();
      }
    } else {
      this.currentStepId = 'greeting';
      this.currentStep = {};
    }
  }

  appendQuestion() {
    if (this.currentStep['options'] != null) {
      this.disableInput = false;
    }
    else if (this.currentStep['options'] == null && this.currentStep['action'] == true) {
      this.disableInput = false;
    }
    else {
      this.handleUserInput(null, 'option');
    }
  }

  handleSelectionOfOption(event) {
    this.handleUserInput(event, 'option');
  }

  onClickOfNextArrowButton(event) {
    if (this.disableInput == false) {
      this.disableInput = true;
      this.handleUserInput(event, 'user');
    }
  }

  onFileChange(event) {
    this.handleUserInput(event, 'doc');
  }

  appendAnswersOntoTheScreens() {
    if (this.currentStepId != 'adv_date_television' && this.currentStepId != 'adv_date_radio' && this.currentStepId != 'digital_media_date' && this.currentStepId != 'adv_medium_other_date' && this.currentStepId != 'adv_publication_date' &&
      this.currentStepId != 'date_promotional_material' && this.currentStepId != 'sms_details' && this.currentStepId != 'hoarding_date' && this.currentStepId != 'package_mfg_details' && this.currentStepId != 'adv_time' && this.currentStepId != 'digital_media_time'
      && this.currentStepId != 'invalid_time' && this.currentStepId != 'invalid_date') {
      this.text_message.reset();
    }
    this.askQuestion();
  }

  showNotification(message, type) {
    this.successNotification = '';
    this.errorNotification = '';
    this.hideNotification = false;
    if (type == "success") {
      this.successNotification = message;
    }
    else if (type == "error") {
      this.errorNotification = message;
    }
  }

  timeNotification() {
    this.showTime = true;
    this.expiryTime = 120;
    this.isExpiryCompleted = true;
    this.disableInput = false;
    this.interval = setInterval(() => {
      if (this.expiryTime > 0) {
        this.expiryTime--;
        const minutes: number = Math.floor(this.expiryTime / 60);
        this.timeLeft = ('00' + minutes).slice(-2) + ':' + ('00' + Math.floor(this.expiryTime - minutes * 60)).slice(-2);
      } else {
        clearInterval(this.interval);
        this.showTime = false;
        this.isExpiryCompleted = false;
        this.disableInput = true;
      }
    }, 1000)
  }

  async handleUserInput(event, cameFrom) {
    this.hideNotification = true;
    let stepData = this.currentStep;
    switch (stepData['step']) {
      case 'greeting':
        let userRecord = {
          text: 'Let\'s start',
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.currentStepId = 'proceed_complaint_registration';
        this.askQuestion();
        break;
      case 'proceed_complaint_registration':
        await this.handleProceedComplaintRegistration(event, cameFrom);
        break;
      case 'proceed_information_collection':
        await this.handleProceedInformationCollection(event, cameFrom);
        break;
      case 'cancel_information_collection':
        await this.handleCancelInformationCollection(event, cameFrom);
        break;
      case 'user_type':
        await this.handleUserType(event, cameFrom);
        break;
      case 'user_contact':
        await this.handleUserContact(event, cameFrom);
        break;
      case 'incorrect_otp':
        await this.handleIncorrectOtp(event, cameFrom);
        break;
      case 'incorrect_verify_otp':
        await this.handleIncorrectVerifyOtp(event, cameFrom);
        break;
      case 'user_otp':
        await this.handleUserOtp(event, cameFrom);
        break;
      case 'complaint_desc':
        await this.handleComplaintDesc(event, cameFrom);
        break;
      case 'adv_medium':
        await this.handleAdvMedium(event, cameFrom);
        break;
      case 'adv_time':
        await this.handleAdvTime(event, cameFrom);
        break;
      case 'adv_date_television':
        await this.handleAdvDateTelevision(event, cameFrom);
        break;
      case 'adv_date_radio':
        await this.handleAdvDateRadio(event, cameFrom);
        break;
      case 'adv_platform':
        await this.handleAdvPlatform(event, cameFrom);
        break;
      case 'adv_platform_other':
        await this.handleAdvPlatformOther(event, cameFrom);
        break;
      case 'digital_media_date':
        await this.handleDigitalMediaDate(event, cameFrom);
        break;
      case 'digital_media_time':
        await this.handleDigitalMediaTime(event, cameFrom);
        break;
      case 'adv_medium_other':
        await this.handleAdvMediumOther(event, cameFrom);
        break;
      case 'adv_medium_other_date':
        await this.handleAdvMediumOtherDate(event, cameFrom);
        break;
      case 'have_link':
        await this.handleHaveLink(event, cameFrom);
        break;
      case 'submit_link':
        await this.handleSubmitLink(event, cameFrom);
        break;
      case 'have_documents':
        await this.handleHaveDocuments(event, cameFrom);
        break;
      case 'submit_documents':
        await this.handleSubmitDocuments(event, cameFrom, stepData['step']);
        break;
      case 'adv_printed_medium':
        await this.handleAdvPrintedMedium(event, cameFrom);
        break;
      case 'adv_printed_edition':
        await this.handleAdvPrintedEdition(event, cameFrom);
        break;
      case 'adv_publication_date':
        await this.handleAdvPublicationDate(event, cameFrom);
        break;
      case 'type_promotional':
        await this.handleTypePromotional(event, cameFrom);
        break;
      case 'place_promotional_material':
        await this.handlePlacePromotionalMaterial(event, cameFrom);
        break;
      case 'date_promotional_material':
        await this.handleDatePromotionalMaterial(event, cameFrom);
        break;
      case 'snapshot_promotional_material':
        await this.handleSnapshotPromotionalMaterial(event, cameFrom, stepData['step']);
        break;
      case 'sms_sender':
        await this.handleSmsSender(event, cameFrom);
        break;
      case 'sms_screenshot':
        await this.handleSmsScreenshot(event, cameFrom, stepData['step']);
        break;
      case 'sms_details':
        await this.handleSmsDetails(event, cameFrom);
        break;
      case 'hoarding_place':
        await this.handleHoardingPlace(event, cameFrom);
        break;
      case 'hoarding_snapshot':
        await this.handleHoardingSnapshot(event, cameFrom, stepData['step']);
        break;
      case 'hoarding_date':
        await this.handleHoardingDate(event, cameFrom);
        break;
      case 'package_snapshot':
        await this.handlePackageSnapshot(event, cameFrom, stepData['step']);
        break;
      case 'package_mfg_details':
        await this.handlePackageMfgDetails(event, cameFrom);
        break;
      case 'change_number':
        await this.handleChangeNumber(event, cameFrom);
        break;
      case 'change_email':
        await this.handleChangeEmail(event, cameFrom);
        break;
      case 'user_name':
        await this.handleUserName(event, cameFrom);
        break;
      case 'user_email':
        await this.handleUserEmail(event, cameFrom);
        break;
      case 'user_organization':
        await this.handleUserOrganization(event, cameFrom);
        break;
      case 'user_postal':
        await this.handleUserPostal(event, cameFrom);
        break;
      case 'user_profession':
        await this.handleUserProfession(event, cameFrom);
        break;
      case 'user_validation_otp':
        await this.handleUserValidationOtp(event, cameFrom);
        break;
      case 'verify_user_confirmation':
        await this.handleVerifyUserConfirmation(event, cameFrom);
        break;
      case 'adv_channel_tv':
        await this.handleAdvChannelTv(event, cameFrom);
        break;
      case 'adv_station_radio':
        await this.handleAdvStationRadio(event, cameFrom);
        break;
      case 'digital_media_desc':
        await this.handleDigitalMediaDesc(event, cameFrom);
        break;
      case 'adv_claims':
        await this.handleAdvClaims(event, cameFrom);
        break;
      case 'complaint_suggestions':
        await this.handleComplaintSuggestions(event, cameFrom);
        break;
      case 'another_complaint':
        await this.handleAnotherComplaint(event, cameFrom);
        break;
      default:
        break;
    }
  }

  handleChangeEmail(event, cameFrom) {
    if (cameFrom == 'option') {
      let userRecord = {
        text: event.name,
        time: new Date(),
        author: 'user',
        options: null
      };
      this.displayContent.push(userRecord);
      if (event.value == 1) {
        this.currentStepId = 'user_email';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
      else {
        this.currentStepId = 'user_postal';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
    else {
      // userInputHandler
    }
  }

  handleChangeNumber(event, cameFrom) {
    if (cameFrom == 'option') {
      let userRecord = {
        text: event.name,
        time: new Date(),
        author: 'user',
        options: null
      };
      this.displayContent.push(userRecord);
      if (event.value == 1) {
        this.currentStepId = 'user_contact';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
      else {
        this.currentStepId = 'user_name';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
    else {
      // userInputHandler
    }
  }

  handleIncorrectOtp(event, cameFrom) {
    if (cameFrom == 'option') {
      let userRecord = {
        text: event.name,
        time: new Date(),
        author: 'user',
        options: null
      };
      this.displayContent.push(userRecord);
      if (event.value == 1) {
        this.sendOtp(this.phone_num);
        this.displayContent.forEach((element, index) => {
          if (element.text == this.otp) {
            this.displayContent.splice(index, 1);
          }
        });
      }
    }
    else {
      // userInputHandler
    }
  }

  handleAnotherComplaint(event, cameFrom) {
    if (cameFrom == 'option') {
      let userRecord = {
        text: event.name,
        time: new Date(),
        author: 'user',
        options: null
      };
      this.displayContent.push(userRecord);
      if (event.value == 1) {
        this.complaintForm.reset();
        this.currentStepId = 'complaint_desc';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
      else {
        this.currentStepId = 'end_msg';
        this.appendAnswersOntoTheScreens();
        this.authService.logout();
        this.disableInput = true;
      }
    }
    else {
      // userInputHandler
    }
  }

  registerComplaint() {
    this.thirdPartyService.createChatbotComplaint(this.complaintForm.value).subscribe(res => {
      this.hideNotification = false;
      this.showNotification(res.message, "success");
      if (res.status == 200) {
        setTimeout(() => {
          this.currentStepId = 'thank_you_msg';
          this.appendAnswersOntoTheScreens();
          let userRecord = {
            text: 'Your complaint tracking ID is : ' + this.COMP_ID,
            time: new Date(),
            author: 'bot',
            options: null
          };
          this.displayContent.push(userRecord);
          this.currentStepId = 'asci_res_msg';
          this.appendAnswersOntoTheScreens();
          setTimeout(() => {
            this.currentStepId = 'another_complaint';
            this.appendAnswersOntoTheScreens();
            this.enterOption();
          }, 4000)
        }, 3000)
      }
    }, err => {
      this.showNotification(err.message, "error");
      // this.currentStepId = 'error_complaint_creation';
      // this.appendAnswersOntoTheScreens();
    })
  }

  handleComplaintSuggestions(event, cameFrom) {
    if (cameFrom == 'option') {
      let userRecord = {
        text: event.name,
        time: new Date(),
        author: 'user',
        options: null
      };
      this.displayContent.push(userRecord);
      this.complaintForm.controls['CLASSIFICATION_ID'].setValue(event.ID);
      this.registerComplaint();
    }
    else {
      // userInputHandler
    }
  }

  handleAdvClaims(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.currentStepId = 'complaint_suggestions';
        this.enterOption();
        this.complaintForm.controls['COMPLAINT_DESCRIPTION'].setValue(event);
      }
    }
    this.appendAnswersOntoTheScreens();
  }

  handleUserName(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,     // split first name & last name
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.userForm.controls['FIRST_NAME'].setValue(event);
        this.userForm.controls['LAST_NAME'].setValue(' ');
        this.currentStepId = 'user_email';
        this.enterMsg();
      }
    }
    this.appendAnswersOntoTheScreens();
  }

  handleUserEmail(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.userForm.controls['EMAIL_ID'].setValue(event);
        if (!!this.userForm.controls['EMAIL_ID'].valid) {
          this.userCheck(this.phone_num, event);
        }
        else {
          this.currentStepId = 'invalid_email';
          this.appendAnswersOntoTheScreens();
          this.currentStepId = 'user_email';
          this.appendAnswersOntoTheScreens();
          this.enterMsg();
        }
      }
      else {
        this.currentStepId = 'user_email';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  userCheck(number, email) {
    this.authService.getOTPForChatbot(number, email).subscribe(res => {
      if (res.body.data.USER_EXISTS == true) {
        this.showNotification(res.body.message, "error");
        this.currentStepId = 'user_email';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
      else if (res.body.data.USER_EXISTS == false) {
        this.currentStepId = 'change_email';
        this.appendAnswersOntoTheScreens();
        this.enterOption();
      }
    })
  }

  handleUserPostal(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.userForm.controls['PINCODE'].setValue(event);
        if (!!this.userForm.controls['PINCODE'].valid) {
          this.currentStepId = 'user_profession';
          this.appendAnswersOntoTheScreens();
          this.enterMsg();
        }
        else {
          this.currentStepId = 'invalid_postal';
          this.appendAnswersOntoTheScreens();
          this.currentStepId = 'user_postal';
          this.appendAnswersOntoTheScreens();
          this.enterMsg();
        }
      }
      else {
        this.currentStepId = 'user_postal';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  handleUserProfession(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.userForm.controls['PROFESSION_NAME'].setValue(event);
        this.currentStepId = 'user_organization';
        this.enterMsg();
      }
    }
    this.appendAnswersOntoTheScreens();
  }

  handleUserOrganization(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.userForm.controls['ORGANIZATION_NAME'].setValue(event);
        this.createUser();
      }
      else {
        this.currentStepId = 'user_organization';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  handleProceedComplaintRegistration(event, cameFrom) {
    if (cameFrom == 'option') {
      let userRecord = {
        text: event.name,
        time: new Date(),
        author: 'user',
        options: null
      };
      this.displayContent.push(userRecord);
      if (event.value == '1') {
        this.currentStepId = 'proceed_information_collection';
        this.infoHidden = false;
        this.warningMsg = chats[this.currentStepId]['information'];
      }
      else {
        this.currentStepId = 'cancel_complaint_registration';
      }
    }
    else {
      // userInputHandler
    }
    this.appendAnswersOntoTheScreens();
  }

  handleProceedInformationCollection(event, cameFrom) {
    if (cameFrom == 'option') {
      let userRecord = {
        text: event.name,
        time: new Date(),
        author: 'user',
        options: null
      };
      this.displayContent.push(userRecord);
      if (event.value == '1') {
        this.currentStepId = 'user_type';
        this.infoHidden = true;
      }
      else {
        this.currentStepId = 'cancel_information_collection';
        this.infoHidden = true;
      }
    }
    else {
      // userInputHandler
    }
    this.appendAnswersOntoTheScreens();
  }

  handleCancelInformationCollection(event, cameFrom) {
    if (cameFrom == 'option') {
      let userRecord = {
        text: event.name,
        time: new Date(),
        author: 'user',
        options: null
      };
      this.displayContent.push(userRecord);
      if (event.value == '1') {
        this.currentStepId = 'user_type';
      }
      else {
        this.currentStepId = 'exit_information_collection';
      }
    }
    else {
      // userInputHandler
    }
    this.appendAnswersOntoTheScreens();
  }

  handleUserType(event, cameFrom) {
    if (cameFrom == 'option') {
      if (event.value == '1') {
        let userRecord = {
          // text: 'I am complaining as an individual',
          text: event.name,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.userForm.controls['ROLE_ID'].setValue(event.ID.toString());
        this.currentStepId = 'user_contact';
        this.enterMsg();
      }
      else {
        let userRecord = {
          text: event.name,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.currentStepId = 'different_user_type';
      }
    }
    else {
      // userInputHandler
    }
    this.appendAnswersOntoTheScreens();
  }

  handleUserContact(event, cameFrom) {
    this.phone_num = event;
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.userForm.controls['PHONE_NUMBER'].setValue(event);
        if (!!this.userForm.controls['PHONE_NUMBER'].valid) {
          this.sendOtp(this.phone_num);
        }
        else {
          this.currentStepId = 'invalid_phone_number';
          this.appendAnswersOntoTheScreens();
          this.currentStepId = 'user_contact';
          this.appendAnswersOntoTheScreens();
          this.enterMsg();
        }
      }
      else {
        this.currentStepId = 'user_contact';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  handleUserOtp(event, cameFrom) {
    if (cameFrom == 'option') {
      if (!!event) {
        let userRecord = {
          text: event.name,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        if (event.value == '1') {
          this.sendOtp(this.phone_num);
        }
      }
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.verifyOtp(event);
        this.otp = event;
      }
      else {
        this.disableInput = true;
        this.currentStepId = 'incorrect_otp';
        this.appendAnswersOntoTheScreens();
        this.enterOption();
      }
    }
  }

  sendOtp(number) {
    this.otpCount++;
    if (this.otpCount < 5) {
      this.authService.getOTPForChatbot(number, '').subscribe(res => {
        this.C_ID = res.body.data.C_ID;
        if (res.body.data.USER_EXISTS == true) {
          if (res.body.data.ROLE_ID == 7) {
            let userRecord = {
              text: 'Hi, ' + res.body.data.FIRST_NAME + ' ' + res.body.data.LAST_NAME,
              time: new Date(),
              author: 'bot',
              options: null
            };
            this.displayContent.push(userRecord);
            if (res.body.data.CONFIRMED == 1) {
              this.sendAuthVerifyOtp(number, "existUser");
            }
            else {
              this.currentStepId = 'unconfirmed_user';
              this.appendAnswersOntoTheScreens();
              this.resendOtp(this.C_ID);
            }
          }
          else {
            this.currentStepId = 'invalid_user';
            this.appendAnswersOntoTheScreens();
            this.currentStepId = 'user_contact';
            this.appendAnswersOntoTheScreens();
            this.enterMsg();
          }
        }
        else {
          let userRecord = {
            text: 'Hi, Guest',
            time: new Date(),
            author: 'bot',
            options: null
          };
          this.displayContent.push(userRecord);
          this.currentStepId = 'change_number';
          this.appendAnswersOntoTheScreens();
          this.enterOption();
        }
      }, err => {
        this.currentStepId = 'incorrect_otp';
        this.appendAnswersOntoTheScreens();
        this.enterOption();
      });
    }
    else {
      this.currentStepId = 'exceeds_otp_limit';
      this.warningMsg = chats[this.currentStepId]['information'];
      this.disableInput = true;
      this.infoHidden = false;
      this.appendAnswersOntoTheScreens();
      setTimeout(() => {
        this.close();
      }, 4000)
    }
  }

  verifyOtp(otp) {
    this.showTime = false;
    clearInterval(this.interval);
    this.isExpiryCompleted = false;
    this.authService.loginByOTP(otp, this.userToken['C_ID'], this.userToken['session_token']).subscribe(res => {
      window.localStorage.setItem(
        'userInfo',
        JSON.stringify({
          userId: res.data.USER_ID,
          C_ID: res.data.C_ID,
          firstName: res.data.FIRST_NAME,
          lastName: res.data.LAST_NAME,
          department: res.data.DEPARTMENT,
          emailId: res.data.EMAIL_ID,
          mobile: res.data.MOBILE,
          emailVerified: res.data.EMAIL_VERIFIED,
          mobileVerified: res.data.MOBILE_VERIFIED,
          roleId: res.data.ROLE_ID,
          roleName: res.data.ROLE_NAME,
          access_token: res.data.TOKEN_DATA.access_token,
          id_token: res.data.TOKEN_DATA.id_token,
          refresh_token: res.data.TOKEN_DATA.refresh_token,
          expiresIn: res.data.TOKEN_DATA.expires_in
        }))
      this.fileTypes = JSON.parse(window.localStorage.getItem('attachmentType'));
      this.complaintForm.controls['USER_ID'].setValue(res.data.USER_ID);
      this.showNotification(res.message, "success");
      this.NAME_OF_THE_USER = res.data.FIRST_NAME + ' ' + res.data.LAST_NAME;
      let userRecord = {
        text: 'Thank you ' + this.NAME_OF_THE_USER + ' :) \n' + 'Now I would like to know about the advertisement you want to register a complaint against.',
        time: new Date(),
        author: 'bot',
        options: null
      };
      this.displayContent.push(userRecord);
      this.currentStepId = 'complaint_desc';
      this.appendAnswersOntoTheScreens();
      this.enterMsg();
    }, err => {
      this.currentStepId = 'incorrect_otp';
      this.appendAnswersOntoTheScreens();
      this.enterOption();
    });
  }

  createBlankComplaint() {
    // this.COMP_ID = "";
    this.thirdPartyService.createBlankComplaint().subscribe(res => {
      this.COMP_ID = res.data.ID;
      this.complaintForm.controls['ID'].setValue(this.COMP_ID);
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  createUser() {
    this.authService.createUserFromChatbot(this.userForm.value).subscribe(res => {
      this.showNotification(res.message, "success");
      if (!!res.data.C_ID) {
        this.C_ID = res.data.C_ID;
        this.currentStepId = 'user_validation_otp';
        this.appendAnswersOntoTheScreens();
        this.timeNotification();
        this.enterMsg();
      }
    }, err => {
      // this.showNotification(err.error.message, "error");
      this.currentStepId = 'error_user_creation';
      this.appendAnswersOntoTheScreens();
      this.currentStepId = 'user_email';
      this.appendAnswersOntoTheScreens();
      this.enterMsg();
    })
  }

  resendOtp(C_ID) {
    this.validationOtpCount++;
    if (this.validationOtpCount < 4) {
      this.authService.resendOtp(C_ID).subscribe(res => {
        this.currentStepId = 'user_validation_otp';
        this.appendAnswersOntoTheScreens();
        this.timeNotification();
        this.enterMsg();
      })
    }
    else {
      this.currentStepId = 'exceeds_otp_limit';
      this.warningMsg = chats[this.currentStepId]['information'];
      this.disableInput = true;
      this.infoHidden = false;
      this.appendAnswersOntoTheScreens();
      setTimeout(() => {
        this.close();
      }, 4000)
    }
  }

  handleUserValidationOtp(event, cameFrom) {
    if (cameFrom == 'option') {
      if (!!event) {
        let userRecord = {
          text: event.name,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        if (event.value == '1') {
          this.resendOtp(this.C_ID);
        }
      }
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.verifyOtpAfterSignup(event);
        this.otp = event;
      }
      else {
        this.disableInput = true;
        this.currentStepId = 'user_validation_otp';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  verifyOtpAfterSignup(otp) {
    this.showTime = false;
    clearInterval(this.interval);
    this.isExpiryCompleted = false;
    this.authService.signUpByOtp(otp, this.C_ID).subscribe(res => {
      this.showNotification(res.body.message, "success")
      this.sendAuthVerifyOtp(this.phone_num, "newUser");
    }, err => {
      this.currentStepId = 'incorrect_verify_otp';
      this.appendAnswersOntoTheScreens();
    });
  }

  handleIncorrectVerifyOtp(event, cameFrom) {
    if (cameFrom == 'option') {
      let userRecord = {
        text: event.name,
        time: new Date(),
        author: 'user',
        options: null
      };
      this.displayContent.push(userRecord);
      if (event.value == 1) {
        this.resendOtp(this.C_ID);
        this.displayContent.forEach((element, index) => {
          if (element.text == this.otp) {
            this.displayContent.splice(index, 1);
          }
        });
      }
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.verifyOtpAfterSignup(event);
      }
      else {
        this.disableInput = true;
        this.currentStepId = 'user_validation_otp';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  sendAuthVerifyOtp(phone, cameFrom) {
    this.authService.getOTPByNumber(phone).subscribe(res => {
      if (res.body.data.USER_CONFIRMED == 1) {
        this.userToken = res.body.data.TOKEN_DATA;
        this.timeNotification();
        if (cameFrom == "existUser") {
          this.currentStepId = 'user_otp';
          this.appendAnswersOntoTheScreens();
          this.enterMsg();
        }
        else {
          this.currentStepId = 'verify_user_confirmation';
          this.appendAnswersOntoTheScreens();
        }
      }else{
        this.notify.showNotification(
          "User is not yet confirmed",
          "top",
          "warning",
          0
        );
      }
    }, err => {
      this.currentStepId = 'user_otp';
      this.appendAnswersOntoTheScreens();
      this.enterMsg();
    });
  }

  handleVerifyUserConfirmation(event, cameFrom) {
    if (cameFrom == 'option') {
      if (!!event) {
        let userRecord = {
          text: event.name,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        if (event.value == '1') {
          this.sendAuthVerifyOtp(this.phone_num, "resend");
        }
      }
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.verifyOtp(event);
        this.otp = event;
      }
      else {
        this.disableInput = true;
        this.currentStepId = 'incorrect_otp';
        this.appendAnswersOntoTheScreens();
        this.enterOption();
      }
    }
  }

  handleComplaintDesc(event, cameFrom) {
    this.createBlankComplaint();
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.currentStepId = 'adv_medium';
        this.enterOption();
        this.complaintForm.controls['BRAND_NAME'].setValue(event);
      }
    }
    this.appendAnswersOntoTheScreens();
  }

  handleAdvMedium(event, cameFrom) {
    if (cameFrom == 'option') {
      let userRecord = {
        text: event.name,
        time: new Date(),
        author: 'user',
        options: null
      };
      this.displayContent.push(userRecord);
      if (event.value == '1') {
        this.currentStepId = 'adv_date_television';
        this.text_message.setValue('DD/MM/YYYY');
        this.enterMsg();
      }
      else if (event.value == '2') {
        this.currentStepId = 'adv_platform';
        this.enterOption();
      }
      else if (event.value == '3') {
        this.currentStepId = 'adv_date_radio';
        this.text_message.setValue('DD/MM/YYYY');
        this.enterMsg();
      }
      else if (event.value == '4') {
        this.currentStepId = 'adv_printed_medium';
        this.enterOption();
      }
      else if (event.value == '5') {
        this.currentStepId = 'type_promotional';
        this.enterOption();
      }
      else if (event.value == '6') {
        this.currentStepId = 'sms_sender';
        this.enterMsg();
      }
      else if (event.value == '7') {
        this.currentStepId = 'hoarding_place';
        this.enterMsg();
      }
      else if (event.value == '8') {
        this.currentStepId = 'package_snapshot';
        this.enterOption();
        this.disableUpload = false;
      }
      else if (event.value == '9') {
        this.currentStepId = 'adv_medium_other';
        this.enterMsg();
      }
      this.complaintForm.controls['ADVERTISEMENT_SOURCE_ID'].setValue(event.ID);
      this.complaintForm.controls['SOURCE_NAME'].setValue(event.name);
    }
    else {
      // userInputHandler
    }
    this.appendAnswersOntoTheScreens();
  }

  handleDigitalMediaDate(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        let currentdate = moment(new Date()).format('yyyy-MM-DD');
        let eventdate = moment(event, "DD/MM/YYYY").format('yyyy-MM-DD');
        if (moment(event, "DD/MM/YYYY").isValid() && event.match(this.datePattern) && new Date(currentdate) >= new Date(eventdate)) {
          this.displayContent.push(userRecord);
          this.currentStepId = 'digital_media_time';
          this.appendAnswersOntoTheScreens();
          this.text_message.setValue('HH:MM:SS');
          this.enterMsg();
          this.complaintForm.controls['DATE'].setValue(moment(event, "DD/MM/YYYY").format('yyyy-MM-DD'));
        }
        else {
          this.currentStepId = 'invalid_date';
          this.appendAnswersOntoTheScreens();
          this.currentStepId = 'digital_media_date';
          this.appendAnswersOntoTheScreens();
        }
      }
      else {
        this.currentStepId = 'digital_media_date';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  handleDigitalMediaTime(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        if (event.match(this.timePattern)) {
          this.displayContent.push(userRecord);
          this.currentStepId = 'have_link';
          this.appendAnswersOntoTheScreens();
          this.enterOption();
          this.complaintForm.controls['TIME'].setValue(event);
        }
        else {
          this.currentStepId = 'invalid_time';
          this.appendAnswersOntoTheScreens();
          this.currentStepId = 'digital_media_time';
          this.appendAnswersOntoTheScreens();
        }
      }
      else {
        this.currentStepId = 'digital_media_time';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  handleAdvDateTelevision(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        let currentdate = moment(new Date()).format('yyyy-MM-DD');
        let eventdate = moment(event, "DD/MM/YYYY").format('yyyy-MM-DD');
        if (moment(event, "DD/MM/YYYY").isValid() && event.match(this.datePattern) && new Date(currentdate) >= new Date(eventdate)) {
          this.displayContent.push(userRecord);
          this.currentStepId = 'adv_channel_tv';
          this.appendAnswersOntoTheScreens();
          this.enterMsg();
          this.complaintForm.controls['DATE'].setValue(moment(event, "DD/MM/YYYY").format('yyyy-MM-DD'));
        }
        else {
          this.currentStepId = 'invalid_date';
          this.appendAnswersOntoTheScreens();
          this.currentStepId = 'adv_date_television';
          this.appendAnswersOntoTheScreens();
        }
      }
      else {
        this.currentStepId = 'adv_date_television';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  handleAdvDateRadio(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        let currentdate = moment(new Date()).format('yyyy-MM-DD');
        let eventdate = moment(event, "DD/MM/YYYY").format('yyyy-MM-DD');
        if (moment(event, "DD/MM/YYYY").isValid() && event.match(this.datePattern) && new Date(currentdate) >= new Date(eventdate)) {
          this.displayContent.push(userRecord);
          this.currentStepId = 'adv_station_radio';
          this.appendAnswersOntoTheScreens();
          this.enterMsg();
          this.complaintForm.controls['DATE'].setValue(moment(event, "DD/MM/YYYY").format('yyyy-MM-DD'));
        }
        else {
          this.currentStepId = 'invalid_date';
          this.appendAnswersOntoTheScreens();
          this.currentStepId = 'adv_date_radio';
          this.appendAnswersOntoTheScreens();
        }
      }
      else {
        this.currentStepId = 'adv_date_radio';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  handleAdvTime(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        if (event.match(this.timePattern)) {
          this.displayContent.push(userRecord);
          this.currentStepId = 'adv_claims';
          this.appendAnswersOntoTheScreens();
          this.enterMsg();
          this.complaintForm.controls['TIME'].setValue(event);
        }
        else {
          this.currentStepId = 'invalid_time';
          this.appendAnswersOntoTheScreens();
          this.currentStepId = 'adv_time';
          this.appendAnswersOntoTheScreens();
        }
      }
      else {
        this.currentStepId = 'adv_time';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  handleAdvChannelTv(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.currentStepId = 'digital_media_time';
        this.text_message.setValue('HH:MM:SS');
        this.enterMsg();
        this.complaintForm.controls['SOURCE_PLACE'].setValue(event);
      }
    }
    this.appendAnswersOntoTheScreens();
  }

  handleAdvStationRadio(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.currentStepId = 'digital_media_time';
        this.text_message.setValue('HH:MM:SS');
        this.enterMsg();
        this.complaintForm.controls['SOURCE_PLACE'].setValue(event);
      }
    }
    this.appendAnswersOntoTheScreens();
  }

  handleAdvPlatform(event, cameFrom) {
    if (cameFrom == 'option') {
      let userRecord = {
        text: event.name,
        time: new Date(),
        author: 'user',
        options: null
      };
      this.displayContent.push(userRecord);
      if (event.value != '9') {
        this.currentStepId = 'digital_media_date';
        this.text_message.setValue('DD/MM/YYYY');
        this.enterMsg();
      }
      else if (event.value == '9') {
        this.currentStepId = 'adv_platform_other';
        this.enterMsg();
      }
      this.complaintForm.controls['PLATFORM_ID'].setValue(event.ID);
    }
    else {
      // userInputHandler
    }
    this.appendAnswersOntoTheScreens();
  }

  handleAdvPlatformOther(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.currentStepId = 'digital_media_date';
        this.text_message.setValue('DD/MM/YYYY');
        this.enterMsg();
        this.complaintForm.controls['SOURCE_PLACE'].setValue(event);
      }
    }
    this.appendAnswersOntoTheScreens();
  }

  handleAdvMediumOther(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.currentStepId = 'adv_medium_other_date';
        this.text_message.setValue('DD/MM/YYYY');
        this.enterMsg();
        this.complaintForm.controls['SOURCE_NAME'].setValue(event);
      }
    }
    this.appendAnswersOntoTheScreens();
  }

  handleAdvMediumOtherDate(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        let currentdate = moment(new Date()).format('yyyy-MM-DD');
        let eventdate = moment(event, "DD/MM/YYYY").format('yyyy-MM-DD');
        if (moment(event, "DD/MM/YYYY").isValid() && event.match(this.datePattern) && new Date(currentdate) >= new Date(eventdate)) {
          this.displayContent.push(userRecord);
          this.currentStepId = 'digital_media_time';
          this.appendAnswersOntoTheScreens();
          this.text_message.setValue('HH:MM:SS');
          this.enterMsg();
          this.complaintForm.controls['DATE'].setValue(moment(event, "DD/MM/YYYY").format('yyyy-MM-DD'));
        }
        else {
          this.currentStepId = 'invalid_date';
          this.appendAnswersOntoTheScreens();
          this.currentStepId = 'adv_medium_other_date';
          this.appendAnswersOntoTheScreens();
        }
      }
      else {
        this.currentStepId = 'adv_medium_other_date';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  handleHaveLink(event, cameFrom) {
    if (cameFrom == 'option') {
      let userRecord = {
        text: event.name,
        time: new Date(),
        author: 'user',
        options: null
      };
      this.displayContent.push(userRecord);
      if (event.value == '1') {
        this.currentStepId = 'submit_link';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
      else {
        this.currentStepId = 'have_documents';
        this.appendAnswersOntoTheScreens();
        this.enterOption();
      }
    }
    else {
      // userInputHandler
    }
  }

  handleSubmitLink(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        if (event.length > 21) {
          this.longText = ' ...'
          let userRecord = {
            text: event.slice(0, 20) + this.longText,
            time: new Date(),
            author: 'user',
            options: null
          };
          this.displayContent.push(userRecord);
        }
        else {
          let userRecord = {
            text: event,
            time: new Date(),
            author: 'user',
            options: null
          };
          this.displayContent.push(userRecord);
        }
        this.currentStepId = 'have_documents';
        this.enterOption();
        this.complaintForm.controls['SOURCE_URL'].setValue(event);
      }
    }
    this.appendAnswersOntoTheScreens();
  }

  handleHaveDocuments(event, cameFrom) {
    if (cameFrom == 'option') {
      let userRecord = {
        text: event.name,
        time: new Date(),
        author: 'user',
        options: null
      };
      this.displayContent.push(userRecord);
      if (event.value == '1') {
        this.currentStepId = 'submit_documents';
        this.appendAnswersOntoTheScreens();
        this.enterOption();
        this.disableUpload = false;
      }
      else {
        this.currentStepId = 'digital_media_desc';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
    else {
      // userInputHandler
    }
  }

  async handleSubmitDocuments(event, cameFrom, stepId) {
    if (cameFrom == 'doc') {
      this.disableInput = true;
      if (!!event) {
        let returnVal = await this.attachFile(event, stepId);
        if (returnVal[0].success == true) {
          this.currentStepId = 'adv_claims';
          this.appendAnswersOntoTheScreens();
          this.enterMsg();
          this.disableUpload = true;
        }
        else {
          this.currentStepId = returnVal[0].current;
          this.appendAnswersOntoTheScreens();
        }
      }
    }
    else {
      if (cameFrom == 'option') {
        // userOptionHandler
      }
      else {
        // userInputHandler
      }
    }
  }

  handleDigitalMediaDesc(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.currentStepId = 'adv_claims';
        this.enterMsg();
        this.complaintForm.controls['ADVERTISEMENT_DESCRIPTION'].setValue(event);
      }
    }
    this.appendAnswersOntoTheScreens();
  }

  handleAdvPrintedMedium(event, cameFrom) {
    if (cameFrom == 'option') {
      let userRecord = {
        text: event.name,
        time: new Date(),
        author: 'user',
        options: null
      };
      this.displayContent.push(userRecord);
      this.currentStepId = 'adv_printed_edition';
      this.enterMsg();
      this.complaintForm.controls['PRINT_SOURCE_ID'].setValue(event.ID);
    }
    else {
      // userInputHandler
    }
    this.appendAnswersOntoTheScreens();
  }

  handleAdvPrintedEdition(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.currentStepId = 'adv_publication_date';
        this.text_message.setValue('DD/MM/YYYY');
        this.enterMsg();
        this.complaintForm.controls['SOURCE_NAME'].setValue(event);
      }
    }
    this.appendAnswersOntoTheScreens();
  }

  handleAdvPublicationDate(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        let currentdate = moment(new Date()).format('yyyy-MM-DD');
        let eventdate = moment(event, "DD/MM/YYYY").format('yyyy-MM-DD');
        if (moment(event, "DD/MM/YYYY").isValid() && event.match(this.datePattern) && new Date(currentdate) >= new Date(eventdate)) {
          this.displayContent.push(userRecord);
          this.currentStepId = 'digital_media_time';
          this.appendAnswersOntoTheScreens();
          this.text_message.setValue('HH:MM:SS');
          this.enterMsg();
          this.complaintForm.controls['DATE'].setValue(moment(event, "DD/MM/YYYY").format('yyyy-MM-DD'));
        }
        else {
          this.currentStepId = 'invalid_date';
          this.appendAnswersOntoTheScreens();
          this.currentStepId = 'adv_publication_date';
          this.appendAnswersOntoTheScreens();
        }
      }
      else {
        this.currentStepId = 'adv_publication_date';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  handleTypePromotional(event, cameFrom) {
    if (cameFrom == 'option') {
      let userRecord = {
        text: event.name,
        time: new Date(),
        author: 'user',
        options: null
      };
      this.displayContent.push(userRecord);
      this.currentStepId = 'place_promotional_material';
      this.enterMsg();
      this.complaintForm.controls['P_M_SOURCE_ID'].setValue(event.ID);
    }
    else {
      // userInputHandler
    }
    this.appendAnswersOntoTheScreens();
  }

  handlePlacePromotionalMaterial(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.currentStepId = 'date_promotional_material';
        this.text_message.setValue('DD/MM/YYYY');
        this.enterMsg();
        this.complaintForm.controls['SOURCE_PLACE'].setValue(event);
      }
    }
    this.appendAnswersOntoTheScreens();
  }

  handleDatePromotionalMaterial(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        let currentdate = moment(new Date()).format('yyyy-MM-DD');
        let eventdate = moment(event, "DD/MM/YYYY").format('yyyy-MM-DD');
        if (moment(event, "DD/MM/YYYY").isValid() && event.match(this.datePattern) && new Date(currentdate) >= new Date(eventdate)) {
          this.displayContent.push(userRecord);
          this.currentStepId = 'snapshot_promotional_material';
          this.appendAnswersOntoTheScreens();
          this.enterOption();
          this.complaintForm.controls['DATE'].setValue(moment(event, "DD/MM/YYYY").format('yyyy-MM-DD'));
          this.disableUpload = false;
        }
        else {
          this.currentStepId = 'invalid_date';
          this.appendAnswersOntoTheScreens();
          this.currentStepId = 'date_promotional_material';
          this.appendAnswersOntoTheScreens();
        }
      }
      else {
        this.currentStepId = 'date_promotional_material';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  async handleSnapshotPromotionalMaterial(event, cameFrom, stepId) {
    if (cameFrom == 'doc') {
      this.disableInput = true;
      if (!!event) {
        let returnVal = await this.attachFile(event, stepId);
        if (returnVal[0].success == true) {
          this.currentStepId = 'adv_time';
          this.appendAnswersOntoTheScreens();
          this.text_message.setValue('HH:MM:SS');
          this.enterMsg();
          this.disableUpload = true;
        }
        else {
          this.currentStepId = returnVal[0].current;
          this.appendAnswersOntoTheScreens();
        }
      }
    }
    else {
      if (cameFrom == 'option') {
        // userOptionHandler
      }
      else {
        // userInputHandler
      }
    }
  }

  handleSmsSender(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.currentStepId = 'sms_screenshot';
        this.enterOption();
        this.complaintForm.controls['SOURCE_NAME'].setValue(event);
        this.disableUpload = false;
      }
    }
    this.appendAnswersOntoTheScreens();
  }

  async handleSmsScreenshot(event, cameFrom, stepId) {
    if (cameFrom == 'doc') {
      this.disableInput = true;
      if (!!event) {
        let returnVal = await this.attachFile(event, stepId);
        if (returnVal[0].success == true) {
          this.currentStepId = 'sms_details';
          this.appendAnswersOntoTheScreens();
          this.text_message.setValue('DD/MM/YYYY');
          this.enterMsg();
          this.disableUpload = true;
        }
        else {
          this.currentStepId = returnVal[0].current;
          this.appendAnswersOntoTheScreens();
        }
      }
    }
    else {
      if (cameFrom == 'option') {
        // userOptionHandler
      }
      else {
        // userInputHandler
      }
    }
  }

  handleSmsDetails(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        let currentdate = moment(new Date()).format('yyyy-MM-DD');
        let eventdate = moment(event, "DD/MM/YYYY").format('yyyy-MM-DD');
        if (moment(event, "DD/MM/YYYY").isValid() && event.match(this.datePattern) && new Date(currentdate) >= new Date(eventdate)) {
          this.displayContent.push(userRecord);
          this.currentStepId = 'adv_time';
          this.appendAnswersOntoTheScreens();
          this.text_message.setValue('HH:MM:SS');
          this.enterMsg();
          this.complaintForm.controls['DATE'].setValue(moment(event, "DD/MM/YYYY").format('yyyy-MM-DD'));
        }
        else {
          this.currentStepId = 'invalid_date';
          this.appendAnswersOntoTheScreens();
          this.currentStepId = 'sms_details';
          this.appendAnswersOntoTheScreens();
        }
      }
      else {
        this.currentStepId = 'sms_details';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  handleHoardingPlace(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        this.displayContent.push(userRecord);
        this.currentStepId = 'hoarding_snapshot';
        this.enterOption();
        this.complaintForm.controls['SOURCE_PLACE'].setValue(event);
        this.disableUpload = false;
      }
    }
    this.appendAnswersOntoTheScreens();
  }

  async handleHoardingSnapshot(event, cameFrom, stepId) {
    if (cameFrom == 'doc') {
      this.disableInput = true;
      if (!!event) {
        let returnVal = await this.attachFile(event, stepId);
        if (returnVal[0].success == true) {
          this.currentStepId = 'hoarding_date';
          this.appendAnswersOntoTheScreens();
          this.text_message.setValue('DD/MM/YYYY');
          this.enterMsg();
          this.disableUpload = true;
        }
        else {
          this.currentStepId = returnVal[0].current;
          this.appendAnswersOntoTheScreens();
        }
      }
    }
    else {
      if (cameFrom == 'option') {
        // userOptionHandler
      }
      else {
        // userInputHandler
      }
    }
  }

  handleHoardingDate(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        let currentdate = moment(new Date()).format('yyyy-MM-DD');
        let eventdate = moment(event, "DD/MM/YYYY").format('yyyy-MM-DD');
        if (moment(event, "DD/MM/YYYY").isValid() && event.match(this.datePattern) && new Date(currentdate) >= new Date(eventdate)) {
          this.displayContent.push(userRecord);
          this.currentStepId = 'adv_time';
          this.appendAnswersOntoTheScreens();
          this.text_message.setValue('HH:MM:SS');
          this.enterMsg();
          this.complaintForm.controls['DATE'].setValue(moment(event, "DD/MM/YYYY").format('yyyy-MM-DD'));
        }
        else {
          this.currentStepId = 'invalid_date';
          this.appendAnswersOntoTheScreens();
          this.currentStepId = 'hoarding_date';
          this.appendAnswersOntoTheScreens();
        }
      }
      else {
        this.currentStepId = 'hoarding_date';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  async handlePackageSnapshot(event, cameFrom, stepId) {
    if (cameFrom == 'doc') {
      this.disableInput = true;
      if (!!event) {
        let returnVal = await this.attachFile(event, stepId);
        if (returnVal[0].success == true) {
          this.currentStepId = 'package_mfg_details';
          this.appendAnswersOntoTheScreens();
          this.text_message.setValue('DD/MM/YYYY');
          this.enterMsg();
          this.disableUpload = true;
        }
        else {
          this.currentStepId = returnVal[0].current;
          this.appendAnswersOntoTheScreens();
        }
      }
    }
    else {
      if (cameFrom == 'option') {
        // userOptionHandler
      }
      else {
        // userInputHandler
      }
    }
  }

  handlePackageMfgDetails(event, cameFrom) {
    if (cameFrom == 'option') {
      // userOptionHandler
    }
    else {
      if (event) {
        let userRecord = {
          text: event,
          time: new Date(),
          author: 'user',
          options: null
        };
        let currentdate = moment(new Date()).format('yyyy-MM-DD');
        let eventdate = moment(event, "DD/MM/YYYY").format('yyyy-MM-DD');
        if (moment(event, "DD/MM/YYYY").isValid() && event.match(this.datePattern) && new Date(currentdate) >= new Date(eventdate)) {
          this.displayContent.push(userRecord);
          this.currentStepId = 'adv_time';
          this.appendAnswersOntoTheScreens();
          this.text_message.setValue('HH:MM:SS');
          this.enterMsg();
          this.complaintForm.controls['DATE'].setValue(moment(event, "DD/MM/YYYY").format('yyyy-MM-DD'));
        }
        else {
          this.currentStepId = 'invalid_date';
          this.appendAnswersOntoTheScreens();
          this.currentStepId = 'package_mfg_details';
          this.appendAnswersOntoTheScreens();
        }
      }
      else {
        this.currentStepId = 'package_mfg_details';
        this.appendAnswersOntoTheScreens();
        this.enterMsg();
      }
    }
  }

  skipOption(message, type) {
    if (message.step == 'user_profession') {
      this.currentStepId = 'user_organization';
      this.appendAnswersOntoTheScreens();
      this.enterMsg();
    }
    else if (message.step == 'user_organization') {
      this.createUser();
    }
    else if (message.step == 'user_email' && this.skipMessage == false) {
      this.skipMessage = true;
    }
    else if (message.step == 'user_email' && this.skipMessage == true && type == 'back') {
      this.skipMessage = false;
      this.currentStepId = 'user_email';
    }
    else if (message.step == 'user_email' && this.skipMessage == true && type == 'skip') {
      this.skipMessage = false;
      this.currentStepId = 'user_postal';
      this.appendAnswersOntoTheScreens();
      this.enterMsg();
    }
  }

  getMessage() {
    this.askQuestion();
  }

  ngAfterViewChecked() {
    this.scrollToBottom();
  }

  ngAfterContentChecked() {
    if (this.panelOpenState == true) {
      this.hiddenIntroMsg = true;
    }
  }

  @HostListener('mouseenter') onMouseEnter() {
    this.hover = true;
  }

  close() {
    if (!this.accordion) { return }
    this.accordion.closeAll();
    this.hidden = !this.hidden
    this.panelOpenState = !this.panelOpenState
    this.currentStepId = 'greeting';
    this.currentStep = {};
    this.currentStep = [];
    this.displayContent = [];
    this.text_message.reset();
    this.userForm.reset();
    this.otpCount = 0;
    this.validationOtpCount = 0;
    this.hiddenIntroMsg = true;
    clearInterval(this.interval);
    this.showTime = false;
    this.isExpiryCompleted = false;
    setTimeout(() => {
      this.hiddenIntroMsg = false
    }, 5000)
  }

  closeIntro() {
    this.hiddenIntroMsg = true;
    // setTimeout(() => {
    //   this.hiddenIntroMsg = false
    // }, 5000)
  }

  openPanel() {
    this.hiddenIntroMsg = true;
    this.panelOpenState = !this.panelOpenState;
    if (this.panelOpenState != true) {
      setTimeout(() => {
        this.hiddenIntroMsg = false
      }, 5000)
    }
    this.showObjectiveMessage = false;
    this.showObjectiveMessage1 = false;
    this.showObjectiveMessage2 = false;
    this.showObjectiveMessage3 = false;
    this.showMediaMessage = false;
    this.hidden = !this.hidden
    this.currentStepId = 'greeting';
    this.currentStep = {};
    this.currentStep = [];
    this.displayContent = [];
    this.text_message.reset();
    this.userForm.reset();
    this.otpCount = 0;
    this.validationOtpCount = 0;
    clearInterval(this.interval);
    this.showTime = false;
    this.isExpiryCompleted = false;
  }

  scrollToBottom(): void {
    try {
      this.myScrollContainer.nativeElement.scrollTop = this.myScrollContainer.nativeElement.scrollHeight;
    } catch (err) { }
  }

  async attachFile(event: any, stepId) {
    if (!this.uploadService.validateFileExtension(event)) {
      this.currentStepId = 'document_format_error';
      this.appendAnswersOntoTheScreens();
      return [{ success: false, current: stepId }];
    }
    let sizeOfAllFiles = this.uploadService.getTotalFileSize(event.target.files);
    let totalFileSelected = event.target.files.length;
    if (totalFileSelected < 11 && sizeOfAllFiles <= 36700160) {
      for (let i = 0; i <= event.target.files.length - 1; i++) {
        let selectedFile = event.target.files[i];
        let userRecord = {
          text: selectedFile['name'],
          time: new Date(),
          author: 'user',
          options: null,
          type: (event.target.files[i]['type']).split('/')[0]
        };
        this.displayContent.push(userRecord);
        let tempObjforGetsigned = {
          id: this.COMP_ID,
          section: 'chatbot',
          filename: selectedFile['name'],
          type: selectedFile['type']
        }
        await this.uploadService.getSignedUrlForChatbot(tempObjforGetsigned).then(async (res) => {
          if (res && res['data'] && res['data']['SIGNED_URL']) {
            let tempObjForUpload = {
              url: res['data']['SIGNED_URL'],
              file: selectedFile
            }
            try {
              await this.uploadSignedFile(tempObjForUpload, 0);
              let saveBody = {
                ID: this.COMP_ID,
                KEY: res['data']['PATH'],
                SOURCE_NAME: selectedFile['name'],
                TYPE: selectedFile['type'],
                SIZE: selectedFile['size'],
                TAB: 'complainant'
              }
              await this.saveDocumentToDB(saveBody)
            } catch (error) {
              // this.handleError(error);
              return;
            }

          }
        })
          .catch((err) => {
            // this.handleError(err);
          });
      }
      return [{ success: true, current: stepId }];
    } else {
      this.currentStepId = 'document_size_error';
      this.appendAnswersOntoTheScreens();
      return [{ success: false, current: stepId }];
    }
  }

  async saveDocumentToDB(tempObj) {
    await this.cs.saveChatbotFileToDB(tempObj)
      .then((data) => {
        return data;
      })
      .catch(err => {
      })
  }

  async uploadSignedFile(tempObj, i) {
    let progressInit = 0;
    await this.uploadService.uploadFilethroughSignedUrl(tempObj, progressInit => {
    }).catch((err) => {
      // this.handleError(err);
    });
  }

  // handleError(err: any) {
  //   this.notify.showNotification(
  //     err.error.message,
  //     "top",
  //     (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
  //     err.error.status
  //   );
  // }

}