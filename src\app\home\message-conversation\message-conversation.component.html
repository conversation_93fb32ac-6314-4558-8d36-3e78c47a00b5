<div fxLayout="row">
    <div fxFlex="100%">
        <mat-tab-group class="right-screen" style="width: 100%;" (selectedTabChange)="onTabChanged($event)">
            <mat-tab class="email-tab">
                <div class="tab-head">
                    <ng-template mat-tab-label class="email-ng-temp">
                        <div fxLayout="row" fxLayoutGap="9%">
                            <div><img src="../../assets/images/mail.png"></div>
                            <div>Emails</div>
                        </div>
                    </ng-template>
                </div>
                <div class="email-screen" fxLayout="column">
                    <div class="detail-header-container" fxLayout="column" fxLayoutAlign="start" fxLayoutGap="5px">
                        <div>
                            <img class="hide-icon" src="../assets/images/manage-icon.svg" />&nbsp;
                            <span class="black-text title" title="{{brand_name + ' ' + comp_detail}}">
                                {{brand_name | slice:0: 20}}{{brand_name.length>21? '..':''}} {{comp_detail | slice:0:
                                75}}{{comp_detail.length>76? '..':''}}</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="2%">
                            <div *ngIf="data.details.REGISTERED == 1">
                                <span class="grey-text">Case ID :&nbsp;</span>
                                <span class="small black-text">{{case_Id}}</span>
                            </div>
                            <div>
                                <span class="grey-text">Company :&nbsp;</span>
                                <span class="small black-text" title="{{company_name}}">{{company_name| slice:0:
                                    10}}{{company_name.length>11? '..':''}}</span>
                            </div>
                            <div>
                                <span class="grey-text">Brand :&nbsp;</span>
                                <span class="small black-text" title="{{brand_name}}">{{brand_name| slice:0:
                                    10}}{{brand_name.length>11? '..':''}}</span>
                            </div>
                        </div>
                    </div>

                    <div class="divider-container">
                        <mat-divider></mat-divider>
                    </div>

                    <div fxLayout="row">
                        <div class="email-inbox" style="width: 36%;">
                            <div fxLayout="column" fxLayoutGap="10px">
                                <div class="inbox-body">
                                    <div fxLayout="column" fxLayoutGap="10px" *ngIf="assigneeDetails.length != 0">
                                        <div class="individuals-chat">
                                            <mat-nav-list class="chat-list">
                                                <div class="advertiser-list scroll">
                                                    <mat-list-item class="chat-item" (click)='viewAssigneeMails(assg, i)'
                                                        *ngFor="let assg of assigneeDetails; let i=index;" [ngClass]="{'chat-item-selected': i + 1 == assgIndex}">
                                                        <div fxLayout="row" fxLayoutGap="10px" style="width: 100%"
                                                            class="chat-item-container">
                                                            <div class="flex" style="width: 15%;">
                                                                <div class="blue-icon flex">
                                                                    <!-- <div class="blue-icon flex" style="height: 26px;width: 26px;"> -->
                                                                    <span class="small">{{getShortName(assg)}}</span>
                                                                </div>
                                                            </div>
                                                            <div class="chat-msg" fxLayout="column" style="width: 70%;">
                                                                <span class="black-text">{{assg.FIRST_NAME}}
                                                                    {{assg.LAST_NAME}}</span>
                                                                <!-- <span class="grey-text italic">{{ind.text}}</span> -->
                                                            </div>
                                                            <div class="flex" fxLayout="row" style="width: 10%;">
                                                                <div class="flex" style="margin: auto;">
                                                                    <img src="../assets/images/Chat_Arrow-icon.svg">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <mat-divider [inset]="true" class="list-divider"></mat-divider>
                                                    </mat-list-item>
                                                </div>
                                            </mat-nav-list>
                                        </div>
                                    </div>
                                    <div fxLayout="column" fxLayoutGap="10px" *ngIf="userDetails.length != 0">
                                        <div class="individuals-chat">
                                            <mat-nav-list class="chat-list">
                                                <div class="advertiser-list scroll">
                                                    <mat-list-item class="chat-item" (click)='viewMails(user, i)'
                                                        *ngFor="let user of userDetails; let i=index;" [ngClass]="{'chat-item-selected': i + 1 == userIndex}">
                                                        <div fxLayout="row" fxLayoutGap="10px" style="width: 100%"
                                                            class="chat-item-container">
                                                            <div class="flex" style="width: 15%;">
                                                                <div class="blue-icon flex" *ngIf="user.ID != 0">
                                                                    <!-- <div class="blue-icon flex" style="height: 26px;width: 26px;"> -->
                                                                    <span class="small">{{getShortName(user)}}</span>
                                                                </div>
                                                                <div class="blue-icon flex" *ngIf="user.ID == 0">
                                                                    <!-- <div class="blue-icon flex" style="height: 26px;width: 26px;"> -->
                                                                    <span class="small">?</span>
                                                                </div>
                                                            </div>
                                                            <div class="chat-msg" fxLayout="column" style="width: 70%;" *ngIf="user.ID != 0">
                                                                <span class="black-text">{{user.FIRST_NAME}} {{user.LAST_NAME}}</span>
                                                                <!-- <span class="grey-text italic">{{ind.text}}</span> -->
                                                            </div>
                                                            <div class="chat-msg" fxLayout="column" style="width: 70%;" *ngIf="user.ID == 0">
                                                                <span class="large black-text">{{user.EMAIL_ID}}</span>
                                                                <!-- <span class="grey-text italic">{{ind.text}}</span> -->
                                                            </div>
                                                            <div class="flex" fxLayout="row" style="width: 10%;">
                                                                <div class="flex" style="margin: auto;">
                                                                    <img src="../assets/images/Chat_Arrow-icon.svg">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <mat-divider [inset]="true" class="list-divider"></mat-divider>
                                                    </mat-list-item>
                                                </div>
                                            </mat-nav-list>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <mat-divider vertical style="height: calc(100% - 20px)"></mat-divider>

                        <div class="email-body" fxLayout="column">
                            <div fxLayout="row">
                                <div class="body-title" fxLayout="column">
                                    <div>
                                        <p class="title black-text">{{sender_name}}</p>
                                    </div>
                                    <!-- <div fxLayout="row" *ngIf="allAdvMembers" class="members">
                                        <span class="grey-text">{{advertiserDetails.length}} members</span>&nbsp;
                                        <div>
                                            <img src="../assets/images/Down_Arrow.svg">
                                        </div>
                                    </div>
                                    <div fxLayout="row" *ngIf="allCompMailMembers" class="members">
                                        <span class="grey-text">{{companyEmails.length}} members</span>&nbsp;
                                        <div>
                                            <img src="../assets/images/Down_Arrow.svg">
                                        </div>
                                    </div> -->
                                    <div fxLayout="row" class="members">
                                    </div>
                                </div>
                                <span style=" flex: 1 1 auto;"></span>
                                <div class="refresh-container flex">
                                    <button mat-button class="refresh-btn title flex" style="vertical-align: middle;" (click)="refreshMails()">
                                        Refresh&nbsp;<img
                                            style="vertical-align: middle; position: relative; bottom: 1px; left: 6px; padding-right: 6px;"
                                            src="../../../assets/images/Refresh-icon.svg">
                                    </button>
                                    <!-- <button mat-button class="refresh-btn title flex" style="vertical-align: middle;"
                                        *ngIf="allAdvMembers || allCompMailMembers" (click)="refreshAllMails()">
                                        Refresh&nbsp;<img
                                            style="vertical-align: middle; position: relative; bottom: 1px; left: 6px; padding-right: 6px;"
                                            src="../../../assets/images/Refresh-icon.svg">
                                    </button> -->
                                </div>
                            </div>
                            <div class="body-box" fxLayout="column">
                                <div class="emails-header" fxLayout="row">
                                    <div>
                                        <p class="title black-text" style="padding-top: 10px;">Emails</p>
                                    </div>
                                    <span style=" flex: 1 1 auto;"></span>
                                    <div fxLayoutAlign="end center">
                                        <button mat-button class="create-btn title flex" [disabled]="((userData.roleId == 4 || userData.roleId == 7 || userData.roleId == 8) && (assigneeDetails.length == 0 && userDetails.length == 0))"
                                            (click)="sendEmail()">Create email</button>
                                    </div>
                                </div>
                                <div class="content-box email-container scroll" fxLayout="column" fxLayoutGap="10px">
                                    <div *ngIf="mailsSent.length == 0" class="no-mails">
                                        No email yet
                                    </div>
                                    <div class="white-box" *ngFor="let mail of mailsSent">
                                        <div class="content-box" fxLayout="column" fxLayoutGap="10px">
                                            <div>
                                                <p class="small black-text">Subject : {{mail.TITLE}}</p>
                                            </div>
                                            <div fxLayout="row">
                                                <div *ngIf="mail.FROM_ASCI == 0" fxFlex="65%">
                                                    <p class="small blue-text" fxLayout="row"><span fxFlex="7%">To : </span><span class="ellipsis">{{mail.TO_EMAIL_ID}}</span></p>
                                                </div>
                                                <div *ngIf="mail.FROM_ASCI == 1" fxFlex="65%">
                                                    <p class="small blue-text">From : {{sender_mail}}</p>
                                                </div>
                                                <span fxFlex="5%"></span>
                                                <div fxFlex="30%">
                                                    <p class="small grey-text">{{mail.SENT_DATE |
                                                        date:'dd/MM/yyyy'}},&nbsp;{{mail.SENT_DATE | date:'EEE'}},&nbsp;{{mail.SENT_DATE | date:'h:mm a'}}</p>
                                                </div>
                                            </div>
                                            <div fxLayout="row" *ngIf="mail.CC.length != 0">
                                                <div fxFlex="80%">
                                                    <p class="small blue-text" fxLayout="row"><span fxFlex="7%">Cc : </span><span class="ellipsis1">{{mail.CC}}</span></p>
                                                </div>
                                            </div>
                                            <div>
                                                <p class="msg-text1 black-text" [innerHTML]="safeHTML(mail.BODY)">{{mail.BODY}}</p>
                                            </div>
                                            <div fxLayout="column" fxLayoutGap="5px" *ngIf="mail.FROM_ASCI == 1">
                                                <div>
                                                    <img src="../assets/images/logo.png" style="height: 25px;">
                                                </div>
                                                <p class="small grey-text">402, A Wing, Aurus Chambers, S.S. Amrutwar Marg, Worli, Mumbai - 400013</p>
                                                <div>
                                                    <img src="../assets/images/signature-promotion.png" style="height: 40px; cursor: pointer;" (click)="viewAcademyDetails()">
                                                </div>
                                            </div>
                                            <div *ngIf="mail.ATTACHMENTS.length != 0" fxLayout="column">
                                                <div fxLayout="row" *ngFor="let doc of mail.ATTACHMENTS">
                                                    <img src="../../../assets/images/link.svg"
                                                        style="margin-right: 3px;">
                                                    <div class="media-container">
                                                        <a (click)="openNewTab(doc.ATTACHMENT_SOURCE)"
                                                            class="media-anchor">
                                                            {{doc.ATTACHMENT_NAME}}
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </mat-tab>
            <mat-tab class="message-tab">
                <div class="tab-head">
                    <ng-template mat-tab-label class="message-ng-temp">
                        <div fxLayout="row" fxLayoutGap="6%">
                            <div><img src="../../../assets/images/msg-s.png"></div>
                            <div class="flex" fxLayout="row" fxLayoutGap="5px">
                                <div>Messages</div>
                                <!-- <div class="red-text tab flex">{{messages_count | number : '2.0-0'}}</div> -->
                            </div>
                        </div>
                    </ng-template>
                </div>
                <div class="email-screen" fxLayout="column">
                    <div class="detail-header-container" fxLayout="column" fxLayoutAlign="start" fxLayoutGap="5px">
                        <div>
                            <img class="hide-icon" src="../assets/images/manage-icon.svg" />&nbsp;
                            <span class="black-text title" title="{{brand_name + ' ' + comp_detail}}">
                                {{brand_name | slice:0: 20}}{{brand_name.length>21? '..':''}} {{comp_detail | slice:0:
                                75}}{{comp_detail.length>76? '..':''}}</span>
                        </div>
                        <div fxLayout="row" fxLayoutGap="2%">
                            <div *ngIf="data.details.REGISTERED == 1">
                                <span class="grey-text">Case ID :&nbsp;</span>
                                <span class="small black-text">{{case_Id}}</span>
                            </div>
                            <div>
                                <span class="grey-text">Company :&nbsp;</span>
                                <span class="small black-text" title="{{company_name}}">{{company_name| slice:0:
                                    10}}{{company_name.length>11? '..':''}}</span>
                            </div>
                            <div>
                                <span class="grey-text">Brand :&nbsp;</span>
                                <span class="small black-text" title="{{brand_name}}">{{brand_name| slice:0:
                                    10}}{{brand_name.length>11? '..':''}}</span>
                            </div>
                        </div>
                    </div>

                    <div class="divider-container">
                        <mat-divider></mat-divider>
                    </div>

                    <div fxLayout="row">
                        <div class="email-inbox" style="width: 36%;">
                            <div fxLayout="column" fxLayoutGap="10px">
                                <div class="inbox-body">
                                    <div fxLayout="column" fxLayoutGap="10px" *ngIf="userDetails.length != 0">
                                        <div class="individuals-chat">
                                            <mat-nav-list class="chat-list">
                                                <div class="advertiser-list scroll">
                                                    <mat-list-item class="chat-item" (click)='viewSMS(user, i)'
                                                        *ngFor="let user of userDetails; let i=index;" [ngClass]="{'chat-item-selected': i + 1 == userIndex}">
                                                        <div fxLayout="row" fxLayoutGap="10px" style="width: 100%"
                                                            class="chat-item-container">
                                                            <div class="flex" style="width: 15%;">
                                                                <div class="blue-icon flex">
                                                                    <!-- <div class="blue-icon flex" style="height: 26px;width: 26px;"> -->
                                                                    <span class="small">{{getShortName(user)}}</span>
                                                                </div>
                                                            </div>
                                                            <div class="chat-msg" fxLayout="column" style="width: 70%;">
                                                                <span class="black-text">{{user.FIRST_NAME}}
                                                                    {{user.LAST_NAME}}</span>
                                                                <!-- <span class="grey-text italic">{{ind.text}}</span> -->
                                                            </div>
                                                            <div class="flex" fxLayout="row" style="width: 10%;">
                                                                <div class="flex" style="margin: auto;">
                                                                    <img src="../assets/images/Chat_Arrow-icon.svg">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <mat-divider [inset]="true" class="list-divider"></mat-divider>
                                                    </mat-list-item>
                                                </div>
                                            </mat-nav-list>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <mat-divider vertical style="height: calc(100% - 20px)"></mat-divider>

                        <div class="email-body" fxLayout="column">
                            <div fxLayout="row">
                                <div class="body-title" fxLayout="column">
                                    <div>
                                        <p class="title black-text">{{sender_name}}</p>
                                    </div>
                                    <!-- <div fxLayout="row" *ngIf="value.name == 'All company members'" class="members">
                                <span class="grey-text">{{messages_count | number : '2.0-0'}} members</span>&nbsp;
                                <div>
                                    <img src="../assets/images/Down_Arrow.svg">
                                </div>
                            </div> -->
                                    <div fxLayout="row" class="members">
                                    </div>
                                </div>
                                <span style=" flex: 1 1 auto;"></span>
                                <div class="refresh-container flex">
                                    <button mat-button class="refresh-btn title flex" style="padding-bottom: 3px;"
                                        (click)="refreshChat()">
                                        Refresh chat&nbsp;<img src="../../../assets/images/Refresh-icon.svg">
                                    </button>
                                </div>
                            </div>
                            <div class="body-box" fxLayout="column">
                                <div class="content-box chat-container scroll" fxLayout="column" fxLayoutGap="10px">
                                    <div *ngIf="SMSSent.length == 0" class="no-mails">
                                        No message yet
                                    </div>
                                    <div *ngFor="let sms of SMSSent">
                                        <div fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="5px"
                                            style="margin-top: 10px; margin-bottom: 10px;">
                                            <mat-divider fxFlex="1 0"></mat-divider>
                                            <div fxLayout="row">
                                                <span class="grey-text">{{sms.SENT_DATE |
                                                    date:'dd/MM/yyyy'}},&nbsp;{{sms.SENT_DATE | date:'EEE'}}</span>
                                            </div>
                                            <mat-divider fxFlex="1 0"></mat-divider>
                                        </div>

                                        <div fxLayout="column" fxLayoutGap="10px" *ngIf="sms.FROM_ASCI == 1">
                                            <div fxLayout="column" fxLayoutGap="5px" style="width: 50%">
                                                <div fxLayout="row">
                                                    <div class="name-box flex">
                                                        <img src="../assets/images/ASCI-icon.svg">
                                                    </div>
                                                    <span style=" flex: 1 1 auto;"></span>
                                                    <div>
                                                        <span class="grey-text">{{sms.SENT_DATE | date:'hh:mm
                                                            a'}}</span>
                                                    </div>
                                                </div>
                                                <div class="white-box" style="padding: 10px 10px 0px; width: auto;"
                                                    *ngIf="sms.ATTACHMENTS.length == 0">
                                                    <p class="msg-text blue-text">{{sms.BODY}}</p>
                                                </div>
                                                <div class="white-box" style="padding: 10px 10px 10px; width: auto;"
                                                    *ngIf="sms.ATTACHMENTS.length > 0">
                                                    <div class="msg-text blue-text">{{sms.BODY}}</div>
                                                    <div *ngFor="let doc of sms.ATTACHMENTS">
                                                        <a (click)="openNewTab(doc.ATTACHMENT_SOURCE)"
                                                            class="media-anchor">
                                                            {{doc.ATTACHMENT_NAME}}
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div fxLayout="column" fxLayoutGap="10px" fxLayoutAlign="end end"
                                            *ngIf="sms.FROM_ASCI != 1">
                                            <div fxLayout="column" fxLayoutGap="5px" style="width: 50%;">
                                                <div fxLayout="row">
                                                    <div>
                                                        <span class="grey-text">{{sms.SENT_DATE | date:'hh:mm
                                                            a'}}</span>
                                                    </div>
                                                    <span style=" flex: 1 1 auto;"></span>
                                                    <div class="name-box1 small">
                                                        {{profile}}
                                                    </div>
                                                </div>
                                                <div class="white-box" style="padding: 10px 10px 0px; width: auto;"
                                                    *ngIf="sms.ATTACHMENTS.length == 0">
                                                    <p class="msg-text black-text">{{sms.BODY}}</p>
                                                </div>
                                                <div class="white-box" style="padding: 10px 10px 10px; width: auto;"
                                                    *ngIf="sms.ATTACHMENTS.length > 0">
                                                    <div class="msg-text black-text">{{sms.BODY}}</div>
                                                    <div *ngFor="let doc of sms.ATTACHMENTS">
                                                        <a (click)="openNewTab(doc.ATTACHMENT_SOURCE)"
                                                            class="media-anchor">
                                                            {{doc.ATTACHMENT_NAME}}
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="type-box" fxLayout="row" fxLayoutGap="10px">
                                    <div>
                                        <button class="attach-btn flex" (click)="openUpload()"
                                            [disabled]="text_message.invalid">
                                            <img src="../../../assets/images/attach.png" style="position: relative;"
                                                class="attach-img">
                                        </button>
                                    </div>
                                    <div style="width: 90%;">
                                        <input type="text" [formControl]="text_message"
                                            placeholder="Type your message..."
                                            (keyup.enter)="sendSMS(text_message.value)" autocomplete="off"
                                            class="msg-box small black-text" [readonly]="userDetails.length == 0">
                                    </div>
                                    <div>
                                        <button class="send-btn flex" style="border: none;"
                                            (click)="sendSMS(text_message.value)" [disabled]="userDetails.length == 0">
                                            <img src="../../../assets/images/send.png"
                                                style="position: relative;right: 1px;bottom: 1px;" class="send-img">
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </mat-tab>
        </mat-tab-group>
        <div class="close-div">
            <button mat-icon-button class="close-btn" mat-dialog-close>
                <mat-icon style="margin-bottom: 5px;">close</mat-icon>
            </button>
        </div>
    </div>
</div>