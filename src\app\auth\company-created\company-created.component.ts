import { Component, HostListener, OnInit } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-company-created',
  templateUrl: './company-created.component.html',
  styleUrls: ['./company-created.component.scss']
})
export class CompanyCreatedComponent implements OnInit {

  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private dialogRef: MatDialogRef<CompanyCreatedComponent>
  ) { }

  ngOnInit(): void {
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  okButton() {
    this.dialogRef.close('refresh');
  }

}