.mat-tab-label-active {
    background-color: #F8F9F9 !important;
    opacity: 1!important;
}
.mat-tab-label {
    opacity: 1!important;
    height: 7vh;
}
.mat-tab-group.mat-tab-group.mat-primary .mat-ink-bar {
    background-color: none;
    border-bottom: none;
    height: 0px;
}
.right-screen.mat-tab-header, .mat-tab-nav-bar {
    border-bottom: 0px solid #D8DCDE;
    border-bottom-width: 0px;
    height: 1vh;
}
.right-screen .mat-tab-label, .mat-tab-label-active {
    min-width: 100px !important;
    padding: 2% !important;
}
.mat-dialog-container {
    padding: 0px !important;
}
.right-screen .mat-tab-label-active {
    border-right: 1px solid #D8DCDE;
    border-left: 1px solid #D8DCDE;
}
.close-btn {
    color: solid rgba(47, 57, 65, 0.6);
    background: #F3F3F3;
    border: 1px solid rgba(47, 57, 65, 0.6);
    position: relative;
    right: 20px;
    top: -3px;
    z-index: 2;
}
.close-div {
    position: relative;
    bottom: 98vh;
    left: 900px;
    z-index: 2;
}
.detail-header-container {
    background-color: #F8F9F9;
    width: 100%;
    padding: 7px 20px;
}
.black-text {
    color: #000000;
}
.large { 
    font-size: 14px;
    line-height: 19px;
    font-weight: normal;
}
.small {
    font-size: 12px;
    line-height: 14px;
    font-weight: normal;
}
.msg-text {
    font-size: 12px;
    line-height: 19px;
    font-weight: normal;
}
.msg-text1 {
    font-size: 12px;
    line-height: 19px;
    font-weight: normal;
    height: 110px;
    overflow-x: hidden;
    overflow-y: scroll;
    outline: none;
    resize: none;
    margin-bottom: 10px;
}
:host ::ng-deep .msg-text1 p {
    margin-bottom: 0px !important;
}
::-webkit-scrollbar {
    width: 5px;
}
::-webkit-scrollbar-thumb {
    background: #ccc
}
.grey-text {
    color: #92A2B1;
    font-size: 12px;
    line-height: 16px;
    font-weight: normal;
}
.italic {
    font-style: italic;
    font-weight: normal;
}
.blue-text {
    color: #0088CB;
}
.ellipsis {
    overflow: hidden;
    width: 380px;
    display: inline-block;
    display: -webkit-inline-box;
    word-break: break-word;
    text-overflow: ellipsis;
    white-space: normal;
    -webkit-box-orient: vertical;
    position: relative;
}
.ellipsis1 {
    overflow: hidden;
    width: 390px;
    display: inline-block;
    display: -webkit-inline-box;
    word-break: break-word;
    text-overflow: ellipsis;
    white-space: normal;
    -webkit-box-orient: vertical;
    position: relative;
}
:host ::ng-deep ul {
    margin-bottom: 0px !important;
  }
  :host ::ng-deep h1 {
    margin-bottom: 0px !important;
  }
  :host ::ng-deep h2 {
    margin-bottom: 0px !important;
  }
  :host ::ng-deep h3 {
    margin-bottom: 0px !important;
  }
  :host ::ng-deep h4 {
    margin-bottom: 0px !important;
  }
  :host ::ng-deep .ellipsis,.ellipsis1,.ellipsis2 p {
    margin-bottom: 0px !important;
  }
.title {
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
}
.red-text {
    color: #ED2F45;
    background: rgba(237, 47, 69, 0.2);
    border-radius: 4px;
    font-weight: 600;
}
.tab {
    width: 18px;
    height: 16px;
    font-size: 8px;
    line-height: 11px;
}
.chat {
    width: 28px;
    height: 20px;
    font-size: 12px;
    line-height: 16px;
}
.blue-icon {
    color:#0088CB;
    width: 32px;
    height: 32px;
    background: #CCE7F5;
    border: 1px solid #FFFFFF;
    border-radius: 50px;
    padding: 5px;
}
.space {
    padding: 10px 0px; 
}
.flex {
    display: flex;
    align-items: center;
    justify-content: center;
}
.scroll::-webkit-scrollbar {
    display: none;
}       
.scroll { 
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.email-screen {
    height: 90vh;
}
.email-inbox {
    padding: 1%;
    height: 80vh;
}
.inbox-button {
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
    padding: 7px 20px;
}
.advertiser-button {
    color: #0088CB;
    background: rgba(0, 136, 203, 0.2);
}
.complainant-button {
    color: #0088CB;
    background: rgba(0, 136, 203, 0.2);
}
.disable-button {
    color: #92A2B1;
    background: #F8F9F9;
}
.inbox-body {
    background: #F8F9F9;
    border-radius: 4px 4px 0px 0px;
    padding: 10px;
    height: 70vh;
}
.search-container {
    background: #FFFFFF;
    border: 1px solid #D8DCDE;
    box-sizing: border-box;
    border-radius: 2px;
}   
.search-input {
    height: 30px;
    width: 100%;
    border: none;
    padding: 1% 2%;
}
.inbox-head {
    color: #92A2B1;
    font-weight: normal;
    font-size: 12px;
    line-height: 16px;
}
.white-box {
    background: #FFFFFF;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    padding: 10px 5px;
}
.hover:hover {
    cursor: pointer;
    background-color: rgb(238, 238, 238);
}
.complaint-list {
    height: 300px;
}
.complaint-list::-webkit-scrollbar {
    display: none;
}       
.complaint-list { 
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.chat-msg {
    padding: 5px;
}
.company-icon-container {
    width: fit-content;
    height: 50px; 
    padding: 5px 7px;   
}
.individuals-chat {
    overflow-y: scroll;
    overflow-x: hidden;
    width: 100%;
    height: 86%;
}
.individuals-chat::-webkit-scrollbar {
    display: none;
}
.individuals-chat {
    -ms-overflow-style: none;
    scrollbar-width: none; 
}
.chat-list {
    overflow-y: scroll;
    overflow-x: hidden;
    width: 100%;
    height: 200px;
}
.chat-list::-webkit-scrollbar {
    display: none;
}
.chat-list {
    -ms-overflow-style: none;
    scrollbar-width: none; 
}
.chat-list1 {
    overflow-y: scroll;
    overflow-x: hidden;
    width: 100%;
    height: 330px;
}
.chat-list1::-webkit-scrollbar {
    display: none;
}
.chat-list1 {
    -ms-overflow-style: none;
    scrollbar-width: none; 
}
.chat-list2 {
    overflow-y: scroll;
    overflow-x: hidden;
    width: 100%;
    height: 130px;
    padding-bottom: 10px;
}
.chat-list2::-webkit-scrollbar {
    display: none;
}
.chat-list2 {
    -ms-overflow-style: none;
    scrollbar-width: none; 
}
.chat-list3 {
    overflow-y: scroll;
    overflow-x: hidden;
    width: 100%;
    height: 385px;
}
.chat-list3::-webkit-scrollbar {
    display: none;
}
.chat-list3 {
    -ms-overflow-style: none;
    scrollbar-width: none; 
}
.chat-list4 {
    overflow-y: scroll;
    overflow-x: hidden;
    width: 100%;
    height: 300px;
}
.chat-list4::-webkit-scrollbar {
    display: none;
}
.chat-list4 {
    -ms-overflow-style: none;
    scrollbar-width: none; 
}
.email-body {
    width: 70%;
    height: 60%;
    padding: 10px 20px;
}
.members {
    height:26px;
}
.refresh-btn {
    color: #5A6F84;
    background: #CFD7DF;
    border-radius: 12px;
    height: 30px;
    margin-bottom: 10px;
}
.body-box {
    background: #F8F9F9;
    border-radius: 4px;
    padding: 0px 5px 7px;
}
.emails-header {
    padding: 1%;
    border-bottom: 1px solid #D8DCDE;
}
.create-btn {
    color: #0088CB;
    background: #FFFFFF;
    border: 1px solid #0088CB;
    border-radius: 12px;
    height: auto;
    padding: 5px 17px;
    padding-bottom: 5px;
}
.create-btn[disabled] {
    color: #92A2B1;
    background: #F8F9F9;
    border: 1px solid #92A2B1;
    border-radius: 12px;
    height: auto;
    padding: 5px 17px;
    padding-bottom: 5px;
}
.content-box {
    padding: 20px 10px 10px;
}
.email-container {
    height: 58vh;
    overflow-y: scroll;
    overflow-x: hidden;
}
.chat-container {
    height: 57vh;
    overflow-y: scroll;
    overflow-x: hidden;
}
.name-box1 {
    color: #0088CB;
    background: #FFFFFF;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
    border-radius: 50px;
    font-weight: 600;
    width: 28px;
    height: 28px;
    padding: 3% 1% 1% 1%;
}
.name-box {
    color: #0088CB;
    background: #FFFFFF;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
    border-radius: 50px;
    font-weight: 600;
    width: 28px;
    height: 28px;
    padding: 1%;
}
.type-box {
    background: #FFFFFF;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    padding: 10px;
}
.msg-box {
    background: #FFFFFF;
    border: 1px solid #DFE0E2;
    box-sizing: border-box;
    border-radius: 8px;
    width: 100%;
    height: 38px;
    padding: 10px 20px;
}
.attach-btn {
    background: #F8F8F9;
    color: #888C92;
    border: 1px solid #DFE0E2;
    box-sizing: border-box;
    border-radius: 7px;
    width: 39px;
    height: 39px;
    text-align: center;
}
.attach-btn[disabled] {
    opacity: 0.4;
} 
.send-btn {
    background: #0088CB;
    box-shadow: inset 0px 3px 6px rgba(212, 241, 255, 0.2);
    border-radius: 8px;
    width: 39px !important;
    height: 39px;
    padding-right: 10px;
}
.send-btn[disabled] {
    opacity: 0.4;
}
.media-anchor {
    color: #0088CB;
    font-size: 12px;
    word-break: break-all;
    cursor: pointer;
}
.person-button {
    width: fit-content;
    height: 50px; 
    padding: 12px 7px; 
}
.chat-item-selected {
    background-color: rgba(63,81,181,.15);
}
.no-mails {
    font-family: 'Segoe UI';
    font-style: italic;
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
    color: #92A2B1;
    margin-left: 230px;
}