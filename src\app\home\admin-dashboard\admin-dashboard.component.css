.common-toolbar {
    border-bottom: 1px solid #D8DCDE;
    border-top: 0;
    border-left: none;
    border-right: none;
}

.heading-container {
    width: 60%;
}

.options-container {
    width: 40%;
}

.container-text {
    color: #92A2B1;
    font-size: 12px;
    margin-left: 17px;
    padding-top: 4px;
}

.float-right-container {
    position: absolute;
    top: 5px;
    right: 10px;
    display: flex;
    flex-direction: row;
    gap: 20px;
}

.right-search-btn {
    color: gray;
    border: 1px solid gray;
}

.box {
    background: #F8F9F9;
    height: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.box::-webkit-scrollbar {
    display: none;
}

.toolbar1 {
    padding: 0% 2% 0% 0%;
    background: #F8F9F9;
}

.card-row {
    padding-top: 1%;
}

.card-row1 {
    padding: 1% 0%;
}

.card {
    width: 24%;
    margin-right: 1%;
    border: 0.5px solid #D8DCDE;
    box-sizing: border-box;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    cursor: pointer;
    padding: 2% 0% 2% 0%;
    height: 86px;
}

.card:hover {
    border: 0.5px solid #0088CB;
}

.digit {
    font-weight: bold;
    font-size: 22px;
    font-weight: 500;
    line-height: 20px;
    color: #000000;
}

.category {
    margin-top: 3%;
    font-size: 11px;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: #92A2B1;
}

.category1 {
    margin-top: 3%;
    font-size: 11px;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: #92A2B1;
}

.dashboard-container {
    width: 100%;
    height: 90%;
}

.manage {
    margin-left: 5%;
    width: 23%
}

.dashboard-admin-heading {
    font-weight: 600;
    font-size: 18px;
    line-height: 23.94px;
    color: #ED2F45;
}

.dashboard-admin {
    width: 77%;
    height: 100%;
    border-left: 1px solid rgb(245, 245, 245);
}

.dasboard-subheading {
    font-weight: 400;
    font-size: 14px;
    line-height: 18.62px;
}

.list-time {
    font-weight: 400;
    font-size: 12px;
    line-height: 15.96px;
    color: #92A2B1;
}

.dashboard-complaint-list {
    overflow-y: scroll;
    overflow-x: hidden;
    width: 100%;
    height: 88%;
}

.dashboard-complaint-list::-webkit-scrollbar {
    display: none;
}

.dashboard-complaint-list {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.item-head-container:hover {
    cursor: pointer;
}

.complaint-list {
    overflow: scroll;
    height: 100%;
}

.comp_name {
    color: rgb(25, 150, 199);
}

.comp_prio1 {
    color: #2ED9FF;
}

.comp_prio2 {
    color: #F89E1B;
}

.comp_prio3 {
    color: #FF4242;
}

.comp_prio4 {
    color: #EA2D2D;
}

:host ::ng-deep .mat-form-field-flex>.mat-form-field-infix {
    padding: 0.4em 0px !important;
}

:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-label {
    margin-top: -5px;
    margin-bottom: -5px;
}

:host ::ng-deep label.ng-star-inserted {
    transform: translateY(-0.59375em) scale(.75) !important;
}

.head-row {
    padding: 0% 1% 1% 2%;
    height: 95%;
    background: #F8F9F9;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    overflow-y: scroll;
    overflow-x: hidden;
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.head-row::-webkit-scrollbar {
    display: none;
}

.example-full-width {
    margin-top: 1%;
    width: 130px;
}

:host::ng-deep .mat-select-placeholder {
    color: #000000;
}

:host ::ng-deep .mat-select-trigger {
    height: 18px;
}

.item-head-container:hover {
    cursor: pointer;
}

.head-container {
    display: flex;
    flex-direction: row;
    gap: 20px;
    text-align: center;
}

.comp-head {
    padding-top: 5px;
}

.head-text {
    padding: 15px 9px 6px 1px;
    font-weight: 500;
    font-size: 16px;
    line-height: 21.28px;
}

.head-spacer {
    flex: 1 1 auto;
}

.overdue-container {
    color: crimson;
}

.list-item-container {
    width: 95%;
    height: auto !important;
    padding: 1px 3px 1px 3px;
}

.list-item-container:hover {
    background-color: rgb(235, 235, 235);
}

.item-content-container {
    display: flex;
    flex-direction: row;
    gap: 10px;
}

.name-container,
.date-container {
    font-size: small;
    color: rgb(85, 84, 84);
}

.content-div {
    max-width: 95%;
    overflow-wrap: break-word;
}

.item-icon {
    font-size: medium;
}

.status-chip {
    height: 3px !important;
}

.comp-status {
    min-height: 22px !important;
    width: auto;
    font-size: smaller;
    border-width: 1px;
    border-style: solid;
    border-radius: 5px;
    padding: 0px 7px 0px 7px;
}

.complaint-display-head {
    margin: 5px 0px 5px 15px;
}

.tab-title-container {
    font-size: small;
    height: 40px;
    width: 100px;
}

.item-head-container {
    padding-top: 12px;
    font-weight: 400;
    font-size: 13px;
    line-height: 17.29px;
}

.status {
    width: 60px;
}

.one {
    background-color: #e0f7fa;
    width: 60px;
}

.tab {
    width: 1000px;
    height: auto;
}

.toolbar {
    background-color: white;
    height: 9%;
    width: 100%;
}

.adm-head {
    font-size: 19px;
}

.toolbar-btns {
    display: flex;
    flex-direction: row;
    grid-gap: 10px;
}

.search-btn {
    color: gray;
    border: 1px solid gray;
    border-radius: 15px;
}

.bell-btn {
    color: crimson;
    border: 1px solid gray;
    border-radius: 15px;
}

.admin-btn {
    color: white;
    background-color: #0088CB;
    border-radius: 15px;
}

.option-btn {
    line-height: 25%;
    height: 550%;
}

.option-text {
    line-height: 200%;
    color: #000000;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
}

.theme-blue-button-admin[disabled] {
    opacity: 0.4;
    color: #ffffff;
}

.reports-head {
    color: #000000;
    font-weight: 600;
    font-size: 18px;
    line-height: 23.94px;
    height: 24px;
    padding-top: 5px;
}

.reports-container {
    height: auto;
    padding-top: 2%;
}

.reports-head-container {
    padding: 0px 20px;
    vertical-align: middle;
    display: flex;
    align-items: center;
}

.range-head {
    color: #000000;
    font-weight: 600;
    font-size: 14px;
    height: 24px;
}

.month-container {
    padding: 10px 0;
}

.month-field {
    width: 130px;
    margin-right: 15px;
}

.year-field {
    width: 130px;
}

:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-wrapper {
    margin: 0px !important;
    padding-bottom: 0;
}

.reports-btn-container {
    padding: 5px 20px;
}

.reports-btn {
    padding: 6px 12px;
    border: none;
}

@media screen and (min-width: 1251px) and (max-width: 1305px) {

    .category,
    .category1 {
        font-size: 10px;
        font-weight: 600;
    }
}

@media only screen and (max-width: 1250px) {
    .dashboard-admin-heading {
        padding-left: 0;
        padding-top: 0;
    }

    .dashboard-container {
        background: #E5E5E5;
    }

    .dasboard-subheading {
        padding-right: 20px;
        padding-top: 0px;
    }
}

.admin-menu {
    margin-top: 50px !important;
}

.admin-option-container {
    width: max-content;
    min-width: 150px;
}

.reporting-module-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.charts-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    gap: 1%;
}

.vertical-bar-chart-container,
.bubble-chart-container,
.donut-chart-container,
.zoomable-sunburst-container {
    width: 50%;
    min-height: 500px;
    background-color: #FFFFFF;
    border: 1px solid #00000026;
    border-radius: 4px;
    padding: 1%;
    display: flex;
    flex-direction: column;
    gap: 10px;
    box-shadow: 0px 4px 4px 0px #00000040;
}

.horizontal-bar-chart-container,
.pie-chart-container {
    width: 100%;
    min-height: 500px;
    background-color: #FFFFFF;
    border: 1px solid #00000026;
    border-radius: 4px;
    padding: 1%;
    display: flex;
    flex-direction: column;
    gap: 10px;
    box-shadow: 0px 4px 4px 0px #00000040;
}

.chart-heading {
    font-size: 14px;
    color: #2F3941;
}

:host ::ng-deep .mat-tab-group {
    width: 100%;
}

:host ::ng-deep .mat-tab-label {
    color: #0088CB;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    opacity: 1 !important;
}

:host ::ng-deep .mat-tab-list .mat-tab-labels .mat-tab-label-active {
    color: #000000;
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    opacity: 1 !important;
}

:host ::ng-deep .mat-tab-group.mat-primary .mat-ink-bar {
    /* background-color: #000000 !important; */
    display: none !important;
}

:host ::ng-deep .dashboard-tabs .mat-tab-list .mat-tab-labels .mat-tab-label-active .mat-tab-label-content {
    font-weight: 600;
}