import { Component, EventEmitter, Input, OnChanges, OnInit, Output, ViewChild } from '@angular/core';
import { FormGroup, FormControl, FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { DatePipe } from '@angular/common';
import { ComplaintsService } from '../../services/complaints.service';
import { NotificationService } from '../../services/notification.service';
import { environment } from 'src/environments/environment';
import { colorObj } from '../../shared/color-object';
import { debounceTime, distinctUntilChanged, first } from 'rxjs/operators';
import { AuthService } from 'src/app/services/auth.service';
import { GeneralPublicComponent } from '../general-public/general-public.component';
import { IntraIndustryComponent } from '../intra-industry/intra-industry.component';
import { ManageCasesComponent } from '../manage-cases/manage-cases.component';
import { ThirdPartyService } from 'src/app/services/third-party.service';
import { NamsFormComponent } from '../nams-form/nams-form.component';
import { RelatedComplaintsComponent } from '../related-complaints/related-complaints.component';
import { MatDialog } from '@angular/material/dialog';
import moment from 'moment';

@Component({
  selector: 'app-complaint-register',
  templateUrl: './complaint-register.component.html',
  styleUrls: ['./complaint-register.component.css'],
  providers: [DatePipe]
})
export class ComplaintRegisterComponent implements OnInit, OnChanges {

  name = 'add claim';
  // values = [];

  claimreplica = [{ challenge: '', key: '', annexure: '', type: '', summary: '' }];
  activeTab;

  selected = 'male';

  isSelected: boolean = false;
  consumer: boolean = false;
  company: boolean = false;
  nams: boolean = false;
  nams_step: string = '';

  behalf: any[];
  contactMethod: any[];
  ads: any[];
  classification: any[];
  stateList: any[];
  districtList: any[];
  titles: any[];
  platforms: any[];
  printMediums: any[];
  promotionalMaterialSource: any[];
  govtBody: any[];
  foundno = '1';
  foundname = 'Smith,Mr.John';
  foundemail = '<EMAIL>';
  foundlocation = 'Merry street,16 Kimberlay Road,LOndon,NW6 7Sg';
  foundmsg = "Tanishq's ad seems very offensive in terms of a complaint";
  person1 = 'JS';
  person2 = 'TM';
  person3 = '+18';
  userInfo: any;
  roleId: number = 0;
  selectedAdvId: number;
  selectedUserTypeID: number;
  selectedUserType: String;
  companyNameControl = new FormControl();
  companyList: any[];
  companyId: number;
  phNumList: any[];
  userId: number;
  public files: any[] = [];
  public moveTabIndex = 0;
  complaintObj: any;
  blankComplaintID: number = 0;
  userExist: boolean = false;
  is_FTC: number = 0;
  date;
  ad_seen_date;
  // STEP 3 DISPLAY VARIABLES
  public rec_date;
  public source;
  public method;
  public contact_involved;
  public reference;
  public comp_details;
  public classfy;
  public medium;
  public channel;
  public sourcePlace;
  public platform;
  public printMedium;
  public promotionalMaterial;
  public adv_seen_date: any;
  public comp_fname: any;
  public comp_lname: any;
  public comp_email: any;
  public comp_phone: any;
  public comp_against_company: any;
  public comp_against_product: any;
  public media_outlet: any;
  public product_category: any;
  public media: any;
  public ad_language: any;
  public media_link: any;
  public creative_id: any;
  public code_violated;
  public comp_desc: any;
  public duration: any;
  public transcription: any;
  public transcriptionText: any;
  public authority;
  public namsDate: any;
  public publication: any;
  public influencer: any;
  public profile_url: any;
  public influencer_contact_no: any;
  public influencer_email_address: any;
  public documents = "No";
  public claims = "No";
  contactMetId = 2;
  userCreated: boolean = false;
  suomoto: boolean = false;
  mobile: number;
  detailText: string = '';
  againstText: string = '';
  medialLinkText: string = '';
  compDescText: string = '';
  reqUserId: number;
  complaintSource: string = '';
  loading: boolean = false;
  isDirectComplaint: boolean = true;

  step1Form: FormGroup;
  mobilenopattern = /^[0-9]{10}$/;
  postalCodepattern = /^[0-9]{6}$/;
  textPattern = /^[a-zA-Z\s]*$/;
  emailPattern = environment.emailPatterm;

  @ViewChild(GeneralPublicComponent, { static: false }) generalPublicComp: GeneralPublicComponent;
  @ViewChild(IntraIndustryComponent, { static: false }) intraIndustryComp: IntraIndustryComponent;
  @ViewChild(ManageCasesComponent, { static: false }) manageCasesComp: ManageCasesComponent;
  @ViewChild(NamsFormComponent, { static: false }) namsFormComp: NamsFormComponent;

  @Output() refreshEvent: EventEmitter<any> = new EventEmitter<any>();

  search = new FormGroup({
    reference: new FormControl(''),
    name: new FormControl(''),
    initial_name: new FormControl(''),
    phone: new FormControl(''),
    email_address: new FormControl(''),
    post_code: new FormControl(''),
    search_address: new FormControl('')
    // address: new FormControl('')
  });

  public tabVisible: boolean = true;
  isShown: boolean = false;
  @Input() navigationState: any;

  complaintData: any[] = []
  guidelines: string;
  complainantId: any;
  created_date: any;
  network: any;

  constructor(public router: Router,
    private FB: FormBuilder,
    private datePipe: DatePipe,
    private matDialog: MatDialog,
    private complaintService: ComplaintsService,
    private notify: NotificationService,
    private authService: AuthService,
    private namsService: ThirdPartyService) {
    this.complaintService.complaintRegisterData.subscribe(res => {
      this.complaintData = res ? res : [];
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
    this.getStep1Form();
  }

  getStep1Form() {
    this.step1Form = this.FB.group({
      complainant: ['', Validators.required],
      salutation: ['', Validators.required],
      first_name: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(25), Validators.pattern(this.textPattern)]],
      last_name: ['', [Validators.required, Validators.maxLength(25), Validators.pattern(this.textPattern)]],
      address: [''],
      postal_code: ['', [Validators.required, Validators.pattern(this.postalCodepattern)]],
      city: [''],
      contact_method: ['', Validators.required],
      email: ['', [Validators.required, Validators.pattern(this.emailPattern)]], //email id is mandatory as per the current cognito pool
      phone_number: ['', [Validators.required, Validators.pattern(this.mobilenopattern)]],
      organization: [''],
      govt_body: ['', Validators.required],
      companyID: ['', Validators.required],
      state: ['', Validators.required],
      district: ['', Validators.required],
      profession: [''],
      role: ['']
    })
  }

  ngOnInit(): void {
    // this.moveTabIndex = this.moveTabIndex + 4;
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.behalf = JSON.parse(window.localStorage.getItem('userType'));
    this.contactMethod = JSON.parse(window.localStorage.getItem('contactMethodType'));
    this.ads = JSON.parse(window.localStorage.getItem('advertisementSource'));
    this.classification = JSON.parse(window.localStorage.getItem('classification'));
    this.stateList = JSON.parse(window.localStorage.getItem('stateType'));
    this.titles = JSON.parse(window.localStorage.getItem('salutation'));
    this.platforms = JSON.parse(window.localStorage.getItem('platform'));
    this.printMediums = JSON.parse(window.localStorage.getItem('printSource'));
    this.promotionalMaterialSource = JSON.parse(window.localStorage.getItem('promotionalMaterialSource'));
    this.govtBody = JSON.parse(window.localStorage.getItem('governmentDepartment'));
    this.patchComplaintFormValues();

    this.step1Form.get('companyID')!.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0) {
          this.getCompanyList(value);
        }
      }, err => {

      })

    this.step1Form.get('phone_number')!.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (typeof value === 'string') {
          value = +value;
        }
        const len = Math.ceil(Math.log10(value + 1));
        if (len > 6) {
          this.getPhNumList(value, len);
        }
      }, err => {

      })

    this.getStep();

  }

  ngOnChanges() {
  }

  getStep() {
    this.complaintService.currentStep
      .pipe(first()).subscribe(step => {
        if (step === 'tams' || step === 'tams-reech') {
          this.nams_step = 'tams';
          this.isDirectComplaint = false;
          this.namsService.currentNamsComplaintDate.subscribe(data => {
            this.created_date = data;
          });
          this.complaintService.currentNamsComplaintId
            .pipe(first()).subscribe(id => {
              if (id != 0) {
                this.complaintSource = 'tams';
                this.step1Form.patchValue({
                  'complainant': 6
                })
                this.step1Form.controls['complainant'].disable();
                this.behalf.forEach(element => {
                  if (element.ID == 6) {
                    this.showFields(element, 'ts');
                  }
                });
              }
            })
        } else if (step === 'reech') {
          this.nams_step = 'reech';
          this.isDirectComplaint = false;
          this.namsService.currentNamsComplaintDate.subscribe(data => {
            this.created_date = data;
          });
          this.complaintService.currentNamsComplaintId
            .pipe(first()).subscribe(id => {
              if (id != 0) {
                this.complaintSource = 'reech';
                this.step1Form.patchValue({
                  'complainant': 7
                })
                this.step1Form.controls['complainant'].disable();
                this.behalf.forEach(element => {
                  if (element.ID == 7) {
                    this.showFields(element, 'ts');
                  }
                });
              }
            })
        } else if (step === 'chatbot') {
          this.isDirectComplaint = false;
          this.complaintService.currentChatbotComplaintId
            .pipe(first()).subscribe(id => {
              if (id != 0) {
                this.complaintSource = 'CHATBOT';
                this.step1Form.patchValue({
                  'complainant': 1
                })
                this.behalf.forEach(element => {
                  if (element.ID == 1) {
                    this.showFields(element, 'ts');
                  }
                });
                this.namsService.getChatbotDetails(id).subscribe((res: any) => {
                  this.mobile = res.data.MOBILE;
                  this.created_date = res.data.CREATED_DATE;
                  this.step1Form.controls['complainant'].disable();
                  this.step1Form.patchValue({
                    'phone_number': this.mobile
                  })
                  if (typeof this.mobile === 'string') {
                    this.mobile = +this.mobile;
                  }
                  const len = Math.ceil(Math.log10(this.mobile + 1));
                  this.getPhNumList(this.mobile, len);
                }, err => {
                  this.notify.showNotification(
                    err.error.message,
                    "top",
                    (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                    err.error.status
                  )
                });
              }
            })
        } else if (step === 'whatsapp') {
          this.isDirectComplaint = false;
          this.complaintService.currentWhatsappComplaintId
            .pipe(first()).subscribe(id => {
              if (id != 0) {
                this.complaintSource = 'WHATSAPP';
                this.step1Form.controls['complainant'].disable();
                this.complaintService.getWhatsappComplaint(id).subscribe((res: any) => {
                  this.created_date = res.data.CREATED_DATE;
                  this.mobile = res.data.MOBILE;
                  this.step1Form.patchValue({
                    'phone_number': this.mobile
                  })
                  if (typeof this.mobile === 'string') {
                    this.mobile = +this.mobile;
                  }
                  const len = Math.ceil(Math.log10(this.mobile + 1));
                  this.getPhNumListwhatsappEmail(this.mobile, len);
                }, err => {
                  this.notify.showNotification(
                    err.error.message,
                    "top",
                    (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                    err.error.status
                  )
                });
              }
            }, err => {
              this.notify.showNotification(
                err.error.message,
                "top",
                (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                err.error.status
              );
            })
        } else if (step === 'sysGen') {
          this.isDirectComplaint = false;
          this.complaintService.currentSysComplaintId
            .pipe(first()).subscribe(id => {
              if (id != 0) {
                this.complaintSource = 'SYSGEN';
                let complaintType = this.complaintService.getComplaintType;
                this.step1Form.patchValue({
                  'complainant': complaintType
                })
                this.behalf.forEach(element => {
                  if (element.ID == complaintType) {
                    this.showFields(element, 'ts');
                  }
                });
                this.complaintService.getComplaint(id).subscribe(res => {
                  var momentDate = moment(res.data.CREATED_DATE)
                  this.created_date = momentDate.format();
                  this.mobile = res.data.MOBILE;
                  this.step1Form.controls['complainant'].disable();
                  this.step1Form.patchValue({
                    'phone_number': this.mobile
                  })
                  if (typeof this.mobile === 'string') {
                    this.mobile = +this.mobile;
                  }
                  const len = Math.ceil(Math.log10(this.mobile + 1));
                  this.getPhNumList(this.mobile, len);
                }, err => {
                  this.notify.showNotification(
                    err.error.message,
                    "top",
                    (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                    err.error.status
                  );
                })
              }
            }, err => {
              this.notify.showNotification(
                err.error.message,
                "top",
                (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                err.error.status
              );
            })
        } else if (step === 'email') {
          this.isDirectComplaint = false;
          this.complaintService.currentEmailComplaintId
            .pipe(first()).subscribe(id => {
              if (id != 0) {
                this.complaintSource = 'EMAIL';
                this.step1Form.controls['complainant'].disable();
                this.complaintService.getMailComplaint(id).subscribe(
                  (res: any) => {
                    var momentDate = moment(res.data.CREATED_DATE)
                    this.created_date = momentDate.format();
                    this.mobile = res.data.MOBILE;
                    this.step1Form.patchValue({
                      'phone_number': this.mobile
                    })
                    if (typeof this.mobile === 'string') {
                      this.mobile = +this.mobile;
                    }
                    const len = Math.ceil(Math.log10(this.mobile + 1));
                    this.getPhNumListwhatsappEmail(this.mobile, len);
                  }, err => {
                    this.notify.showNotification(
                      err.error.message,
                      "top",
                      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                      err.error.status
                    )
                  });
              }
            }, err => {
              this.notify.showNotification(
                err.error.message,
                "top",
                (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                err.error.status
              );
            })
        } else if (step == 'direct') {
          this.complaintSource = 'DIRECT';
        }
        if (!this.isDirectComplaint) {
          this.step1Form.get('phone_number').disable();
        }
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
  }

  patchComplaintFormValues() {
    if (this.complaintData.length > 0 && this.complaintData[0]) {
      this.step1Form.patchValue(this.complaintData[0]);
      return;
    } else {
      return;
    }
  }

  getCompanyList(value) {
    this.authService.getCompanies(value).subscribe(res => {
      this.companyList = res.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  getPhNumList(value, phNumlength) {
    if (!(this.complaintSource === 'WHATSAPP' || this.complaintSource === 'EMAIL')) {
      this.authService.getPhNums(value).subscribe(res => {
        this.phNumList = res.data;
        let phNum = null;
        if (Array.isArray(this.phNumList) && this.phNumList.length > 0) {
          phNum = this.phNumList[0].MOBILE;
          if (phNum.length > 10) {
            if (phNum.substring(0, 2) == '91') {
              phNum = phNum.substring(2);
            }
          }
        }
        if (phNumlength == 10 && this.phNumList.length > 0 && !!phNum && value == phNum) {
          if (!(this.selectedUserTypeID == 5 || this.selectedUserTypeID == 6 || this.selectedUserTypeID == 7)) {
            if (this.roleId != 0 && this.roleId == this.phNumList[0].ROLE_ID) {
              this.userId = this.phNumList[0].ID;
              this.getUserDetails(this.phNumList[0].ID);
            } else {
              this.step1Form.get('phone_number').setValue('');
              this.notify.showNotification(
                `This user can't be associated with ${this.selectedUserType}, as this user is already registered with as a different user.`,
                "top",
                "warning",
                0
              )
            }
          }
        } else {
          this.step1Form.get('salutation').enable();
          this.step1Form.get('first_name').enable();
          this.step1Form.get('last_name').enable();
          this.step1Form.get('phone_number').enable();
          this.step1Form.get('companyID').enable();
          this.step1Form.get('email').enable();
          this.step1Form.get('city').enable();
          this.step1Form.get('address').enable();
          this.step1Form.get('postal_code').enable();
          this.step1Form.get('organization').enable();
          this.step1Form.get('profession').enable();
          this.step1Form.get('govt_body').enable();

          this.step1Form.controls['first_name'].setValidators([Validators.required, Validators.minLength(3), Validators.pattern(this.textPattern)]);
          this.step1Form.get('first_name').updateValueAndValidity();
          this.step1Form.controls['last_name'].setValidators([Validators.required, Validators.pattern(this.textPattern)]);
          this.step1Form.get('last_name').updateValueAndValidity();
          this.step1Form.controls['salutation'].setValidators(Validators.required);
          this.step1Form.get('salutation').updateValueAndValidity();
          this.step1Form.controls['contact_method'].setValidators(Validators.required);
          this.step1Form.get('contact_method').updateValueAndValidity();
          this.step1Form.controls['phone_number'].setValidators([Validators.required, Validators.pattern(this.mobilenopattern)]);
          this.step1Form.get('phone_number').updateValueAndValidity();
          this.step1Form.controls['state'].setValidators(Validators.required);
          this.step1Form.get('state').updateValueAndValidity();
          this.step1Form.controls['district'].setValidators(Validators.required);
          this.step1Form.get('district').updateValueAndValidity();
          if (this.selectedUserTypeID == 2) {
            this.step1Form.controls['companyID'].setValidators(Validators.required);
          }
          if (this.selectedUserTypeID == 3) {
            this.step1Form.controls['govt_body'].setValidators(Validators.required);
          }
        }
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
  }

  getPhNumListwhatsappEmail(value, phNumlength) {
    this.authService.getPhNums(value).subscribe(res => {
      this.phNumList = res.data;
      let phNum = this.phNumList[0].MOBILE;
      if (phNum.length > 10) {
        if (phNum.substring(0, 2) == '91') {
          phNum = phNum.substring(2);
        }
      }
      if (phNumlength == 10 && this.phNumList.length > 0 && value == phNum) {
        if (this.phNumList[0].ROLE_ID == 1 || this.phNumList[0].ROLE_ID == 2 ||
          this.phNumList[0].ROLE_ID == 3 || this.phNumList[0].ROLE_ID == 6) {
          this.step1Form.patchValue({
            'complainant': 5
          })
          this.userId = this.phNumList[0].ID;
          this.behalf.forEach(el => {
            if (el['ID'] == 5) {
              this.step1Form.controls['complainant'].disable();
              this.showFields(el, 'ts');
            }
          })
        } else if (this.phNumList[0].ROLE_ID == 9) {
          this.nams_step = 'tams';
          this.complaintSource = 'WHATSAPP'
          this.step1Form.patchValue({
            'complainant': 6
          })
          this.userId = this.phNumList[0].ID;
          this.step1Form.get('organization').setValue('NAMS (TAMS)');
          this.behalf.forEach(el => {
            if (el['ID'] == 6) {
              this.step1Form.controls['complainant'].disable();
              this.showFields(el, 'ts');
            }
          })
        } else {
          this.behalf.forEach(el => {
            if (this.phNumList[0].ROLE_ID == el['ROLE_ID']) {
              this.step1Form.patchValue({
                'complainant': el['ID']
              })
              this.showFields(el, 'ts');
              this.step1Form.patchValue({
                'phone_number': this.mobile
              })
              this.step1Form.get('phone_number').disable();
              this.step1Form.controls['complainant'].disable();
              this.userId = this.phNumList[0].ID;
              this.getUserDetails(this.phNumList[0].ID);
            }
          })
        }
      } else {
        this.userExist = false;
        this.step1Form.controls['complainant'].disable();
        this.behalf.forEach(element => {
          if (element.ID == 1) {
            this.step1Form.patchValue({
              'complainant': element['ID']
            })
            this.showFields(element, 'ts');
          }
        });
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  showFields(data, from) {
    if (from === 'html' && (data.ID == 6 || data.ID == 7)) {
      return;
    }
    this.complaintService.complainantID.next({ complainantId: this.step1Form.get('complainant').value });
    this.isSelected = true;
    this.roleId = data.ROLE_ID;
    this.selectedUserTypeID = data.ID;
    this.selectedUserType = data.USER_TYPE_NAME;
    this.step1Form.get('companyID').clearValidators();
    this.step1Form.get('companyID').updateValueAndValidity();
    if (!(this.complaintSource === 'WHATSAPP' || this.complaintSource === 'EMAIL')) {
      this.step1Form.get('phone_number').setValue('');
    }
    if (data.ID == 1 || data.ID == 3) {
      this.consumer = true;
      this.company = false;
      this.nams = false;
    } else if (data.ID == 2 || data.ID == 5 || data.ID == 4) {
      this.company = true;
      this.consumer = false;
      this.nams = false;
      if (data.ID == 2) {
        this.step1Form.controls['companyID'].setValidators(Validators.required);
      }
    } else if (data.ID == 6 || data.ID == 7) {
      this.consumer = false;
      this.company = false;
      this.nams = true;
    } else {
      this.isSelected = false;
    }
    if (data.ID == 5 || data.ID == 6 || data.ID == 7) {
      this.suomoto = true;
      if (!(this.complaintSource === 'WHATSAPP' || this.complaintSource === 'EMAIL')) {
        this.userId = this.userInfo.userId;
        this.step1Form.patchValue({
          'phone_number': this.userInfo.mobile
        })
      }
      this.step1Form.get('phone_number').disable();
      this.getUserDetails(this.userId);
    } else {
      this.suomoto = false;
      if (!(this.complaintSource === 'WHATSAPP' || this.complaintSource === 'EMAIL')) {
        this.step1Form.get('phone_number').enable();
        this.step1Form.get('phone_number').setValue('');
      }
      this.step1Form.get('first_name').setValue('');
      this.step1Form.get('last_name').setValue('');
      this.step1Form.get('email').setValue('');
      this.step1Form.get('state').setValue('');
      this.step1Form.get('district').setValue('');
      this.step1Form.get('address').setValue('');
      this.step1Form.get('postal_code').setValue('');
      this.step1Form.get('contact_method').setValue('');
      this.step1Form.get('organization').setValue('');
      this.step1Form.get('companyID').setValue('');
      this.step1Form.get('govt_body').setValue('');
      this.step1Form.get('profession').setValue('');

      this.step1Form.controls['first_name'].setValidators([Validators.required, Validators.minLength(3), Validators.pattern(this.textPattern)]);
      this.step1Form.get('first_name').updateValueAndValidity();
      this.step1Form.controls['last_name'].setValidators([Validators.required, Validators.pattern(this.textPattern)]);
      this.step1Form.get('last_name').updateValueAndValidity();
      this.step1Form.controls['salutation'].setValidators(Validators.required);
      this.step1Form.get('salutation').updateValueAndValidity();
      this.step1Form.controls['contact_method'].setValidators(Validators.required);
      this.step1Form.get('contact_method').updateValueAndValidity();
      this.step1Form.controls['phone_number'].setValidators([Validators.required, Validators.pattern(this.mobilenopattern)]);
      this.step1Form.get('phone_number').updateValueAndValidity();
      this.step1Form.controls['state'].setValidators(Validators.required);
      this.step1Form.get('state').updateValueAndValidity();
      this.step1Form.controls['district'].setValidators(Validators.required);
      this.step1Form.get('district').updateValueAndValidity();
      if (data.ID == 3) {
        this.step1Form.controls['govt_body'].setValidators(Validators.required);
      } else {
        this.step1Form.get('govt_body').clearValidators();
        this.step1Form.get('govt_body').updateValueAndValidity();
      }
    }
  }

  selectState(event) {
    let districtList = JSON.parse(window.localStorage.getItem('districtType'));
    this.districtList = districtList.filter(el => {
      return event.value == el.STATE_TYPE_ID;
    })
  }

  onSelectionChange(event, cond) {
    if (cond === 'company') {
      this.companyList.forEach(element => {
        if (event.option.value === element.COMPANY_NAME) {
          this.companyId = element.ID;
        }
      });
    } else if (cond === 'phNum') {
      this.phNumList.forEach(element => {
        if (event.option.value === element.MOBILE) {
          if (this.roleId == element.ROLE_ID) {
            this.userId = element.ID;
            this.getUserDetails(this.userId)
          } else {
            this.step1Form.get('phone_number').setValue('');
            this.step1Form.controls['phone_number'].setValidators([Validators.required, Validators.pattern(this.mobilenopattern)]);
            this.step1Form.get('phone_number').updateValueAndValidity();
            this.notify.showNotification(
              `This user can't be associated with ${this.selectedUserType}, as this user is already registered with as a different user.`,
              "top",
              "warning",
              0
            )
          }
        }
      });
    }
  }

  getUserDetails(userId) {
    this.authService.getUserDetailsById(userId).subscribe(res => {
      this.userExist = true;
      this.step1Form.get('salutation').disable();
      this.step1Form.get('first_name').disable();
      this.step1Form.get('last_name').disable();
      this.step1Form.get('companyID').disable();
      this.step1Form.get('email').disable();
      this.step1Form.get('city').disable();
      this.step1Form.get('address').disable();
      this.step1Form.get('postal_code').disable();
      this.step1Form.get('organization').disable();
      this.step1Form.get('govt_body').disable();
      this.step1Form.get('profession').disable();

      this.step1Form.get('salutation').clearValidators();
      this.step1Form.get('salutation').updateValueAndValidity();
      this.step1Form.get('first_name').clearValidators();
      this.step1Form.get('first_name').updateValueAndValidity();
      this.step1Form.get('last_name').clearValidators();
      this.step1Form.get('last_name').updateValueAndValidity();
      this.step1Form.get('contact_method').clearValidators();
      this.step1Form.get('contact_method').updateValueAndValidity();
      this.step1Form.get('phone_number').clearValidators();
      this.step1Form.get('phone_number').updateValueAndValidity();
      this.step1Form.get('state').clearValidators();
      this.step1Form.get('state').updateValueAndValidity();
      this.step1Form.get('district').clearValidators();
      this.step1Form.get('district').updateValueAndValidity();
      if (this.selectedUserTypeID == 2) {
        this.step1Form.get('companyID').clearValidators();
        this.step1Form.get('companyID').updateValueAndValidity();
      }
      if (this.selectedUserTypeID == 3) {
        this.step1Form.get('govt_body').clearValidators();
        this.step1Form.get('govt_body').updateValueAndValidity();
      }
      this.districtList = JSON.parse(window.localStorage.getItem('districtType'));
      this.step1Form.patchValue({
        'first_name': res.data.FIRST_NAME,
        'last_name': res.data.LAST_NAME,
        'email': res.data.EMAIL_ID,
        'state': res.data.STATE_TYPE_ID,
        'district': res.data.DISTRICT_TYPE_ID,
        'address': res.data.ADDRESS,
        'postal_code': res.data.PINCODE,
        'contact_method': res.data.CONTACT_METHOD_TYPE_ID,
        'organization': res.data.ORGANIZATION_NAME,
        'companyID': res.data.COMPANY_NAME,
        'govt_body': res.data.GOVERNMENT_DEPARTMENT_ID,
        'profession': res.data.PROFESSION_NAME,
        'salutation': res.data.SALUTATION_ID
      })
      this.reqUserId = res.data.USER_ID;
      if ((this.complaintSource === 'tams' && this.selectedUserTypeID == 6) ||
        (this.nams_step === 'tams' && this.complaintSource === 'WHATSAPP')) {
        this.step1Form.get('organization').setValue('NAMS (TAMS)');
      }
      if (this.complaintSource === 'reech' && this.selectedUserTypeID == 7) {
        this.step1Form.get('organization').setValue('NAMS (REECH)');
      }
    }, err => {
      this.userExist = false;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  step1Next(model) {
    this.isSelected = true;
    this.complaintService.complaintRegister(model, 0);
    this.complaintService.updateButtonName("Admin");
    if (this.selectedUserTypeID == 2 || this.selectedUserTypeID == 5) {
      model['companyID'] = this.companyId;
    } else {
      model['companyID'] = 0;
    }
    // this.createBlankComplaintRecord(this.userId);
    if (!this.userCreated) {
      this.userCreated = true;
      this.step1Form.get('phone_number').disable();
      if (!this.userExist) {
        if (this.complaintSource === 'WHATSAPP' || this.complaintSource === 'EMAIL') {
          model['phone_number'] = this.mobile;
        }
        this.complaintService.createUserByComplaint(model, this.roleId).subscribe(res => {
          this.reqUserId = res.data.USER_ID;
          if (this.complaintSource === 'SYSGEN') {
            this.blankComplaintID = this.complaintService.getBlankId;
            this.moveTabIndex = this.moveTabIndex + 2; //MOVE TAB TO STEP 2
          } else {
            this.createBlankComplaintRecord(res.data.USER_ID);
          }
          this.notify.showNotification(
            res.message,
            "top",
            (!!colorObj[res.status] ? colorObj[res.status] : "success"),
            res.status
          )
        }, err => {
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
        })
      } else {
        if (this.complaintSource === 'SYSGEN') {
          this.blankComplaintID = this.complaintService.getBlankId;
          this.moveTabIndex = this.moveTabIndex + 2; //MOVE TAB TO STEP 2
        } else {
          this.createBlankComplaintRecord(this.userId);
        }
      }
    } else {
      this.moveTabIndex = this.moveTabIndex + 2; //MOVE TAB TO STEP 2
    }
  }

  createBlankComplaintRecord(createdUserId) {
    this.complaintService.createBlankComplaint(this.selectedUserTypeID, this.userInfo.userId, createdUserId, this.is_FTC, this.created_date).subscribe(res => {
      this.blankComplaintID = res.data.COMPLAINT_ID;
      this.complaintService.updateBlankComplaintId = this.blankComplaintID;
      this.moveTabIndex = this.moveTabIndex + 2; //MOVE TAB TO STEP 2
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  setFromFilterOutput(event) {
    if (event.cond === 'create') {
      this.complaintObj = event.obj;
      this.complaintObj['USER_ID'] = this.reqUserId;
      this.complaintObj['ID'] = this.blankComplaintID;
      this.documents = this.complaintObj['files_attached'];
      delete this.complaintObj['files_attached'];
      if (this.complaintObj.CLAIMS.length != 0) {
        if (this.complaintObj.CLAIMS[0].ANNEXURE_NO != "") {
          this.claims = 'Yes';
        }
      }
      this.date = new Date();
      let todaydate = this.datePipe.transform(this.date, 'dd/MM/yyyy');
      this.rec_date = todaydate;
      this.ad_seen_date = this.complaintObj.ADVERTISEMENT_MEDIUM[0].DATE;
      let seen_date = this.datePipe.transform(this.ad_seen_date, 'dd/MM/yyyy');
      this.adv_seen_date = seen_date;
      let nams_date = this.datePipe.transform(this.complaintObj['DATE'], 'dd/MM/yyyy')
      this.namsDate = nams_date;
      this.contactMethod.forEach(element => {
        if (element.ID == this.step1Form.value.contact_method) {
          this.source = element.CONTACT_METHOD_TYPE_NAME;
        }
      });
      this.classification.forEach(el => {
        if (el.ID == this.complaintObj.CLASSIFICATION_ID) {
          this.classfy = el.CLASSIFICATION_NAME;
        }
      })
      this.ads.forEach(el => {
        if (el.ID == this.complaintObj.ADVERTISEMENT_MEDIUM[0].ADVERTISEMENT_SOURCE_ID) {
          this.medium = el.ADVERTISEMENT_SOURCE_NAME;
          this.selectedAdvId = el.ID;
        }
      })
      this.comp_details = (this.complaintObj.ADVERTISEMENT_DESCRIPTION).replace(/<[^>]+>/g, '');
      if (this.comp_details.length > 40) {
        this.detailText = ' ...'
      }
      this.platforms.forEach(el => {
        if (el.ID == this.complaintObj.ADVERTISEMENT_MEDIUM[0].PLATFORM_ID) {
          this.platform = el.PLATFORM_NAME;
        }
      })
      this.printMediums.forEach(el => {
        if (el.ID == this.complaintObj.ADVERTISEMENT_MEDIUM[0].PRINT_SOURCE_ID) {
          this.printMedium = el.PRINT_SOURCE_NAME;
        }
      })
      this.promotionalMaterialSource.forEach(el => {
        if (el.ID == this.complaintObj.ADVERTISEMENT_MEDIUM[0].P_M_SOURCE_ID) {
          this.promotionalMaterial = el.P_M_SOURCE_NAME;
        }
      })
      this.channel = this.complaintObj.ADVERTISEMENT_MEDIUM[0].SOURCE_NAME;
      this.sourcePlace = this.complaintObj.ADVERTISEMENT_MEDIUM[0].SOURCE_PLACE;
      this.media_link = this.complaintObj.ADVERTISEMENT_MEDIUM[0].SOURCE_URL;
      if (this.media_link != '' && this.media_link != null && this.media_link.length > 40) {
        this.medialLinkText = ' ...'
      }
      this.comp_fname = this.step1Form.controls['first_name'].value || (this.navigationState['from'] === 'NAMS' ? this.userInfo['firstName'] + " " + this.userInfo['lastName'] : '');
      this.comp_lname = this.step1Form.controls['last_name'].value;
      this.comp_email = this.step1Form.controls['email'].value;
      this.comp_phone = this.step1Form.controls['phone_number'].value || (this.navigationState['from'] === 'NAMS' ? this.userInfo['mobile'] : '');
      this.comp_against_company = this.complaintObj.COMPANY_NAME + `'s ` + this.complaintObj.BRAND_NAME;
      if (this.comp_against_company.length > 40) {
        this.againstText = ' ...'
      }
      this.comp_against_product = this.complaintObj['BRAND_NAME'];
      this.network = this.complaintObj['NETWORK'];
      this.media_outlet = this.complaintObj['MEDIA_OUTLET'];
      this.product_category = this.complaintObj['PRODUCT_CATEGORY'];
      this.media = this.complaintObj['MEDIA'];
      this.ad_language = this.complaintObj['AD_LANGUAGE'];
      this.creative_id = this.complaintObj['CREATIVE_ID'];
      this.duration = this.complaintObj['DURATION'];
      this.transcription = this.complaintObj['TRANSCRIPTION'];
      if (!!this.transcription && this.transcription.length > 40) {
        this.transcriptionText = ' ...'
      }
      this.comp_desc = this.complaintObj['COMPLAINT_DESCRIPTION'];
      if (this.comp_desc.length > 40) {
        this.compDescText = ' ...'
      }
      if (this.complaintObj['CODE_VIOLATED'] && this.complaintObj['CODE_VIOLATED']['length'] != 0) {
        this.code_violated = this.complaintObj.CODE_VIOLATED;
      }
      if (this.complaintObj['GUIDELINES'] && this.complaintObj['GUIDELINES']['length'] != 0) {
        this.guidelines = this.complaintObj.GUIDELINES;
      }

      this.publication = this.complaintObj['PUBLICATION_URL'];
      this.influencer = this.complaintObj['INFLUENCER_NAME'];
      this.profile_url = this.complaintObj['PROFILE_URL'];
      this.influencer_contact_no = this.complaintObj['INFLUENCER_MOBILE'];
      this.influencer_email_address = this.complaintObj['INFLUENCER_EMAIL'];

      this.moveTabIndex = this.moveTabIndex + 2; //MOVE TAB TO STEP 3
    } else if (event.cond === 'back') {
      this.moveTabIndex = this.moveTabIndex - 2; //MOVE TAB TO STEP 1
    }
  }

  submitForm() {
    const dialogRef = this.matDialog.open(RelatedComplaintsComponent, {
      width: '1500px',
      height: 'auto',
      data: { complaintObj: this.complaintObj },
      disableClose: false
    });
    dialogRef.afterClosed().subscribe(
      data => {
        if (data) {
          this.loading = true;
          if (data.cond === 'submit') {
            this.complaintObj['ML_DATA'] = data['ml_data'];
            this.complaintObj['PARENT_ID'] = data['parent_id'];
            this.complaintService.createComplaint(this.complaintObj).subscribe(res => {
              this.moveTabIndex = this.moveTabIndex - 5; //MOVE TAB TO STEP 1
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              this.userCreated = false;
              this.loading = false;
              this.complaintService.complaintRegister({}, 0);
              this.complaintService.complaintRegister({}, 1);
              if (this.consumer) {
                this.generalPublicComp.resetComplaintForm();
              } else if (this.company) {
                this.intraIndustryComp.resetComplaintForm();
              } else if (this.nams) {
                this.namsFormComp.resetComplaintForm();
              }
              this.nams = false;
              this.complaintService.callManageCaseComponent();
              this.complaintService.updateStep('direct');
              this.complaintService.updateChatbotComplaintId(0);
              this.complaintService.updateWhatsappComplaintId(0);
              this.complaintService.updateNamsComplaintId = 0;
              this.step1Form.reset();
              this.complaintSource = '';
              this.router.routeReuseStrategy.shouldReuseRoute = () => false;
              this.router.onSameUrlNavigation = 'reload';
              this.router.navigate(['manage-cases']);
            }, err => {
              this.loading = false;
              this.notify.showNotification(
                err.error.message,
                "top",
                (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                err.error.status
              )
            })
          } else if (data.cond === 'cancel') {
            this.cancel();
          }
        }
      }
    );

  }

  cancel() {
    this.step1Form.reset();
    this.complaintService.updateChatbotComplaintId(0);
    this.complaintService.updateWhatsappComplaintId(0);
    this.complaintService.complaintRegister({}, 0);
    this.complaintService.complaintRegister({}, 1);
    this.complaintService.updateStep('direct');
    this.complaintSource = '';
    this.nams = false;
    if (this.consumer) {
      this.generalPublicComp.resetComplaintForm();
    } else if (this.company) {
      this.intraIndustryComp.resetComplaintForm();
    } else if (this.nams) {
      this.namsFormComp.resetComplaintForm();
    }
    this.isSelected = false;
    this.files = [];
    if (this.userCreated) {
      this.userCreated = false;
      if (this.blankComplaintID != 0) {
        this.complaintService.deleteBlankComplaint(this.blankComplaintID).subscribe(res => {
          this.router.navigate(['manage-cases']);
          this.loading = false;
        }, err => {
          this.router.navigate(['manage-cases']);
          this.notify.showNotification(
            err.error.message,
            "top",
            (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
            err.error.status
          )
          this.loading = false;
        })
      } else {
        this.router.navigate(['manage-cases']);
        this.loading = false;
      }
    } else {
      this.loading = false;
      this.router.navigate(['manage-cases']);
    }
  }

  searchForm() {
    this.isShown = !this.isShown;
  }

  backSearch() {
    this.isShown = !this.isShown;
  }

  openNewTab(source) {
    if (source.indexOf("https") == -1) {
      window.open("https://" + source, "_blank");
    } else {
      window.open(source, "_blank");
    }
  }


}
