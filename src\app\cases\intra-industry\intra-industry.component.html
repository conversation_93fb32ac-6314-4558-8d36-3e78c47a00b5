<div class="comp-head" *ngIf="isFTCComplaint == true">
  FAST TRACK COMPLAINT (FTC) DETAILS
</div>
<div class="comp-head" *ngIf="isFTCComplaint == false || !isFTCComplaint">
  COMPLAINT DETAILS
</div>
<div class="comp-head-mandatory">
  * labled fields are mandatory
</div>
<form [formGroup]="advertiserForm" class="form">
  <div class="step1-container">
    <div class="row-container">
      <div class="input-container">
        <div class="text-container">
          Advertiser's Company<span style="color: red;"> * </span>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="basic-input-field">
            <input matInput type="text" formControlName="company" [matAutocomplete]="autoCompany"
              (blur)="companyInput()" (input)="companyChange()">
            <mat-autocomplete #autoCompany="matAutocomplete" (optionSelected)="onSelectionChange($event)">
              <mat-option *ngFor="let company of companyList;" [value]="company.COMPANY_NAME" style="
                          font-style: normal;
                          font-weight: normal;">
                {{company.COMPANY_NAME}}
              </mat-option>
            </mat-autocomplete>
            <mat-error class="error-msg" *ngIf="advertiserForm.controls['company'].errors?.required">
              Company Name is required
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="input-container">
        <div class="text-container">
          Brand Name of Product/Service <span style="color: red;">*</span>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="basic-input-field">
            <input matInput formControlName="brand" autocomplete="off">
            <mat-error class="error-msg" *ngIf="advertiserForm.controls['brand'].errors?.required">
              Brand Name is required
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="input-container">
        <div class="text-container">
          Product Name <span style="color: red;">*</span>
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="basic-input-field">
            <input matInput formControlName="product" autocomplete="off">
            <mat-error class="error-msg" *ngIf="advertiserForm.controls['product'].errors?.required">
              Product Name is required
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>

    <div class="row-container">
      <div class="input-container">
        <div class="text-container">
          Product category
        </div>
        <div class="control-container">
          <mat-form-field appearance="outline" class="basic-input-field">
            <input matInput formControlName="product_category" autocomplete="off">
          </mat-form-field>
        </div>
      </div>
    </div>

    <div class="row-container" style="margin-left:20px;">
      <div class="text-container" style="margin-bottom: 11px;">
        <mat-label style="font-weight: 500;">Advertisement medium details (Max 4)</mat-label>
      </div>
    </div>
    <div formArrayName="advMedium">
      <div class="outer-btn-container" *ngFor="let advMedium of getControls1(); let in = index;" formGroupName="{{in}}">
        <div class="outer-row-container">
          <div fxLayout="row" style="padding: 1% 0% 2% 2%;">
            <div fxFlex="20%">
              <img src="../../assets/images/drop-arrow.png" style="margin-right: 5px">
              <mat-label style="color: #0088CB; font-size: 13px;">Advertisement spot {{in+1}}</mat-label>
            </div>
            <div fxFlex="78%"></div>
            <div fxFlex="2%" fxLayoutAlign="end">
              <button mat-button matSuffix mat-icon-button aria-label="Clear" class="delete-red"
                [disabled]="advertiserForm.controls.advMedium.controls.length == 1" (click)="removeAdve(in)">
                <img src="../assets/images/Trash-icon.svg" style="margin-top: -15px;">
              </button>
            </div>
          </div>
          <div class="inner-row-container">
            <div class="input-container">
              <div class="text-container">
                Where did you see the Ad <span style="color: red;">*</span>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <mat-select formControlName="seen_medium">
                    <mat-option *ngFor="let source of ads" [value]="source.ID" (click)="showFields(source, in)">
                      {{source.ADVERTISEMENT_SOURCE_NAME}}</mat-option>
                  </mat-select>
                  <mat-error class="error-msg" *ngIf="advMedium.controls['seen_medium'].value==''">
                    Please Choose Advertisement Source Name
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
            <div class="input-container">
              <div
                *ngIf="advMedium.controls['seen_medium'].value == 1 || advMedium.controls['seen_medium'].value == 2 || advMedium.controls['seen_medium'].value == 3 || advMedium.controls['seen_medium'].value == 5
              || advMedium.controls['seen_medium'].value == 6 || advMedium.controls['seen_medium'].value == 7 || advMedium.controls['seen_medium'].value == 8 || advMedium.controls['seen_medium'].value == 9">
                <div class="text-container">
                  <span
                    *ngIf="advMedium.controls['seen_medium'].value == 1 || advMedium.controls['seen_medium'].value == 2">
                    Please specify the channel <span style="color: red;">*</span></span>
                  <span *ngIf="advMedium.controls['seen_medium'].value == 3">Please specify the platform <span
                      style="color: red;">*</span></span>
                  <span *ngIf="advMedium.controls['seen_medium'].value == 5">Where have you seen the advertisement?
                    <span style="color: red;">*</span></span>
                  <span *ngIf="advMedium.controls['seen_medium'].value == 6">Type of material <span
                      style="color: red;">*</span></span>
                  <span *ngIf="advMedium.controls['seen_medium'].value == 8">Sender <span
                      style="color: red;">*</span></span>
                  <span *ngIf="advMedium.controls['seen_medium'].value == 9">Source <span
                      style="color: red;">*</span></span>
                  <span *ngIf="advMedium.controls['seen_medium'].value == 7">MFD/PKD Date <span
                      style="color: red;">*</span></span>
                </div>
                <div class="control-container">
                  <mat-form-field appearance="outline" class="input-field"
                    *ngIf="!(advMedium.controls['seen_medium'].value == 3 || advMedium.controls['seen_medium'].value == 5 || advMedium.controls['seen_medium'].value == 6 || advMedium.controls['seen_medium'].value == 7)">
                    <input matInput formControlName="method" autocomplete="off">
                    <mat-error class="error-msg"
                      *ngIf="(advMedium.controls['method'].dirty ||advMedium.controls['method'].touched)">
                      Please specify the source
                    </mat-error>
                  </mat-form-field>
                  <mat-form-field appearance="outline" class="input-field"
                    *ngIf="advMedium.controls['seen_medium'].value == 3">
                    <mat-select formControlName="platform">
                      <mat-option *ngFor="let plat of platforms" [value]="plat.ID" (click)="changePlatform(plat)">
                        {{plat.PLATFORM_NAME}}</mat-option>
                    </mat-select>
                    <mat-error class="error-msg"
                      *ngIf="advMedium.controls['platform'].errors?.required  && (advMedium.controls['platform'].dirty ||advMedium.controls['platform'].touched)">
                      Please choose the platform
                    </mat-error>
                  </mat-form-field>
                  <mat-form-field appearance="outline" class="input-field"
                    *ngIf="advMedium.controls['seen_medium'].value == 5">
                    <mat-select formControlName="printSource">
                      <mat-option *ngFor="let print of printSources" [value]="print.ID">
                        {{print.PRINT_SOURCE_NAME}}</mat-option>
                    </mat-select>
                    <mat-error class="error-msg" *ngIf="advMedium.controls['printSource'].errors?.required ">
                      Please choose the print source
                    </mat-error>
                  </mat-form-field>
                  <mat-form-field appearance="outline" class="input-field"
                    *ngIf="advMedium.controls['seen_medium'].value == 6">
                    <mat-select formControlName="promotionType">
                      <mat-option *ngFor="let promType of promotionTypes" [value]="promType.ID">
                        {{promType.P_M_SOURCE_NAME}}</mat-option>
                    </mat-select>
                    <mat-error class="error-msg" *ngIf="advMedium.controls['promotionType'].errors?.required">
                      Please Choose the type of material
                    </mat-error>
                  </mat-form-field>
                  <mat-form-field appearance="outline" class="input-field"
                    *ngIf="advMedium.controls['seen_medium'].value == 7">
                    <input matInput [matDatepicker]="picker" formControlName="date" [max]="maxDate" autocomplete="off">
                    <span matSuffix><img src="../../assets/images/calendar.svg" style="position: relative;top: -8px;"
                        (click)="picker.open()"></span>
                    <mat-datepicker #picker></mat-datepicker>
                    <mat-error class="error-msg" *ngIf="advMedium.controls['date'].value==''">
                      Please choose the date
                    </mat-error>
                  </mat-form-field>
                </div>
              </div>
            </div>
          </div>

          <div class="inner-row-container" *ngIf="advMedium.controls['seen_medium'].value == 3 && platform_id == 9">
            <div class="input-container">
              <div class="text-container">
                Specify the platform name <span style="color: red;">*</span>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <input matInput formControlName="sourcePlace" autocomplete="off">
                  <mat-error class="error-msg" *ngIf="advMedium.controls['sourcePlace'].errors?.required">
                    Please Specify the platform
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
          </div>

          <div class="inner-row-container">
            <div class="input-container" *ngIf="advMedium.controls['seen_medium'].value != 7">
              <div class="text-container" *ngIf="advMedium.controls['seen_medium'].value != 2">
                Date on which the ad was seen <span style="color: red;">*</span>
              </div>
              <div class="text-container" *ngIf="advMedium.controls['seen_medium'].value == 2">
                Date on which the ad was heard <span style="color: red;">*</span>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <input matInput [matDatepicker]="picker" formControlName="date" [max]="maxDate" autocomplete="off">
                  <span matSuffix><img src="../../assets/images/calendar.svg" style="position: relative;top: -8px;"
                      (click)="picker.open()"></span>
                  <mat-datepicker #picker></mat-datepicker>
                  <mat-error class="error-msg" *ngIf="advMedium.controls['date'].value==''">
                    Please select the date
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
            <div class="input-container" *ngIf="advMedium.controls['seen_medium'].value == 4 || advMedium.controls['seen_medium'].value == 5
              || advMedium.controls['seen_medium'].value == 6 || advMedium.controls['seen_medium'].value == 9">
              <div class="text-container">
                <span *ngIf="advMedium.controls['seen_medium'].value == 4">Name the place where you saw the hoarding?
                  <span style="color: red;">*</span></span>
                <span *ngIf="advMedium.controls['seen_medium'].value == 5">Specify the publication name & edition <span
                    style="color: red;">*</span></span>
                <span *ngIf="advMedium.controls['seen_medium'].value == 6">Name the place where you saw the Ad? <span
                    style="color: red;">*</span></span>
                <span *ngIf="advMedium.controls['seen_medium'].value == 9">Place if applicable</span>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field"
                  *ngIf="!(advMedium.controls['seen_medium'].value == 5)">
                  <input matInput formControlName="sourcePlace" autocomplete="off">
                  <mat-error class="error-msg"
                    *ngIf="advMedium.controls['sourcePlace'].errors?.required && !(advMedium.controls['seen_medium'].value == 5)">
                    Please specify source place
                  </mat-error>
                </mat-form-field>
                <mat-form-field appearance="outline" class="input-field"
                  *ngIf="advMedium.controls['seen_medium'].value == 5">
                  <input matInput formControlName="method" autocomplete="off">
                  <mat-error class="error-msg"
                    *ngIf="advMedium.controls['method'].errors?.required  && advMedium.controls['seen_medium'].value == 5">
                    Please specify publication name & edition
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
            <div class="input-container" *ngIf="advMedium.controls['seen_medium'].value == 1
            || advMedium.controls['seen_medium'].value == 2 || advMedium.controls['seen_medium'].value == 3">
              <div class="text-container">
                What was the time <span style="color: red;">*</span>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <input matInput formControlName="time" matTimepicker autocomplete="off">
                  <span matSuffix><img src="../../../assets/images/Clock.png" style="position: relative; top: -7px;"
                      matTimepicker></span>
                  <mat-error class="error-msg" *ngIf="advMedium.controls['time'].value==''">
                    Please choose the time
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="inner-row-container">
            <div class="input-container">
              <div class="text-container">
                Do you have a copy of the advertisement and/or other supporting documentation? Please
                upload/paste a link. <span style="color: red;">*</span>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field" style="width: 93%">
                  <span matPrefix class="url-container-text">
                    <img src="../../assets/images/url.png">
                    &nbsp;
                  </span>
                  <input matInput type="url" formControlName="add_url" placeholder="Add url" autocomplete="off">
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="inner-row-container">
            <div fxFlex="43%"></div>
            <p style="color: rgba(0, 0, 0, 0.6);">or</p>
          </div>
          <div class="inner-row-container">
            <div class="example-boundary">
              <div fxLayout="column" cdkDrag (click)="fileInput.click()" style="cursor: pointer;">
                <div>
                  <img style="margin-left: 27%;" src="../../assets/images/upload-icon.png">
                </div>
                <div fxLayout="row" style="margin-left: 13%;">
                  <p style="color: #0088CB;">Upload</p>
                  <p style="color: #707070;margin-left: 4px;">your files here</p>
                  <p class="progress-spinner"
                    *ngIf="(filesAdvertiseProgress[in]?.progress > 0 && filesAdvertiseProgress[in]?.progress !== 100)">
                    <mat-spinner strokeWidth="3" [diameter]="20"></mat-spinner>
                  </p>
                </div>
                <input style="display: none" [disabled]="isUploadAdvProgress" #attachments type="file" multiple="true"
                  accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt,.jpeg,.png,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv"
                  (change)="onFileChanged($event, in)" #fileInput>
              </div>
            </div>
          </div>
          <div class="inner-row-container-docs" style="margin-left: 20px; margin-bottom: 20px;">
            <div fxLayout="column" class="docs_attached" style="margin-bottom: 20px;margin-top: 30px;">
              <div *ngFor="let videos of advMedium.controls['advFileArray'].value; let index = index" fxLayout="row"
                fxLayoutGap="5px" class="file-container" style="width: 93%;">
                <div fxLayout="row" fxFlex="100%" class="adv_docs">
                  <!-- <div fxFlex="4%" style="height: 33px; width: 35px; background: rgba(0, 136, 203, 0.25);
                    border-radius: 3px 0px 0px 3px;">
                    <img src="../../assets/images/vvid.png" style="margin: 12px 10px;">
                  </div> -->
                  <div fxLayout="row" fxFlex="100%" style="vertical-align: middle;align-items: center;">
                    <div fxFlex="95%" fxLayoutAlign="start" style="color: rgb(150, 148, 148);">
                      <p class="adv-file-text">{{videos.ATTACHMENT_NAME}}</p>
                    </div>
                    <div fxFlex="5%" fxLayoutAlign="end" style="height: 30px;">
                      <button mat-button mat-icon-button (click)="preview(videos.ATTACHMENT_SOURCE)"
                        style="bottom: 5px;">
                        <img src="../../../assets/images/View.png" style="margin: 0px 10px 0px 10px;">
                      </button>
                    </div>
                    <div fxFlex="5%" fxLayoutAlign="end" style="height: 30px;">
                      <button mat-button mat-icon-button aria-label="Clear" (click)="removeVideoFile(in, index)"
                        style="bottom: 5px;">
                        <img src="../../assets/images/close-red.png" style="margin: 0px 10px 0px 10px;">
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <section *ngIf="filesAdvertiseProgress[in]?.progress > 0">
                <mat-progress-bar [color]='primary' [mode]="determinate" [value]="filesAdvertiseProgress[in]?.progress"
                  style="width: 93%;">
                </mat-progress-bar>
              </section>
            </div>
          </div>
          <mat-error style="position: relative;bottom: 77px;left: 23px;font-size: 12px;"
            *ngIf="advMedium.controls['add_url'].dirty ||advMedium.controls['add_url'].touched">
            {{getFileVal(in)}}
          </mat-error>
        </div>
      </div>
    </div>
    <div fxLayout="row">
      <div fxFlex="63vw" fxLayoutAlign="end" style="margin-left: 19px;">
        <button mat-flat-button class="add-source-button" (click)="addAdve()">
          + Add advertisement medium
        </button>
      </div>
    </div>
    <div class="row-container" style="margin-top: 10px;">
      <div class="input-container">
        <div class="text-container" style="font-weight: 500; margin-bottom: 10px;">
          Describe the Advertisement (max 5000 characters)<span style="color: red;"> * </span>
        </div>
        <!-- <div class="textarea-field">
          <div class="toolbar">
            <ul class="tool-list">
              <li class="tool">
                <button type="button" data-command="bold" class="tool--btn">
                  <i class=' fas fa-bold' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command="italic" class="tool--btn">
                  <i class=' fas fa-italic' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command="underline" class="tool--btn">
                  <i class=' fas fa-underline' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command='justifyLeft' class="tool--btn">
                  <i class=' fas fa-align-left' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command='justifyCenter' class="tool--btn">
                  <i class=' fas fa-align-center' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command='justifyRight' class="tool--btn">
                  <i class=' fas fa-align-right' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command='justifyFull' class="tool--btn">
                  <i class=' fas fa-align-justify' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command="insertOrderedList" class="tool--btn">
                  <i class=' fas fa-list-ol' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command="insertUnorderedList" class="tool--btn">
                  <i class=' fas fa-list-ul' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command="indent" class="tool--btn">
                  <i class=' fas fa-indent' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command="outdent" class="tool--btn">
                  <i class=' fas fa-outdent' style="font-size: 15px;"></i>
                </button>
              </li>
            </ul>
          </div>
          <mat-divider></mat-divider>
          <textarea matInput id="output" formControlName="ad_description" rows="5"
            placeholder="Tell us about the advertisement in brief... &#13;&#10;Eg: The advertisement shows two kids playing in mud and dirtying themselves, mother of one says don't worry you can take a bath with this soap and it has 10times more power to protect against disease causing bacteria and viruses."
            style="text-align:justify; text-justify: inter-word;" autocomplete="off">
          </textarea> -->
        <quill-editor class="textarea-field"
          placeholder="Tell us about the advertisement in brief... &#13;&#10;Eg: The advertisement shows two kids playing in mud and dirtying themselves, mother of one says don't worry you can take a bath with this soap and it has 10 times more power to protect against disease causing bacteria and viruses."
          [modules]="quillConfiguration" formControlName="ad_description" (onContentChanged)="textChangedAdDesc($event)"
          style="text-align:justify; text-justify: inter-word;" autocomplete="off"> </quill-editor>
        <!-- </div> -->
        <mat-error class="error-msg"
          *ngIf="advertiserForm.controls['ad_description'].touched && advertiserForm.controls['ad_description'].errors?.required"
          style="position: relative;bottom: 19px;font-size: 12px;">
          Describe the advertisement with max length 5000
        </mat-error>
      </div>
    </div>

    <div class="row-container" style="margin-left:20px;">
      <div class="text-container" style="margin-bottom: 11px;">
        <mat-label style="font-weight: 500;">ASCI Code provision violated (Max 5)</mat-label>
        <span style="color: red;"> * </span>
      </div>
    </div>
    <div formArrayName="asciCode">
      <div class="outer-btn-container" *ngFor="let asciCodeControl of getControls2(); let in = index;"
        formGroupName="{{in}}">
        <div class="outer-row-container">
          <div fxLayout="row" style="padding: 1% 0% 2% 2%;">
            <div fxFlex="14%">
              <img src="../../assets/images/drop-arrow.png" style="margin-right: 5px">
              <mat-label style="color: #0088CB; font-size: 13px;">Code violated {{in+1}}</mat-label>
            </div>
            <div fxFlex="78%"></div>
            <div fxFlex="8%" fxLayoutAlign="end">
              <button mat-button matSuffix mat-icon-button aria-label="Clear" class="delete-red"
                [disabled]="advertiserForm.controls.asciCode.controls.length == 1" (click)="removeCodes(in)">
                <img src="../assets/images/Trash-icon.svg" style="margin-top: -15px;">
              </button>
            </div>
          </div>
          <div class="inner-row-container">
            <div class="input-container">
              <div class="text-container">
                Chapter
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <mat-select formControlName="chapter">
                    <!-- <mat-select-trigger> -->
                    <!-- Chapter {{chapters[0].chapter}}
                    </mat-select-trigger> -->
                    <mat-option *ngFor="let chap of chapter; let i = index;" [value]="chap.ID"
                      (click)="selectChapter(chap, in)">
                      <strong>Ch:{{chap.ID}}</strong> {{chap.CHAPTER_NAME}}
                    </mat-option>
                  </mat-select>
                  <mat-error class="error-msg" *ngIf="asciCodeControl.controls['chapter'].value==''">
                    Please Choose the chapter
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
            <div class="input-container">
              <div class="text-container">
                Clause
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <mat-select formControlName="clause" multiple>
                    <!-- <mat-select-trigger>
                      <ng-container *ngFor="let clause of cLauses">
                        {{ clause.CLAUSE_NUMBER }}
                    </ng-container>
                     </mat-select-trigger> -->
                    <mat-option *ngFor="let clauseValues of asciCodeControl.controls['clauseArray'].value"
                      [value]="clauseValues.CLAUSE_NUMBER" matTooltip="{{clauseValues.CLAUSE_DESCRIPTION}}"
                      (click)="selectClause(in)">
                      <strong>{{clauseValues.CLAUSE_NUMBER}}</strong> : {{clauseValues.CLAUSE_DESCRIPTION}}
                    </mat-option>
                    <!--passing [value]="clauseValues.CLAUSE_NUMBER" instead of [value]="clauseValues.ID" -->
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="inner-row-container" *ngIf="isChapterPreamble" style="padding: 10px 0 ;">
            <div class="input-container">
              <div class="text-container">
                <span style="color: #707070;">Chapter Preamble:</span>
                {{asciCodeControl.controls['preamble'].value}}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div fxLayout="row">
        <div fxFlex="63vw" fxLayoutAlign="end" style="margin-left: 19px;">
          <button mat-flat-button class="add-source-button" (click)="addCodes()">
            + Add code violated
          </button>
        </div>
      </div>
    </div>

    <div class="row-container" style="margin-left:20px">
      <div class="text-container" style="margin-bottom: 11px;">
        <mat-label style="font-weight: 500;">ASCI Guideline violated (Max 5)</mat-label>
        <span style="color: red;"> * </span>
      </div>
    </div>
    <div formArrayName="guideline">
      <div class="outer-btn-container" *ngFor="let asciCodeControl1 of getControls5(); let in = index;"
        formGroupName="{{in}}"
        [ngClass]="{'outer-btn-container1': asciCodeControl1.controls['gPreamble'].value == null}">
        <div
          [ngClass]="{'outer-row-container1': asciCodeControl1.controls['gPreamble'].value == null, 'outer-row-container': asciCodeControl1.controls['gPreamble'].value != null}">
          <div fxLayout="row" style="padding: 1% 0% 2% 2%;">
            <div fxFlex="18%">
              <img src="../../assets/images/drop-arrow.png" style="margin-right: 5px">
              <mat-label style="color: #0088CB; font-size: 13px;">Guideline violated {{in+1}}</mat-label>
            </div>
            <div fxFlex="76%"></div>
            <div fxFlex="6%" fxLayoutAlign="end">
              <button mat-button matSuffix mat-icon-button aria-label="Clear" class="delete-red"
                [disabled]="advertiserForm.controls.guideline.controls.length == 1 || in == 0"
                (click)="removeGuidelines(in)">
                <img src="../assets/images/Trash-icon.svg" style="margin-top: -15px;">
              </button>
            </div>
          </div>
          <div class="inner-row-container">
            <div class="input-container">
              <div class="text-container">
                Select guideline
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <mat-select formControlName="gChapter">
                    <!-- <mat-select-trigger>
                      Guideline {{guidelinesASCI.gChapter}}
                    </mat-select-trigger> -->
                    <mat-option
                      *ngFor="let guideline of asciCodeControl1.controls['gGuidelineArray'].value; let i = index;"
                      matTooltip="{{guideline.GUIDELINE_NAME}}" [value]="guideline.ID"
                      (click)="selectGuideline(guideline, in)">
                      {{guideline.GUIDELINE_NAME}}
                    </mat-option>
                  </mat-select>
                  <mat-error class="error-msg" *ngIf="asciCodeControl1.controls['gChapter'].value==''">
                    Please choose the guideline
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
            <div class="input-container" *ngIf="asciCodeControl1.controls['gPreamble'].value != null">
              <div class="text-container">
                Clause
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <mat-select formControlName="gClause" multiple>
                    <mat-option *ngFor="let clauseValues of asciCodeControl1.controls['gClauseArray'].value"
                      [value]="clauseValues.G_CLAUSE_NUMBER" matTooltip="{{clauseValues.G_CLAUSE_DESCRIPTION}}"
                      (click)="selectClause(in)">
                      <strong>{{clauseValues.G_CLAUSE_NUMBER}}</strong> : {{clauseValues.G_CLAUSE_DESCRIPTION}}
                    </mat-option>
                    <!--passing [value]="clauseValues.G_CLAUSE_NUMBER" instead of [value]="clauseValues.ID" -->
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="inner-row-container" *ngIf="celebrityGuideline">
            <div class="input-container">
              <div class="text-container">
                Celebrity names
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <mat-chip-list #chipLists aria-label="celebrity" class="input-control">
                    <mat-chip *ngFor="let celebrity of asciCodeControl1.controls['gCelebrityArray'].value" [selectable]="selectable" [removable]="removable"
                      (removed)="removeCelebrity(celebrity, in)" style="font-size: 11px;">
                      {{celebrity}}
                      <mat-icon matChipRemove>cancel</mat-icon>
                    </mat-chip>
                    <input placeholder="Enter celebrity names" [matChipInputFor]="chipLists" [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                      [matChipInputAddOnBlur]="addOnBlur" (matChipInputTokenEnd)="addCelebrity($event, in)"
                      style="word-wrap: break-word;word-break: break-all">
                  </mat-chip-list>
                  <mat-error class="error-msg" *ngIf="asciCodeControl1.controls['gCelebrityArray'].value==[]">
                    Please enter celebrity names
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="inner-row-container"
            *ngIf="isGuidelinePreamble && asciCodeControl1.controls['gPreamble'].value != null" style="padding: 10px 0 ;">
            <div class="input-container">
              <div class="text-container">
                <span style="color: #707070;">Guideline Preamble:</span>
                {{asciCodeControl1.controls['gPreamble'].value}}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div fxLayout="row">
        <div fxFlex="63vw" fxLayoutAlign="end" style="margin-left: 19px;">
          <button mat-flat-button class="add-source-button" (click)="addGuidelines()" [disabled]="disableGuideline">
            + Add guideline violated
          </button>
        </div>
      </div>
    </div>

    <div class="row-container" style="margin-left:20px;">
      <div class="text-container" style="margin-bottom: 11px;">
        <mat-label style="font-weight: 600;">Claims raised (Max 3)</mat-label>
        <!-- <span style="color: red;">*</span> -->
      </div>
    </div>
    <div formArrayName="claims">
      <div class="outer-btn-container" *ngFor="let claims of getControls3(); let in = index;" formGroupName="{{in}}">
        <div class="outer-row-container">
          <div fxLayout="row" style="padding: 1% 0% 2% 2%;">
            <div fxFlex="20%">
              <!-- <img src="../../assets/images/drop-arrow.png" style="margin-right: 5px"> -->
              <mat-label style="color: #000000; font-size: 13px; font-weight: 500;">Claim Challenged
                {{in+1}}</mat-label>
            </div>
            <div fxFlex="73%"></div>
            <div fxFlex="7%" fxLayoutAlign="end">
              <button mat-button matSuffix mat-icon-button aria-label="Clear" class="delete-red"
                [disabled]="advertiserForm.controls.claims.controls.length == 1" (click)="removeClaims(in)">
                <img src="../assets/images/Trash-icon.svg" style="margin-top: -15px;">
              </button>
            </div>
          </div>
          <div class="inner-row-container">
            <div class="input-container">
              <div class="text-container">
                Claim challenged
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <input matInput formControlName="claimchallenge" autocomplete="off">
                </mat-form-field>
              </div>
            </div>
            <div class="input-container">
              <div class="text-container">
                Annexure no
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <input matInput formControlName="annexure" autocomplete="off">
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="inner-row-container">
            <div class="input-container">
              <div class="text-container">
                Key objection (max 5000 characters)
              </div>
              <div class="objection-text">
                <!-- <div appearance="outline" class="textarea-field1">
                  <div class="toolbar">
                    <ul class="tool-list">
                      <li class="tool">
                        <button type="button" data-command="bold" class="tool--btn">
                          <i class=' fas fa-bold' style="font-size: 15px;"></i>
                        </button>
                      </li>
                      <li class="tool">
                        <button type="button" data-command="italic" class="tool--btn">
                          <i class=' fas fa-italic' style="font-size: 15px;"></i>
                        </button>
                      </li>
                      <li class="tool">
                        <button type="button" data-command="underline" class="tool--btn">
                          <i class=' fas fa-underline' style="font-size: 15px;"></i>
                        </button>
                      </li>
                      <li class="tool">
                        <button type="button" data-command='justifyLeft' class="tool--btn">
                          <i class=' fas fa-align-left' style="font-size: 15px;"></i>
                        </button>
                      </li>
                      <li class="tool">
                        <button type="button" data-command='justifyCenter' class="tool--btn">
                          <i class=' fas fa-align-center' style="font-size: 15px;"></i>
                        </button>
                      </li>
                      <li class="tool">
                        <button type="button" data-command='justifyRight' class="tool--btn">
                          <i class=' fas fa-align-right' style="font-size: 15px;"></i>
                        </button>
                      </li>
                      <li class="tool">
                        <button type="button" data-command='justifyFull' class="tool--btn">
                          <i class=' fas fa-align-justify' style="font-size: 15px;"></i>
                        </button>
                      </li>
                      <li class="tool">
                        <button type="button" data-command="insertOrderedList" class="tool--btn">
                          <i class=' fas fa-list-ol' style="font-size: 15px;"></i>
                        </button>
                      </li>
                      <li class="tool">
                        <button type="button" data-command="insertUnorderedList" class="tool--btn">
                          <i class=' fas fa-list-ul' style="font-size: 15px;"></i>
                        </button>
                      </li>
                      <li class="tool">
                        <button type="button" data-command="indent" class="tool--btn">
                          <i class=' fas fa-indent' style="font-size: 15px;"></i>
                        </button>
                      </li>
                      <li class="tool">
                        <button type="button" data-command="outdent" class="tool--btn">
                          <i class=' fas fa-outdent' style="font-size: 15px;"></i>
                        </button>
                      </li>
                    </ul>
                  </div>
                  <mat-divider></mat-divider>
                  <textarea matInput id="output" formControlName="objections" rows="5"
                    placeholder="Describe your key objections.." maxlength="5000" autocomplete="off" 
                    style="text-align:justify; text-justify: inter-word;"></textarea> -->
                <quill-editor class="textarea-field" placeholder="Describe your key objections.."
                  [modules]="quillConfiguration" formControlName="objections"
                  (onContentChanged)="textChangedKeyObjections($event,in)" autocomplete="off"
                  style="text-align:justify; text-justify: inter-word;"> </quill-editor>
                <!-- </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <div fxLayout="row">
        <div fxFlex="63vw" fxLayoutAlign="end" style="margin-left: 19px;">
          <button mat-flat-button class="add-source-button" (click)="addClaimchallenges()">
            + Add claim
          </button>
        </div>
      </div>
    </div>

    <div class="row-container">
      <div class="input-container">
        <div class="text-container" style="margin-bottom: 10px; font-weight: 500;">
          Summary of list of documents to your complaint / claim
        </div>
        <div class="example-boundary1">
          <div fxLayout="column" cdkDrag (click)="fileInput.click()" style="cursor: pointer;">
            <div fxLayout="row" fxLayoutAlign="center">
              <img src="../../assets/images/upload-icon.png">
            </div>
            <div fxLayout="row" fxLayoutAlign="center">
              <p style="color: #0088CB;">Upload</p>
              <p style="color: #707070; margin-left: 4px;">your files here (max-size : 35MB & max-files :
                10 files)</p>
              <p class="progress-spinner" *ngIf="isUploadProgress">
                <mat-spinner strokeWidth="3" [diameter]="20"></mat-spinner>
              </p>
            </div>
            <input style="display: none" #attachments type="file" (change)="onFileSelected($event)" #fileInput
              multiple="true"
              accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt,.jpeg,.png,.mp3,.mp4,.m4a,.flac,.wav,.mov,.wmv">
          </div>
        </div>
      </div>
    </div>
    <div formArrayName="claimsDocument">
      <div class="outer-btn-container" *ngFor="let claimsDoc of getControls4(); let in = index;" formGroupName="{{in}}">
        <div class="input-container">
          <div fxLayout="column" fxLayoutGap="8px" class="docs_attached">
            <div fxLayout="row" fxLayoutGap="5px" class="file-container">
              <div class="outer-btn-container">
                <div class="outer-row-container">
                  <div fxLayout="row" style="padding: 1% 0% 2% 2%;">
                    <div>
                      <mat-label style="color: #000000; font-size: 13px; font-weight: 500;">Document
                        {{in+1}}
                      </mat-label>
                    </div>
                    <div fxFlex="86%"></div>
                    <div>
                      <button mat-button matSuffix mat-icon-button aria-label="Clear" class="delete-red"
                        (click)="removeDocFile(in)">
                        <img src="../assets/images/Trash-icon.svg" style="margin-top: -15px;">
                      </button>
                    </div>
                  </div>
                  <div fxLayout="column" fxLayoutGap="20px">
                    <div class="inner-row-container">
                      <div class="input-container">
                        <div fxLayout="row" fxLayoutGap="5px" fxFlex="100%" class="adv_docs">
                          <div fxFlex="4%" style="height: 33px; width: 35px; background: rgba(0, 136, 203, 0.25);
                                                    border-radius: 3px 0px 0px 3px;">
                            <img src="../../assets/images/File.png" style="margin: 12px 10px;">
                          </div>
                          <div fxLayout="row" fxFlex="95%" style="vertical-align: middle;align-items: center;">
                            <div fxFlex="60%" fxLayoutAlign="start" style="color: rgb(150, 148, 148);">
                              {{claimsDoc.controls['attachmentName'].value}} </div>
                            <div fxFlex="21%" fxLayoutAlign="end" style="margin-bottom: -10px;"
                              (click)="preview(claimsDoc.controls['attachmentSource'].value)">
                              <p class="link-text">View</p>
                            </div>
                            <div fxFlex="18%" fxLayoutAlign="end" style="margin-bottom: -10px;"
                              (click)="fileInput.click()">
                              <input style="display: none" #attachments type="file"
                                (change)="onFileReplaced($event, in)"
                                accept=".gif,.jpg,.jpeg,.png,.doc,.docx,.xlsx,.PPTX,.pdf,.csv,.txt" #fileInput>
                              <p class="link-text">Change document</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="inner-row-container">
                      <div class="input-container">
                        <div class="text-container">
                          Type of document
                        </div>
                        <div class="control-container">
                          <mat-form-field appearance="outline" class="claims-input-field">
                            <input matInput formControlName="documentType" autocomplete="off">
                          </mat-form-field>
                        </div>
                      </div>
                      <div class="input-container">
                        <div class="text-container">
                          Annexure no.
                        </div>
                        <div class="control-container">
                          <mat-form-field appearance="outline" class="claims-input-field">
                            <input matInput formControlName="doc_annex" autocomplete="off">
                          </mat-form-field>
                        </div>
                      </div>
                      <div class="input-container">
                        <div class="text-container">
                          Document name
                        </div>
                        <div class="control-container">
                          <mat-form-field appearance="outline" class="claims-input-field">
                            <input matInput formControlName="doc_name" autocomplete="off">
                          </mat-form-field>
                        </div>
                      </div>
                    </div>
                    <section id="progress-id">
                      <mat-progress-bar [color]='primary' [mode]="determinate" [value]="filesProgress[in]?.progress">
                      </mat-progress-bar>
                    </section>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row-container">
      <div class="input-container">
        <div class="text-container" style="margin-bottom: 10px; font-weight: 500;">
          Specify the Claims/Visual Frames you find objectionable
          (max 5000 characters)<span style="color: red;"> * </span>
        </div>
        <!-- <div class="textarea-field">
          <div class="toolbar">
            <ul class="tool-list">
              <li class="tool">
                <button type="button" data-command="bold" class="tool--btn">
                  <i class=' fas fa-bold' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command="italic" class="tool--btn">
                  <i class=' fas fa-italic' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command="underline" class="tool--btn">
                  <i class=' fas fa-underline' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command='justifyLeft' class="tool--btn">
                  <i class=' fas fa-align-left' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command='justifyCenter' class="tool--btn">
                  <i class=' fas fa-align-center' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command='justifyRight' class="tool--btn">
                  <i class=' fas fa-align-right' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command='justifyFull' class="tool--btn">
                  <i class=' fas fa-align-justify' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command="insertOrderedList" class="tool--btn">
                  <i class=' fas fa-list-ol' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command="insertUnorderedList" class="tool--btn">
                  <i class=' fas fa-list-ul' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command="indent" class="tool--btn">
                  <i class=' fas fa-indent' style="font-size: 15px;"></i>
                </button>
              </li>
              <li class="tool">
                <button type="button" data-command="outdent" class="tool--btn">
                  <i class=' fas fa-outdent' style="font-size: 15px;"></i>
                </button>
              </li>
            </ul>
          </div>
          <mat-divider></mat-divider>
          <textarea matInput formControlName="complaint_description" rows="5"
            placeholder="Your claims regarding complaints &#13;&#10;Eg: Claim 1: 10 times more power protect against disease causing bacteria and viruses. &#13;&#10;OR Visual 1: Representation of coronavirus like virus implying soap gives 10times more protection against covid."
            maxlength="200" autocomplete="off" style="text-align:justify; text-justify: inter-word;"></textarea> -->
        <quill-editor class="textarea-field"
          placeholder="Your claims regarding complaints &#13;&#10;Eg: Claim 1: 10 times more power protect against disease causing bacteria and viruses. &#13;&#10;OR Visual 1: Representation of coronavirus like virus implying soap gives 10times more protection against covid."
          [modules]="quillConfiguration" formControlName="complaint_description" autocomplete="off"
          (onContentChanged)="textChangedComplaintDesc($event)" style="text-align:justify; text-justify: inter-word;">
        </quill-editor>
        <!-- </div> -->
        <mat-error class="error-msg"
          *ngIf="advertiserForm.controls['complaint_description'].touched && advertiserForm.controls['complaint_description'].errors?.required"
          style="position: relative;bottom: 19px;font-size: 12px;">
          Specify the claims with max length 5000
        </mat-error>
      </div>
    </div>

    <div class="divider-container">
      <mat-divider></mat-divider>
    </div>

    <div class="btn-container">
      <div class="next-container">
        <button mat-flat-button class="next-btn" [disabled]="checkMandatoryFields() || advertiserForm.invalid"
          (click)="step2Next('submit')">
          <span class="bolder">{{buttonName}}</span>
        </button>
      </div>
      <div class="cancel-container">
        <button mat-stroked-button [disabled]="isUploadProgress || isUploadAdvProgress" class="cancel-btn"
          (click)="cancel()">
          <mat-icon *ngIf="isUploadProgress || isUploadAdvProgress">
            <mat-spinner diameter="20">
            </mat-spinner>
          </mat-icon>
          <span class="bolder">Back</span>
        </button>
      </div>
    </div>
  </div>
</form>