/* You can add global styles to this file, and also import other style files */
@import "../node_modules/angular-calendar/css/angular-calendar.css";
@import '~quill/dist/quill.core.css';
@import '~quill/dist/quill.snow.css';

:host {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, Montserrat, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    font-size: 14px;
    color: #333;
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

@font-face {
    font-family: "Montserrat";
    src: local("Montserrat"), url(./assets/fonts/Montserrat/Montserrat-Regular.ttf) format("truetype");
}

* {
    font-family: 'Arial';
    /* font-weight: 500; */
}

button, input, optgroup, select, textarea  {
    font-family: 'Arial';
}

body {
    margin: 0;
    padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
}

p {
    margin: 0;
}

textarea:focus,
input:focus {
    outline: none;
}

.theme-hyperlink,
.theme-hyperlink:visited,
.theme-hyperlink:hover {
    color: #F89E1B;
    text-decoration: none;
    margin: 0 10px;
    cursor: pointer;
}

.theme-button {
    height: 40px;
    border-radius: 12px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    background-color: #F89E1B;
    font-size: 15px;
    text-align: center;
    text-transform: uppercase;
    color: #FFFFFF;
    border: none;
    width: max-content;
    cursor: pointer;
    padding: 0 10%;
}

.theme-input-text {
    background: #FFFFFF;
    border: 1px solid #E2E8F0;
    box-sizing: border-box;
    border-radius: 12px;
    height: 42px;
    width: 95%;
    font-size: 15px;
    line-height: 24px;
    padding: 0px 16px;
}

.theme-text {
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 19px;
    color: #000000;
}

.theme-heading {
    font-style: normal;
    font-weight: 600;
    font-size: 24px;
    line-height: 32px;
}

/* Tool Bar Theme  Start*/

.theme-blue-button-admin {
    flex-direction: row;
    justify-content: center;
    align-items: center;
    background: #0088CB;
    border-radius: 12px !important;
    line-height: 7px !important;
    padding-top: 0px;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: white;
    width: auto;
    height: 35px;
}

.theme-search {
    box-sizing: border-box;
    right: 3px;
    top: 1px;
    color: gray;
    height: 34px !important;
    width: 35px !important;
    border: 1px solid #CFD7DF !important;
    border-radius: 12px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.theme-search-after {
    border: 1px solid #CFD7DF !important;
    background-color: white !important;
    height: 37px;
    width: 275px;
    border-radius: 15px !important;
    display: inline-block;
}

/* Tool Bar Theme End */

.theme-red-button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 6px 12px;
    background: #ED2F45;
    border-radius: 12px;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    height: 35px;
    line-height: 19px;
    text-align: center;
    color: #FFFFFF;
}

/* Inbox Screen */

.theme-classification {
    background: #E9F5FF;
    border: 1px solid #9CC4F0;
    box-sizing: border-box;
    border-radius: 3px;
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    color: #4DA1FF;
}

/* Inbox Screen */

/* Manage Case $ Task Screen */

.theme-status-todo {
    background: #DDE9FF !important;
    border-radius: 2px;
    font-style: normal;
    font-weight: 600;
    font-size: 10px;
    line-height: 13px;
    color: #0088CB;
}

.theme-status-active {
    background: rgba(248, 158, 27, 0.17) !important;
    border-radius: 2px;
    font-style: normal;
    font-weight: 600;
    font-size: 10px;
    line-height: 13px;
    color: #F89E1B !important;
    width: 95px;
    height: 24px;
    border: 1px;
    font-size: 11px;
    border-radius: 2px;
    padding-left: 5px;
}

.theme-status-done {
    background: rgba(4, 165, 133, 0.17) !important;
    border-radius: 2px;
    font-style: normal;
    font-weight: 600;
    font-size: 10px;
    line-height: 13px;
    color: #04A585 !important;
}

.theme-status-hold {
    background: rgba(237, 47, 69, 0.17) !important;
    border-radius: 2px;
    font-style: normal;
    font-weight: 600;
    font-size: 10px;
    line-height: 13px;
    color: #ED2F45 !important;
}

/* Manage Case $ Task Screen */

/* Manage Case Screen */

.theme-onhold {
    color: #ED2F45 !important;
    background: rgba(237, 47, 69, 0.17) !important;
    border: 0.5px solid #ED2F45 !important;
    box-sizing: border-box;
    border-radius: 12px;
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
}

.theme-new {
    color: #0088CB !important;
    background: #DDE9FF !important;
    border: 0.5px solid #0088CB !important;
    box-sizing: border-box;
    border-radius: 12px;
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
}

.theme-resolution {
    color: #04A585 !important;
    background: rgba(4, 165, 133, 0.17) !important;
    border: 0.5px solid #04A585 !important;
    box-sizing: border-box;
    border-radius: 12px;
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
}

.theme-inprogress {
    color: #F89E1B !important;
    background: rgba(248, 158, 27, 0.17) !important;
    border: 0.5px solid #F89E1B !important;
    box-sizing: border-box;
    border-radius: 12px;
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
}

.theme-outofremit {
    color: #000000 !important;
    background: rgba(0, 0, 0, 0.17) !important;
    border: 0.5px solid #000000 !important;
    box-sizing: border-box;
    border-radius: 12px;
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
}

.theme-invalid {
    color: #000000 !important;
    background: rgba(0, 0, 0, 0.17) !important;
    border: 0.5px solid #000000 !important;
    box-sizing: border-box;
    border-radius: 12px;
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
}

.theme-closed {
    color: #000000 !important;
    background: rgba(0, 0, 0, 0.17) !important;
    border: 0.5px solid #000000 !important;
    box-sizing: border-box;
    border-radius: 12px;
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
}

/* Manage Case Screen */

.bold {
    font-weight: 500;
}

.bolder {
    font-weight: 600 !important;
}

.broader {
    padding: 0 15%;
}

html,
body {
    height: 100%;
}

.cdk-overlay-container,
.cdk-overlay-pane {
    z-index: 9999 !important;
}

.popover {
    position: fixed !important;
}

.success {
    background-color: #04A585;
    font-weight: 600;
    color: #fff;
}

.error {
    background-color: #ED2F45;
    color: #fff;
}

.warning {
    background-color: #fb9c1c;
    color: #fff;
}