.flex {
    display: flex;
    align-items: center;
    justify-content: center;
}

.header {
    background: #F8F9F9;
    border-radius: 4px 4px 0px 0px;
    width: 100%;
    height: 80px;
    padding: 0px 10px 0px 5px;
    margin-bottom: 0px !important;
}

.heading-container {
    height: 40px;
}

.heading {
    color: #000000;
    font-weight: 500;
    font-size: 16px;
    line-height: 15px;
    padding: 15px 10px 5px 5px;
}

.head-contents {
    color: #000000;
    font-weight: 450;
    font-size: 14px;
    line-height: 21px;
    padding: 5px 5px 15px 5px;
}

.classfy-container {
    max-height: 57vh;
    margin-bottom: 15px;
    cursor: pointer;
}

.related-name-container {
    text-indent: 0px;
    height: 25px;
    font-size: 14px;
    padding-bottom: 35px;
}

.related-name {
    color: #2AB2FF;
    font-weight: 500;
    font-size: 14px;
    padding-left: 5px;
}

.related-detail-container {
    text-indent: 20px;
    padding-bottom: 8px;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 16px;
}

.related-attribute {
    color: #CFD7DF;
}

.related-value {
    color: #000000;
}

.chips-btn-container {
    margin-left: 1%;
    position:relative;
    padding-bottom: 30px;
}

.relcomp-container {
    margin-left: 10px;
}

.person-chips {
    font-size: 10px;
    line-height: 13px;
    display:flex;
    align-items: center;
    border-radius: 50px;
}

#person-chip1 {
    color: #8B97A3;
    background-color: #CFD7DF;
    z-index: 3;
}

.add-btn {
    color: rgb(163, 163, 163);
    padding-left: 5px;
    padding-top: 3px;
    word-spacing: 1px;
    line-height: 15px;
    border: 1px dashed rgb(163, 163, 163) ;
    border-radius: 20px;
    height: 32px;
    margin-bottom: 15px;
    margin-top: 5px;
    width: 200px;
    margin-left: 5px;
}

.footer-content-container {
    width: 100% !important;
}

.footer-btn {
    color: #000000;
    background-color: #FFFFFF;
    border-radius: 12px;
    margin-right: 20px;
}
button[disabled]{
    opacity: 0.3;
    background-color: rgba(0,0,0,.12);
}

.footer-right-btn {
    display: flex;
    justify-content: flex-end;
}

.delete-icon {
    font-size: large;
    vertical-align: baseline !important;
    margin-right: 10px;
}

.similar-content {
    width: 100% !important;
    height: 55vh;
    padding-top: 3px;
    overflow: auto;
}

.divider {
    border-right: 1px solid #D8DCDE;
    height: 50vh;
}

.left-panel::-webkit-scrollbar {
    display: none;
}

.left-panel {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.left-panel {
    width: 100% !important;
    height: 50vh;
    overflow-y: scroll;
    overflow-x: hidden;
}

.right-panel::-webkit-scrollbar {
    display: none;
}

.right-panel {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.right-panel {
    width: 100% !important;
    height: 50vh;
    overflow-y: scroll;
    overflow-x: hidden;
}

.comp-matcard {
    background-color: #FFFFFF;
    height: 50vh;
    box-shadow: 0px 0px 0px rgba(0, 0, 0, 0);
}

.mat-card-scroll::-webkit-scrollbar {
  display: none;
}

.mat-card-scroll {
  -ms-overflow-style: none;
  scrollbar-width: none;
  height: 50vh;
  overflow-y: scroll;
  overflow-x: hidden;
}

.detail-attribute {
    color: #92a2b1;
    font-size: 14px;
    line-height: 19px;
}
.media-anchor {
    color: #0088CB;
    word-break: break-all;
    cursor: pointer;
}
.comp-msg-container {
    width: 90%;
    font-size: 14px;
    line-height: 19px;
    text-align: justify;
}
.comp-tab-container::-webkit-scrollbar {
    display: none;
}  
.comp-tab-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
} 
.comp-tab-container {
    height: 65%;
    width: 99%;
    overflow-y: scroll;
    overflow-x: hidden;
}
::ng-deep .detail-subtab.mat-tab-header,
.mat-tab-nav-bar {
    border-bottom: 0;
}
:host ::ng-deep .detail-subtab .mat-tab-label,
.mat-tab-link {
  width: 18%;
  border-width: 1px 1px 0 1px;
  border-style: solid;
  border-color: #D8DCDE;
  border-radius: 3px 3px 0px 0px;
  font-weight: 600 !important;
  font-size: 12px;
  line-height: 16px;
  color: #000000;
  opacity: 1 !important;
  background-color: rgba(238, 238, 238, 0.568) !important;
}
:host ::ng-deep .detail-subtab .mat-tab-label-active {
    background-color:  #0088CB !important;
    opacity: 1 !important;
    color: #ffffff;
    font-weight: 600 !important;
}  
:host ::ng-deep .detail-subtab .mat-ink-bar {
    display: none !important;
}
:host ::ng-deep .detail-subtab .mat-tab-label .mat-tab-label-content {
    font-weight: 600;
}  
.detail-subtab {
    padding-left: 1%;
    padding-top: 2%;
}
:host ::ng-deep .detail-subtab-group .mat-ink-bar {
    display: none !important;
}
.doc-container {
    padding-top: 10px;
    padding-bottom: 10px;
    width: 100%;
}
.content-head-container {
    width: 100%;
    height: 100%;
    margin-bottom: 30px;
}
.intra-expansion {
    border: 1px solid #D8DCDE;
    padding: 0% 2% 0% 2%;
}
:host ::ng-deep .panel-header>.mat-expansion-indicator:after {
    color: #0088CB;
}
.panel-title {
    color: #000000;
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
}
.attribute-container1 {
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
    height: 20px;
    padding: 2px 0px;
    width: 236px;
}
.attribute-container2 {
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
    height: 20px;
    padding: 3px 0px;
}
.grey-text {
    color: #92A2B1;
    font-size: 14px;
    line-height: 19px;
}
.panel-body{
    background: #FFFFFF;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
    height: 28px;
    margin-bottom: 4px;
    padding: 0px !important;
}
.flex {
    display: flex;
    align-items: center;
    justify-content: center;
}
.doc-fxrow-container {
    width: 100%;
    margin-top: 2%;
}
.mat-card-doc {
    padding: 0px;
    width: fit-content;
    height: fit-content;
}
.doc-icon-container {
    background-color:#3a3a3a;
    height: 86px;
    width: 176px;
    border-radius: 5px;
    display: flex;
    justify-content: center;
}
.doc-icon-container>span {
    color: rgb(168, 168, 168);
}
.doc-image-container {
    display: flex;
    justify-content: center;
    align-items: center;
}
.doc-caption {
    background-color: rgb(255, 255, 255);
    height: fit-content;
    padding: 13px;
}
.doc-caption>p {
    height: 18px;
    width: 94px;
    padding: 0;
    overflow: hidden;
    position: relative;
    display: inline-block;
    margin: 0 5px 0 5px;
    text-align: center;
    font-size: 12px;
    text-decoration: none;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #000;
    font-weight: 700;
}
.action-buttons {
    border: 1px solid gainsboro;
    position: relative;
    bottom: 17px;
    left: 122px;
    background: white;
    width: 104px;
    height: 64px;
    padding-left: 13px;
    padding-top: 10px;
    z-index: 1;
}
.action-buttons-internal {
    border: 1px solid gainsboro;
    position: relative;
    bottom: 32px;
    left: 122px;
    background: white;
    width: 112px;
    height: 81px;
    padding-left: 19px;
    padding-top: 9px;
}
.option-btn {
    line-height: 25%;
    height: 550%;
}  
.option-text {
    line-height: 200%;
    padding-left: 8px;
}
.mat-card-doc {
    padding: 0px;
    width: fit-content;
    height: fit-content;
}
.no-claims {
    color: rgb(160, 160, 156);
    margin-top: 10px;
    margin-left: 10px;
    font-size: 15px;
}
.raised-container {
    width: 65%;
}
.head-container {
    padding: 5px 0px;
}  
.head-container>div {
    max-height: 20px;
}
.arrow-img-icon {
    vertical-align: super !important;
}
.intra-h3 {
    color: #000000;
    font-weight: normal;
    font-size: 14px;
    line-height: 19px;
}
.challenge-container {
    border: 1px solid #D8DCDE;
    border-radius: 4px;
    padding: 10px;
}
.attribute-container {
    padding: 10px 0px 0px;
}
.violated-popups {
    color: #0088CB;
    text-align: center;
    width: 100%;
    height: 30px;
    background: #FFFFFF;
    border: 1px solid #0088CB;
    box-sizing: border-box;
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
}
.violated-container {
    width: 37%;
}
.chapters-container {
    padding: 10px;
    overflow-wrap: anywhere;
    padding-top: 0px;
}  
.chapter-text {
    color: #000000;
    font-weight: normal;
    font-size: 14px;
    margin-bottom: 11px;
    line-height: 19px;
    width:192px;
    text-align: -webkit-auto;
}
.width50 {
    width: 50%;
}
.circle-icon {
    font-size: 4px;
    vertical-align: sub;
    height: 16px;
    margin-top: 8px;
    bottom: 6px;
    position: relative;
}
.doc-link{
    color: #0088CB;
    text-decoration: underline;
    font-size: 12px;
    padding-left: 10px;
}
.chat-item-selected {
    background-color: #F3F3F3;
}