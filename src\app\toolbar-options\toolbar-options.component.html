<mat-toolbar class="toolbar" fxLayout="row">
  <div class="toolbar-btns" fxLayout="row" fxLayoutAlign="end start" fxLayoutGap="5px">
    <!-- <div class="search-container">
      <button mat-icon-button class="search-btn" (click)='expandSearch()' *ngIf="searchContainer">
        <img class="hide-icon" src="../assets/images/search-icon.png"
          style="width: 14px;position: relative;bottom: 2px;" />
      </button>
      <div [hidden]="hiddenSearch">
        <form>
          <mat-form-field class="search-box">
            <input matInput type="text" class="search-input" [matAutocomplete]="auto" [formControl]="searchForm">
            <img matPrefix src="../assets/images/header-search.png" aria-hidden="true" (click)="SearchFilter()"
              style="position: relative; padding-left:10px; top: -5.5px; cursor: pointer;">
            <img matSuffix src="../assets/images/search-cancel.png" aria-hidden="true" (click)="clearSearchFilter()"
              style="position: relative; left:-13px; top: -5px; cursor: pointer;">
            <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn">
              <mat-option class="option-text" *ngFor="let option of filteredOptions | async" [value]="option">
                {{option.name}}
              </mat-option>
            </mat-autocomplete>
          </mat-form-field>
        </form>
      </div>
    </div> -->
    <div class="admin-div">
      <button mat-button [matMenuTriggerFor]="admin" class="theme-blue-button-admin">
        <span class="bolder">
          {{roleName}}
        </span> 
        <mat-icon class="arrow" style="position: relative;bottom: 2px;">arrow_drop_down</mat-icon>
      </button>
      <mat-menu #admin="matMenu" class="admin-menu">
        <div class="admin-option-container">
          <button mat-menu-item routerLink="/my-profile" class="option-btn" (click)="viewProfile(true)">
            <span class="option-text">My profile</span>
          </button>
          <mat-divider class="option-divider"></mat-divider>
          <button mat-menu-item class="option-btn" (click)='logout()'>
            <span class="option-text">Log out</span>
          </button>
        </div>
      </mat-menu>
    </div>
  </div>
</mat-toolbar>