version: 0.2

env:
    variables:
        S3_BUCKET: "aw-cms-uat-ui-build"
phases:
  install:
    runtime-versions:
        nodejs: 12
    commands:
    - echo $CODEBUILD_SRC_DIR
    - npm install -y npm@latest
    - npm install -g @angular/cli
    - rm package-lock.json
  pre_build:
    commands:
    - npm install
  build:
    commands:
    - echo build started on `date`
    - export NODE_OPTIONS=--max-old-space-size=8192
    - node --max_old_space_size=4000 ./node_modules/@angular/cli/bin/ng build --configuration=uat
    #- ng build --configuration=uat
    - ls -l -F
  post_build:
    commands:
            # Clear S3 bucket.
            - aws s3 rm s3://${S3_BUCKET} --recursive
            - echo S3 bucket is cleared.
            - aws s3 cp dist/web s3://${S3_BUCKET} --recursive
            - echo Build completed on `date`
artifacts:
    files:
      - dist/**/*