import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuardService } from '../services/auth-guard.service';
import { TaskBoardComponent } from './task-board.component';
import { TasktableComponent } from './tasktable/tasktable.component';

const routes: Routes = [
  {
    path: '',
    component: TaskBoardComponent,
    children: [
      {
        path: 'task-table',
        canActivate: [AuthGuardService],
        data: {
          role: [1,2,3,6]
        },
        component: TasktableComponent
      },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TaskBoardRoutingModule { }
