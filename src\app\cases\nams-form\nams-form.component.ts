import { I } from '@angular/cdk/keycodes';
import { ThisR<PERSON>eiver } from '@angular/compiler';
import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { Router } from '@angular/router';
import { DateAdapter } from 'angular-calendar';
import moment from 'moment';
import { promise } from 'protractor';
import { Observable, SubscriptionLike } from 'rxjs';
import { debounceTime, distinctUntilChanged, first, map, startWith } from 'rxjs/operators';
import { AuthService } from 'src/app/services/auth.service';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { ThirdPartyService } from 'src/app/services/third-party.service';
import { UploadService } from 'src/app/services/upload.service';
import { MY_FORMATS } from 'src/app/shared/calendarFormat';
import { colorObj } from 'src/app/shared/color-object';
import { environment } from 'src/environments/environment';

export interface Company {
  COMPANY_NAME: string;
  ID: number;
}
@Component({
  selector: 'app-nams-form',
  templateUrl: './nams-form.component.html',
  styleUrls: ['./nams-form.component.scss'],
  providers: [
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
  ],
})
export class NamsFormComponent implements OnInit {

  step2Form: FormGroup;
  selectedAdvId: number;
  userInfo: any;
  ads: any[];
  classification: any[];
  platforms: any[];
  printSources: any[];
  promotionTypes: any[];
  files: any[] = [];
  files_attached = "No";
  @Output() outFilter: EventEmitter<any> = new EventEmitter<any>();
  buttonName: string;
  companyNameControl = new FormControl();
  companyList: any[];
  advCompanyList: any[];
  companyId: number = 0;
  blankComplaintId: number;
  maxDate = new Date();
  backDisable: boolean = false;
  isUploadProgress: boolean = false;
  fileProgress: number = 0;
  fileInfoPath: string = ''
  complainantID: any;
  namsComplaintId: number;
  nams: string = 'tams'
  complaintSourceId: number = 0;
  emailPattern = environment.emailPatterm;
  mobilenopattern = /^[0-9]{10}$/;
  subscription: SubscriptionLike;
  filteredOptions: Observable<any>[] = [];
  readonly: boolean = true;
  imgURL: string;
  public bucketUrl = `${environment.BUCKET_URL}`;
  mediaObj = {}
  platformId: number = 0;
  sourceName: String = '';
  printSourceId: number = 0;
  sourcePlace: String = '';
  momentDate: string;
  engagements: number = 0;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private complaintService: ComplaintsService,
    private uploadService: UploadService,
    private authService: AuthService,
    private notify: NotificationService,
    private namsService: ThirdPartyService) {
    this.getStep2Form();
  }

  ngOnInit(): void {
    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.ads = JSON.parse(window.localStorage.getItem('advertisementSource'));
    this.classification = JSON.parse(window.localStorage.getItem('classification'));
    this.platforms = JSON.parse(window.localStorage.getItem('platform'));
    this.printSources = JSON.parse(window.localStorage.getItem('printSource'));
    this.promotionTypes = JSON.parse(window.localStorage.getItem('promotionalMaterialSource'));
    this.complaintService.currentButtonName
      .pipe(first()).subscribe(cond => {
        if (cond === 'Admin') {
          this.buttonName = 'Next';
        } else {
          this.buttonName = 'Submit';
        }
      })

    // this.complaintService.currentBlankComplaintId.pipe(first()).subscribe(id => {
    //   this.blankComplaintId = id;
    // })
    this.blankComplaintId = this.complaintService.getNamsBlankId;
    if (this.userInfo.roleId == 7) {
      this.complainantID = 1;
    }
    else if (this.userInfo.roleId == 4) {
      this.complainantID = 3;
    }
    else {
      this.complaintService.complainantID.subscribe((data) => {
        this.complainantID = data['complainantId'];
      })
    }
    this.step2Form.get('company')!.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0) {
          this.getCompanyList(value);
        }
      }, err => {

      })

    this.complaintService.currentStep
      .pipe(first()).subscribe(step => {
        this.nams = step;
        if (step === 'tams') {
          this.backDisable = false;
          this.step2Form.get('seen_medium').clearValidators();
          this.step2Form.get('platform').clearValidators();
          this.step2Form.get('publication_link').clearValidators();
          this.step2Form.get('influencer_name').clearValidators();
          this.step2Form.updateValueAndValidity();
          let id = this.complaintService.getNamsBlankId;
          if (id != 0) {  
            this.namsComplaintId = id;
            this.namsService.getDetails(id).subscribe((res: any) => {
              let complaintObj = res.data;
              this.complaintSourceId = complaintObj['COMPLAINT_SOURCE_ID'];
              if(new Date(complaintObj['DATE']) > new Date() || complaintObj['DATE'] == '' || complaintObj['DATE'] == null){
                this.momentDate = ''
              } else {
                this.momentDate = new Date(complaintObj['DATE']).toISOString()
              }
              let finalDate;
              if(complaintObj['STARTIME']){
                let timeArr = complaintObj['STARTIME'].split(':');
                let newDateTime = new Date().setHours(timeArr[0], timeArr[1],0,0);
                finalDate = new Date(newDateTime);
              }
              this.checkMedia(complaintObj['MEDIA'], complaintObj);
              this.step2Form.patchValue({
                'company': complaintObj['COMPANY_NAME'],
                'advertiser': complaintObj['ADVERTISER'],
                'add_url': !!complaintObj['CREATIVE_HYPERLINK'] ? environment.BUCKET_URL.concat(complaintObj['CREATIVE_HYPERLINK'].toString()) : '',
                'brand': complaintObj['BRAND'],
                'description': '', //complaintObj['TRANSCRIPTION'],
                'compDesc': complaintObj['COMPLAINT'],
                'media_outlet': complaintObj['MEDIA_OUTLET'],
                'date': this.momentDate,
                'time': finalDate,
                'product_category': complaintObj['SUPER_CATEGORY'],
                'edition': complaintObj['EDITION'],
                'suppliment': complaintObj['SUPPLEMENT'],
                'ad_language': complaintObj['AD_LANGUAGE'],
                'creative_id': complaintObj['CREATIVE_ID'],
                'translation_hyperlink': !!complaintObj['TRANSLATION_HYPERLINK'] ? environment.BUCKET_URL.concat(complaintObj['TRANSLATION_HYPERLINK'].toString()) : '',
                'transcription': complaintObj['TRANSCRIPTION'],
                'duration': complaintObj['DURATION']
              })
              // this.step2Form.controls['creative_id'].disable();
              // this.getCompanyId(complaintObj.ADVERTISER);
              this.companyId = complaintObj['COMPANY_ID'];
              this.step2Form.patchValue({
                'company': complaintObj['COMPANY_NAME']
              })
            }, err => {
              this.notify.showNotification(
                err.error.message,
                "top",
                (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                err.error.status
              )
            });
          }
        } else if (step === 'reech') {
          this.backDisable = false;
          this.step2Form.get('advertiser').clearValidators();
          this.step2Form.get('media_outlet').clearValidators();
          this.step2Form.get('media').clearValidators();
          this.step2Form.updateValueAndValidity();
          let id = this.complaintService.getNamsBlankId;
          if (id != 0) {
            this.step2Form.patchValue({
              'seen_medium': 3
            });
            // this.step2Form.controls['seen_medium'].disable();
            // this.step2Form.controls['platform'].disable();
            this.namsComplaintId = id;
            this.namsService.getReechDetails(id).subscribe((res: any) => {
              let complaintObj = res.data;
              this.complaintSourceId = complaintObj['COMPLAINT_SOURCE_ID'];
              this.engagements = complaintObj['ENGAGEMENTS'];
              if(new Date(complaintObj['PUBLICATION_DATE']) > new Date() || complaintObj['PUBLICATION_DATE'] == '' || complaintObj['PUBLICATION_DATE'] == null){
                this.momentDate = ''
              } else {
                this.momentDate = new Date(complaintObj['PUBLICATION_DATE']).toISOString()
              }
              this.step2Form.patchValue({
                'company': complaintObj['COMPANY_NAME'],
                'brand': complaintObj['BRAND'],
                'publication_link': complaintObj['PUBLICATION_URL'],
                'influencer_name': complaintObj['INFLUENCER_NAME'],
                'influencer_profile_URL': complaintObj['PROFILE_URL'],
                'compDesc': complaintObj['PUBLICATION_CONTENT'],
                'date': this.momentDate,
                'add_url': complaintObj['PUBLICATION_IMAGE_URL'],
              })
              // this.step2Form.controls['creative_id'].disable();
              this.companyId = complaintObj['COMPANY_ID'];
              this.step2Form.patchValue({
                'company': complaintObj['COMPANY_NAME']
              })
              this.step2Form.patchValue({
                'network': complaintObj['NETWORK']
              })
              this.platforms.forEach(el => {
                if(el.PLATFORM_NAME.toLowerCase() === complaintObj['NETWORK'].toLowerCase()){
                  this.step2Form.patchValue({
                    'platform':el['ID']
                  })
                }
              })
            }, err => {
              this.notify.showNotification(
                err.error.message,
                "top",
                (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                err.error.status
              )
            });
          }
        } else if (step === 'tams-reech'){
          this.backDisable = false;
          this.readonly = false;
          this.step2Form.get('seen_medium').clearValidators();
          this.step2Form.get('platform').clearValidators();
          this.step2Form.get('publication_link').clearValidators();
          this.step2Form.get('influencer_name').clearValidators();
          this.step2Form.updateValueAndValidity();
          let id = this.complaintService.getNamsBlankId;
          if (id != 0) {  
            this.namsComplaintId = id;
            this.namsService.getReechDetails(id).subscribe((res: any) => {
              let complaintObj = res.data;
              this.complaintSourceId = 8;
              if(new Date(complaintObj['PUBLICATION_DATE']) > new Date() || complaintObj['PUBLICATION_DATE'] == '' || complaintObj['PUBLICATION_DATE'] == null){
                this.momentDate = ''
              } else {
                this.momentDate = new Date(complaintObj['PUBLICATION_DATE']).toISOString()
              }
              this.step2Form.patchValue({
                'company': complaintObj['COMPANY_NAME'],
                'brand': complaintObj['BRAND'],
                'compDesc': complaintObj['PUBLICATION_CONTENT'],
                'date': this.momentDate,
                'suppliment': complaintObj['PUBLICATION_IMAGE_URL'],
                'edition': complaintObj['PUBLICATION_URL'],
                'creative_id': complaintObj['PROFILE_URL']
              })
              // this.step2Form.controls['creative_id'].disable();
              this.companyId = complaintObj['COMPANY_ID'];
              this.step2Form.patchValue({
                'company': complaintObj['COMPANY_NAME']
              })
            }, err => {
              this.notify.showNotification(
                err.error.message,
                "top",
                (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                err.error.status
              )
            });
          }
        } else if (step === 'whatsapp') {
          this.backDisable = false;
          this.nams = 'tams';
          this.readonly = false;
          this.complaintService.currentWhatsappComplaintId
            .pipe(first()).subscribe(id => {
              this.step2Form.get('seen_medium').clearValidators();
              this.step2Form.get('platform').clearValidators();
              this.step2Form.get('publication_link').clearValidators();
              this.step2Form.get('influencer_name').clearValidators();
              this.step2Form.updateValueAndValidity();
              this.step2Form.get('creative_id').setValidators(Validators.required);
              if (id != 0) {  
                this.complaintService.currentWhatsappComplaintObj
                .pipe(first()).subscribe(whatsappObj => {
                  let complaintObj = whatsappObj;
                  this.namsComplaintId = complaintObj['ID'];
                  this.complaintSourceId = 2;
                  if(new Date(complaintObj['ADVERTISEMENT_SEEN_DATE']) > new Date() || complaintObj['ADVERTISEMENT_SEEN_DATE'] == '' || complaintObj['ADVERTISEMENT_SEEN_DATE'] == null){
                    this.momentDate = ''
                  } else {
                    this.momentDate = new Date(complaintObj['ADVERTISEMENT_SEEN_DATE']).toISOString()
                  }
                  let timeArray = complaintObj['ADVERTISEMENT_SEEN_TIME'].split(" ");
                  let newDateTime = new Date().setHours(timeArray[0], timeArray[1], 0, 0);
                  this.companyId = complaintObj['COMPANY_ID'];
                  this.step2Form.patchValue({
                    'company': complaintObj['COMPANY_NAME'],
                    // 'advertiser': complaintObj['ADVERTISER'],
                    // 'add_url': !!complaintObj['CREATIVE_HYPERLINK'] ? environment.BUCKET_URL.concat(complaintObj['CREATIVE_HYPERLINK'].toString()) : '',
                    'brand': complaintObj['BRAND_NAME'],
                    'description': complaintObj['ADVERTISEMENT_DESCRIPTION'],
                    'compDesc': complaintObj['COMPLAINT_DESCRIPTION'],
                    // 'media_outlet': complaintObj['MEDIA_OUTLET'],
                    // 'media': complaintObj['MEDIA'],
                    'date': this.momentDate,
                    'time': new Date(newDateTime)
                    // 'product_category': complaintObj['PRODUCT_CATEGORY'],
                    // 'edition': complaintObj['EDITION'],
                    // 'suppliment': complaintObj['SUPPLEMENT'],
                    // 'ad_language': complaintObj['AD_LANGUAGE'],
                    // 'creative_id': complaintObj['CREATIVE_ID'],
                    // 'translation_hyperlink': !!complaintObj['TRANSLATION_HYPERLINK'] ? environment.BUCKET_URL.concat(complaintObj['TRANSLATION_HYPERLINK'].toString()) : ''
                  })
                  // this.step2Form.controls['creative_id'].disable();
                  // this.getCompanyId(complaintObj.ADVERTISER);
                }, err => {
                  this.notify.showNotification(
                    err.error.message,
                    "top",
                    (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                    err.error.status
                  )
                });
              }
          })
        } else if (step === 'email') {
          this.backDisable = false;
          this.nams = 'tams';
          this.readonly = false;
          this.complaintService.currentEmailComplaintId
            .pipe(first()).subscribe(id => {
              this.step2Form.get('seen_medium').clearValidators();
              this.step2Form.get('platform').clearValidators();
              this.step2Form.get('publication_link').clearValidators();
              this.step2Form.get('influencer_name').clearValidators();
              this.step2Form.updateValueAndValidity();
              this.step2Form.get('creative_id').setValidators(Validators.required);
              if (id != 0) {  
                this.namsComplaintId = id;
                this.complaintService.getMailComplaint(id).subscribe((res: any) => {
                  let complaintObj = res.data;
                  this.complaintSourceId = 4;
                  if(new Date(complaintObj['ADVERTISEMENT_SEEN_DATE']) > new Date() || complaintObj['ADVERTISEMENT_SEEN_DATE'] == '' || complaintObj['ADVERTISEMENT_SEEN_DATE'] == null){
                    this.momentDate = ''
                  } else {
                    this.momentDate = new Date(complaintObj['ADVERTISEMENT_SEEN_DATE']).toISOString()
                  }
                  let timeArray = complaintObj['ADVERTISEMENT_SEEN_TIME'].split(" ");
                  let newDateTime = new Date().setHours(timeArray[0], timeArray[1], 0, 0);
                  this.companyId = complaintObj['COMPANY_ID'];
                  this.step2Form.patchValue({
                    'company': complaintObj['COMPANY_NAME'],
                    // 'advertiser': complaintObj['ADVERTISER'],
                    // 'add_url': !!complaintObj['CREATIVE_HYPERLINK'] ? environment.BUCKET_URL.concat(complaintObj['CREATIVE_HYPERLINK'].toString()) : '',
                    'brand': complaintObj['BRAND_NAME'],
                    'description': complaintObj['ADVERTISEMENT_DESCRIPTION'],
                    'compDesc': complaintObj['COMPLAINT_DESCRIPTION'],
                    // 'media_outlet': complaintObj['MEDIA_OUTLET'],
                    // 'media': complaintObj['MEDIA'],
                    'date': this.momentDate,
                    'time': new Date(newDateTime)
                    // 'product_category': complaintObj['PRODUCT_CATEGORY'],
                    // 'edition': complaintObj['EDITION'],
                    // 'suppliment': complaintObj['SUPPLEMENT'],
                    // 'ad_language': complaintObj['AD_LANGUAGE'],
                    // 'creative_id': complaintObj['CREATIVE_ID'],
                    // 'translation_hyperlink': !!complaintObj['TRANSLATION_HYPERLINK'] ? environment.BUCKET_URL.concat(complaintObj['TRANSLATION_HYPERLINK'].toString()) : ''
                  })
                  if(!complaintObj['COMPANY_ID']){
                    // this.step2Form.get('company').setValue('');
                    this.getCompanyName(complaintObj['COMPANY_NAME']);
                  }
                  // this.step2Form.controls['creative_id'].disable();
                  // this.getCompanyId(complaintObj.ADVERTISER);
                }, err => {
                  this.notify.showNotification(
                    err.error.message,
                    "top",
                    (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                    err.error.status
                  )
                });
              }
          })
        }
      })
  }

  getCompanyName(companyValue) {
    if(!!companyValue){
      this.authService.getCompanies(companyValue).subscribe(res => {
        if (res.data.length > 0) {
          this.companyId = res.data[0].ID;
          this.step2Form.patchValue({
            'company': res.data[0].COMPANY_NAME
          })
        }
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
  }

  addAdvCompany(advMedium?: any) {
    let advIndex = (<FormArray>this.step2Form.get('advCompany')).length;
    if (advIndex < 4) {
      let fg = this.fb.group({
        advCompany: this.fb.control(''),
        advCompanyId: this.fb.control(''),
        advCompanyList: this.fb.control(''),
        advEmail: this.fb.control('', Validators.pattern(this.emailPattern)),
        advBrand: this.fb.control(''),
        advProduct: this.fb.control(''),
        index: this.fb.control('')
      });
      (<FormArray>this.step2Form.get('advCompany')).push(fg);
      let userIndex = (<FormArray>this.step2Form.get('advCompany')).length - 1;
      fg.get("index").patchValue(userIndex);
      this.subscription = fg.get('advCompany').valueChanges.pipe(
        debounceTime(200)).subscribe(value =>
          this.onValueChanged(value, userIndex));
    }
  }

  onValueChanged(value, index): void {
    this.getAdvCompanyList(value, index);
  }

  getAdvCompanyList(value, index) {
    if (!!value && value.length > 0) {
      this.authService.getCompanies(value).subscribe(res => {
        const advCompanyArray = this.step2Form.get('advCompany') as FormArray;
        const adv_company_obj = advCompanyArray.controls[index] as FormGroup;
        adv_company_obj.get('advCompanyList').patchValue(res.data, { emitEvent: false, onlySelf: true });
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
  }



  getControls1() {
    return (this.step2Form.get('advCompany') as FormArray).controls;
  }

  removeCodes(index: number) {
    (<FormArray>this.step2Form.get('advCompany')).removeAt(index);
  }

  getCompanyId(value) {
    this.authService.getCompanies(value).subscribe(res => {
      if (res.data.length > 0) {
        this.companyId = res.data[0].ID;
        this.step2Form.patchValue({
          'company': res.data[0].COMPANY_NAME
        })
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }


  getCompanyList(value) {
    this.authService.getCompanies(value).subscribe(res => {
      this.companyList = res.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  getStep2Form() {
    this.step2Form = this.fb.group({
      company: new FormControl('', Validators.required),
      product: new FormControl(''),
      brand: new FormControl(''),
      advertiser: new FormControl('', Validators.required),
      media_outlet: new FormControl('', Validators.required),
      media: new FormControl('', Validators.required),
      seen_medium: new FormControl('', Validators.required),
      platform: new FormControl('', Validators.required),
      publication_link: new FormControl('', Validators.required),
      date: new FormControl('', Validators.required),
      time: new FormControl(''),
      product_category: new FormControl('', Validators.required),
      edition: new FormControl(''),
      influencer_name: new FormControl('', Validators.required),
      suppliment: new FormControl(''),
      ad_language: new FormControl(''),
      creative_id: new FormControl(''),
      network:new FormControl(''),
      influencer_profile_URL: new FormControl(''),
      influencer_contact_no: new FormControl('', Validators.pattern(this.mobilenopattern)),
      influencer_email_address: new FormControl('', Validators.pattern(this.emailPattern)),
      advCompany: this.fb.array([]),
      doc_file: new FormControl(''),
      file_array: new FormControl([]),
      add_url: new FormControl(''),
      translation_hyperlink: new FormControl(''),
      duration: new FormControl(''),
      transcript_hyperlink: new FormControl(''),
      transcription: new FormControl(''),
      description: new FormControl('', Validators.required),
      compDesc: new FormControl('', Validators.required),
    })
  }

  onSelectionChange(event) {
    this.companyList.forEach(element => {
      if (event.option.value === element.COMPANY_NAME) {
        this.companyId = element.ID;
      }
    });
  }

  companyChange(){
    this.companyId = 0;
  }

  companyInput() {
    if (!(this.userInfo.roleId == 1 || this.userInfo.roleId == 2 || this.userInfo.roleId == 3 || this.userInfo.roleId == 6)) {
      this.companyId = 0;
    } else if ((this.userInfo.roleId == 1 || this.userInfo.roleId == 2 || this.userInfo.roleId == 3 || this.userInfo.roleId == 6) && this.companyId == 0) {
      this.step2Form.get('company').setValue('');
    }
  }

  onAdvCompSelectionChange(event, index) {
    const advCompanyArray = this.step2Form.get('advCompany') as FormArray;
    const advCompanyObj = advCompanyArray.controls[index] as FormGroup;
    let companyList = advCompanyObj.get('advCompanyList').value;
    companyList.forEach(element => {
      if (event.option.value === element.COMPANY_NAME) {
        advCompanyObj.get('advCompanyId').patchValue(element.ID);
      }
    });
    this.advCompanyList = [];
  }

  advCompanyInput(advCompanyObj) {
    if (advCompanyObj.value['advCompanyId'] == 0 || advCompanyObj.value['advCompanyId'] == '') {
      advCompanyObj.get('advCompany').setValue('');
    }
    this.advCompanyList = [];
  }

  savedraft() {
    this.step2Next('draft');
  }

  step2Next(cond) {
    if (!!this.subscription) {
      this.subscription.unsubscribe();
    }
    let submitted = 1;
    if (cond === 'draft') {
      submitted = 0;
    }
    let formData: any;
    formData = this.step2Form.value;
    let advCompany = formData['advCompany'];
    let advCompanyArray = [];
    for (let i = 0; i < advCompany.length; i++) {
      if (!!advCompany[i].advCompanyId) {
        advCompanyArray.push({
          "COMPANY_ID": advCompany[i].advCompanyId,
          "BRAND_NAME": '',
          "PRODUCT_NAME": '',
          "EMAIL_ID": advCompany[i].advEmail
        })
      }
    }
    let translationHyperlink = '';
    if (!!this.step2Form.value.translation_hyperlink) {
      if (this.step2Form.value.translation_hyperlink.indexOf('https') == -1) {
        translationHyperlink = environment.BUCKET_URL.concat(this.step2Form.value.translation_hyperlink.toString());
      } else {
        translationHyperlink = this.step2Form.value.translation_hyperlink;
      }
    }

    let sourceUrl = '';
    if (!!this.step2Form.value.add_url) {
      if (this.step2Form.value.add_url.indexOf('https') == -1) {
        sourceUrl = environment.BUCKET_URL.concat(this.step2Form.value.add_url.toString());
      } else {
        sourceUrl = this.step2Form.value.add_url;
      }
    }
    // let advSourceId = 0
    // if(this.step2Form.value.media.toLowerCase() === 'tv'){
    //   advSourceId = 1;
    // }else if(this.step2Form.value.media.toLowerCase() === 'digital'){
    //   advSourceId = 3;
    // }else if(this.step2Form.value.media.toLowerCase() === 'print'){
    //   advSourceId = 5;
    // }
    let obj = {
      "ID": 0,
      "REFERENCE_ID": this.namsComplaintId,
      "TRANSCRIPTION": this.step2Form.value.transcription,
      "DURATION": this.step2Form.value.duration,
      "COMPLAINT_TYPE_ID": this.complainantID,
      "COMPANY_ID": this.companyId,
      "PANEL_ID": 1,
      "COMPLAINT_SOURCE_ID": this.complaintSourceId,
      "COMPLAINT_STATUS_ID": 1,
      "PRIORITY_ID": 1,
      "STAGE_ID": 1,
      "CLASSIFICATION_ID": this.step2Form.value.initial_class,
      "GOVERNMENT_DEPARTEMENT_ID": 1,
      "USER_TYPE_ID": this.complainantID,
      "USER_ID": this.userInfo.userId,
      "CREATED_BY_USER_ID": this.userInfo.userId,
      "ASSIGNEE_USER_ID": 0,
      "COMPLAINT_PRODUCT_CATEGORY_ID": 0,
      "EXTENDED_STAGE_ID": 1,
      "EXTENDED_DAYS": "9",
      "BRAND_NAME": this.step2Form.value.brand,
      "PRODUCT_NAME": this.step2Form.value.product,
      "ADVERTISEMENT_DESCRIPTION": this.step2Form.value.description,
      "COMPLAINT_DESCRIPTION": this.step2Form.value.compDesc,
      "COMPANY_NAME": this.step2Form.value.company,
      // "OBJECTIONABLE": "",
      // "WEB_LINK": "",
      "NOTIFY": "7",
      "TnC": "1",
      // "SUGGESTION": "",
      "DUE_DATE": "",
      // "ADDITIONAL_CLASSIFICATION": "",
      "ADVERTISOR_NAME": this.step2Form.value.advertiser,
      "MEDIA_OUTLET": this.step2Form.value.media_outlet,
      "MEDIA": this.step2Form.value.media,
      "PRODUCT_CATEGORY": this.step2Form.value.product_category,
      "EDITION": this.step2Form.value.edition,
      "SUPPLIMENT": this.step2Form.value.suppliment,
      "AD_LANGUAGE": this.step2Form.value.ad_language,
      "CREATIVE_ID": this.step2Form.value.creative_id,
      "TRANSLATION_HYPERLINK": translationHyperlink,
      "DATE": moment(this.step2Form.value.date).format('yyyy-MM-DD'),
      "TIME": moment(this.step2Form.value.time).format('HH:mm:ss'),
      "SUBMITTED": submitted,
      "ENGAGEMENTS": this.engagements,
      "NETWORK": this.step2Form.value.network,
      "INFLUENCER_NAME": this.step2Form.value.influencer_name,
      "PUBLICATION_URL": this.step2Form.value.publication_link,
      "PROFILE_URL": this.step2Form.value.influencer_profile_URL,
      "PUBLICATION_DATE": "",
      "INFLUENCER_MOBILE": this.step2Form.value.influencer_contact_no,
      "INFLUENCER_EMAIL": this.step2Form.value.influencer_email_address,
      "COMPANY_INFO": advCompanyArray,
      "CLAIMS": [],
      "CLAIMS_DOCUMENT": [],
      "ADVERTISEMENT_MEDIUM": [
        {
          "ADVERTISEMENT_SOURCE_ID": this.nams === 'reech' ? 3 : Object.keys(this.mediaObj).length === 0 ? '' : this.mediaObj[0]['ID'],
          "SOURCE_NAME": this.nams === 'reech' ? '' : this.sourceName,
          "SOURCE_PLACE": this.nams === 'reech' ? '' : this.sourcePlace,
          "PLATFORM_ID": this.nams === 'reech' ? this.step2Form.value.platform : this.platformId,
          "DATE": moment(this.step2Form.value.date).format('yyyy-MM-DD'),
          "TIME": moment(this.step2Form.value.time).format('HH:mm:ss'),
          "ATTACHMENT_DATA": this.step2Form.value.file_array,
          "SOURCE_URL": sourceUrl,
          "PRINT_SOURCE_ID": this.nams === 'reech' ? '' : this.printSourceId,
          "P_M_SOURCE_ID": ""
        }
      ],
      "CODE_VIOLATED": [],
      "files_attached": this.files_attached
    }
    console.log("obj"+JSON.stringify(obj));
    this.complaintService.complaintRegister(obj, 1);
    this.outFilter.emit({ 'cond': 'create', 'obj': obj });
  }

  back() {
    let obj = {}
    // this.step2Form.reset();
    // this.complaintService.currentChatbotComplaintId
    //   .pipe(first()).subscribe(id => {
    //     if (id == 0) {
    //       this.step2Form.reset();
    //     }
    //   })
    this.outFilter.emit({ 'cond': 'back', 'obj': obj });
  }

  cancel() {
    this.step2Form.reset();
    this.backDisable = false;
    this.complaintService.updateNamsComplaintId = 0;
    this.complaintService.updateStep('direct');
    this.complaintService.complaintRegister({}, 0);
    this.complaintService.complaintRegister({}, 1);
    this.router.navigate(['manage-cases']);
  }

  async onFileSelected(event: any) {
    let sizeOfFiles = this.uploadService.getTotalFileSize(event.target.files);
    let totalFileSelected = this.step2Form.value.file_array.length + event.target.files.length;
    if (totalFileSelected < 11 && sizeOfFiles <= 36700160) {
      if (event.target["files"] && event.target.files['length'] != 0) {
        for (let i = 0; i <= event.target.files.length - 1; i++) {
          this.files_attached = "Yes"
          let selectedFile = event.target.files[i];
          this.fileProgress = 0;
          // this.complaintService.currentBlankComplaintId
          //   .pipe(first()).subscribe(id => {
          //     this.blankComplaintId = id;
          //   })
          this.blankComplaintId = this.complaintService.getNamsBlankId;
          let tempObjforGetsigned = {
            id: this.blankComplaintId,
            section: 'a_medium',
            filename: selectedFile['name'],
            type: selectedFile['type']
          }
          this.isUploadProgress = true;
          await this.uploadService.getSignedUrl(tempObjforGetsigned)
            .then(async (res) => {
              if (res && res['data'] && res['data']['SIGNED_URL']) {
                this.fileInfoPath = res['data']['PATH'];
                this.step2Form.value['file_array'].push({
                  ATTACHMENT_SOURCE: res['data']['PATH'],
                  ATTACHMENT_NAME: selectedFile['name'],
                  TYPE_OF_DOCUMENT: selectedFile['type'],
                  SIZE: selectedFile['size']
                });
                let tempObjForUpload = {
                  url: res['data']['SIGNED_URL'],
                  file: selectedFile
                }
                let progressInit = 0;
                await this.uploadService.uploadFilethroughSignedUrl(tempObjForUpload, progressInit => {
                  this.fileProgress = progressInit
                }).then((data12) => {
                  this.isUploadProgress = false;
                  //   this.fileProgress = 0;
                  this.notify.showNotification(
                    'File uploaded successfully',
                    "top",
                    (!!colorObj[200] ? colorObj[200] : "success"),
                    200
                  );
                }).catch((err) => {
                  this.fileProgress = 0;
                  this.isUploadProgress = false;
                  this.handleError(err);
                });
              }
            })
            .catch(err => {
              this.isUploadProgress = false;
              this.handleError(err)
            });
        }
      } else {
        this.files_attached = "No"
      }

    } else {
      this.notify.showNotification(
        "Max size 35MB file allowed",
        "top",
        "warning",
        0
      );
    }
  }

  removeVideoFile(index, key) {
    if (this.step2Form.value['file_array'] != 0) {
      this.step2Form.value['file_array'].splice(index, 1);
      let body = {
        ID: this.blankComplaintId,
        KEY: key
      }
      this.uploadService.deleteObjectFromS3(body).toPromise()
        .then((data1) => {
          this.notify.showNotification(
            data1.message,
            "top",
            (!!colorObj[data1.status] ? colorObj[data1.status] : "success"),
            data1.status
          );
        }).catch(err => this.handleError(err));
    }
  }


  handleError(err) {
    this.notify.showNotification(
      err.error.message,
      "top",
      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
      err.error.status
    );
  }


  resetComplaintForm() {
    this.step2Form.reset();
  }

  public transcriptionAlignment = '';
  keyUp(event: KeyboardEvent) {
    this.transcriptionAlignment = this.transcriptionAlignment.replace(/\n/g, ' ');
  }

  public descriptionAlignment = '';
  keyUp1(event: KeyboardEvent) {
    this.descriptionAlignment = this.descriptionAlignment.replace(/\n/g, ' ');
  }

  public claimsAlignment = '';
  keyUp2(event: KeyboardEvent) {
    this.claimsAlignment = this.claimsAlignment.replace(/\n/g, ' ');
  }

  preview(source) {
    this.imgURL = this.bucketUrl + source;
    window.open(this.imgURL, 'window 1', '');
  }

  checkMedia(media, complaintObj){
    if(media.toLowerCase() === 'tv'){
      this.mediaObj = this.ads.filter(el => {
        return el['ID'] == 1 
      })
      // channel name pick from Media outlet
      this.sourceName = complaintObj['MEDIA_OUTLET'];
    }else if(media.toLowerCase() === 'digital'){
      this.mediaObj = this.ads.filter(el => {
        return el['ID'] == 3 
      })
      // platform is others - 9
      this.platformId = 9;
      this.sourcePlace = complaintObj['MEDIA_OUTLET'];
    }else if(media.toLowerCase() === 'print'){
      this.mediaObj = this.ads.filter(el => {
        return el['ID'] == 5 
      })
      // where have you seen this adver - 1 & source name pick from media outlet
      this.sourceName = complaintObj['MEDIA_OUTLET'];
      this.printSourceId = 1;
    }
    this.step2Form.patchValue({
      'media': this.mediaObj[0]['ADVERTISEMENT_SOURCE_NAME']
    })
  }
}
