<div class="common-toolbar-mobile" style="width: 100%; height: auto;" *ngIf="isMobile">
    <app-mobile-header></app-mobile-header>
</div>
<span class="dashboard-admin-heading" *ngIf="isMobile">
    <p style="text-align: center;margin-top: 20px;margin-bottom: 3px;"> Welcome {{userName}}!</p>
</span>
<div class="dasboard-subheading" *ngIf="isMobile">
    <p style="text-align: center;">Dear ASCI Officers, please move to a larger device for seamlessly accessing all
        functionalities of the system</p>
</div>
<div class="dashboard-container" *ngIf="isMobile">
</div>

<app-icons *ngIf="!isMobile"></app-icons>
<div class="common-toolbar" fxLayout="row" style="width: 100%; height: 50px;" *ngIf="!isMobile">
    <div class="heading-container">
        <app-heading [pagename]="pagename"></app-heading>
    </div>
    <div class="options-container">
        <app-toolbar-options></app-toolbar-options>
    </div>
</div>

<app-mat-spinner-overlay overlay="true" *ngIf="loading && !isMobile">
</app-mat-spinner-overlay>

<mat-toolbar class="toolbar2" fxLayout="row" *ngIf="!isMobile">
    <div fxFlex="4%"></div>
    <div class="header-search">
        <input type="text" name="search" [formControl]="KEYWORD" autocomplete="off" placeholder="Brand/Creative ID/Description" 
        style="font-style: normal;
        font-weight: normal;
        font-size: 14.5px; color: #2F3941;" (keyup)="getKeyword(KEYWORD.value)">
    </div>
    <mat-divider [vertical]="true" class="vertical-divider"></mat-divider>
    <div class="header-search">
        <input type="text" name="search" [formControl]="ADVERTISER" placeholder="Advertiser" autocomplete="off"
        style="font-style: normal;
        font-weight: normal;
        font-size: 14px; color: #2F3941;" (keyup)="getAdvertiser(ADVERTISER.value)">
    </div>
    <mat-divider [vertical]="true" class="vertical-divider"></mat-divider>
    <div class="header-search">
        <input type="text" name="search" [formControl]="MEDIUM" placeholder="Medium" autocomplete="off" 
        style="font-style: normal;
        font-weight: normal;
        font-size: 14px; color: #2F3941;width:156px" (keyup)="getMedium(MEDIUM.value)">
    </div>
    <mat-divider [vertical]="true" class="vertical-divider"></mat-divider>
    <div class="header-search">
        <button mat-flat-button class="date-button" (click)="picker.open()" style="vertical-align: middle;">
            <input matInput [matDatepicker]="picker" (dateChange)="filter_complaints(1)" class="date"
            placeholder="Date-from" [formControl]="DATE_FROM" [readonly]="true" autocomplete="off" style="width: 84px;">
            <mat-datepicker #picker></mat-datepicker>
            <mat-icon class="material-icons-outlined" style="margin-bottom: 3px;transform: scale(0.6);">calendar_today</mat-icon>
        </button>
    </div>
    <mat-divider [horizontal]="true" class="horizontal-divider"></mat-divider>
    <div class="header-search">
        <button mat-flat-button class="date-button" (click)="picker1.open()" style="vertical-align: middle;">
            <input matInput [matDatepicker]="picker1" [formControl]="DATE_TO" (dateChange)="filter_complaints(1)" class="date"
            placeholder="Date-to" [readonly]="true" autocomplete="off" style="width: 84px;">
            <mat-datepicker #picker1></mat-datepicker>
            <mat-icon class="material-icons-outlined" style="margin-bottom: 3px;transform: scale(0.6);">calendar_today</mat-icon>
        </button>
    </div>
    <mat-divider [vertical]="true" class="vertical-divider"></mat-divider>
    <div class="header-search">
        <button class="clear-button" (click)="clearFilter()">
            <span class="material-icons">delete_outline</span>
          </button>
      </div>
    <div fxFlex="75%"></div>
    <div fxLayout="row">
        <div>
            <button mat-button [matMenuTriggerFor]="menu" class="theme-blue-button-admin">
                <span class="bolder">Bulk action</span>
                <mat-icon>arrow_drop_down</mat-icon>
            </button>
            <mat-menu #menu="matMenu" class="admin-menu">
                <div class="admin-option-container">
                    <button mat-menu-item class="option-btn" (click)="bulkShortlist()" [disabled]="disableAction">
                        <span class="option-text">Shortlist</span>
                    </button>
                    <button mat-menu-item class="option-btn" (click)="bulkReject()" [disabled]="disableAction">
                        <span class="option-text">Reject</span>
                    </button>
                    <mat-divider class="option-divider"></mat-divider>
                    <button mat-menu-item class="option-btn" (click)="bulkDelete()" [disabled]="disableDelete">
                        <span class="option-text">Delete</span>
                    </button>
                </div>
            </mat-menu>
        </div>
    </div>
</mat-toolbar>

<mat-card class="card" [hidden]="loading || isMobile">
    <div class="table-scroll">
        <table mat-table  class="admin-table" [dataSource]="dataSource" matSort #table>
            <div>
                <ng-container matColumnDef="check">
                    <th mat-header-cell *matHeaderCellDef style="width: 5%;">
                        <mat-checkbox class="select-chkb" [(ngModel)]="select_all"
                            (ngModelChange)="onSelectAll($event)">
                        </mat-checkbox>
                    </th>
                    <td mat-cell *matCellDef="let element" style="width: 5%;">
                        <mat-checkbox class="chkb-list" [(ngModel)]="element.isselected"
                            (change)="selectComplaint($event.checked, element.ID, element.status)"></mat-checkbox>
                    </td>
                </ng-container>

                <ng-container matColumnDef="BRAND">
                    <th mat-header-cell *matHeaderCellDef style="width: 15%;"><b>Brand Name</b>
                        <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!brand_asc && !brand_desc" (click)="sortComplaints('BRAND', 'ASC')">
                        <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="brand_asc" (click)="sortComplaints('BRAND', 'DESC')">
                        <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="brand_desc" (click)="sortComplaints('', '')">
                    </th>
                    <td mat-cell *matCellDef="let element" style="width: 15%; padding-right: 2%; cursor: pointer;"
                        (click)="complaintDetails(element.ID, element.status)">
                        {{element.BRAND}}
                    </td>
                </ng-container>

                <ng-container matColumnDef="MEDIA">
                    <th mat-header-cell *matHeaderCellDef style="width: 7%; padding-right: 2%;"><b>Medium</b></th>
                    <td mat-cell *matCellDef="let element" style="width: 7%; padding-right: 2%;">
                        {{element.MEDIA}} </td>
                </ng-container>

                <ng-container matColumnDef="advertiser">
                    <th mat-header-cell *matHeaderCellDef style="width: 11%;"><b>Advertiser</b></th>
                    <td mat-cell *matCellDef="let element" style="width: 11%; padding-right: 2%; font-size: 13px;">
                        <p class="ellipsis1"> {{element.ADVERTISER}} </p>
                    </td>
                </ng-container>

                <ng-container matColumnDef="date">
                    <th mat-header-cell *matHeaderCellDef style="width: 10%;"><b>Advertisement Date</b></th>
                    <td mat-cell *matCellDef="let element" style="padding-left: 15px; width: 5%;"> {{element.DATE | date:'dd-MM-yyyy'}} </td>
                </ng-container>

                <ng-container matColumnDef="description">
                    <th mat-header-cell *matHeaderCellDef style="width: 19%; padding-right: 2%;"><b>Description</b></th>
                    <td mat-cell *matCellDef="let element" style="width: 19%; padding-right: 2%;">
                        <p class="ellipsis">{{element.COMPLAINT}} </p>
                    </td>
                </ng-container>

                <ng-container matColumnDef="status">
                    <th mat-header-cell *matHeaderCellDef style="width: 10%;"><b>Status</b>
                        <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!status_asc && !status_desc" (click)="sortComplaints('REGISTERED', 'ASC')">
                        <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="status_asc" (click)="sortComplaints('REGISTERED', 'DESC')">
                        <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="status_desc" (click)="sortComplaints('', '')">
                    </th>
                    <td mat-cell *matCellDef="let element" style="width: 10%;"
                        [ngClass]="{'aprv' : 'Confirmed' == element.status, 'pen' : 'Unconfirmed' == element.status }">
                        {{element.status}} </td>
                </ng-container>

                <ng-container matColumnDef="action">
                    <th mat-header-cell *matHeaderCellDef style="width: 7%; padding-left: 1%;"><b>Action</b></th>
                    <td mat-cell *matCellDef="let element" style="width: 10%;">
                        <div fxLayout="row" fxLayoutGap="15px">
                            <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Confirmed'"
                                (click)="complaintDetails(element.ID, element.status)"
                                style="position: relative; top: 11px; display: flex; align-items: center; cursor: pointer; left: 11px;">
                                <img src="../../../assets/images/View.png" width="20px">
                                <p style="
                                    font-style: normal;
                                    font-weight: normal;
                                    font-size: 12px;color: rgba(0, 136, 203, 0.6);">View</p>
                            </div>
                            <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Confirmed'"
                                (click)="deleteComplaint(element.ID)"
                                style="position: relative; top: 10px; left: 16px; display: flex; align-items: center; cursor: pointer;">
                                <img src="../../../assets/images/delete-red.png" width="15px">
                                <p style="
                                    font-style: normal;
                                    font-weight: normal;
                                    font-size: 12px;color: rgba(237, 47, 69, 0.6);">Delete</p>
                            </div>
                            <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                                (click)="shortList(element.ID)"
                                style="position: relative; top: 10px; left: 0px; display: flex; align-items: center; cursor: pointer;">
                                <img src="../../../assets/images/Admin-Approve.png" width="15px">
                                <p style="
                                    font-style: normal;
                                    font-weight: normal;
                                    font-size: 12px;color: rgba(4, 165, 133, 0.6);">Shortlist</p>
                            </div>
                            <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                                (click)="rejectComplaint(element.ID)"
                                style="position: relative; top: 10px; left: 0px; display: flex; align-items: center; cursor: pointer;">
                                <img src="../../../assets/images/Reject-orange.png" width="15px">
                                <p style="
                                    font-style: normal;
                                    font-weight: normal;
                                    font-size: 12px;color: rgba(248, 158, 27, 0.6);">Reject</p>
                            </div>
                        </div>
                    </td>
                </ng-container>
                <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;">
                </tr>
            </div>
        </table>
    </div>
</mat-card>
<mat-paginator (page)="changePage($event)" [pageSize]="10" [pageIndex]="0" *ngIf="!zeroComplaints && !isMobile"></mat-paginator> 
<span class="label" *ngIf="!zeroComplaints && !isMobile"> {{rangeLabel}} of {{totalCount}}</span>  
<div class="text-message" *ngIf="zeroComplaints && !isMobile">
    <span>
        <br>
        No Complaints available ....
        <br>
    </span>
</div>