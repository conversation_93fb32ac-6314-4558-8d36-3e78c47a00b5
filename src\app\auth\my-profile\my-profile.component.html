<div *ngIf="!mobileView">
    <app-icons *ngIf="userRoleId == 1 || userRoleId == 2 || userRoleId == 3 || userRoleId == 6"></app-icons>

    <div class="common-toolbar" fxLayout="row" style="width: 100%;height: 50px;">
        <div class="pagename" routerLink="/home/<USER>"
            *ngIf="userRoleId == 4 || userRoleId == 5 || userRoleId == 7 || userRoleId == 8">
            <img src="../assets/images/logo.png" style="cursor: pointer;">
        </div>
        <div class="pagename" *ngIf="userRoleId == 9">
            <img src="../assets/images/logo.png" style="cursor: pointer;">
        </div>
        <div class="heading-container">
            <app-heading [pagename]="pagename"></app-heading>
        </div>
        <div class="options-container">
            <app-toolbar-options></app-toolbar-options>
        </div>
    </div>
</div>
<!-- <div *ngIf="mobileView" class="common-toolbar" style="width: 100%;height: auto;">
    <app-mobile-header></app-mobile-header>
</div> -->

<div *ngIf="mobileView" class="header-container" fxLayout="row">
    <div class="headline-container" fxLayout="row" fxLayoutAlign="start center" style="color: #fff;font-size: 20px;">
        <img  src="../assets/images/arrow.png" style="padding-right: 10px; padding-top: 3px;" (click)="viewDashboard()"/>
        My Profile
    </div>
    <span style="flex: 1 1 auto;"></span>
    <div fxLayout="row" fxLayoutAlign="end center">
        <img class="bell-btn" src="../assets/images/bell-mobile.png" (click)="viewNotifications()" *ngIf="roleId == 4 || roleId == 5 || roleId == 7 || roleId == 8"/>
        <button [matMenuTriggerFor]="admin" mat-mini-fab class="flex profile-btn">{{roleName}}</button>
    </div>
    <mat-menu #admin="matMenu" class="admin-menu">
        <div class="admin-option-container">
            <button mat-menu-item routerLink="/my-profile" class="option-btn" (click)="viewProfile(true)" *ngIf="roleId == 4 || roleId == 5 || roleId == 7 || roleId == 8">
                <span class="option-text">My profile</span>
            </button>
            <mat-divider class="option-divider" *ngIf="roleId == 4 || roleId == 5 || roleId == 7 || roleId == 8"></mat-divider>
            <button mat-menu-item class="option-btn" (click)='logout()'>
                <span class="option-text">Log out</span>
            </button>
        </div>
    </mat-menu>
</div>
<!-- <div class="myprofile-body" fxLayout="row" fxLayoutGap="1%"> -->
<div class="myprofile-body">
    <div
        [ngClass]="{'profile-div-condition': userRoleId == 4 || userRoleId == 5 || userRoleId == 7 || userRoleId == 8 || userRoleId == 9, 'profile-div': userRoleId == 1 || userRoleId == 2 || userRoleId == 3 || userRoleId == 6}">
        <div fxFlex="15px"></div>
        <!-- <div fxLayout="column" fxLayoutGap="10px" class="buttons" fxFlex="12%"> -->
        <div class="buttons">
            <div class="namsfiles" *ngIf="userRoleId == 9">
                <button mat-flat-button class="namsfiles-button" style="width: 165px;border-radius: 4px;"
                    (click)="namsfiles()" [ngStyle]="{'color':filescolor(),'background-color':filesBGcolor()}">
                    <img class="show-icon" style="position: absolute;" [ngStyle]="{'visibility': filesIcon_black()}"
                        src="../../../assets/images/File-black.png" />
                    <img class="hide-icon" [ngStyle]="{'visibility': filesIcon_white()}"
                        src="../../../assets/images/File-white.png" />
                    <span style="font-style: normal;
                        font-weight: normal;
                        font-size: 14px;  position: relative;left: -15px;">NAMS Files</span>
                </button>
            </div>
            <div class="userprofile">
                <button mat-flat-button class="userprofile-button" (click)="userprofile()"
                    [ngStyle]="{'color':profilecolor(),'background-color':profileBGcolor()}">
                    <img class="show-icon" style="position: absolute;" [ngStyle]="{'visibility': showTagIcon_black()}"
                        src="../assets/images/User-black-icon.svg" />
                    <img class="hide-icon" [ngStyle]="{'visibility': showTagIcon_white()}"
                        src="../assets/images/User-white-icon.svg" />
                    <span class="userprofile-text">User profile</span>
                </button>
            </div>
            <div class="changepswd">
                <button mat-flat-button class="changepswd-button" (click)="changepassword()"
                    [ngStyle]="{'color':pswdcolor(),'background-color':pswdBGcolor()}">
                    <img class="show-icon" style="position: absolute;" [ngStyle]="{'visibility': pswdTagIcon_black()}"
                        src="../assets/images/Lock-black-icon.svg" />
                    <img class="hide-icon" [ngStyle]="{'visibility': pswdTagIcon_white()}"
                        src="../assets/images/Lock-white-icon.svg" />
                    <span class="changepaswd-text">Change password</span>
                </button>
            </div>
        </div>
    </div>
    <div class="complaint-container">
        <form [formGroup]="addform">
            <div *ngIf="isuserprofile">
                <div class="userprofile-content">
                    <img src="../assets/images/user.svg" style="position: relative;top: -5px;">
                    <span style="font-weight: 600;
                    font-style: normal;
                    font-size: 18px;
                    color: #000000;"> &nbsp; Profile
                    </span>
                </div>

                <div class="contents">
                  <div class="scrollable-content">
                    <div class="names">
                        <div class="control-container">
                            <div> Title</div>
                            <mat-form-field appearance="outline" class="input-field_title">
                                <mat-select formControlName="salutation" placeholder="Title"
                                    (selectionChange)="selectTitle($event)" style="font-style: normal;
                                    font-weight: normal; height: 20px;">
                                    <mat-option *ngFor="let title of titleList" [value]="title.SALUTATION_NAME">
                                        {{title.SALUTATION_NAME}}</mat-option>
                                </mat-select>
                                <mat-error  *ngIf="!addform.get('salutation').valid">
                                    Title is required
                                </mat-error>
                            </mat-form-field>
                        </div>
                        <div class="control-container">
                            <div> First name</div>
                            <mat-form-field appearance="outline" class="input-field_fname">
                                <input matInput formControlName="fname" style="font-style: normal;
                                font-weight: normal; height: 20px;" autocomplete="off">
                                <mat-error *ngIf="addform.controls['fname'].errors?.required">
                                    First name is required
                                </mat-error>
                                <mat-error *ngIf="addform.controls['fname'].errors?.pattern">
                                    Only text is allowed
                                </mat-error>
                                <mat-error *ngIf="addform.controls['fname'].errors?.minlength">
                                    First name should be of min length 3 and max length 25
                                </mat-error>
                                <mat-error *ngIf="addform.controls['fname'].errors?.maxlength">
                                    First name should be of min length 3 and max length 25
                                </mat-error>
                            </mat-form-field>
                        </div>
                        <div class="control-container">
                            <div class="last_name">Last name</div>
                            <mat-form-field appearance="outline" class="input-field_lastname">
                                <input matInput formControlName="lname" style="font-style: normal;
                                font-weight: normal; height: 20px;" autocomplete="off">
                                <mat-error *ngIf="addform.controls['lname'].errors?.required">
                                    Last Name is required
                                </mat-error>
                                <mat-error *ngIf="addform.controls['lname'].errors?.pattern">
                                    Only text is allowed
                                </mat-error>
                                <mat-error *ngIf="addform.controls['lname'].errors?.maxlength">
                                    Last Name should be of max length 25
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div *ngIf="!mobileView">
                        <div class="names1">
                            <div>Phone number</div>
                        </div>
                        <div class="phone_number">
                            <mat-form-field appearance="outline" class="input-field_phno">
                                <input matInput formControlName="phone" type="number" style="font-style: normal;
                                font-weight: normal; height: 20px;" autocomplete="off">
                            <mat-error *ngIf="addform.controls['phone'].errors?.required">
                                Phone number is required
                            </mat-error> 
                            <mat-error *ngIf="addform.controls['phone'].errors?.pattern">
                                Enter valid phone number with length 10
                            </mat-error> 
                            </mat-form-field>
                            <span *ngIf="isMobileVerified == 'true'" class="phno-varify">
                                <mat-icon style="position: relative;top:6px">verified_user</mat-icon> Verified
                            </span>
                        </div>
                    </div>
                    <div *ngIf="mobileView">
                        <div class="mobview-phverify" fxLayout="row">
                            <div class="names1">
                                <div>Phone number</div>
                            </div>
                            <span style="flex: 1 1 auto;"></span>
                            <!-- <div > -->
                            <span *ngIf="isMobileVerified == 'true'" class="phno-varify-mob" fxLayout="row"
                                fxLayoutAlign="end center">
                                <img src="../assets/images/Check circle.png" style="position: relative;left: -5px;"> Verified
                            </span>
                            <!-- </div> -->
                        </div>
                        <div class="phone_number">
                            <mat-form-field appearance="outline" class="input-field_phno">
                                <input matInput formControlName="phone" style="font-style: normal;
                                font-weight: normal; height: 20px;" autocomplete="off">
                            </mat-form-field>
                        </div>
                    </div>

                    <div *ngIf="!mobileView">
                        <div class="names1">
                            <div>
                                Email address
                                <span *ngIf="isEmailVerified == 'false'" style="font-size: 12px;color:#ED2F45">( Not
                                    Verified )</span>
                            </div>
                        </div>
                        <div class="email_add">
                            <mat-form-field appearance="outline" class="input-field_phno">
                                <input matInput formControlName="emailId" style="font-style: normal;
                                font-weight: normal; height: 20px;position: relative;bottom: 2px;" autocomplete="off">
                                <mat-error *ngIf="addform.controls['emailId'].errors?.required">
                                    Email id is required
                                </mat-error>   
                                <mat-error *ngIf="addform.controls['emailId'].errors?.pattern">
                                    Please Enter Valid Email id
                                </mat-error> 
                            </mat-form-field>
                            <span *ngIf="isEmailVerified == 'true'"
                                style="color:#3ba791;font-size: 12px;position: relative;left: 6px;bottom: 3px;">
                                <mat-icon style="position: relative;top:6px">verified_user</mat-icon> Verified
                            </span>
                            <span (click)="verifyEmail('email')" *ngIf="isEmailVerified == 'false'"
                                style="color:#0088CB;font-size: 12px;position:relative;left: 5px;cursor:pointer;bottom: 4px;">
                                Verify Now
                            </span>
                        </div>
                    </div>

                    <div *ngIf="mobileView">
                        <div class="mobview-emailverify" fxLayout="row">
                            <div class="names1">
                                <div>
                                    Email address
                                    <span *ngIf="isEmailVerified == 'false'" style="font-size: 12px;color:#ED2F45">( Not
                                        Verified )</span>
                                </div>
                            </div>
                            <span *ngIf="isEmailVerified == 'true'" style="flex: 1 1 auto;"></span>
                            <span *ngIf="isEmailVerified == 'true'" fxLayout="row" fxLayoutAlign="end center"
                                style="color:#3ba791;font-size: 12px;">
                                <img src="../assets/images/Check circle.png" style="position: relative;left: -5px;"> Verified
                            </span>
                            <span *ngIf="isEmailVerified == 'false'" style="flex: 1 1 auto;"></span>
                            <span *ngIf="isEmailVerified == 'false'" (click)="verifyEmail('email')" fxLayout="row"
                                fxLayoutAlign="end center" style="color:#0088CB;font-size: 12px;cursor:pointer;">
                                Verify Now
                            </span>
                        </div>
                        <div class="email_add">
                            <mat-form-field appearance="outline" class="input-field_phno">
                                <input matInput formControlName="emailId"
                                    pattern="[a-zA-Z0-9.-_]{1,}@[a-zA-Z.-]{2,}[.]{1}[a-zA-Z]{2,}" style="font-style: normal;
                                    font-weight: normal; height: 20px;position: relative;bottom: 2px;" autocomplete="off">
                            </mat-form-field>
                        </div>
                    </div>

                    <div class="names-row">
                        <div class="control-container">
                            <div> State</div>
                            <mat-form-field appearance="outline" class="input-field">
                                <mat-select formControlName="state" (selectionChange)="selectState($event)"
                                    placeholder="Select state" style="font-style: normal;
                                    font-weight: normal; height: 20px;">
                                    <mat-option *ngFor="let state of stateList" [value]="state.STATE_TYPE_NAME">
                                        {{state.STATE_TYPE_NAME}}
                                    </mat-option>
                                </mat-select>
                                <mat-error  *ngIf="!addform.get('state').valid">
                                    Please choose your state
                                </mat-error>
                            </mat-form-field>
                        </div>
                        <div class="control-container">
                            <div>District</div>
                            <mat-form-field appearance="outline" class="input-field_dist">
                                <mat-select formControlName="district" (selectionChange)="selectDistrict($event)"
                                    placeholder="Select district" style="font-style: normal;
                                    font-weight: normal; height: 20px;">
                                    <mat-option *ngFor="let district of districtList"
                                        [value]="district.DISTRICT_TYPE_NAME">
                                        {{district.DISTRICT_TYPE_NAME}}
                                    </mat-option>
                                </mat-select>
                                <mat-error  *ngIf="!addform.get('district').valid">
                                    Please choose your district
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="names1" *ngIf="userRoleId == 2 || userRoleId == 3 || userRoleId == 6">
                        <div>Department Type</div>
                    </div>
                    <div class="departmentname" *ngIf="userRoleId == 2 || userRoleId == 3 || userRoleId == 6">
                        <mat-form-field appearance="outline" class="input-field_phno">
                            <mat-select formControlName="department" style="font-style: normal;
                            font-weight: normal; height: 20px;" (selectionChange)="onDeptChange($event)">
                                <mat-option *ngFor="let dept of departmentList" [value]="dept.DEPARTMENT_TYPE_NAME">
                                    {{dept.DEPARTMENT_TYPE_NAME}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div class="names1" *ngIf="userRoleId == 5">
                        <div>Company name</div>
                    </div>
                    <div class="departmentname" *ngIf="userRoleId == 5">
                        <mat-form-field appearance="outline" class="input-field_phno">
                            <input matInput formControlName="company" style="font-style: normal;
                            font-weight: normal; height: 20px;position: relative;bottom: 2px;" autocomplete="off">
                        </mat-form-field>
                    </div>
                    <div class="names1">
                        <div>Postal Code</div>
                    </div>
                    <div class="rolename">
                        <mat-form-field appearance="outline" class="input-field_phno">
                            <input matInput formControlName="pin" style="font-style: normal;
                            font-weight: normal; height: 20px;position: relative;bottom: 2px;" autocomplete="off">
                            <mat-error *ngIf="addform.controls['pin'].errors?.required">
                                Postal Code is required
                            </mat-error>   
                            <mat-error *ngIf="addform.controls['pin'].errors?.pattern">
                                Enter valid 6 digit postal code
                            </mat-error>     
                        </mat-form-field>
                    </div>
                    <div class="names1" *ngIf="userRoleId == 7">
                        <div>Organization</div>
                    </div>
                    <div class="rolename" *ngIf="userRoleId == 7">
                        <mat-form-field appearance="outline" class="input-field_phno">
                            <input matInput formControlName="organization" style="font-style: normal;
                            font-weight: normal; height: 20px;position: relative;bottom: 2px;" autocomplete="off">
                        </mat-form-field>
                    </div>
                    <div class="names1" *ngIf="userRoleId == 7">
                        <div>Profession</div>
                    </div>
                    <div class="rolename" *ngIf="userRoleId == 7">
                        <mat-form-field appearance="outline" class="input-field_phno">
                            <input matInput formControlName="profession" style="font-style: normal;
                            font-weight: normal; height: 20px;position: relative;bottom: 2px;" autocomplete="off">
                        </mat-form-field>
                    </div>
                    <div class="names1">
                        <div>Role</div>
                    </div>
                    <div class="rolename">
                        <mat-form-field appearance="outline" class="input-field_phno">
                            <input matInput formControlName="role" style="font-style: normal;
                            font-weight: normal; height: 20px;position: relative;bottom: 2px;" autocomplete="off">
                        </mat-form-field>
                    </div>
                    <!-- *ngIf="addform.get('role').value == 1 || addform.get('role').value == 2" -->
                  </div>
                    <div class="update-div">
                        <button mat-flat-button class="update-btn" (click)="update(addform.value)"
                            [disabled]="addform.invalid">
                            <span class="bolder">Update</span>
                        </button>
                    </div>
                </div>
            </div>

            <div *ngIf="isverifyaccount">
                <div class="userprofile-content">
                    <mat-icon style="position: relative;top: 7px; width: 1%;">search</mat-icon> &nbsp;
                    <span style="font-weight: 600;
                    font-style: normal;
                    font-size: 18px;
                    color: #000000;"> &nbsp; Verify account
                    </span>
                </div>
                <div class="contents">
                    <div class="names1">
                        We have sent you an OTP on your registered Email Id to verify your account,
                        <p>Please enter to verify...</p>
                    </div>
                    <div class="names1">
                        <div>Enter OTP</div>
                    </div>
                    <div class="enter_otp">
                        <mat-form-field appearance="outline" class="input-field_otp">
                            <input #otpfield matInput style="font-style: normal;
                            font-weight: normal; height: 20px;" autocomplete="off">
                        </mat-form-field>
                    </div>
                    <div style="position: relative; padding-left: 438px;margin-top: -15px;">
                        <button class="resend" (click)="verifyEmail('email')" [disabled]="isExpiryCompleted"> Resend OTP
                        </button>
                    </div>
                    <span *ngIf="showTime"
                        style="color: #ED2F45;position: relative;left: 445px;top: 1px;">{{timeLeft}}</span>
                    <div class="lastdivdr">
                        <mat-divider></mat-divider>
                    </div>
                    <div class="toolbar-btns2">
                        <div>
                            <button mat-flat-button class="cancel-btn" (click)="cancel()">Cancel</button>
                            <button mat-flat-button class="register-btn"
                                (click)="verifyOtp(otpfield.value)">Verify</button>
                        </div>
                    </div>
                </div>
            </div>

            <div *ngIf="isnamsfiles" style="width: 100%;">
                <div fxLayout="row" fxLayoutGap="2%">
                    <div class="file-history" style="width: 58%;">
                        <mat-icon style="position: relative;top:8px;height:19px;padding-right: 26px;">access_time
                        </mat-icon> File History
                        <div class="nams-table" infiniteScroll [infiniteScrollDistance]="2" [infiniteScrollThrottle]="300" 
                            (scrolled)="onScrollDown()" [scrollWindow]="false">
                            <table mat-table #table [dataSource]="dataSource" matSort>
                                <ng-container matColumnDef="FILE_NAME">
                                    <th mat-header-cell *matHeaderCellDef style="width: 70%; font-weight: bold;"> File
                                        Name </th>
                                    <td mat-cell *matCellDef="let row" [matTooltip]="row.FILE_NAME" style="width: 70%;">
                                        {{row.FILE_NAME | slice:0:35}} </td>
                                </ng-container>
                                <ng-container matColumnDef="CREATED_DATE">
                                    <th mat-header-cell *matHeaderCellDef style="width: 30%; font-weight: bold;"> Upload
                                        Date </th>
                                    <td mat-cell *matCellDef="let row" style="width: 30%; padding-left: 20px;">
                                        {{row.CREATED_DATE | date:'dd/MM/yyyy'}} </td>
                                </ng-container>
                                <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
                                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                            </table>
                        </div>
                    </div>
                    <div class="file-upload-main" style="width: 78%;">
                        <img src="../../assets/images/upload.png" style="padding-right: 8px;">File Upload
                        <div class="file-upload">
                            <div class="browse-files">
                                <img src="../../assets/images/fileimage.png"
                                    style="position: relative;top: 10px;left: 57px;">
                                <div class="browse-file-heading">
                                    <input hidden type="file" #uploader (change)="uploadFile($event)"
                                        accept=".xls,.xlsx,.csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel" />
                                    <span style="text-decoration: underline; cursor: pointer;"
                                        (click)="uploader.click()">Browse File</span>
                                    <!-- <span style="color:#5A6F84;"> or paste the link below</span> -->
                                </div>
                                <!-- <div class="panel-body" fxLayout="row">
                                    <div class="doc-icon-container flex">
                                      <button mat-icon-button class="doc-btn flex">
                                        <img src="../../assets/images/Link.png" style="position: relative;bottom: 8px;width: 11px;">
                                      </button>
                                    </div>
                                    <div class="link-container">
                                        <input matInput>
                                    </div>
                                  </div> -->
                            </div>
                            <div class="panel-body1" fxLayout="row" style="width:530px">
                                <div class="doc-icon-container flex">
                                    <button mat-icon-button class="doc-btn flex">
                                        <img src="../../assets/images/media_doc.svg"
                                            style="position: relative; bottom: 1px;">
                                    </button>
                                </div>
                                <div class="link-container">
                                    <p>{{selectedFile?.name}}</p>
                                </div>
                                <!-- <button mat-button mat-icon-button (click)="preview(url)" style="bottom: 7px;" *ngIf="!!selectedFile?.name">
                                    <img src="../../../assets/images/View.png" style="padding-bottom: 4px;">
                                </button> -->
                                <button mat-button mat-icon-button aria-label="Clear" (click)="removeFile()" style="bottom: 9px;">
                                    <img src="../../assets/images/close-red.png" class="close-icon">
                                </button>
                            </div>
                            <mat-divider></mat-divider>
                            <div class="upload-buttons">
                                <!-- <button mat-stroked-button class="cancel-button" (click)="cancel()">Cancel</button> -->
                                <button mat-flat-button class="upload-button" (click)="submit()">Upload</button>
                            </div>
                        </div>
                        <app-mat-spinner-overlay overlay="true" *ngIf="isUploadProgress">
                        </app-mat-spinner-overlay>
                    </div>
                </div>
            </div>

            <div *ngIf="ischangepassword">
                <div class="userprofile-content">
                    <img src="../assets/images/Lockpswd.svg" style="position: relative;top: -5px;">
                    <span style="font-style: normal;
                    font-weight: 600;
                    font-size: 18px;
                    color: #000000;"> &nbsp;Change password
                    </span>
                </div>
                <form [formGroup]="changepswdform">
                    <div class="contents" style="margin-top: 25px;">
                        <div class="names1">
                            <div>Old password</div>
                        </div>
                        <div class="phone_number">
                            <mat-form-field  appearance="outline" class="input-field_phno">
                                <input matInput id="oldPassword" formControlName="old_pswd" style="font-style: normal;
                                font-weight: normal; height: 20px;" autocomplete="off">
                            </mat-form-field>
                            <mat-error style="margin-top: -19px;font-size: 12px;" *ngIf="changepswdform.get('old_pswd').touched && changepswdform.controls['old_pswd'].errors?.required">
                                Old Password is required
                            </mat-error>   
                            <mat-error style="margin-top: -19px;font-size: 12px;" *ngIf="changepswdform.controls['old_pswd'].errors?.pattern">
                                Password should contain at least one uppercase letter, one lowercase letter, one number and one special character (#?!@$%^&*-) and should be of length 8-15 characters
                            </mat-error> 
                        </div>
                        <div class="names1">
                            <div>New password</div>
                        </div>
                        <div class="email_add">
                            <mat-form-field appearance="outline" class="input-field_phno">
                                <input matInput formControlName="new_pswd" style="font-style: normal;
                                font-weight: normal; height: 20px;" autocomplete="off">
                            </mat-form-field>
                            <mat-error style="margin-top: -19px;font-size: 12px;" *ngIf="changepswdform.get('new_pswd').touched && changepswdform.controls['new_pswd'].errors?.required">
                                New Password is required
                            </mat-error>   
                            <mat-error style="margin-top: -19px;font-size: 12px;" *ngIf="changepswdform.controls['new_pswd'].errors?.pattern">
                                Password should contain at least one uppercase letter, one lowercase letter, one number and one special character (#?!@$%^&*-) and should be of length 8-15 characters
                            </mat-error> 
                        </div>
                        <div class="names1">
                            <div>Confirm new password</div>
                        </div>
                        <div class="departmentname">
                            <mat-form-field appearance="outline" class="input-field_phno">
                                <input matInput formControlName="confirm_pswd" style="font-style: normal;
                                font-weight: normal; height: 20px;" autocomplete="off">
                            </mat-form-field>
                            <mat-error style="margin-top: -19px;font-size: 12px;" *ngIf="changepswdform.get('confirm_pswd').touched && changepswdform.controls['confirm_pswd'].errors?.required">
                                Confirm Password is required
                            </mat-error>   
                            <mat-error style="margin-top: -19px;font-size: 12px;" *ngIf="changepswdform.controls['confirm_pswd'].errors?.pattern">
                                Password should contain at least one uppercase letter, one lowercase letter, one number and one special character (#?!@$%^&*-) and should be of length 8-15 characters
                            </mat-error> 
                        </div>
                        <div class="update-div">
                            <button mat-flat-button class="update-btn" (click)="changePassword(changepswdform.value)"
                                [disabled]="changepswdform.invalid"> 
                                <span class="bolder">Update</span> 
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </form>
    </div>
</div>