.close-div {
    position: relative;
    bottom: 9px;
}

.close-btn {
    color: solid rgba(47, 57, 65, 0.6);
    background: #F3F3F3;
    border: 1px solid rgba(47, 57, 65, 0.6);
}

.input-field {
    width: 100%;
}

.recommendation-box {
    background: #F5F5F5;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 25%);
    border-radius: 4px;
    margin-top: 26px;
    padding: 11px;
    width: 96%;
    box-sizing: border-box;
}

.highlighted-recommendation-box {
    background: #F5F5F5;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 25%);
    border-radius: 4px;
    margin-top: 26px;
    padding: 11px;
    width: 96%;
    box-sizing: border-box;
    border-style: solid;
    border-width: 2px;
    border-color: #9d9898;
}

.box-heading {
    color: rgba(0, 0, 0, 0.4);
    font-style: italic;
}

.cancel-btn {
    background: #FFFFFF;
    border-radius: 3px;
    border: none;
    margin-right: 10px;
}

button[disabled] {
    border: 1px solid #999999;
    background-color: #cccccc;
    color: #666666;
    opacity: 0.3;
}

.more-icon[disabled] {
    position: relative;
    top: 5px;
}

.publish-div {
    position: relative;
    bottom: 6px;
}

.mat-menu-panel {
    min-height: 44px !important;
}

::ng-deep .publish-menu {
    min-height: 0px;
}

::ng-deep .mat-menu-content {
    padding-bottom: 0px !important;
    padding-top: 0px !important;
}

.recommendations {
    height: 384px;
    overflow-y: scroll;
    overflow-x: hidden;
}

.recommendations::-webkit-scrollbar {
    display: none;
}

.recommendations {
    -ms-overflow-style: none;
    scrollbar-width: none;
}