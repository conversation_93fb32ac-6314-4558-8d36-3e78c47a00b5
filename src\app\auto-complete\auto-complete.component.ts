import { Component, OnInit } from '@angular/core';
import { ComplaintsService } from '../services/complaints.service';
import {FormControl} from '@angular/forms';
import {Observable} from 'rxjs';
import {map, startWith} from 'rxjs/operators';

@Component({
  selector: 'app-auto-complete',
  templateUrl: './auto-complete.component.html',
  styleUrls: ['./auto-complete.component.css']
})
export class AutoCompleteComponent implements OnInit {

  myControl = new FormControl();
  options: string[] = ['India', 'USA', 'UK', 'Canada','Singapore','Malaysia'];
  filteredOptions: Observable<string[]>;
  listByValue = [];

  constructor(private cs:ComplaintsService) { }

  ngOnInit(): void {
    this.filteredOptions = this.myControl.valueChanges.pipe(
      startWith(''),
      map(value => this._filter(value))
    );
  }
  private _filter(value: string): string[] {
    const filterValue = value.toLowerCase();
    return this.options.filter(option => option.toLowerCase().includes(filterValue));
    }

  getValue(value) {
    this.cs.autoComplete(value).subscribe(data=>this.listByValue = data);
  }

}
