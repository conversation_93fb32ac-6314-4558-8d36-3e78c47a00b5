import { Component, HostListener, Inject, OnInit, <PERSON><PERSON>, ViewChild } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { AddEditCompanyComponent } from 'src/app/auth/add-edit-company/add-edit-company.component';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { ThirdPartyService } from 'src/app/services/third-party.service';
import { colorObj } from 'src/app/shared/color-object';
import { environment } from 'src/environments/environment';
import { ReechDeleteReasonComponent } from '../reech-delete-reason/reech-delete-reason.component';
import { NamsComplaintsComponent } from '../nams-complaints/nams-complaints.component';
import { NamsReechComponent } from '../nams-reech/nams-reech.component';
import { saveAs as importedSaveAs } from "file-saver";
import { ViewMediaFileComponent } from 'src/app/cases/view-media-file/view-media-file.component';
import { DomSanitizer } from '@angular/platform-browser';
import { ViewDocumentComponent } from 'src/app/cases/view-document/view-document.component';

@Component({
  providers: [NamsComplaintsComponent, NamsReechComponent],
  selector: 'app-nams-details',
  templateUrl: './nams-details.component.html',
  styleUrls: ['./nams-details.component.scss']
})
@Pipe({ name: 'safeHtml' })

export class NamsDetailsComponent implements OnInit {

  namComplaintId: number = 0;
  similarCompLoading: boolean = true;
  loadingDetails: boolean = true;
  namsTamsDetails: any = [];
  complaintTypeId: number;
  compSourceId: number;
  id: number;
  public classificationTag: string = '';
  public msgvalue: any = [];
  public bucketUrl = `${environment.BUCKET_URL}`;
  complaint_attachment_data: string;
  similarityComplaintList: any[];
  extraSimilarComplaints = 0;
  matchFound: boolean = true;
  complaintSource: any[];
  userType: any[];
  loading: boolean = false;
  idArray = [];
  confirmationMsg: any = {};
  detail_link: any;
  nams: string = 'tams';
  translation_link: string;
  hiddenMore: boolean = true;
  compDetails: any = [];
  complaint_id: any;
  detail_company: any;
  company_details: any;
  detail_adsource: any;
  detail_platform: any;
  detail_channel: any;
  detail_addate: any;
  similar_detail_link: any;
  detail_place: any;
  longText1: string;
  longText4: string;
  longText5: string;
  docUrl;
  media_outlet: any;
  media: any;
  super_category: any;
  edition: any;
  suppliment: any;
  ad_language: any;
  creative_id: any;
  translation_hyper: any;
  influencer_name: any;
  engagements: any;
  publication_url: any;
  profile_url: any;
  influencer_contact: any;
  influencer_email: any;
  complaint_source_id: number;
  seen_date: any;
  transcription: any;
  duration: any;
  detail_advert: string;
  detail_complaint: string;
  noOfDocs = 0;
  adDocs: any;
  adMedium: any;
  imgURL: string;
  url: string;
  parent_id: any;
  comp_date: any;
  detail_date: any;
  viewMoreId: any;
  panelOpenState = false;
  docFileList: any[] = [];
  complainantFiles = [];
  advertiserFiles = [];
  internalFiles = [];
  complaintClaims: any = [];
  complaintCodeViolated: any = [];
  guidelines: any = [];
  similarDocs: any;
  isMobile: boolean;
  innerWidth: number;
  reech_tams_checked: boolean = false;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    private router: Router,
    private notify: NotificationService,
    private cs: ComplaintsService,
    private namsService: ThirdPartyService,
    private namsTams: NamsComplaintsComponent,
    private namsReech: NamsReechComponent,
    private dialogRef: MatDialogRef<NamsDetailsComponent>,
    public dialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private sanitized: DomSanitizer
  ) { }

  safeHTML(unsafe: string) {
    return this.sanitized.bypassSecurityTrustHtml(unsafe);
  }

  ngOnInit(): void {
    this.complaintSource = JSON.parse(window.localStorage.getItem('complaintSource'));
    this.userType = JSON.parse(window.localStorage.getItem('userType'));
    this.getComplaintDetails();
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  getComplaintDetails() {
    this.similarCompLoading = true;
    this.namComplaintId = this.data.ID;
    this.nams = this.data.nams;
    if (this.nams === 'tams') {
      this.namsService.getDetails(this.data.ID).subscribe((res: any) => {
        this.loadingDetails = false;
        this.namsTamsDetails = res.data;
        this.complaintTypeId = res.data.COMPLAINT_TYPE_ID;
        this.namsService.updateNamsComplaintDate(res.data.CREATED_DATE);
        this.compSourceId = this.namsTamsDetails['COMPLAINT_SOURCE_ID'];
        this.id = this.namsTamsDetails['ID'];
        if (this.namsTamsDetails['TRANSLATION_HYPERLINK'] != '') {
          this.translation_link = this.bucketUrl + this.namsTamsDetails['TRANSLATION_HYPERLINK'];
        }
        this.getClassificationTag(this.namsTamsDetails['COMPLAINT'], this.namsTamsDetails['CREATIVE_HYPERLINK'], this.namsTamsDetails['BRAND'],
          this.namsTamsDetails['PRODUCT'], this.namsTamsDetails['COMPANY_NAME'], this.namsTamsDetails['COMPLAINT_SOURCE_ID'], this.namsTamsDetails['ID'],
          this.namsTamsDetails['CREATIVE_ID']);
        this.detail_link = this.bucketUrl + this.namsTamsDetails['CREATIVE_HYPERLINK'];
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      });
    } else if (this.nams === 'reech') {
      this.namsService.getReechDetails(this.data.ID).subscribe((res: any) => {
        this.loadingDetails = false;
        this.namsTamsDetails = res.data;
        this.complaintTypeId = res.data.COMPLAINT_TYPE_ID;
        this.namsService.updateNamsComplaintDate(res.data.CREATED_DATE);
        this.compSourceId = this.namsTamsDetails['COMPLAINT_SOURCE_ID'];
        this.id = this.namsTamsDetails['ID'];
        this.getClassificationTag(this.namsTamsDetails['PUBLICATION_CONTENT'], this.namsTamsDetails['CREATIVE_HYPERLINK'], this.namsTamsDetails['BRAND'],
          this.namsTamsDetails['PRODUCT'], this.namsTamsDetails['COMPANY_NAME'], this.namsTamsDetails['COMPLAINT_SOURCE_ID'], this.namsTamsDetails['ID'],
          this.namsTamsDetails['CREATIVE_ID']);
        this.detail_link = this.bucketUrl + this.namsTamsDetails['CREATIVE_HYPERLINK'];
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      });
    }
  }

  shortList() {
    this.namComplaintId = this.data.ID;
    this.nams = this.data.nams;
    this.idArray.push(this.namComplaintId.toString());
    this.confirmationMsg.title = 'Are you sure you want to shortlist the complaint ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.idArray, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        if (this.nams === 'tams') {
          this.namsService
            .shortlistNamsComplaint(dialogResult.id)
            .subscribe(res => {
              if (res) {
                this.notify.showNotification(
                  res.message,
                  "top",
                  (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                  res.status
                )
                this.namsTams.getComplaintById(dialogResult.id);
                this.dialogRef.close('tams-refresh');
              }
            }, err => {
              this.notify.showNotification(
                err.error.message,
                "top",
                (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                err.error.status
              )
            });
        } else if (this.nams === 'reech') {
          this.namsService
            .shortlistReechComplaint(dialogResult.id)
            .subscribe(res => {
              if (res) {
                this.notify.showNotification(
                  res.message,
                  "top",
                  (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                  res.status
                )
                this.namsReech.getComplaintById(dialogResult.id);
                this.dialogRef.close('reech-refresh');
              }
            }, err => {
              this.notify.showNotification(
                err.error.message,
                "top",
                (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                err.error.status
              )
            });
        }
      }
    });
  }

  reject() {
    this.namComplaintId = this.data.ID;
    this.nams = this.data.nams;
    this.idArray = [];
    this.idArray.push(this.namComplaintId.toString());
    this.confirmationMsg.title = 'Are you sure you want to reject the complaint ?';
    if (this.nams === 'tams') {
      const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
        data: { id: this.idArray, title: this.confirmationMsg.title },
        disableClose: true
      });
      dialogRef.afterClosed().subscribe(dialogResult => {
        if (dialogResult && dialogResult.state) {
          this.namsService
            .rejectNamsComplaint(dialogResult.id)
            .subscribe(res => {
              if (res) {
                this.notify.showNotification(
                  res.message,
                  "top",
                  (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                  res.status
                )
                this.dialogRef.close('delete');
              }
            }, err => {
              this.notify.showNotification(
                err.error.message,
                "top",
                (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                err.error.status
              )
            });
        }
      });
    } else if (this.nams === 'reech') {
      const dialogRef = this.dialog.open(ReechDeleteReasonComponent, {
        data: { id: this.idArray, button: "Reject" },
        disableClose: true
      });
      dialogRef.afterClosed().subscribe(dialogResult => {
        if (dialogResult && dialogResult.state) {
          this.namsService
            .rejectReechComplaint(dialogResult.id, dialogResult.reason)
            .subscribe(res => {
              if (res) {
                this.notify.showNotification(
                  res.message,
                  "top",
                  (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                  res.status
                )
                this.dialogRef.close('delete');
              }
            }, err => {
              this.notify.showNotification(
                err.error.message,
                "top",
                (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                err.error.status
              )
            });
        }
      });
    }
  }

  getClassificationTag(desc, hyperLink, brand, product, company, compSourceId, id, creativeId) {
    this.cs.getComplaintClassification(desc, compSourceId, id).subscribe((res) => {
      this.classificationTag = res['data']['ml_classification_name'];
      this.getSimilarityCheck(this.classificationTag, desc, hyperLink, brand, product, company, compSourceId, id, creativeId)
    }, (err) => {
      if (err.error.status == 401) {
        window.localStorage.clear();
        this.router.navigate(['auth/login']);
      }
      this.classificationTag = "-";
      this.getSimilarityCheck('', desc, hyperLink, brand, product, company, compSourceId, id, creativeId)
    })
  }

  getSimilarityCheck(classificationTag, desc, hyperLink, brand, product, company, compSourceId, id, creativeId) {
    this.cs.getSimilarityCheck(classificationTag, desc, hyperLink, brand, product, company, compSourceId, id, creativeId).subscribe((res) => {
      this.similarCompLoading = false;
      if (Array.isArray(res.data.classification) && res.data.classification[1] != undefined) {
        this.msgvalue = res.data.classification[1];
        this.complaint_attachment_data = res.data.classification[0].complaint_attachment_data;
        this.similarityComplaintList = JSON.parse(JSON.stringify(res.data.classification));
        this.similarityComplaintList.shift();
        if (Array.isArray(res.data.classification) && res.data.classification.length > 2) {
          this.extraSimilarComplaints = res.data.classification.length - 2;
        }
        if (this.msgvalue['complaint_identification'] === "No Match found") {
          this.matchFound = false;
        } else {
          this.matchFound = true;
        }
      }
    }, (err) => {
      this.similarCompLoading = false;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  previewLink(source) {
    if (source.indexOf("https") == -1) {
      window.open("https://" + source, "_blank");
    } else {
      window.open(source, "_blank");
    }
  }

  openNewTab() {
    const dialogRef = this.dialog.open(ViewMediaFileComponent, {
      width: '476px',
      height: 'auto',
      data: { adMedium: this.adMedium, adDocs: this.adDocs },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(
      data => {
      }
    );
  }

  addNewComplaint(similarComp) {
    this.loading = true;
    this.cs.addNewComplaint(similarComp, this.complaint_attachment_data, this.compSourceId, this.id).subscribe((res) => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      this.similarCompLoading = true;
      this.dialogRef.close('add');
      window.location.reload();
    }, (err) => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  createComp() {
    // this.cs.createBlankComplaint(this.complaintTypeId, this.userInfo.userId, this.userInfo.userId, this.is_FTC).subscribe(res => {
    //   this.blankComplaintID = res.data.COMPLAINT_ID;
    //   this.cs.updateBlankComplaintId(this.blankComplaintID);
    this.cs.updateNamsComplaintId = this.namComplaintId;
    if(this.reech_tams_checked){
      this.nams = "tams-reech";
    }
    this.cs.updateStep(this.nams);
    this.router.navigate(['/cases/manage-cases'], { state: { from: "NAMS" } });
    this.dialogRef.close('add');
    // }, err => {
    //   this.notify.showNotification(
    //     err.error.message,
    //     "top",
    //     (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
    //     err.error.status
    //   )
    // })
  }

  deleteComp() {
    this.idArray.push(this.namComplaintId.toString());
    if (this.nams === 'tams') {
      this.confirmationMsg.title = 'Are you sure you want to delete the complaint ?';
      const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
        data: { id: this.idArray, title: this.confirmationMsg.title },
        disableClose: true
      });
      dialogRef.afterClosed().subscribe(dialogResult => {
        if (dialogResult && dialogResult.state) {
          this.namsService
            .deleteNamsComplaint(dialogResult.id)
            .subscribe(res => {
              if (res) {
                this.notify.showNotification(
                  res.message,
                  "top",
                  (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                  res.status
                )
                this.dialogRef.close('delete');
              }
            }, err => {
              this.notify.showNotification(
                err.error.message,
                "top",
                (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                err.error.status
              )
            });
        }
      });
    }
    else if (this.nams === 'reech') {
      const dialogRef = this.dialog.open(ReechDeleteReasonComponent, {
        data: { id: this.idArray, button: "Delete" },
        disableClose: true
      });
      dialogRef.afterClosed().subscribe(dialogResult => {
        if (dialogResult && dialogResult.state) {
          this.namsService
            .deleteReechComplaint(dialogResult.id, dialogResult.reason)
            .subscribe(res => {
              if (res) {
                this.notify.showNotification(
                  res.message,
                  "top",
                  (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                  res.status
                )
                this.dialogRef.close('delete');
              }
            }, err => {
              this.notify.showNotification(
                err.error.message,
                "top",
                (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                err.error.status
              )
            });
        }
      })
    }
  }

  addCompany(row) {
    let name = 'nams_tams';
    if (this.nams === 'reech') {
      name = 'nams_reech';
    }
    const dialogRef = this.dialog.open(AddEditCompanyComponent, {
      width: '610px',
      data: { row, name: name },
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe((data) => {
      if (data === 'refresh') {
        this.getComplaintDetails();
      }
    });
  }

  showLessDetails(case_id) {
    this.viewMoreId = case_id;
    this.hiddenMore = !this.hiddenMore;
  }

  async showMoreDetails(case_id) {
    this.viewMoreId = case_id;
    await this.similarComplaintDetails(case_id);
  }

  async similarComplaintDetails(case_id) {
    this.parent_id = "";
    this.noOfDocs = 0;
    this.cs.getSimilarComplaints(this.parent_id, case_id).subscribe(res => {
      this.compDetails = res.data;
      if (this.compDetails.length == 0) {
        this.hiddenMore = true;
        this.notify.showNotification(
          res.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status] : "warning"),
          res.status
        )
      }
      if (this.compDetails.length != 0) {
        this.hiddenMore = !this.hiddenMore;
        this.complaint_id = this.compDetails.ID;
        this.detail_company = this.compDetails.BRAND_NAME;
        this.complaint_source_id = this.compDetails.COMPLAINT_SOURCE_ID;
        this.seen_date = this.compDetails.DATE;
        this.detail_advert = this.compDetails.ADVERTISEMENT_DESCRIPTION;
        this.adDocs = this.compDetails.ADVERTISEMENT_MEDIUM_DOCUMENT;
        this.adMedium = res.data.ADVERTISEMENT_MEDIUM;
        this.comp_date = this.compDetails.REGISTERED_DATE;
        this.detail_date = this.compDetails.CREATED_DATE;
        for (let i = 0; i < this.compDetails.ADVERTISEMENT_MEDIUM_DOCUMENT.length; i++) {
          if (this.compDetails.ADVERTISEMENT_MEDIUM_DOCUMENT[i].ATTACHMENT_SOURCE !== '') {
            this.noOfDocs += 1;
          }
        }
        if (this.detail_advert.length > 10) {
          this.longText1 = ' ...'
        }
        this.detail_complaint = this.compDetails.COMPLAINT_DESCRIPTION;
        if (this.compDetails.ADVERTISEMENT_MEDIUM.length != 0) {
          this.detail_adsource = this.compDetails.ADVERTISEMENT_MEDIUM;
          this.detail_platform = this.compDetails.ADVERTISEMENT_MEDIUM[0].PLATFORM_ID;
          this.detail_channel = this.compDetails.ADVERTISEMENT_MEDIUM[0].SOURCE_NAME;
          this.detail_addate = this.compDetails.ADVERTISEMENT_MEDIUM[0].DATE;
          this.complaintClaims = res.data.CLAIMS;
          this.complaintCodeViolated = res.data.CODEVIOLATED;
          this.guidelines = res.data.GUIDELINES;
          this.similar_detail_link = this.compDetails.ADVERTISEMENT_MEDIUM[0].SOURCE_URL;
          if (this.similar_detail_link) {
            if (this.similar_detail_link.length > 28) {
              this.longText4 = '..';
            } else {
              this.longText4 = ' ';
            }
          }
          this.docUrl = this.bucketUrl + this.similar_detail_link;
          this.detail_place = this.compDetails.ADVERTISEMENT_MEDIUM[0].SOURCE_PLACE;
        }
        for (let i = 0; i <= this.complaintCodeViolated.length - 1; i++) {
          if (this.complaintCodeViolated[i].CLAUSES_ID != null) {
            this.complaintCodeViolated[i].CLAUSES_ID = ((this.complaintCodeViolated[i].CLAUSES_ID).replace(/"/g, '')).slice(1, -1);
          }
        }
        for (let i = 0; i <= this.guidelines.length - 1; i++) {
          if (this.guidelines[i].G_CLAUSES_ID != null) {
            this.guidelines[i].G_CLAUSES_ID = ((this.guidelines[i].G_CLAUSES_ID).replace(/"/g, '')).slice(1, -1);
          }
        }
        if (this.compDetails.COMPLAINT_SOURCE_ID == 7) {
          this.media_outlet = this.compDetails.MEDIA_OUTLET;
          this.media = this.compDetails.MEDIA;
          this.edition = this.compDetails.EDITION;
          this.super_category = this.compDetails.PRODUCT_CATEGORY;
          this.ad_language = this.compDetails.AD_LANGUAGE;
          this.suppliment = this.compDetails.SUPPLIMENT;
          this.creative_id = this.compDetails.CREATIVE_ID;
          this.duration = this.compDetails.DURATION;
          this.transcription = this.compDetails.TRANSCRIPTION;
          this.translation_hyper = this.compDetails.TRANSLATION_HYPERLINK;
          if (this.translation_hyper) {
            if (this.translation_hyper.length > 28) {
              this.longText5 = '..';
            } else {
              this.longText5 = '..';
            }
          }
        }
        if (this.compDetails.COMPLAINT_SOURCE_ID == 8) {
          this.influencer_name = this.compDetails.INFLUENCER_NAME;
          this.engagements = this.compDetails.ENGAGEMENTS;
          this.publication_url = this.compDetails.PUBLICATION_URL;
          this.profile_url = this.compDetails.PROFILE_URL;
          this.influencer_contact = this.compDetails.INFLUENCER_MOBILE;
          this.influencer_email = this.compDetails.INFLUENCER_EMAIL
        }
        if (this.compDetails.COMPANY_INFO.length != 0) {
          this.company_details = this.compDetails.COMPANY_INFO;
        }
        this.getDocuments(this.complaint_id);
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  download(doc, source) {
    this.url = this.bucketUrl + source;
    this.cs.downloadFile(this.url).subscribe(data => {
      importedSaveAs(data, doc)
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  preview(source) {
    this.imgURL = this.bucketUrl + source;
    window.open(this.imgURL, 'window 1', '');
  }

  getDocuments(id) {
    this.cs.getDocumentsByComplaint(id).subscribe(res => {
      this.docFileList = res.data;
      this.complainantFiles = this.docFileList['COMPLAINANT'];
      this.advertiserFiles = this.docFileList['ADVERTISER'];
      this.internalFiles = this.docFileList['INTERNAL'];
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  viewDocument() {
    const dialogRef = this.dialog.open(ViewDocumentComponent, {
      width: '525px',
      height: '472px',
      data: this.complaint_id,
      disableClose: true
    });
  }

  onValChange(event){
    if(event.checked){
      this.reech_tams_checked = true;
    }else{
      this.reech_tams_checked = false;
    }
  }

}