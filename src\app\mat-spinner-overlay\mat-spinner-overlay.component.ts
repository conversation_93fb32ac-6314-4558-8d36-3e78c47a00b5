import { Component, Input, OnInit } from '@angular/core';
import { trigger, transition, style, animate, state } from '@angular/animations';

@Component({
  selector: 'app-mat-spinner-overlay',
  templateUrl: './mat-spinner-overlay.component.html',
  styleUrls: ['./mat-spinner-overlay.component.css'],
  animations: [
    trigger('loaderAnimation', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate(300, style({ opacity: 1 }))
      ]),
      transition(':leave', [  
        animate(300, style({ opacity: 0 }))
      ])
    ]),
  ]
})
export class MatSpinnerOverlayComponent implements OnInit {

  constructor() { }

  @Input() value : number = 100;
  @Input() diameter: number = 80;
  @Input() mode : string ="indeterminate";
  @Input() strokeWidth : number = 8;
  @Input() overlay: boolean = false;
  @Input() color: string = "primary";

  ngOnInit(): void {
  }

}
