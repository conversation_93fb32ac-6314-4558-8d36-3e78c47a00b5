:host ::ng-deep .mat-form-field-flex>.mat-form-field-infix {
  padding: 0.5em 0px !important;
  position: relative;
  bottom: 4px;
}

:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-label {
  margin-top: -5px;
  margin-bottom: -5px;
}

:host:ng-deep label.ng-star-inserted {
  transform: translateY(-0.59375em) scale(.75) !important;
}

.mat-tab-label,
.mat-tab-link {
  color: rgba(0, 0, 0, .87);
  min-width: 50px !important;
}

::ng-deep .detail-subtab.mat-tab-header,
.mat-tab-nav-bar {
  border-bottom: 0;
}

.mat-tab {
  min-width: 50px !important;
  height: 300px;
}

:host ::ng-deep .detail-subtab .mat-tab-label,
.mat-tab-link {
  /* color: rgba(0,0,0,.87);
  min-width: 15px !important; */
  width: 18%;
  border-width: 1px 1px 0 1px;
  border-style: solid;
  border-color: #D8DCDE;
  border-radius: 3px 3px 0px 0px;
  font-weight: 600 !important;
  font-size: 12px;
  line-height: 16px;
  color: #000000;
  opacity: 1 !important;
  background-color: rgba(238, 238, 238, 0.568) !important;
}

:host ::ng-deep .detail-subtab .mat-tab-label {
  height: 50px !important;
}

.count-badge {
  position: absolute;
  bottom: 27px;
  right: 0px;
  border: 1px solid #ffffff;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;
  font-size: 10px;
  background: #0088cb;
}

:host ::ng-deep .detail-subtab .mat-tab-label-active {
  background-color: #0088CB !important;
  opacity: 1 !important;
  color: #ffffff;
  font-weight: 600 !important;
}

:host ::ng-deep .detail-subtab .mat-ink-bar {
  display: none !important;
}

:host ::ng-deep .detail-subtab .mat-tab-label .mat-tab-label-content {
  font-weight: 600;
}

.detail-subtab {
  /* height: 100%; */
  padding-left: 1%;
  padding-top: 2%;
}

.header {
  background-color: #F8F9F9;
  display: flex;
  width: 100%;
  /* flex-direction: row; */
  /* gap:10px; */
}

.conversation-container {
  margin-top: 13px;
}

.dialog-class .mat-dialog-container {
  position: relative;
  left: 150px;
}

.status-container {
  background: rgba(18, 131, 188, 0.2);
  width: auto;
  min-width: max-content !important;
  height: auto;
  border: 1px solid #D8DCDE;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  /* padding: 20px 10px 0px 10px; */
  padding: 10px 15px 30px 10px;
  margin: 10px 5px;
  margin-right: 2%;
}

.status-onhold {
  border: 0.5px solid #ED2F45;
  background: rgba(237, 47, 69, 0.17) !important;
}

.status-new {
  border: 0.5px solid #0088CB;
  background: #DDE9FF !important;
}

.status-inprogress {
  border: 0.5px solid #F89E1B;
  background: rgba(248, 158, 27, 0.17) !important;
}

.status-resolution {
  border: 0.5px solid #04A585;
  background: rgba(4, 165, 133, 0.17) !important;
}

.status-out {
  border: 0.5px solid #000000;
  background: rgba(0, 0, 0, 0.17) !important;
}

.status-invalid {
  border: 0.5px solid #000000;
  background: rgba(0, 0, 0, 0.17) !important;
}

.status-closed {
  border: 0.5px solid #000000;
  background: rgba(0, 0, 0, 0.17) !important;
}

.text-onhold {
  color: #ED2F45 !important;
}

.text-new {
  color: #0088CB !important;
}

.text-inprogress {
  color: #F89E1B !important;
}

.text-resolution {
  color: #04A585 !important;
}

.text-out {
  color: #000000 !important;
}

.text-invalid {
  color: #000000 !important;
}

.text-closed {
  color: #000000 !important;
}

.status-chip {
  width: auto;
  min-width: max-content !important;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 3px !important;
}

.comp-status {
  /* height: auto !important; */
  min-height: 22px !important;
  width: auto;
  background-color: #ffffff;
  font-size: smaller;
  border-width: 1px;
  border-style: solid;
  border-radius: 15px;
  padding: 0px 7px 0px 7px;
}

.media-anchor {
  color: #0088CB;
  word-break: break-all;
}

.Heading-container {
  display: flex;
  flex-direction: column;
  gap: 7px;
  margin-left: 6px;
  /* width: 50% !important; */
  height: 60px;
  max-height: 60px;
  line-height: 3px;
}

::ng-deep .mat-tooltip {
  color: white !important;
  background-color: #2c2b2b;
  font-size: small;
  border: 1px solid #ccc;
  /* border-radius: 30px; */
  /* box-shadow: 2px #ccc ; */
}

.form::-webkit-scrollbar {
  display: none;
}

.form {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.form {
  width: 100%;
  background-color: #F8F9F9;
  padding-top: 6%;
  height: 62vh;
  overflow-y: scroll;
  overflow-x: hidden;
}

/* .flex-container {
  width: 26%;
  display: flex;
} */

.calendar-container {
  position: relative;
  top: 10px;
  /* right: 250px; */
}

.recommendation-container {
  position: relative;
  top: 10px;
}

.delete-container {
  position: relative;
  top: 10px;
  /* right: 200px; */
}

.float-btn {
  background-color: rgb(255, 255, 255);
  border: 1px solid rgb(223, 223, 223);
  border-radius: 15px;
}

.save-btn-container {
  position: relative;
  top: 12px;
  /* right:10px; */
}

.submit-btn {
  /* .arrow-btn{ */
  border-color: #0088CB;
  background-color: white;
  color: #0088CB;
  width: 140px;
  padding-left: 6%;
  /* background-color: rgb(41, 41, 85); */
  font-size: 12px;
  border-radius: 15px;
}

/* .submit-btn{
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
} */
.arrow-btn {
  /*width:auto;*/
  height: 38px;
  padding-left: 0px;
  margin-left: 2px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}

.arrow-icon {
  width: auto;
  padding-left: 2px;
  padding-right: 2px;
}

.submit-container {
  /* height: 120px; */
  width: 185px;
}

.submit-option {
  height: 40px;
}

#submit-icon {
  padding-right: 0px !important;
  margin-right: 0px !important;
}

.submit-text {
  font-size: small;
  line-height: 30px;
}

.submit-span {
  font-weight: 500;
}

/********************   ASSIGNMENT  ********************************/
/* Hide scrollbar for Chrome, Safari and Opera */
.assignment-container::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.assignment-container {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

/* .assignment-container{
height: 340px;
margin: 10px 0px;
overflow-y: scroll;
overflow-x: hidden;
} */

.assignment-container {
  /* background-color: rgba(230, 230, 230, 0.568); */
  height: 100%;
  /* width: 300px; */
  /* padding-top: 20px; */
  overflow-y: scroll;
  overflow-x: hidden;
  /* position: static; */
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 0px;
  margin: 0px 20px;
}

.text-container {
  height: 20px;
  margin-bottom: 0px;
  /* margin: 0px 10px ;  */
}

.control-container {
  margin-top: 0px;
}

.input-field {
  /* background-color: rgb(255, 255, 255); */
  /* width:225px ; */
  width: 100%;
  /* background: #FAFAFA;
    height: 38px;
    margin-top: 2px; */
  /* height: auto; */
}

/* .input-control{
background-color: rgb(255, 255, 255);
} */
/*************************************  DETAIL CONTAINER  **********************************************/
/* .detail-container::-webkit-scrollbar {
display: none;
}
.detail-container {
-ms-overflow-style: none;
scrollbar-width: none;
} */
.detail-container {
  border: 1px dashed rgb(175, 112, 30);
  width: 100%;
  /* height: 100%; */
  /* position: fixed; */
  /* position: absolute;
  top: 70px;
  left: 300px; */

  /* top: 70px; */
  /* left: 300px; */
  /* margin-top: 1%;
  margin-left: 1%; */
  overflow-y: scroll;
  overflow-x: hidden;
  /* position: static; */
}

.comp-detail-container::-webkit-scrollbar {
  display: none;
}

.comp-detail-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* .vertical-form-container::-webkit-scrollbar {
  display: none;
  }
  .vertical-form-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
  } */
.vertical-form {
  height: 87vh;
  width: 100%;
  /* overflow-y: scroll;
  overflow-x: hidden; */
}

.comp-detail-container {
  /* margin-left: 2%; */
  /* width: 65% !important; */
  width: 73%;
  height: 81vh;
  overflow: scroll;
  /* overflow-y: scroll;
  overflow-x: hidden; */
  margin-left: 0px !important;
  /* padding-left: 10px; */
}

/* .comp-fxrow-container {
  height: 100%;
} */

.comp-attribute-container {
  /* margin-top: 1%; */
  margin-top: 15px;
  /* width: 100%; */
  height: auto;
  margin-left: 10px;
  margin-bottom: 15px;
}

.detail-attribute {
  color: #92A2B1;
  line-height: 1px;
  margin-top: 20px;
}

.comp-msg-container {
  height: auto;
  width: 90%;
  word-wrap: break-word;
  word-break: normal;
}

::-webkit-scrollbar {
  display: none;
}

.comp-msg {
  word-break: break-word;
  line-height: 16px;
  /* overflow: auto;
  text-overflow: ellipsis;
  display: -webkit-box;
  max-height: 32px;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  cursor: pointer; */
}

.option-btn {
  line-height: 25%;
  height: 550%;
}

.option-text {
  line-height: 200%;
  padding-left: 8px;
}

.chart-row-container {
  border: 1px solid rgb(192, 192, 192);
  border-radius: 3px;
}

.chart-icon-container>span {
  border: 1px solid rgb(192, 192, 192);
  color: rgb(48, 48, 48);
  font-size: medium;
  border-radius: 3px;
}

.content-head-container {
  width: 100%;
  height: 100%;
  margin-top: 3px;
}

:host ::ng-deep .content-head-container .mat-tab-label,
.mat-tab-link {
  border-width: 1px 1px 1px 1px;
  border-style: solid;
  border-color: #D8DCDE;
  border-radius: 50px;
  font-weight: 600 !important;
  font-size: 12px;
  line-height: 16px;
  color: #000000;
  opacity: 1 !important;
  background-color: rgba(238, 238, 238, 0.568) !important;
}

:host ::ng-deep .content-head-container .mat-tab-label-active {
  background-color: #0088CB !important;
  opacity: 1 !important;
  color: #ffffff;
  font-weight: 600 !important;
}

:host ::ng-deep .content-head-container .mat-ink-bar {
  display: none !important;
}

:host ::ng-deep .content-head-container .mat-tab-label .mat-tab-label-content {
  font-weight: 600;
}

:host ::ng-deep .content-head-container .mat-tab-header .mat-tab-label {
  height: 43px !important;
  margin-right: 5px;
  margin-bottom: 3px;
}

::ng-deep .content-head-container .mat-tab-header,
.mat-tab-nav-bar {
  border-bottom: 1;
}

.doc-head {
  margin-top: 10px;
  margin-left: 5px;
  /* margin-right: 40%; */
}

/******************** COMPLAINT TAB ***************************/
:host ::ng-deep .detail-subtab-group .mat-ink-bar {
  display: none !important;
}

/*  REMOVE MAT-TAB INK BAR */
/* :host ::ng-deep .mat-ink-bar {
display: none !important;
} */
.status-tab-container {
  margin: 20px 10px;
}

mat-card {
  border: 1px solid rgb(197, 197, 197);
}

mat-card-content {
  margin: 5px 5px
}

.card-subtitle {
  font-weight: 500;
  /* margin: 0px 0px 10px 0px;
padding-left: 0px; */
}

.black-text {
  color: #000000;
  font-weight: 500;
  /* margin: 10px 0px; */
}

.blue-text {
  color: rgb(0, 162, 255);
}

.blue-light-text {
  color: rgb(0, 162, 255);
}

.table-container {
  margin: 5px;
}

.mat-row .mat-cell {
  border-bottom: 1px solid transparent;
  border-top: 1px solid transparent;
  cursor: pointer;
}

.due-icon {
  color: gray;
  font-size: large;
}

.action-buttons {
  border: 1px solid gainsboro;
  position: relative;
  bottom: 17px;
  left: 122px;
  background: white;
  width: 104px;
  height: 64px;
  padding-left: 13px;
  padding-top: 10px;
  z-index: 1;
}

.action-buttons-internal {
  border: 1px solid gainsboro;
  position: relative;
  bottom: 32px;
  left: 122px;
  background: white;
  width: 112px;
  height: 81px;
  padding-left: 19px;
  padding-top: 9px;
}

/***************************************** DOC FILE UPLOAD ************************************************/
.doc-container {
  width: 100%;
  /* padding-left: 2%; */
}

.doc-fxrow-container {
  width: 100%;
  margin-top: 2%;
}

.uploaded-file-container {
  border: 1px solid #aaa;
  /* height: calc(25% - 6px); */
  height: 150px;
  width: 160px;
  border-radius: 5px;
  margin-top: 13px;
  margin-left: 5%;
}

.doc-icon-container {
  background-color: #3a3a3a;
  height: 86px;
  width: 176px;
  border-radius: 5px;
}

.panel-body {
  background: #FFFFFF;
  border: 1px solid #CFD7DF;
  box-sizing: border-box;
  border-radius: 4px;
  height: 28px;
  margin-bottom: 4px;
}

.attribute-container1 {
  font-weight: 600;
  font-size: 12px;
  line-height: 16px;
  height: 20px;
  padding: 2px 0px;
  width: 236px;
}

.attribute-container2 {
  font-weight: 600;
  font-size: 12px;
  line-height: 16px;
  height: 20px;
  padding: 3px 0px;
}

.doc-icon-container>span {
  color: rgb(168, 168, 168);
}

.doc-caption {
  background-color: rgb(255, 255, 255);
  height: fit-content;
  padding: 13px;
}

.doc-icon-container {
  display: flex;
  justify-content: center;
}

.doc-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.mat-card-doc {
  padding: 0px;
  width: fit-content;
  height: fit-content;
}

.doc-caption>p {
  height: 18px;
  width: 94px;
  padding: 0;
  overflow: hidden;
  position: relative;
  display: inline-block;
  margin: 0 5px 0 5px;
  text-align: center;
  font-size: 12px;
  text-decoration: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #000;
  font-weight: 700;
}

.ellipsis {
  overflow: hidden;
  display: inline-block;
  display: -webkit-inline-box;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  white-space: normal;
  -webkit-box-orient: vertical;
  vertical-align: middle;
  position: relative;
  height: auto !important;
}

.ellipsis1 {
  overflow: hidden;
  display: inline-block;
  display: -webkit-inline-box;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  vertical-align: middle;
  position: relative;
  width: 60px;
  height: 15px;
}

.ellipsis2 {
  overflow: hidden;
  display: inline-block;
  display: -webkit-inline-box;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  vertical-align: middle;
  position: relative;
  width: 200px;
  margin-top: 10px;
}

:host ::ng-deep ul {
  margin-bottom: 0px !important;
}

:host ::ng-deep h1 {
  margin-bottom: 0px !important;
}

:host ::ng-deep h2 {
  margin-bottom: 0px !important;
}

:host ::ng-deep h3 {
  margin-bottom: 0px !important;
}

:host ::ng-deep h4 {
  margin-bottom: 0px !important;
}

:host ::ng-deep .ellipsis,
.ellipsis1,
.ellipsis2 p {
  margin-bottom: 0px !important;
}

.dropzone {
  /* width: 160px; */
  height: 120px;
  /* height: 200px; */
  display: table;
  width: 120px;
  border: 1px dashed #aaa;
  border-radius: 15px;
  margin-left: 10px;
  margin-top: 5px;
}

.addfile-text-wrapper {
  /* width: 200px; */
  width: calc(30% - 6px);
  height: 126px;
  display: table-cell;
  vertical-align: middle;
}

.upload-scope-container {
  height: max-content;
  width: 100%;
  text-align: center;
}

input[type="file"] {
  display: none;
}

.upload-label {
  color: rgb(170, 170, 170);
}

.upload-label>span {
  font-size: small;
  font-weight: normal;
  padding-top: 5px;
}

.fileItem {
  width: calc(25% - 6px);
  overflow: hidden;
  height: fit-content;
  margin: 3px;
  padding: 10px;
  display: block;
  position: relative;
  float: left;
  border: 2px dashed #778899;
  border-radius: 5px;
  transition: .3s ease;
}

.fileItem:hover {
  border: 2px solid #c33838;
  background-color: #c33838bf;
}

.fileItem:hover .fileItemIcon::before {
  content: "\f00d";
  color: whitesmoke;
}

.fileItem:hover .fileItemText {
  color: whitesmoke;
}

.fileItemIconDiv {
  text-align: center;
}

.fileItemIcon::before {
  font-style: normal;
  content: "\f15b";
  color: #778899;
  font-family: "Font Awesome 5 Free" !important;
  font-weight: 900;
}

.fileItemText {
  text-align: center;
  margin-top: 10px;
  height: 40px;
}

.tasks-container {
  width: 100%;
  /* height: 100%; */
}

.task-table::-webkit-scrollbar {
  display: none;
}

.task-table {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.task-table {
  margin: 5px 3px 5px 2px;
  border: 1px solid gainsboro;
  width: 99%;
  /* height: 96%; */
  height: 270px;
  overflow-y: scroll;
  overflow-x: hidden;
  border-radius: 5px;
}

:host ::ng-deep .form .mat-select-arrow {
  position: relative;
  top: 5px;
}

.prior {
  background-color: #FFFFFF;
  border-radius: 2px;
  height: 33px;
  font-size: 10px;
  margin-bottom: 15px;
  padding-right: 15%;
}

/* :host ::ng-deep .prior .mat-select-arrow{
  color: #ea2d2d00;
} */

/* :host ::ng-deep .high_arrow .mat-select-arrow{
  color: #EA2D2D;
}
:host ::ng-deep .med_arrow .mat-select-arrow{
  color: #F89E1B;
}
:host ::ng-deep .low_arrow .mat-select-arrow{
  color: #2ED9FF;
} */

.high_prior {
  border: 1.75px solid #EA2D2D;
}

.med_prior {
  border: 1.75px solid #F89E1B;
}

.low_prior {
  border: 1.75px solid #2ED9FF;
}

.icon_color1 {
  color: #EA2D2D;
}

.icon_color2 {
  color: #F89E1B;
}

.icon_color3 {
  color: #2ED9FF;
}

.comp-tab-container::-webkit-scrollbar {
  display: none;
}

.comp-tab-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.comp-tab-container {
  height: 67%;
  width: 99%;
  overflow-y: scroll;
  overflow-x: hidden;
  /* position: relative;
  bottom: 143px; */
}

.mat-column-task {
  /* padding-left: 3%; */
  font-size: 13px;
}

.mat-column-priority {
  font-size: 13px;
}

.mat-column-duedate {
  font-size: 13px;
  position: relative;
  bottom: 2px;
}

.mat-header-cell {
  /* margin-left: 4%; */
  /* width: 100%; */
  background-color: rgb(245, 245, 245);
  font-weight: 400;
  font-size: 12px;
  color: #000000;
}

.mat-header-row {
  height: 40px;
  margin-bottom: 1%;
}

.mat-footer-row {
  height: 40px;
}

th.mat-header-cell:last-of-type,
td.mat-cell:last-of-type,
td.mat-footer-cell:last-of-type {
  padding-right: 0px;
}

.example-second-footer-row {
  background-color: rgb(245, 245, 245);
}

.select {
  cursor: default;
  width: 80px;
  height: 30px;
  border: 1px;
  font-size: 11px;
  border-radius: 3px;
  padding: 0px 0px 0px 7px;
}

.todo {
  color: rgba(212, 54, 54, 0.945);
  background-color: lightgray;
  padding-top: -20px;
  font-size: 11px;
  font-style: normal;
}

.act {
  color: rgba(5, 81, 124, 0.945);
  background-color: rgba(119, 233, 218, 0.527);
  /* background-color: rgb(125, 154, 233); */
  font-size: 10px;
  font-style: normal;
}

.done {
  background-color: rgb(177, 243, 177);
  color: rgb(192, 235, 179);
  font-size: 11px;
  font-style: normal;
}

.priority-btn {
  cursor: default;
  width: 92px;
  height: 30px;
  border: 1px solid;
  border-radius: 3px;
  padding: 0px 0px 0px 6px;
}

.stat {
  margin-top: -10%;
  /* margin-right: 15%; */
}

.circle {
  font-size: 10px;
  /* margin-right: -10%; */
}

.rej {
  color: red;
}

.add-btn {
  /* background-color: rgb(209, 191, 191); */
  color: #0088CB;
}

.high {
  color: red;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.vertical-line {
  width: 1px;
  background-color: rgb(63, 186, 243);
}

.link-text {
  color: rgb(82, 193, 245);
  cursor: pointer;
}

.date::placeholder {
  color: #000000;
}

.update-btn {
  color: white;
  background-color: #0088CB;
  border-radius: 12px;
  font-style: normal;
  font-weight: 600 !important;
  font-size: 14px;
  justify-content: center;
  align-items: center;
  padding: 0px 10px 0px 5px;
  margin-bottom: 20px;
  margin-left: 20px;
}

.update-btn[disabled] {
  opacity: 0.4;
  color: white;
  background-color: #0088CB;
  border-radius: 12px;
  font-style: normal;
  font-weight: 600 !important;
  font-size: 14px;
  justify-content: center;
  align-items: center;
  padding: 0px 10px 0px 5px;
  margin-bottom: 20px;
  margin-left: 20px;
}

.text-message {
  margin-left: 5%;
}

.claims-subhead-container {
  padding: 10px 5px;
}

.claims-subhead {
  color: #2F3941;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 19px;
}

.intra-expansion {
  border: 1px solid #D8DCDE;
  padding: 0% 2% 0% 2%;
}

.panel-title {
  color: #000000;
  font-weight: 600;
  font-size: 14px;
  line-height: 19px;
}

:host ::ng-deep .panel-header>.mat-expansion-indicator:after {
  color: #0088CB;
}

.head-container {
  padding: 5px 0px;
}

.head-container>div {
  max-height: 20px;
}

.arrow-img-icon {
  vertical-align: super !important;
}

.intra-h3 {
  color: #000000;
  font-weight: normal;
  font-size: 14px;
  line-height: 19px;
}

.challenge-container {
  border: 1px solid #D8DCDE;
  border-radius: 4px;
  padding: 10px;
}

.attribute-container {
  padding: 10px 0px 0px;
}

.grey-text {
  color: #92A2B1;
  font-size: 14px;
  line-height: 19px;
}

.violated-popups {
  color: #0088CB;
  text-align: center;
  width: 100%;
  height: 30px;
  background: #FFFFFF;
  border: 1px solid #0088CB;
  box-sizing: border-box;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  line-height: 19px;
}

.raised-container {
  width: 65%;
}

:host ::ng-deep .vertical-form .mat-tab-label {
  min-width: 11vw !important;
  background-color: #F8F9F9;
  height: 52px;
}

.violated-container {
  width: 37%;
}

.chapters-container {
  padding: 10px;
  overflow-wrap: anywhere;
  padding-top: 0px;
}

.chapter-text {
  color: #000000;
  font-weight: normal;
  font-size: 14px;
  margin-bottom: 11px;
  line-height: 19px;
  width: 192px;
  text-align: -webkit-auto;
}

.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

.width50 {
  width: 50%;
}

.circle-icon {
  font-size: 4px;
  vertical-align: sub;
  height: 16px;
  margin-top: 8px;
  bottom: 6px;
  position: relative;
}

.no-claims {
  color: rgb(160, 160, 156);
  margin-top: 10px;
  margin-left: 10px;
  font-size: 15px;
}

.tab-group {
  height: 91vh;
  overflow-y: hidden !important;
}

.dashboard-complaint-list {
  overflow-y: scroll;
  overflow-x: hidden;
  width: 100%;
  height: 100%;
}

.dashboard-complaint-list::-webkit-scrollbar {
  display: none;
}

.dashboard-complaint-list {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.list-item-container::-webkit-scrollbar {
  display: none;
}

.list-item-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.list-item-container {
  width: 100%;
  height: auto !important;
  padding: 1px 3px 1px 3px;
  /* T R B L */
}

.list-item-container:hover {
  background-color: rgb(235, 235, 235);
  cursor: pointer;
}

.message {
  font-size: 14px;
  color: #000000;
}

.item-head-container {
  padding-top: 10px;
  padding-left: 10px;
  font-weight: 300;
  font-size: 13px;
  width: 280px;
  text-align: justify;
}

.item-icon {
  margin-right: 1%;
  font-size: medium;
  padding-top: 1%;
}

.item-icon1 {
  margin-right: -7%;
  font-size: 15px;
}

:host ::ng-deep .tab-group .mat-tab-list .mat-tab-labels .mat-tab-label-active {
  color: #0088CB !important;
  opacity: 1 !important;
}

:host ::ng-deep .tab-group .mat-tab-list .mat-tab-labels .mat-tab-label-active .mat-tab-label-content .nams-label {
  font-weight: 600;
}

:host ::ng-deep .tab-group.mat-tab-group.mat-tab-group.mat-primary .mat-ink-bar {
  background-color: #0088CB !important;
  height: 3px;
}

.flex {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.tooltip-adv {
  color: #1283BC;
  background: rgba(18, 131, 188, 0.2);
  border-radius: 50%;
  padding: 7px;
  margin-right: 3px;
}

.tooltip-com {
  color: #F89E1B;
  background: rgba(248, 158, 27, 0.2);
  border-radius: 50%;
  padding: 7px;
  margin-right: 3px;
}

.tooltip-int {
  color: #04A585;
  background: rgba(4, 165, 133, 0.2);
  border-radius: 50%;
  padding: 7px 9px 7px 9px;
}

.tooltip-text {
  cursor: pointer;
  display: block;
}

.tooltip-text:hover .tooltip-class {
  visibility: visible;
  opacity: 1;
}

.tooltip-class {
  opacity: 0;
  visibility: hidden;
  position: absolute;
  bottom: 30px;
  right: 70px;
  transition: opacity 0.5s ease;
  background: #ffffff;
  border: 1px solid #D8DCDE;
  border-radius: 12px;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 19px;
  width: fit-content;
  padding: 10px;
  z-index: 1 !important;
}

.error-msg {
  position: relative;
  margin-left: -10px;
}

.timeline-btn-container {
  margin: 0px;
  padding: 0px;
  z-index: 1000 !important;
}

.timeline-btn {
  background: #0088CB;
  border-radius: 4px 4px 0px 0px;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
  color: #FFFFFF;
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  height: 35px;
  line-height: 16px;
  padding: 10px 6px;
  margin: 34px 0 0 0;
  transform: rotate(-90deg);
  width: auto;
  position: absolute;
  right: -15px;
}

.timeline-sidebar {
  position: absolute;
  top: 85px;
  right: 0;
  width: 400px;
  height: 78vh;
  z-index: 10000 !important;
}