:host::ng-deep.mat-paginator-page-size-label {
    display: none;
  }
  :host ::ng-deep .mat-paginator-range-label {
    display: none;
  }  
  :host::ng-deep.mat-paginator-page-size-value {
    display: none;
  }  
  :host::ng-deep .mat-paginator-navigation-previous {
    color:#4DA1FF;
  }
  :host::ng-deep .mat-paginator-navigation-next {
    color:#4DA1FF;
  }
  :host::ng-deep .mat-paginator-range-actions {
    margin-right: 24px;
  }
  .label {
    position: relative;
    bottom: 38px;
    color: rgb(155, 155, 155);
    left: 45%;
  }
  .text-message {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
  .common-toolbar {
    border-bottom: 1px solid #D8DCDE;
    border-top: 0;
    border-left: none;
    border-right: none;
  }
  .heading-container {
    width: 60%;
  }
  .options-container {
    width:40%;
  }
  .toolbar2 {
    background-color: rgb(245, 245, 245);
    padding: 9px 18px;
  }
  .toolbar2 >div {
    margin-left: 0.5%;
  }
  td,th {
    width: 1%;
    min-height: 4%;
    text-overflow: ellipsis;
  }
  .pen {
    color:#4DA1FF;
  }
  .not-ext {
    color:#ED2F45;
  }
  .aprv {
    color: #74D365;
  }
  .slider {
    transform: scale(.8);
    color: #74D365;
  }
  :host ::ng-deep .mat-slide-toggle.mat-checked.mat-disabled .mat-slide-toggle-bar {
    background-color: rgb(144, 212, 144) ;
  }
  :host ::ng-deep  .mat-slide-toggle.mat-checked.mat-disabled .mat-slide-toggle-thumb {
    background-color: rgb(28, 187, 28) ;
  }
  :host ::ng-deep .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar {
    background-color: rgb(144, 212, 144) ;
  }
  :host ::ng-deep  .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb {
    background-color: rgb(28, 187, 28) ;
  }
  mat-slide-toggle {
    transform: rotate(180deg)
  }
  .mat-row .mat-cell {
    height: 40px !important;
    border-bottom: 1px solid transparent;
    border-top: 1px solid transparent;
    cursor: pointer;
  }
  .add-btn {
    color: white;
    background-color: #0088CB;
    border-radius: 12px;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 38px;
    z-index: 10;
  }
  .admin-table::-webkit-scrollbar {
    display: none;
  }
  .admin-table {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .admin-table {
    overflow-y: scroll;
    overflow-x: hidden;
    margin-left: 1%;
    width: 98.5%;
    height: min-content;
    position: relative;
    border-radius: 20px;
    border: 1px solid #D8DCDE;
    border-top: 0px;
  }
  .card::-webkit-scrollbar {
    display: none;
  }
  .card {
    -ms-overflow-style: none;
    scrollbar-width: none; 
  }
  table th {
    position: -webkit-sticky; 
    position: sticky;
    top: 0;
    z-index: 1; 
    background: #fff; 
  }
  .card {
    background-color:rgb(245, 245, 245);
    box-shadow: none;
    border-width: 0px;
    height: fit-content;
    /* max-height: 80.5vh; */
    max-height: 68.0vh;
    position: relative;
    align-self: justify;
    width: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .header-search input[type=text] {
    box-sizing: border-box;
    margin-top: 3%;
    position: relative;
    padding-left: 30px;
    color: #92A2B1;
    height: 38px !important;
    width: 147px !important;
    border: 1px solid #CFD7DF!important;
    border-radius: 4px !important;
    border: 1px solid #CFD7DF!important;  
    border-radius: 12px !important;  
    background: url(../../../assets/images/search.png) no-repeat;
    background-position: 5%;
    background-color: white;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    z-index: 10;
  }
  .header-search ::placeholder {
    color: rgba(0, 0, 0, 0.4);
  }
  .mat-cell {
    font-size: 13px;
  }
  .mat-header-cell {
    font-size: 13px;
    color: #707070;
    font-style: normal;
    height: 35px !important;
  }
  .mat-dialog-container {
    padding: 0px!important;
  }
  mat-paginator {
    width: 96.5%;
    margin-top: 21px;
    margin-left: 2%;
    border-radius: 20px;
    background: #FFFFFF;
    box-sizing: border-box;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  }
  .mat-row:nth-child(2n+2) {
    background-color: #F5F5F7;
  }
  :host ::ng-deep .mat-tab-list .mat-tab-labels .mat-tab-label-active {
    color:#0088CB;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    opacity: 1 !important;
  }
  :host ::ng-deep .mat-tab-group.mat-primary .mat-ink-bar {
    background-color: #0088CB !important;
  }
  :host ::ng-deep .admin-tabs .mat-tab-list .mat-tab-labels .mat-tab-label-active .mat-tab-label-content {
    font-weight: 600;
  }
  .table-scroll::-webkit-scrollbar {
    display: none;
  }
  .table-scroll {
    -ms-overflow-style: none;
    scrollbar-width: none; 
  }
  .table-scroll {
    overflow-y: scroll;
    overflow-x: hidden;
    height: 63vh;
  }
  .toggle-text {
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6); 
    margin-top: -2px;
  }
  .create_company {
    color: #5A6F84;
    background-color: #CFD7DF;
    border: 5px ;
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    border-radius: 12px;
    justify-content: center;
    align-items: center;
    height: 27px;
    width: 110px;
  }
  ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: #aae7c8;
  }
  :host ::ng-deep .mat-checkbox .mat-checkbox-checkmark-path {
    stroke:  #006644 !important;
  }
  @media only screen and (max-width: 1250px) {
    .common-toolbar-mobile {
      border-bottom: 1px solid #D8DCDE;
      border-top: 0;
      border-left: none;
      border-right: none;
    }
    .dashboard-admin-heading {
      padding-left: 0;
      padding-top: 0;
      font-weight:600;
      font-size:18px;
      line-height: 23.94px;
      color: #ED2F45;
      padding-left: 20px;
      padding-top: 6px;
    }
    .dashboard-container {
      width: 100%;
      height: 90%;
      background: #E5E5E5;
    }
    .dasboard-subheading {
      padding-right: 20px;
      padding-top: 0px;
      font-weight:400;
      font-size:14px;
      line-height: 18.62px;
      padding-left: 20px;
      padding-top: 3px;
    }
  }