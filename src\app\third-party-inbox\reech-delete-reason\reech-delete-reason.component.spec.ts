import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ReechDeleteReasonComponent } from './reech-delete-reason.component';

describe('ReechDeleteReasonComponent', () => {
  let component: ReechDeleteReasonComponent;
  let fixture: ComponentFixture<ReechDeleteReasonComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ReechDeleteReasonComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ReechDeleteReasonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
