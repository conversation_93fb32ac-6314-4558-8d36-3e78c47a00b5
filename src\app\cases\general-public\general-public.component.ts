import { Component, EventEmitter, OnInit, Output, HostListener } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { Router } from '@angular/router';
import { debounceTime, distinctUntilChanged, first } from 'rxjs/operators';
import { AuthService } from 'src/app/services/auth.service';
import { ComplaintsService } from 'src/app/services/complaints.service';
import { NotificationService } from 'src/app/services/notification.service';
import { ThirdPartyService } from 'src/app/services/third-party.service';
import { UploadService } from 'src/app/services/upload.service';
import { MY_FORMATS } from 'src/app/shared/calendarFormat';
import { colorObj } from 'src/app/shared/color-object';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';
import moment from 'moment';
import { QuillConfiguration } from 'src/app/model/quill-configuration';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-general-public',
  templateUrl: './general-public.component.html',
  styleUrls: ['./general-public.component.css'],
  providers: [
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
  ],
})
export class GeneralPublicComponent implements OnInit {

  quillConfiguration = QuillConfiguration;
  step2Form: FormGroup;
  selectedAdvId: number;
  platform_id: number;
  userInfo: any;
  ads: any[];
  classification: any[];
  platforms: any[];
  printSources: any[];
  promotionTypes: any[];
  files: any[] = [];
  files_attached = "No";
  @Output() outFilter: EventEmitter<any> = new EventEmitter<any>();
  buttonName: string;
  companyNameControl = new FormControl();
  companyList: any[];
  companyId: number = 0;
  blankComplaintId: number;
  maxDate = new Date();
  backDisable: boolean = false;
  isUploadProgress: boolean = false;
  fileProgress: number = 0;
  fileInfoPath: string = ''
  complainantID: any;
  complaintSourceId: any;
  chatbotComplaint: boolean = false;
  chatbotComplaintId: number = 0;
  whatsappComplaint: boolean = false;
  whatsappComplaintId: number = 0;
  emailComplaint: boolean = false;
  emailComplaintId: any = 0;
  sysGenComplaint: boolean = false;
  sysGenComplaintId: any = 0;
  editable: number = 0;
  isChabotEditForm: boolean = false;
  public innerWidth: any;
  isMobileScreen: boolean = false;

  confirmationMsg: any = {};
  roleList: any;
  roleName: any;
  roleId: any;
  selectedTime: string;
  complaint_time: string;
  imgURL: string;
  public bucketUrl = `${environment.BUCKET_URL}`;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private dialog: MatDialog,
    private complaintService: ComplaintsService,
    private uploadService: UploadService,
    private thirdPartyService: ThirdPartyService,
    private authService: AuthService,
    private notify: NotificationService,
    private namsService: ThirdPartyService) {
    this.getStep2Form();
  }

  ngOnInit(): void {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobileScreen = true;
    }
    else {
      this.isMobileScreen = false;
    }

    let output = document.getElementById('output');
    let buttons = document.getElementsByClassName('tool--btn');
    for (let btn = 0; btn < buttons.length; btn++) {
      buttons[btn].addEventListener('click', () => {
        let cmd = buttons[btn].getAttribute('data-command');
        if (cmd === 'createlink') {
          let url = prompt("Enter the link here: ", "http:\/\/");
          document.execCommand(cmd, false, url);
        } else {
          document.execCommand(cmd, false, null);
        }
      })
    }

    // this.roleList = JSON.parse(window.localStorage.getItem('role'));
    // this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    // this.roleId = this.userInfo.roleId;


    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.ads = JSON.parse(window.localStorage.getItem('advertisementSource'));
    this.classification = JSON.parse(window.localStorage.getItem('classification'));
    this.roleList = JSON.parse(window.localStorage.getItem('role'));
    this.roleId = this.userInfo.roleId;

    if (this.userInfo.roleId == 1 || this.userInfo.roleId == 2 || this.userInfo.roleId == 3 || this.userInfo.roleId == 6) {
      this.classification = this.classification.filter(el => {
        return el.ADMINISTRATION_VISIBILITY == 1
      })
    } else {
      this.classification = this.classification.filter(el => {
        return el.USER_VISIBILITY == 1
      })
    }

    if (!(this.userInfo.roleId == 7 || this.userInfo.roleId == 9)) {
      this.roleList.forEach(element => {
        if (this.userInfo.roleId == element.ID) {
          this.roleName = element.ROLE_NAME[0];
        }
      });
    }
    else if (this.userInfo.roleId == 7 || this.userInfo.roleId == 9) {
      this.roleName = this.userInfo.firstName[0];
    }
    this.platforms = JSON.parse(window.localStorage.getItem('platform'));
    this.printSources = JSON.parse(window.localStorage.getItem('printSource'));
    this.promotionTypes = JSON.parse(window.localStorage.getItem('promotionalMaterialSource'));
    this.blankComplaintId = this.complaintService.getBlankId;
    this.complaintService.currentButtonName
      .pipe(first()).subscribe(cond => {
        if (cond === 'Admin') {
          this.buttonName = 'Next';
          this.complaintService.currentStep
            .pipe(first()).subscribe(step => {
              if (step === 'whatsapp') {
                this.backDisable = true;
                this.complaintService.currentWhatsappComplaintId
                  .pipe(first()).subscribe(id => {
                    if (id != 0) {
                      this.complaintService.currentWhatsappComplaintObj
                        .pipe(first()).subscribe(whatsappObj => {
                          this.whatsappComplaint = true;
                          let complaintObj = whatsappObj;
                          this.whatsappComplaintId = complaintObj['ID'];
                          this.companyId = complaintObj['COMPANY_ID'];
                          this.complaintSourceId = 2;
                          this.step2Form.patchValue({
                            'company': complaintObj['COMPANY_NAME'],
                            'brand': complaintObj['BRAND_NAME'],
                            'product': complaintObj['PRODUCT_NAME'],
                            'compDesc': complaintObj['COMPLAINT_DESCRIPTION'],
                            'description': complaintObj['ADVERTISEMENT_DESCRIPTION'],
                            'product_category': complaintObj['PRODUCT_CATEGORY'],
                          })
                          let mediaArray = complaintObj['ATTACHED_MEDIA'];
                          if (Array.isArray(mediaArray) && mediaArray.length > 0) {
                            let count = 1;
                            this.files_attached = "Yes";
                            mediaArray.forEach(el => {
                              this.step2Form.value['file_array'].push({
                                ATTACHMENT_SOURCE: el.SOURCE_URL,
                                ATTACHMENT_NAME: `Media ${count}`
                              });
                              count++;
                            })
                          }
                          if (!!complaintObj['ADVERTISEMENT_SOURCE_NAME']) {
                            this.ads.forEach(el => {
                              if (el['ADVERTISEMENT_SOURCE_NAME'].toLowerCase() === complaintObj['ADVERTISEMENT_SOURCE_NAME'].trim().toLowerCase()) {
                                let timeArray = complaintObj['ADVERTISEMENT_SEEN_TIME'].split(" ");
                                let newDateTime = new Date().setHours(timeArray[0], timeArray[1], 0, 0);
                                this.step2Form.patchValue({
                                  'seen_medium': el['ID'],
                                  'date': new Date(complaintObj['ADVERTISEMENT_SEEN_DATE']),
                                  'time': new Date(newDateTime)
                                })
                                this.changeAdvType(el);
                                if (!(el['ID'] == 3 || el['ID'] == 5 || el['ID'] == 6)) {
                                  this.step2Form.patchValue({
                                    'channel': complaintObj['CHANNEL_PAPER_PLATFORM_NAME']
                                  })
                                }
                                if (el['ID'] == 3) {
                                  this.platforms.forEach(el => {
                                    if (el['PLATFORM_NAME'].toLowerCase() === complaintObj['CHANNEL_PAPER_PLATFORM_NAME'].trim().toLowerCase()) {
                                      this.step2Form.patchValue({
                                        'platform': el['ID']
                                      })
                                    }
                                  })
                                }
                                if (el['ID'] == 5) {
                                  this.printSources.forEach(el => {
                                    if (el['PRINT_SOURCE_NAME'].toLowerCase() === complaintObj['CHANNEL_PAPER_PLATFORM_NAME'].trim().toLowerCase()) {
                                      this.step2Form.patchValue({
                                        'printSource': el['ID']
                                      })
                                    }
                                  })
                                }
                                if (el['ID'] == 6) {
                                  this.promotionTypes.forEach(el => {
                                    if (el['P_M_SOURCE_NAME'].toLowerCase() === complaintObj['CHANNEL_PAPER_PLATFORM_NAME'].trim().toLowerCase()) {
                                      this.step2Form.patchValue({
                                        'promotionType': el['ID']
                                      })
                                    }
                                  })
                                }
                              }
                            })
                          }
                        })
                    }
                  })
              } else if (step === 'email') {
                this.backDisable = true;
                this.complaintService.currentEmailComplaintId
                  .pipe(first()).subscribe(id => {
                    if (id != 0) {
                      this.complaintService.getMailComplaint(id).subscribe((res: any) => {
                        this.emailComplaint = true;
                        let complaintObj = res.data;
                        this.emailComplaintId = complaintObj['ID'];
                        this.companyId = complaintObj['COMPANY_ID'];
                        this.complaintSourceId = 4;
                        this.step2Form.patchValue({
                          'company': complaintObj['COMPANY_NAME'],
                          'brand': complaintObj['BRAND_NAME'],
                          'product': complaintObj['PRODUCT_NAME'],
                          'compDesc': complaintObj['COMPLAINT_DESCRIPTION'],
                          'description': complaintObj['ADVERTISEMENT_DESCRIPTION'],
                          'product_category': complaintObj['PRODUCT_CATEGORY'],
                        })
                        if (!complaintObj['COMPANY_ID']) {
                          // this.step2Form.get('company').setValue('');
                          this.getCompanyName(complaintObj['COMPANY_NAME']);
                        }
                        let mediaArray = complaintObj['ATTACHED_MEDIA'];
                        if (Array.isArray(mediaArray) && mediaArray.length > 0) {
                          let count = 1;
                          this.files_attached = "Yes";
                          mediaArray.forEach(el => {
                            this.step2Form.value['file_array'].push({
                              ATTACHMENT_SOURCE: el.SOURCE_URL,
                              ATTACHMENT_NAME: `Media ${count}`
                            });
                            count++;
                          })
                        }
                        if (!!complaintObj['ADVERTISEMENT_SOURCE_NAME']) {
                          this.ads.forEach(el => {
                            if (el['ADVERTISEMENT_SOURCE_NAME'].toLowerCase() === complaintObj['ADVERTISEMENT_SOURCE_NAME'].trim().toLowerCase()) {
                              let timeArray = complaintObj['ADVERTISEMENT_SEEN_TIME'].split(" ");
                              let newDateTime = new Date().setHours(timeArray[0], timeArray[1], 0, 0);
                              this.step2Form.patchValue({
                                'seen_medium': el['ID'],
                                'date': new Date(complaintObj['ADVERTISEMENT_SEEN_DATE']),
                                'time': new Date(newDateTime)
                              })
                              this.changeAdvType(el);
                              if (!(el['ID'] == 5 || el['ID'] == 6)) {
                                this.step2Form.patchValue({
                                  'channel': complaintObj['CHANNEL_PAPER_PLATFORM_NAME']
                                })
                              }
                              if (el['ID'] == 3) {
                                this.platforms.forEach(el => {
                                  if (el['PLATFORM_NAME'].toLowerCase() === complaintObj['CHANNEL_PAPER_PLATFORM_NAME'].trim().toLowerCase()) {
                                    this.step2Form.patchValue({
                                      'platform': el['ID']
                                    })
                                  }
                                })
                              }
                              if (el['ID'] == 5) {
                                this.printSources.forEach(el => {
                                  if (el['PRINT_SOURCE_NAME'] === complaintObj['CHANNEL_PAPER_PLATFORM_NAME'].trim()) {
                                    this.step2Form.patchValue({
                                      'printSource': el['ID']
                                    })
                                  }
                                })
                              }
                              if (el['ID'] == 6) {
                                this.promotionTypes.forEach(el => {
                                  if (el['P_M_SOURCE_NAME'] === complaintObj['CHANNEL_PAPER_PLATFORM_NAME'].trim()) {
                                    this.step2Form.patchValue({
                                      'promotionType': el['ID']
                                    })
                                  }
                                })
                              }
                            }
                          })
                        }
                      }, err => {
                        this.notify.showNotification(
                          err.error.message,
                          "top",
                          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                          err.error.status
                        )
                      });
                    }
                  })
              } else if (step === 'sysGen') {
                this.backDisable = true;
                this.complaintService.currentSysComplaintId
                  .pipe(first()).subscribe(id => {
                    if (id != 0) {
                      this.complaintService.getComplaint(id).subscribe((res: any) => {
                        this.sysGenComplaint = true;
                        let complaintObj = res.data;
                        this.sysGenComplaintId = complaintObj['ID'];
                        this.complaintSourceId = 3;
                        this.step2Form.patchValue({
                          'company': complaintObj['COMPANY_NAME'],
                          'brand': complaintObj['BRAND_NAME'],
                          'product': complaintObj['PRODUCT_NAME'],
                          'product_category': complaintObj['PRODUCT_CATEGORY'],
                          'description': complaintObj['ADVERTISEMENT_DESCRIPTION'],
                          'compDesc': complaintObj['COMPLAINT_DESCRIPTION'],
                          'initial_class': complaintObj['CLASSIFICATION_ID']
                        })
                        this.companyId = complaintObj['COMPANY_ID'];
                        if (!complaintObj['COMPANY_ID']) {
                          // this.step2Form.get('company').setValue('');
                          this.getCompanyName(complaintObj['COMPANY_NAME']);
                        }
                        let mediumInfo = complaintObj['ADVERTISEMENT_MEDIUM'];
                        if (Array.isArray(mediumInfo) && mediumInfo.length > 0) {
                          const advMediumObj = mediumInfo[0];
                          this.ads.forEach(el => {
                            if (advMediumObj['ADVERTISEMENT_SOURCE_ID'] == el.ID) {
                              this.changeAdvType(el);
                            }
                          })
                          let newDateTime = null;
                          if (!!advMediumObj['TIME']) {
                            let timeArr = advMediumObj['TIME'].split(':');
                            newDateTime = new Date().setHours(timeArr[0], timeArr[1], 0, 0);
                          }
                          this.step2Form.patchValue({
                            'seen_medium': advMediumObj['ADVERTISEMENT_SOURCE_ID'],
                            'channel': advMediumObj['SOURCE_NAME'],
                            'sourcePlace': advMediumObj['SOURCE_PLACE'],
                            'platform': advMediumObj['PLATFORM_ID'],
                            'date': advMediumObj['DATE'],
                            'add_url': advMediumObj['SOURCE_URL'],
                            'printSource': advMediumObj['PRINT_SOURCE_ID'],
                            'promotionType': advMediumObj['P_M_SOURCE_ID'],
                            'time': new Date(newDateTime)
                          })
                        }
                        let mediaArray = complaintObj['ADVERTISEMENT_MEDIUM_DOCUMENT'];
                        if (Array.isArray(mediaArray) && mediaArray.length > 0) {
                          let count = 1;
                          this.files_attached = "Yes";
                          mediaArray.forEach(el => {
                            this.step2Form.value['file_array'].push({
                              ATTACHMENT_NAME: el['ATTACHMENT_NAME'],
                              ATTACHMENT_SOURCE: el['ATTACHMENT_SOURCE'],
                              TYPE_OF_DOCUMENT: el['TYPE_OF_DOCUMENT'],
                              SIZE: el['SIZE'],
                            });
                            count++;
                          })
                        }
                      }, err => {
                        this.notify.showNotification(
                          err.error.message,
                          "top",
                          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                          err.error.status
                        )
                      });
                    }
                  })
              } else if (step === 'chatbot') {
                this.backDisable = false;
                this.complaintService.currentChatbotComplaintId
                  .pipe(first()).subscribe(id => {
                    if (id != 0) {
                      this.namsService.getChatbotDetails(id).subscribe((res: any) => {
                        let complaintObj = res.data;
                        this.chatbotComplaintId = id;
                        this.complaintSourceId = 1;
                        this.chatbotComplaint = true;
                        this.selectedAdvId = complaintObj['ADVERTISEMENT_SOURCE_ID'];
                        this.platform_id = complaintObj['PLATFORM_ID'];
                        let time = complaintObj['TIME'];
                        let newTimeArray = time.split(":");
                        newTimeArray[0] = '0' + newTimeArray[0];
                        newTimeArray[0] = newTimeArray[0].slice(-2);
                        let newTime = newTimeArray.join(':');
                        let d = time.split(':');
                        this.selectedTime = d[0] + ":" + d[1];
                        const momentDate = new Date(complaintObj['DATE']).toISOString();
                        this.step2Form.patchValue({
                          'company': complaintObj['COMPANY_NAME'],
                          'seen_medium': complaintObj['ADVERTISEMENT_SOURCE_ID'],
                          'brand': complaintObj['BRAND_NAME'],
                          'description': complaintObj['ADVERTISEMENT_DESCRIPTION'],
                          'product_category': complaintObj['PRODUCT_CATEGORY'],
                          'platform': complaintObj['PLATFORM_ID'],
                          'channel': complaintObj['SOURCE_PLACE'],
                          'date': momentDate,
                          'time': newTime,
                          'compDesc': complaintObj['COMPLAINT_DESCRIPTION'],
                          'sourcePlace': complaintObj['SOURCE_PLACE'],
                          'promotionType': complaintObj['P_M_SOURCE_ID'],
                          'printSource': complaintObj['PRINT_SOURCE_ID'],
                          'initial_class': complaintObj['CLASSIFICATION_ID'],
                          'add_url': complaintObj['SOURCE_URL']
                        })
                        this.step2Form.get('date').setValue(complaintObj['DATE']);
                        // this.step2Form.updateValueAndValidity();
                        this.companyId = complaintObj['COMPANY_ID'];
                        let mediaArray = complaintObj['ATTACHMENT_DOCUMENT'];
                        if (Array.isArray(mediaArray) && mediaArray.length > 0) {
                          let count = 1;
                          this.files_attached = "Yes";
                          mediaArray.forEach(el => {
                            this.step2Form.value['file_array'].push({
                              ATTACHMENT_SOURCE: el.ATTACHMENT_SOURCE,
                              ATTACHMENT_NAME: `Media ${count}`
                            });
                            count++;
                          })
                        }
                        if (complaintObj['ADVERTISEMENT_SOURCE_ID'] == 5 || complaintObj['ADVERTISEMENT_SOURCE_ID'] == 8 || complaintObj['ADVERTISEMENT_SOURCE_ID'] == 9) {
                          this.step2Form.patchValue({
                            'channel': complaintObj['SOURCE_NAME']
                          })
                        }
                        // this.ads.forEach(element => {
                        //   if (complaintObj.ADVERTISEMENT_SOURCE_ID === element.ID) {
                        //     this.changeAdvType(element);
                        //   }
                        // });
                        if (complaintObj['ADVERTISOR_NAME'] != '' && complaintObj['ADVERTISOR_NAME'] != null) {
                          this.getCompanyId(complaintObj['ADVERTISOR_NAME'])
                        }
                      }, err => {
                        this.notify.showNotification(
                          err.error.message,
                          "top",
                          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                          err.error.status
                        )
                      });
                    }
                  })
              }
            })
        } else {
          this.buttonName = 'Submit';
          if (this.blankComplaintId) {
            this.complaintService.currentEditableForm
              .pipe(first()).subscribe(cond => {
                if (cond === 'System') {
                  this.isChabotEditForm = false;
                  this.complaintService.getUserComplaintById(this.blankComplaintId).subscribe(res => {
                    if (res) {
                      this.sysGenComplaint = true;
                      let complaintObj = res.data;
                      this.sysGenComplaintId = complaintObj['ID'];
                      this.complaintSourceId = 3;
                      this.step2Form.patchValue({
                        'company': complaintObj['COMPANY_NAME'],
                        'brand': complaintObj['BRAND_NAME'],
                        'product': complaintObj['PRODUCT_NAME'],
                        'product_category': complaintObj['PRODUCT_CATEGORY'],
                        'description': complaintObj['ADVERTISEMENT_DESCRIPTION'],
                        'compDesc': complaintObj['COMPLAINT_DESCRIPTION'],
                        'initial_class': complaintObj['CLASSIFICATION_ID']
                      })
                      this.companyId = complaintObj['COMPANY_ID'];
                      if (!complaintObj['COMPANY_ID']) {
                        // this.step2Form.get('company').setValue('');
                        this.getCompanyName(complaintObj['COMPANY_NAME']);
                      }
                      let mediumInfo = complaintObj['ADVERTISEMENT_MEDIUM'];
                      if (Array.isArray(mediumInfo) && mediumInfo.length > 0) {
                        const advMediumObj = mediumInfo[0];
                        this.ads.forEach(el => {
                          if (advMediumObj['ADVERTISEMENT_SOURCE_ID'] == el.ID) {
                            this.changeAdvType(el);
                          }
                        })
                        let newDateTime = null;
                        if (!!advMediumObj['TIME']) {
                          let timeArr = advMediumObj['TIME'].split(':');
                          newDateTime = new Date().setHours(timeArr[0], timeArr[1], 0, 0);
                        }
                        this.step2Form.patchValue({
                          'seen_medium': advMediumObj['ADVERTISEMENT_SOURCE_ID'],
                          'channel': advMediumObj['SOURCE_NAME'],
                          'sourcePlace': advMediumObj['SOURCE_PLACE'],
                          'platform': advMediumObj['PLATFORM_ID'],
                          'date': advMediumObj['DATE'],
                          'add_url': advMediumObj['SOURCE_URL'],
                          'printSource': advMediumObj['PRINT_SOURCE_ID'],
                          'promotionType': advMediumObj['P_M_SOURCE_ID'],
                          'time': new Date(newDateTime)
                        })
                      }
                      let mediaArray = complaintObj['ADVERTISEMENT_MEDIUM_DOCUMENT'];
                      if (Array.isArray(mediaArray) && mediaArray.length > 0) {
                        let count = 1;
                        this.files_attached = "Yes";
                        mediaArray.forEach(el => {
                          this.step2Form.value['file_array'].push({
                            ATTACHMENT_NAME: el['ATTACHMENT_NAME'],
                            ATTACHMENT_SOURCE: el['ATTACHMENT_SOURCE'],
                            TYPE_OF_DOCUMENT: el['TYPE_OF_DOCUMENT'],
                            SIZE: el['SIZE'],
                          });
                          count++;
                        })
                      }
                    }
                  }, err => {
                    this.notify.showNotification(
                      err.error.message,
                      "top",
                      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                      err.error.status
                    );
                  })
                }
                else if (cond === 'Chatbot') {
                  this.isChabotEditForm = true;
                  this.complaintService.getUserChatbotComplaintById(this.blankComplaintId).subscribe(res => {
                    if (res) {
                      this.chatbotComplaint = true;
                      let complaintObj = res.data;
                      this.sysGenComplaintId = complaintObj['ID'];
                      this.complaintSourceId = 3;
                      this.step2Form.patchValue({
                        'company': complaintObj['COMPANY_NAME'],
                        'brand': complaintObj['BRAND_NAME'],
                        'product': complaintObj['PRODUCT_NAME'],
                        'product_category': complaintObj['PRODUCT_CATEGORY'],
                        'description': complaintObj['ADVERTISEMENT_DESCRIPTION'],
                        'compDesc': complaintObj['COMPLAINT_DESCRIPTION'],
                        'initial_class': complaintObj['CLASSIFICATION_ID']
                      })
                      this.companyId = complaintObj['COMPANY_ID'];
                      if (!complaintObj['COMPANY_ID']) {
                        // this.step2Form.get('company').setValue('');
                        this.getCompanyName(complaintObj['COMPANY_NAME']);
                      }
                      this.ads.forEach(el => {
                        if (complaintObj['ADVERTISEMENT_SOURCE_ID'] == el.ID) {
                          this.changeAdvType(el);
                        }
                      })
                      let newDateTime = null;
                      if (!!complaintObj['TIME']) {
                        let timeArr = complaintObj['TIME'].split(':');
                        newDateTime = new Date().setHours(timeArr[0], timeArr[1], 0, 0);
                      }
                      this.step2Form.patchValue({
                        'seen_medium': complaintObj['ADVERTISEMENT_SOURCE_ID'],
                        'channel': complaintObj['SOURCE_NAME'],
                        'sourcePlace': complaintObj['SOURCE_PLACE'],
                        'platform': complaintObj['PLATFORM_ID'],
                        'date': complaintObj['DATE'],
                        'add_url': complaintObj['SOURCE_URL'],
                        'printSource': complaintObj['PRINT_SOURCE_ID'],
                        'promotionType': complaintObj['P_M_SOURCE_ID'],
                        'time': new Date(newDateTime)
                      })
                      let mediaArray = complaintObj['ATTACHMENT_DOCUMENT'];
                      if (Array.isArray(mediaArray) && mediaArray.length > 0) {
                        let count = 1;
                        this.files_attached = "Yes";
                        mediaArray.forEach(el => {
                          this.step2Form.value['file_array'].push({
                            ATTACHMENT_NAME: el['ATTACHMENT_SOURCE_NAME'],
                            ATTACHMENT_SOURCE: el['ATTACHMENT_SOURCE'],
                            TYPE_OF_DOCUMENT: el['ATTACHMENT_SOURCE_TYPE'],
                            SIZE: el['SIZE'],
                          });
                          count++;
                        })
                      }
                    }
                  }, err => {
                    this.notify.showNotification(
                      err.error.message,
                      "top",
                      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
                      err.error.status
                    );
                  })
                }
              })
          }
        }
      })

    // this.complaintService.currentBlankComplaintId.pipe(first()).subscribe(id => {
    //   this.blankComplaintId = id;
    // })
    if (this.userInfo.roleId == 7) {
      this.complainantID = 1;
    }
    else if (this.userInfo.roleId == 4) {
      this.complainantID = 3;
    }
    else {
      this.complaintService.complainantID.subscribe((data) => {
        this.complainantID = data['complainantId'];
      })
    }
    this.step2Form.get('company')!.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0) {
          this.getCompanyList(value);
        }
      }, err => {

      })
  }

  getCompanyId(value) {
    this.authService.getCompanies(value).subscribe(res => {
      this.companyId = res.data[0].ID;
      this.step2Form.patchValue({
        'company': res.data[0].COMPANY_NAME
      })
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  getCompanyList(value) {
    this.authService.getCompanies(value).subscribe(res => {
      this.companyList = res.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  getStep2Form() {
    this.step2Form = this.fb.group({
      company: new FormControl('', Validators.required),
      brand: new FormControl('', Validators.required),
      product: new FormControl('', Validators.required),
      product_category: new FormControl(''),
      seen_medium: new FormControl('', Validators.required),
      channel: new FormControl(''),
      sourcePlace: new FormControl(''),
      printSource: new FormControl(''),
      promotionType: new FormControl('', Validators.required),
      platform: new FormControl(''),
      date: new FormControl('', Validators.required),
      time: new FormControl('', Validators.required),
      doc_file: new FormControl(''),
      file_array: new FormControl([]),
      add_url: new FormControl(''),
      initial_class: new FormControl('', Validators.required),
      description: new FormControl('', Validators.required),
      compDesc: new FormControl('', Validators.required),
      // suggestion: new FormControl(''),
      code_violated: new FormControl(''),
      doc_reference: new FormControl(''),
      reg_authority: new FormControl(''),
      summary: new FormControl(''),
      claim_chall1: new FormControl(''),
      key_obj1: new FormControl(''),
      annexure1: new FormControl(''),
      doc_type1: new FormControl(''),
      claim_summary1: new FormControl('')
    })
  }

  private requireMatch(control: FormControl): ValidationErrors | null {
    // const selection: any = control.value;
    // if (this.companyList && this.companyList.indexOf(selection) < 0) {
    return { requireMatch: true };
    // }
    // return null;
  }

  onSelectionChange(event) {
    this.companyList.forEach(element => {
      if (event.option.value === element.COMPANY_NAME) {
        this.companyId = element.ID;
      }
    });
  }

  companyChange() {
    this.companyId = 0;
  }

  companyInput() {
    if (!(this.userInfo.roleId == 1 || this.userInfo.roleId == 2 || this.userInfo.roleId == 3 || this.userInfo.roleId == 6) && this.companyId == 0) {
      this.companyId = 0;
    } else if ((this.userInfo.roleId == 1 || this.userInfo.roleId == 2 || this.userInfo.roleId == 3 || this.userInfo.roleId == 6) && this.companyId == 0) {
      this.step2Form.get('company').setValue('');
    }
  }

  changePlatform(plat) {
    this.platform_id = plat.ID;
    if (this.platform_id == 9) {
      this.step2Form.controls["sourcePlace"].setValidators(Validators.required);
    }
  }

  changeAdvType(advSource) {
    this.selectedAdvId = advSource.ID;
    this.platform_id = 0;
    this.step2Form.get('channel').clearValidators();
    this.step2Form.get('channel').updateValueAndValidity();
    this.step2Form.get('promotionType').clearValidators();
    this.step2Form.get('promotionType').updateValueAndValidity();
    this.step2Form.get('sourcePlace').clearValidators();
    this.step2Form.get('sourcePlace').updateValueAndValidity();
    this.step2Form.get('time').clearValidators();
    this.step2Form.get('time').updateValueAndValidity();
    this.step2Form.get('platform').clearValidators();
    this.step2Form.get('platform').updateValueAndValidity();
    this.step2Form.get('printSource').clearValidators();
    this.step2Form.get('printSource').updateValueAndValidity();
    // this.step2Form.get('channel').setValue('');
    // this.step2Form.get('sourcePlace').setValue('');
    // this.step2Form.get('time').setValue(null);
    // this.step2Form.get('platform').setValue('');
    // this.step2Form.get('printSource').setValue('');

    if (advSource.ID == 1 || advSource.ID == 2 ||
      advSource.ID == 5 || advSource.ID == 8 || advSource.ID == 9) {
      this.step2Form.controls["channel"].setValidators(Validators.required);
    }
    if (advSource.ID == 4 || advSource.ID == 6) {
      this.step2Form.controls["sourcePlace"].setValidators(Validators.required);
    }
    if (advSource.ID == 1 || advSource.ID == 2 || advSource.ID == 3 || advSource.ID == 9) {
      this.step2Form.controls["time"].setValidators(Validators.required);
    }
    if (advSource.ID == 3) {
      this.step2Form.controls["platform"].setValidators(Validators.required);
    }
    if (advSource.ID == 5) {
      this.step2Form.controls["printSource"].setValidators(Validators.required);
    }
    if (advSource.ID == 6) {
      this.step2Form.controls["promotionType"].setValidators(Validators.required);
    }

    if (advSource.ID == 3 || advSource.ID == 4 || advSource.ID == 6 || advSource.ID == 7) {
      this.step2Form.controls["channel"].reset();
    }
    if (advSource.ID == 1 || advSource.ID == 2 || advSource.ID == 7 || advSource.ID == 8 || advSource.ID == 9) {
      this.step2Form.controls["sourcePlace"].reset();
    }
    if (advSource.ID == 4 || advSource.ID == 5 || advSource.ID == 6 || advSource.ID == 7 ||
      advSource.ID == 8) {
      this.step2Form.controls["time"].reset();
    }
    if (!(advSource.ID == 3)) {
      this.step2Form.controls["platform"].reset();
    }
    if (!(advSource.ID == 5)) {
      this.step2Form.controls["printSource"].reset();
      // this.step2Form.controls["sourcePlace"].reset();
    }
    if (!(advSource.ID == 6)) {
      this.step2Form.controls["promotionType"].reset();
    }
  }

  savedraft() {
    this.step2Next('draft');
  }

  step2Next(cond) {
    let submitted = 1;
    if (cond === 'draft') {
      submitted = 0;
    }
    if (this.chatbotComplaint) {
      this.complaintSourceId = 1;
    } else if (this.whatsappComplaint) {
      this.complaintSourceId = 2;
    } else if (this.emailComplaint) {
      this.complaintSourceId = 4;
    } else if (this.complaintSourceId == undefined || this.complaintSourceId == null || this.sysGenComplaint) {
      this.complaintSourceId = 3;
    }
    let reference_id = null;
    if (this.chatbotComplaint) {
      reference_id = this.chatbotComplaintId;
    } else if (this.whatsappComplaint) {
      reference_id = this.whatsappComplaintId;
    } else if (this.emailComplaint) {
      reference_id = this.emailComplaintId;
    } else if (this.sysGenComplaint) {
      reference_id = this.sysGenComplaintId;
    }
    if (this.chatbotComplaint) {
      this.complaint_time = this.step2Form.value.time;
    }
    else {
      this.complaint_time = moment(this.step2Form.value.time).format('HH:mm:ss');
    }
    this.complaintService.currentEditableForm
      .pipe(first()).subscribe(cond => {
        if (cond === 'Chatbot') {
          let obj = {
            "ID": 0,
            "ADVERTISEMENT_SOURCE_ID": this.selectedAdvId,
            "COMPLAINT_SOURCE_ID": this.complaintSourceId,
            "BRAND_NAME": this.step2Form.value.brand,
            "SOURCE_NAME": this.step2Form.value.channel,
            "SOURCE_PLACE": this.step2Form.value.sourcePlace,
            "PLATFORM_ID": this.step2Form.value.platform,
            "PRODUCT_NAME": this.step2Form.value.product,
            "PRODUCT_CATEGORY": this.step2Form.value.product_category,
            "ADVERTISEMENT_DESCRIPTION": this.step2Form.value.description,
            "COMPLAINT_DESCRIPTION": this.step2Form.value.compDesc,
            "DATE": moment(this.step2Form.value.date).format('yyyy-MM-DD'),
            "TIME": this.complaint_time,
            "CLASSIFICATION_ID": this.step2Form.value.initial_class,
            "P_M_SOURCE_ID": this.step2Form.value.promotionType,
            "PRINT_SOURCE_ID": this.step2Form.value.printSource,
            "SOURCE_URL": this.step2Form.value.add_url,
            "SUBMITTED": submitted,
            "EDITABLE": this.editable,
          }
          this.complaintService.complaintRegister(obj, 1);
          this.outFilter.emit({ 'cond': 'create', 'obj': obj });
        }
        else {
          let obj = {
            "ID": 0,
            "REFERENCE_ID": reference_id,
            "COMPLAINT_TYPE_ID": this.complainantID,
            "COMPANY_ID": this.companyId,
            "PANEL_ID": 1,
            "COMPLAINT_SOURCE_ID": this.complaintSourceId,
            "COMPLAINT_STATUS_ID": 1,
            "PRIORITY_ID": 1,
            "STAGE_ID": 1,
            "CLASSIFICATION_ID": this.step2Form.value.initial_class,
            "GOVERNMENT_DEPARTEMENT_ID": 1,
            "USER_TYPE_ID": this.complainantID,
            "USER_ID": 0,
            "CREATED_BY_USER_ID": this.userInfo.userId,
            "ASSIGNEE_USER_ID": 0,
            "COMPLAINT_PRODUCT_CATEGORY_ID": 1,
            "EXTENDED_STAGE_ID": 1,
            "EXTENDED_DAYS": "9",
            "BRAND_NAME": this.step2Form.value.brand,
            "PRODUCT_NAME": this.step2Form.value.product,
            "ADVERTISEMENT_DESCRIPTION": this.step2Form.value.description,
            "COMPLAINT_DESCRIPTION": this.step2Form.value.compDesc,
            "COMPANY_NAME": this.step2Form.value.company,
            // "OBJECTIONABLE": "",
            // "WEB_LINK": "",
            "NOTIFY": "7",
            "TnC": "1",
            // "SUGGESTION": "",
            "DUE_DATE": "",
            // "ADDITIONAL_CLASSIFICATION": "",
            "ADVERTISOR_NAME": "",
            "PRODUCT_CATEGORY": this.step2Form.value.product_category,
            "SUBMITTED": submitted,
            "EDITABLE": this.editable,
            "CLAIMS": [],
            "CLAIMS_DOCUMENT": [],
            "ADVERTISEMENT_MEDIUM": [
              {
                "ADVERTISEMENT_SOURCE_ID": this.step2Form.value.seen_medium,
                "SOURCE_NAME": this.step2Form.value.channel,
                "SOURCE_PLACE": this.step2Form.value.sourcePlace,
                "PLATFORM_ID": this.step2Form.value.platform,
                "DATE": moment(this.step2Form.value.date).format('yyyy-MM-DD'),
                "TIME": this.complaint_time,
                "ATTACHMENT_DATA": this.step2Form.value.file_array,
                "SOURCE_URL": this.step2Form.value.add_url,
                "PRINT_SOURCE_ID": this.step2Form.value.printSource,
                "P_M_SOURCE_ID": this.step2Form.value.promotionType
              }
            ],
            "CODE_VIOLATED": [],
            "GUIDELINES": [],
            "files_attached": this.files_attached
          }
          this.complaintService.complaintRegister(obj, 1);
          this.outFilter.emit({ 'cond': 'create', 'obj': obj });
        }
      })
  }

  back() {
    let obj = {}
    // this.step2Form.reset();
    // this.complaintService.currentChatbotComplaintId
    //   .pipe(first()).subscribe(id => {
    //     if (id == 0) {
    //       this.step2Form.reset();
    //     }
    //   })
    this.outFilter.emit({ 'cond': 'back', 'obj': obj });
  }

  cancel() {
    this.step2Form.reset();
    this.backDisable = false;
    this.complaintService.updateNamsComplaintId = 0;
    this.complaintService.updateStep('direct');
    this.complaintService.complaintRegister({}, 0);
    this.complaintService.complaintRegister({}, 1);
    this.router.navigate(['manage-cases']);
  }

  async onFileSelected(event: any) {
    this.complaintService.currentEditableForm
      .pipe(first()).subscribe(cond => {
        if (cond === 'Chatbot') {
          this.isChabotEditForm = true;
          let sizeOfAllFiles = this.uploadService.getTotalFileSize(event.target.files);
          let totalFileSelected = event.target.files.length;
          if (totalFileSelected < 11 && sizeOfAllFiles <= 36700160) {
            if (event.target["files"] && event.target.files['length'] != 0) {
              for (let i = 0; i <= event.target.files.length - 1; i++) {
                this.files_attached = "Yes";
                let selectedFile = event.target.files[i];
                this.fileProgress = 0;
                this.blankComplaintId = this.complaintService.getBlankId;
                let tempObjforGetsigned = {
                  id: this.blankComplaintId,
                  section: 'chatbot',
                  filename: selectedFile['name'],
                  type: selectedFile['type']
                }
                this.isUploadProgress = true;
                this.uploadService.getSignedUrlForChatbot(tempObjforGetsigned).then(async (res) => {
                  if (res && res['data'] && res['data']['SIGNED_URL']) {
                    this.fileInfoPath = res['data']['PATH'];
                    this.step2Form.value['file_array'].push({
                      ATTACHMENT_SOURCE: res['data']['PATH'],
                      ATTACHMENT_NAME: selectedFile['name'],
                      TYPE_OF_DOCUMENT: selectedFile['type'],
                      SIZE: selectedFile['size']
                    });
                    let tempObjForUpload = {
                      url: res['data']['SIGNED_URL'],
                      file: selectedFile
                    }
                    let progressInit = 0;
                    await this.uploadSignedFile(tempObjForUpload, 0);
                    let saveBody = {
                      ID: this.blankComplaintId,
                      KEY: res['data']['PATH'],
                      SOURCE_NAME: selectedFile['name'],
                      TYPE: selectedFile['type'],
                      SIZE: selectedFile['size'],
                      TAB: 'complainant'
                    }
                    await this.saveDocumentToDB(saveBody);
                  }
                })
                  .catch((err) => {
                    this.isUploadProgress = false;
                    this.handleError(err);
                  });
              }
            }
            else {
              this.files_attached = "No"
            }
          }
          else {
            this.notify.showNotification(
              "Max size 35MB file allowed",
              "top",
              "warning",
              0
            );
          }
        }
        else {
          this.isChabotEditForm = false;
          let sizeOfFiles = this.uploadService.getTotalFileSize(event.target.files);
          let totalFileSelected = this.step2Form.value.file_array.length + event.target.files.length;
          if (totalFileSelected < 11 && sizeOfFiles <= 36700160) {
            if (event.target["files"] && event.target.files['length'] != 0) {
              for (let i = 0; i <= event.target.files.length - 1; i++) {
                this.files_attached = "Yes";
                let selectedFile = event.target.files[i];
                this.fileProgress = 0;
                // this.complaintService.currentBlankComplaintId
                //   .pipe(first()).subscribe(id => {
                //     this.blankComplaintId = id;
                //   })
                this.blankComplaintId = this.complaintService.getBlankId;
                let tempObjforGetsigned = {
                  id: this.blankComplaintId,
                  section: 'a_medium',
                  filename: selectedFile['name'],
                  type: selectedFile['type']
                }
                this.isUploadProgress = true;
                this.uploadService.getSignedUrl(tempObjforGetsigned)
                  .then(async (res) => {
                    if (res && res['data'] && res['data']['SIGNED_URL']) {
                      this.fileInfoPath = res['data']['PATH'];
                      this.step2Form.value['file_array'].push({
                        ATTACHMENT_SOURCE: res['data']['PATH'],
                        ATTACHMENT_NAME: selectedFile['name'],
                        TYPE_OF_DOCUMENT: selectedFile['type'],
                        SIZE: selectedFile['size']
                      });
                      let tempObjForUpload = {
                        url: res['data']['SIGNED_URL'],
                        file: selectedFile
                      }
                      let progressInit = 0;
                      await this.uploadService.uploadFilethroughSignedUrl(tempObjForUpload, progressInit => {
                        this.fileProgress = progressInit;
                      }).then((data12) => {
                        this.isUploadProgress = false;
                        //   this.fileProgress = 0;
                        this.notify.showNotification(
                          'File uploaded successfully',
                          "top",
                          (!!colorObj[200] ? colorObj[200] : "success"),
                          200
                        );
                      }).catch((err) => {
                        this.fileProgress = 0;
                        this.isUploadProgress = false;
                        this.handleError(err);
                      });
                    }
                  })
                  .catch(err => {
                    this.isUploadProgress = false;
                    this.handleError(err);
                  });
              }
            } else {
              this.files_attached = "No"
            }
          } else {
            this.notify.showNotification(
              "Max size 35MB file allowed",
              "top",
              "warning",
              0
            );
          }
        }
      })
  }

  async uploadSignedFile(tempObj, i) {
    let progressInit = 0;
    await this.uploadService.uploadFilethroughSignedUrl(tempObj, progressInit => {
      this.fileProgress = progressInit;
    }).then((data12) => {
      this.isUploadProgress = false;
      this.notify.showNotification(
        'File uploaded successfully',
        "top",
        (!!colorObj[200] ? colorObj[200] : "success"),
        200
      );
    }).catch((err) => {
      this.fileProgress = 0;
      this.isUploadProgress = false;
      this.handleError(err);
    });
  }

  async saveDocumentToDB(tempObj) {
    await this.complaintService.saveChatbotFileToDB(tempObj)
      .then((data) => {
        return data;
      })
      .catch(err => {
      })
  }

  removeChatbotFile(index, key) {
    if (this.step2Form.value['file_array'] != 0) {
      this.step2Form.value['file_array'].splice(index, 1);
      this.thirdPartyService.deleteChatbotDocFromDB(this.blankComplaintId, key).subscribe(res => {
        if (res) {
          this.notify.showNotification(
            res.message,
            "top",
            (!!colorObj[res.status] ? colorObj[res.status] : "success"),
            res.status
          )
        }
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
      });
    }
  }

  removeVideoFile(index, key) {
    if (this.step2Form.value['file_array'] != 0) {
      this.step2Form.value['file_array'].splice(index, 1);
      let body = {
        ID: this.blankComplaintId,
        KEY: key
      }
      this.uploadService.deleteObjectFromS3(body).toPromise()
        .then((data1) => {
          this.notify.showNotification(
            data1.message,
            "top",
            (!!colorObj[data1.status] ? colorObj[data1.status] : "success"),
            data1.status
          );
        }).catch(err => this.handleError(err));
    }
  }

  handleError(err) {
    this.notify.showNotification(
      err.error.message,
      "top",
      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
      err.error.status
    );
  }

  resetComplaintForm() {
    this.step2Form.reset();
  }

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobileScreen = true;
    }
    else {
      this.isMobileScreen = false;
    }
  }

  logout() {
    this.confirmationMsg.title = 'Are you sure you want to logout ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.authService.logout().subscribe(
          (res) => {
            window.localStorage.clear();
            this.router.navigate(['auth/login']);
          },
          (err) => {
            window.localStorage.clear();
            this.router.navigate(['auth/login']);
          }
        );
      }
    });
  }

  viewProfile(flag) {
    this.authService.profileFlag = flag;
    this.router.navigateByUrl('auth/my-profile');
  }

  viewNotifications() {
    this.router.navigateByUrl('mobile-notifications');
  }

  getCompanyName(companyValue) {
    if (!!companyValue) {
      this.authService.getCompanies(companyValue).subscribe(res => {
        if (res.data.length > 0) {
          this.companyId = res.data[0].ID;
          this.step2Form.patchValue({
            'company': res.data[0].COMPANY_NAME
          })
        }
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
  }

  preview(source) {
    if (source.indexOf('https://') == -1 && source.indexOf('http://') == -1) {
      window.open(this.bucketUrl + source, '_blank');
    } else {
      window.open(source, '_blank');
    }
  }

}