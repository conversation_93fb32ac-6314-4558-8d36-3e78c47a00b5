<div fxLayout="row">
    <mat-toolbar class="toolbar">
        <div fxFlex="35%" fxLayoutAlign="start" *ngIf="addCompany">
            <h2 mat-dialog-title fxLayoutAlign="start" style="font-size: 16px;">
                <mat-icon style="position: relative; top: 7px; font-size: 16px;">add</mat-icon>Add new company
            </h2>
        </div>
        <div fxFlex="35%" fxLayoutAlign="start" *ngIf="!addCompany">
            <h2 mat-dialog-title fxLayoutAlign="start" style="font-size: 16px;">
                <img src="../assets/images/edit-icon.svg">&nbsp; Edit company details
            </h2>
        </div>
        <div fxFlex="60%"></div>
        <div fxFlex="5%" fxLayoutAlign="end">
                <button mat-icon-button class="search-btn" mat-dialog-close>
                    <mat-icon style="margin-bottom: 4px;">close</mat-icon>
                </button>
        </div>
    </mat-toolbar>
</div>

<mat-divider></mat-divider>

<div class="form">
    <form [formGroup]="addcompany">
        <div class="contents">
            <div class="names1" style="padding-top: 15px;">
                <div>Company name <span style="color: #ff0000;">*</span></div>
            </div>
            <div class="compname">
                <input class="input-field_phno" id="companyname" formControlName="cname" style="height: 35px;border: 1px solid #CFD7DF;border-radius: 4px;" autocomplete="off" *ngIf="!autoComplete" [ngStyle]="{'border':addcompany.get('cname').touched && addcompany.controls['cname'].errors?.required ||  addcompany.controls['cname'].errors?.pattern? '1px solid #FF0000' : '1px solid #CFD7DF' }">
                <input class="input-field_phno" style="height: 35px;border-radius: 4px;" type="text" placeholder="Company / Organization name"
                  formControlName="cname" id="companyname"  name="companyname" [matAutocomplete]="autoCompany" *ngIf="autoComplete" [ngStyle]="{'border':addcompany.get('cname').touched && addcompany.controls['cname'].errors?.required ||  addcompany.controls['cname'].errors?.pattern? '1px solid #FF0000' : '1px solid #CFD7DF' }"/>
                <mat-autocomplete #autoCompany="matAutocomplete" (optionSelected)="onSelectionChange($event)">
                    <mat-option *ngFor="let company of companyList;" [value]="company.COMPANY_NAME" style="
                    font-style: normal;
                    font-weight: normal;">
                        {{company.COMPANY_NAME}}
                    </mat-option>
                </mat-autocomplete>
                <mat-error class="error-msg" style="font-size: 12px;" *ngIf="addcompany.get('cname').touched && addcompany.controls['cname'].errors?.required">
                    Company Name is required
                </mat-error>
                <mat-error class="error-msg" style="font-size: 12px;" *ngIf="addcompany.controls['cname'].errors?.pattern">
                    Company name can contain alphabets or numbers or these special characters(.£&@€¥$~?!()*-)
                </mat-error>
              </div>
            <!-- <div class="compname">
                <mat-form-field appearance="outline" class="input-field_phno">
                    <input #autoCompleteInput  matInput id="companyname" formControlName="cname" style="
                    font-style: normal;
                    font-weight: normal; height: 20px;" autocomplete="off" *ngIf="!autoComplete" autofocus="autofocus">
                    <input #autoCompleteInput matInput id="companyname" formControlName="cname" style="font-style: normal; font-weight: normal; height: 20px;"
                    autocomplete="off" [matAutocomplete]="autoCompany" *ngIf="autoComplete" autofocus="autofocus">
                    <mat-autocomplete #autoCompany="matAutocomplete" (optionSelected)="onSelectionChange($event)">
                      <mat-option *ngFor="let company of companyList;" [value]="company.COMPANY_NAME" style="
                      font-style: normal;
                      font-weight: normal;">
                        {{company.COMPANY_NAME}}
                      </mat-option>
                    </mat-autocomplete>
                    <mat-error class="error-msg" *ngIf="addcompany.controls['cname'].errors?.required">
                        Company Name is required
                    </mat-error>
                </mat-form-field>
            </div> -->
            <div class="names1" style="margin-top: 11px;">
                <div>Reachable Contact Details</div>
            </div>
            <div class="contact-info" fxLayout="column">
              <div formArrayName="contact_info">
                <div fxLayout="row" *ngFor="let contactInfo of getControls(); let in = index;" formGroupName="{{in}}">
                  <mat-form-field appearance="outline" class="input-field_phno">
                    <input matInput formControlName="EMAIL_ID" style="
                    font-style: normal;
                    font-weight: normal; height: 20px;" autocomplete="off" placeholder="Enter email">
                    <mat-error class="error-msg" style="font-size: 12px;">
                        Enter valid email id
                    </mat-error>
                  </mat-form-field>
                  <div fxFlex="2%"></div>
                  <mat-form-field appearance="outline" class="input-field_phno">
                    <input matInput formControlName="MOBILE" style="
                      font-style: normal;
                      font-weight: normal; height: 20px;" autocomplete="off" placeholder="Enter mobile number">
                    <mat-error class="error-msg" style="font-size: 12px;">
                        Enter valid  10 digit mobile number
                    </mat-error>
                </mat-form-field>
                  <div fxFlex="2%"></div>
                  <button class="remove-box" [hidden]="addcompany.controls.contact_info.controls.length == 1 || in == 0"
                  (click)="removeContactInfo(in)">
                    <!-- <span class="remove-icon">-</span> -->
                    <img src="../../../assets/images/delete-red.png" width="15px">
                  </button>
                  <span class="fill-space" *ngIf="addcompany.controls.contact_info.controls.length == 1 || in == 0"></span>
                </div>
                <span style="color: #0088CB; cursor: pointer;" (click)="addContactInfo()"
                [hidden]="getControls().length == 3">+ Add More</span>
              </div>
            </div>
            <div class="names1">
                <div>Company address</div>
            </div>
            <div class="address">
                <mat-form-field appearance="outline" class="input-field_phno">
                    <input matInput formControlName="caddress" style="
                    font-style: normal;
                    font-weight: normal; height: 20px;" autocomplete="off">
                </mat-form-field>
            </div>
            <div class="names1">
                <div>About company</div>
            </div>
            <div class="about">
                <mat-form-field class="input-field_phno" appearance="outline">
                    <textarea matInput formControlName="about_company"
                        style="font-size: 14px; height: 50px;" autocomplete="off"></textarea>
                </mat-form-field>
            </div>
        </div>
        <div class="lastdivdr">
            <mat-divider></mat-divider>
        </div>
        <div class="toolbar-btns2">
            <div *ngIf="addCompany">
                <mat-dialog-actions>
                    <button mat-flat-button class="cancel-btn" mat-dialog-close>
                        <span class="bolder">Cancel</span>
                    </button>
                    <button mat-flat-button class="register-btn" (click)="register(addcompany.value)"
                        [disabled]="addcompany.invalid">
                        <span class="bolder">Register company</span>
                    </button>
                </mat-dialog-actions>
            </div>
        </div>
        <div fxLayout="row" *ngIf="!addCompany">
            <mat-toolbar class="toolbar2">
                <div fxFlex="35%" fxLayoutAlign="start">
                    <div class="search-div">
                        <button mat-button class="remove-btn" (click)="removeCompany()"> 
                            <span class="bolder">Remove company</span> 
                        </button>
                    </div>
                </div>
                <div fxFlex="35%"></div>
                <div fxFlex="67%"></div>
                <div class="toolbar-btns" fxFlex="16%" fxLayoutAlign="end">
                    <div class="remove-div">
                        <button mat-flat-button class="cancel-btn" mat-dialog-close> 
                            <span class="bolder">Cancel</span> 
                        </button>
                    </div>
                    <div class="update-div">
                        <button mat-flat-button class="register-btn" (click)="updateCompany(addcompany.value)"
                        [disabled]="addcompany.invalid"> 
                        <span class="bolder">Update details</span> 
                    </button>
                    </div>
                </div>
            </mat-toolbar>
        </div>
    </form>
</div>
