@media only screen and (max-width: 1250px) {
    .common-toolbar-mobile {
        border-bottom: 1px solid #D8DCDE;
        border-top: 0;
        border-left: none;
        border-right: none;
    }

    .dashboard-admin-heading {
        padding-left: 0;
        padding-top: 0;
        font-weight: 600;
        font-size: 18px;
        line-height: 23.94px;
        color: #ED2F45;
        padding-left: 20px;
        padding-top: 6px;
    }

    .dashboard-container {
        width: 100%;
        height: 90%;
        background: #E5E5E5;
    }

    .dasboard-subheading {
        padding-right: 20px;
        padding-top: 0px;
        font-weight: 400;
        font-size: 14px;
        line-height: 18.62px;
        padding-left: 20px;
        padding-top: 3px;
    }
}

.common-toolbar {
    border-bottom: 1px solid #D8DCDE;
    border-top: 0;
    border-left: none;
    border-right: none;
}

.heading-container {
    width: 60%;
}

.options-container {
    width: 40%;
}

.migrate-container {
    width: 100%;
    height: 92%;
    padding-left: 4%;
    background: #F8F9F9;
    overflow-y: scroll;
    overflow-x: hidden;
}

.migrate-container::-webkit-scrollbar {
    display: none;
}

.migrate-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

::ng-deep .mat-form-field-flex>.mat-form-field-infix {
    padding: 0.4em 0px !important;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-label {
    margin-top: -5px;
    margin-bottom: -5px;
}

::ng-deep label.ng-star-inserted {
    transform: translateY(-0.59375em) scale(.75) !important;
}

.mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0em 139px 0em 2px;
}

.step1-container {
    width: 100%;
    margin: 17px 0px 0px 0px;
}

.row-container {
    display: flex;
    flex-direction: row;
    margin-left: 19px;
}

.input-container {
    display: flex;
    flex-direction: column;
    gap: 0px;
    width: 100%;
}

.input-container1 {
    display: flex;
    flex-direction: column;
    gap: 0px;
    width: 33.3%;
}

.text-container {
    margin-bottom: 0px;
}

.control-container {
    margin-top: 0px;
}

.basic-input-field {
    width: 80%;
}

.basic-input-field1 {
    width: 26.6%;
}

.divider-container {
    width: 97%;
}

.collapse-btn {
    height: 35px;
    border: 1px solid #D8DCDE;
    background: #F8F9F9;
    top: -1px;
    left: 95%;
}

.arrow {
    height: 20px;
    margin-bottom: 5px;
}

.btn-container {
    display: flex;
    flex-direction: row;
    margin: 10px 20px;
}

.cancel-btn {
    width: 80px;
    border-radius: 15px;
    border: 1px solid #D8DCDE
}

.next-btn {
    width: 80px;
    border-radius: 15px;
    background-color: #0088CB;
    color: white;
    margin-right: 65px;
}

.archived-complaints {
    width: 100%;
    height: auto;
    max-height: 75%;
    overflow: scroll;
    /* overflow-x: hidden; */
}

.archived-complaints::-webkit-scrollbar {
    display: none;
}

.archived-complaints {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.icon-container {
    padding-left: 2%;
}

.ellipsis {
    overflow: hidden;
    display: inline-block;
    display: -webkit-inline-box;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    white-space: normal;
    -webkit-box-orient: vertical;
    vertical-align: middle;
    position: relative;
    height: auto !important;
}

.details-container {
    padding: 10px 20px;
    border-left: 7px solid #0088CB;
    cursor: pointer;
}

.zero-complaints {
    display: flex;
    align-content: center;
    justify-content: center;
    font-size: large;
}