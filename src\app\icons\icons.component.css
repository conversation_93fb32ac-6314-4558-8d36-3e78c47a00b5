.mat-sidenav-container {
    width: 59px;
    height: 100%;
    z-index: 110;
}

.mat-drawer-container {
    position: fixed !important;
    overflow: visible;
}

.sidenav-fixed {
    position: fixed;
    width: 60px;
}

.mat-drawer {
    background-color: #000000;
    width: 60px;
    height: inherit;
    overflow: initial;
}

.logo-container {
    margin-top: 30%;
    margin-bottom: 51%;
    margin-right: 4px;
    display: flex;
    justify-content: center;
}

.icon-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 10%;
    margin-right: 4px;
}

.icon-container>div {
    margin-bottom: 24px;

}

.btn-container {
    background-color: #FFFFFF;
    height: 35px;
    width: 34px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0% 1%;
}

.mat-drawer .btn-container:hover {
    background-color: rgb(243, 180, 8);
    color: white;
}

.mat-drawer .nams-btn-container:hover {
    background-color: rgb(243, 180, 8);
    color: white;
}

.hide-icon {
    position: relative;
    bottom: 1px;
    left: 1px;
}

.hide-icon2 {
    height: 16px;
    width: 16px;
    position: relative;
    bottom: 1px;
    left: 1px;
}

.show-icon {
    visibility: hidden;
    position: absolute;
    top: 13px;
    left: 12px;
}

.show-icon1 {
    visibility: hidden;
    position: absolute;
    top: 11px;
    left: 12px;
}

.show-icon2 {
    height: 16px;
    width: 16px;
    visibility: hidden;
    position: absolute;
    top: 11px;
    left: 11px;
}

.mat-drawer .btn-container:hover .hide-icon {
    visibility: hidden;
}

.mat-drawer .btn-container:hover .hide-icon2 {
    visibility: hidden;
}

.mat-drawer .btn-container:hover .show-icon {
    visibility: visible;
}

.mat-drawer .btn-container:hover .show-icon1 {
    visibility: visible;
}

.mat-drawer .btn-container:hover .show-icon2 {
    visibility: visible;
}

.mat-drawer .btn-container:hover .tooltip-span {
    visibility: visible;
    opacity: 1;
}

.mat-drawer .btn-container:hover .report-span {
    visibility: visible;
    opacity: 1;
    transition: all .4s;
}

.activeclass {
    background-color: #f3b408;
}

.showclass {
    visibility: visible;
}

.hideclass {
    visibility: hidden;
}

.tooltip-span {
    opacity: 0;
    visibility: hidden;
    white-space: pre-line;
    transition: opacity 0.5s ease;
    color: #000000;
    background: #ffffff;
    border: 1px solid #D8DCDE;
    border-radius: 12px;
    box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
    position: fixed;
    left: -41px;
    margin-left: 100px !important;
    padding: 10px;
    z-index: 9999 !important;
}

.nams-btn-container {
    background-color: #FFFFFF;
    height: 35px;
    width: 34px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0% 1%;
}

.nams-btn-container .tooltip-span::before,
::after {
    visibility: visible;
    opacity: 1;
}

.nams-btn-container::after {
    visibility: visible;
    opacity: 0;
}

.mat-drawer .nams-btn-container:hover .tooltip-span {
    visibility: visible;
    opacity: 1;
}

.mat-drawer .nams-btn-container:hover .hide-icon {
    visibility: hidden;
}

.mat-drawer .nams-btn-container:hover .show-icon {
    visibility: visible;
}

.nams-btn-container:after {
    content: "";
    position: absolute;
    width: 0;
    height: 46;
    border-top: 42px solid transparent;
    border-right: 22px solid #000;
    border-bottom: 51px solid transparent;
    margin-left: 40px;
    margin-top: 4px;
}

.report-span {
    visibility: hidden;
    opacity: 0;
    transition: all .4s 4.6s;
    color: #000000;
    background: #ffffff;
    border: 1px solid #D8DCDE;
    border-radius: 12px;
    box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
    width: max-content;
    height: max-content;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
    position: fixed;
    left: -30px;
    margin-left: 100px !important;
    padding: 10px;
    z-index: 9999 !important;
}

.nams-btn {
    border: none;
    background: none;
    padding-top: 9px;
    font-weight: normal;
}

.complaints-container {
    padding: 0px 0px 5px 0px;
}

.complaints {
    font-weight: normal;
    cursor: pointer;
}