<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">

<app-mat-spinner-overlay overlay="true" *ngIf="loading">
</app-mat-spinner-overlay>

<mat-tab-group #tabGroup class="step-tab-group" [(selectedIndex)]="moveTabIndex">
  <form [formGroup]="step1Form" class="step1Form">
    <mat-tab class="step-tab" label="Step 1" disabled="true">
      <div class="step1-scroll">
        <div class="step1-container">

          <div class="comp-head">
            COMPLAINANT DETAILS
          </div>

          <div class="comp-head-mandatory">
            * labeled fields are mandatory
          </div>

          <div class="row-container" style="margin-top:22px;">
            <div class="input-container">
              <div class="text-container">
                <p>Creating complaint on behalf of <span style="color: red;">*</span></p>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field" required>
                  <mat-select formControlName="complainant" required placeholder="Select" [disabled]="userCreated || nams">
                    <mat-option *ngFor="let person of behalf" (click)="showFields(person, 'html')" [value]="person.ID" [disabled]="person.ID == 6 || person.ID == 7">
                      {{person.USER_TYPE_NAME}}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="!step1Form.get('complainant').valid">
                    Please choose complainant type
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            <div class="input-container" *ngIf="!nams">
              <div class="text-container">
                <p>Phone number <span style="color: red;">*</span></p>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <input matInput formControlName="phone_number" type="number" [matAutocomplete]="autoPhNum"
                    [readonly]="userCreated || suomoto">
                  <mat-autocomplete #autoPhNum="matAutocomplete" (optionSelected)="onSelectionChange($event, 'phNum')">
                    <mat-option *ngFor="let num of phNumList;" [value]="num.MOBILE" style="
                      font-style: normal;
                      font-weight: normal;">
                      {{num.MOBILE}}
                    </mat-option>
                  </mat-autocomplete>
                  <mat-error *ngIf="step1Form.controls['phone_number'].errors?.required">
                    Phone number is required
                </mat-error> 
                <mat-error *ngIf="step1Form.controls['phone_number'].errors?.pattern">
                  Enter valid phone number with length 10
              </mat-error> 
                </mat-form-field>
              </div>
            </div>
          </div>

          <div class="row-container">
            <div class="input-container" *ngIf="!nams">
              <div class="text-container">
                <p>Title <span style="color: red;">*</span></p>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field" style="width: 73px!important;">
                  <mat-select formControlName="salutation" [disabled]="userCreated || suomoto || userExist">
                    <mat-option *ngFor="let title of titles" [value]="title.ID"> {{title.SALUTATION_NAME}}</mat-option>
                  </mat-select>
                  <mat-error *ngIf="!step1Form.get('salutation').valid">
                    Title is required
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
            <div class="input-container" style="position: relative;right: 26px;" *ngIf="!nams">
              <div class="text-container">
                <p>First name <span style="color: red;">*</span></p>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field" style="width:163px!important">
                  <input matInput formControlName="first_name" [readonly]="userCreated || suomoto || userExist"
                    autocomplete="off">
                    <mat-error *ngIf="step1Form.controls['first_name'].errors?.required">
                      First name is required
                    </mat-error>
                    <mat-error *ngIf="step1Form.controls['first_name'].errors?.pattern">
                      Only text is allowed
                    </mat-error>
                    <mat-error *ngIf="step1Form.controls['first_name'].errors?.minlength">
                      First name should be of min length 3 and max length 25
                    </mat-error>
                    <mat-error *ngIf="step1Form.controls['first_name'].errors?.maxlength">
                      First name should be of min length 3 and max length 25
                    </mat-error>
                </mat-form-field>
              </div>
            </div>
            <div class="input-container" style="position: relative;right:25px" *ngIf="!nams">
              <div class="text-container">
                <p>Last name <span style="color: red;">*</span></p>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <input matInput formControlName="last_name" [readonly]="userCreated || suomoto || userExist"
                    autocomplete="off">
                    <mat-error *ngIf="step1Form.controls['last_name'].errors?.required">
                      Last name is required
                    </mat-error>
                    <mat-error *ngIf="step1Form.controls['last_name'].errors?.pattern">
                      Only text is allowed
                    </mat-error>
                    <mat-error *ngIf="step1Form.controls['last_name'].errors?.maxlength">
                      Last name should be of max length 25
                    </mat-error>
                </mat-form-field>
              </div>
            </div>
            <div class="input-container" style="position: relative" [ngStyle]="{'right': (nams == true) ? '0px' : '25px'}" *ngIf="!nams">
              <div class="text-container">
                <p>Email <span style="color: red;">*</span></p>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <input matInput formControlName="email" [readonly]="userCreated || suomoto || userExist"
                    autocomplete="off">
                    <mat-error *ngIf="step1Form.controls['email'].errors?.required">
                      Email is required
                    </mat-error>
                    <mat-error *ngIf="step1Form.controls['email'].errors?.pattern">
                      Please Enter Valid Email id
                    </mat-error>
                </mat-form-field>
              </div>
            </div>
            <div class="input-container" *ngIf="nams">
              <div class="text-container">
                  Organization name
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <input matInput formControlName="organization"
                    autocomplete="off">
                </mat-form-field>
              </div>
            </div>
          </div>

          <div class="row-container" *ngIf="!nams">
            <div class="input-container">
              <div class="text-container">
                <p>State <span style="color: red;">*</span></p>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <mat-select formControlName="state" (selectionChange)="selectState($event)" placeholder="Select state"
                    [disabled]="userCreated || suomoto || userExist">
                    <mat-option *ngFor="let state of stateList" [value]="state.ID">
                      {{state.STATE_TYPE_NAME}}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="!step1Form.get('state').valid">
                    Please choose your state
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
            <div class="input-container">
              <div class="text-container">
                <p>District <span style="color: red;">*</span></p>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <mat-select formControlName="district" placeholder="Select district"
                    [disabled]="userCreated || suomoto || userExist">
                    <mat-option *ngFor="let district of districtList" [value]="district.ID">
                      {{district.DISTRICT_TYPE_NAME}}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="!step1Form.get('district').valid">
                    Please choose your district
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
            <div class="input-container">
              <div class="text-container">
                <p> City</p>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <input matInput formControlName="city" [readonly]="userCreated || suomoto || userExist"
                    autocomplete="off">
                </mat-form-field>
              </div>
            </div>
          </div>

          <div class="row-container" *ngIf="!nams">
            <div class="input-container">
              <div class="text-container">
                <p>Address</p>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <input matInput formControlName="address" [readonly]="userCreated || suomoto || userExist"
                    autocomplete="off">
                </mat-form-field>
              </div>
            </div>
            <div class="input-container">
              <div class="text-container">
                <p>Postal code <span style="color: red;">*</span></p>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <input matInput type="number" formControlName="postal_code"
                    [readonly]="userCreated || suomoto || userExist" autocomplete="off" required>
                    <mat-error *ngIf="step1Form.controls['postal_code'].errors?.required">
                      Postal Code is required
                    </mat-error>
                    <mat-error *ngIf="step1Form.controls['postal_code'].errors?.pattern">
                      Enter 6 digit postal code
                    </mat-error>
                </mat-form-field>
              </div>
            </div>
            <div class="input-container">
              <div class="text-container">
                <p>Contact method <span style="color: red;">*</span></p>
              </div>
              <div class="control-container">
                <mat-form-field appearance="outline" class="input-field">
                  <mat-select [(ngModel)]="contactMetId" formControlName="contact_method" placeholder="Select method"
                    [disabled]="userCreated">
                    <mat-option *ngFor="let contactType of contactMethod" [value]="contactType.ID">
                      {{contactType.CONTACT_METHOD_TYPE_NAME}}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="!step1Form.get('contact_method').valid">
                    Please choose contact method
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
          </div>

          <div class="row-container" *ngIf="!nams">
            <span *ngIf="isSelected" class="isSelected-span">
              <div class="input-container">
                <div class="text-container">
                  <p *ngIf="selectedUserTypeID == 1">
                    Organization name
                  </p>
                  <p *ngIf="selectedUserTypeID == 2">Company / Organization name<span style="color: red;">*</span></p>
                  <p *ngIf="selectedUserTypeID == 3">Government Body Name<span style="color: red;">*</span></p>
                  <p *ngIf="selectedUserTypeID == 4">Consumer Organization / Industry name</p>
                </div>
                <div class="control-container"
                  *ngIf="selectedUserTypeID == 1 || selectedUserTypeID == 4">
                  <mat-form-field appearance="outline" class="input-field">
                    <input matInput formControlName="organization" [readonly]="userCreated || suomoto || userExist"
                      autocomplete="off">
                  </mat-form-field>
                </div>
                <div class="control-container"
                  *ngIf="selectedUserTypeID == 3">
                  <mat-form-field appearance="outline" class="input-field">
                    <mat-select formControlName="govt_body" [readonly]="userCreated || suomoto || userExist">
                      <mat-option *ngFor="let govt of govtBody" [value]="govt.ID">
                        {{govt.GOVERNMENT_DEPARTEMENT_NAME}}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
                <div class="control-container" *ngIf="selectedUserTypeID == 2">
                  <mat-form-field appearance="outline" class="input-field">
                    <input matInput type="text" formControlName="companyID" name="companyname"
                      [matAutocomplete]="autoCompany" [readonly]="userCreated || suomoto || userExist" />
                  </mat-form-field>
                  <mat-autocomplete #autoCompany="matAutocomplete"
                    (optionSelected)="onSelectionChange($event, 'company')">
                    <mat-option *ngFor="let company of companyList;" [value]="company.COMPANY_NAME" style="
                      font-style: normal;
                      font-weight: normal;">
                      {{company.COMPANY_NAME}}
                    </mat-option>
                  </mat-autocomplete>
                </div>
              </div>
              <div class="input-container"
                *ngIf="!(selectedUserTypeID == 5 || selectedUserTypeID == 6 || selectedUserTypeID == 7)" style="margin-left:30px">
                <div class="text-container">
                  <p>Profession</p>
                </div>
                <div class="control-container">
                  <mat-form-field appearance="outline" class="input-field">
                    <input matInput formControlName="profession" [readonly]="userCreated || suomoto || userExist"
                      autocomplete="off">
                  </mat-form-field>
                </div>
              </div>
            </span>
          </div>

          <div class="divider-container">
            <mat-divider></mat-divider>
          </div>

          <div class="btn-container">
            <div class="next-container">
              <button mat-flat-button class="next-btn" color="warn" [disabled]="step1Form.invalid"
                (click)="step1Next(step1Form.value)">
                <span class="bolder">Next</span>
              </button>
            </div>
            <div class="cancel-container">
              <button mat-stroked-button class="cancel-btn" (click)="cancel()">
                <span class="bolder">Cancel</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </mat-tab>
  </form>

  <mat-tab disabled="true">
    <div>
      <ng-template mat-tab-label>
        <mat-icon style="color: rgb(185, 185, 185);">arrow_right_alt</mat-icon>
      </ng-template>
    </div>
  </mat-tab>

  <mat-tab class="step-tab" label="Step 2" disabled="true">
    <div *ngIf="company">
      <app-intra-industry (outFilter)="setFromFilterOutput($event)"></app-intra-industry>
    </div>
    <div *ngIf="consumer">
      <app-general-public (outFilter)="setFromFilterOutput($event)"></app-general-public>
    </div>
    <div *ngIf="nams">
      <app-nams-form (outFilter)="setFromFilterOutput($event)"></app-nams-form>
    </div>
  </mat-tab>

  <mat-tab disabled="true">
    <div>
      <ng-template mat-tab-label>
        <mat-icon style="color: rgb(185, 185, 185);">arrow_right_alt</mat-icon>
      </ng-template>
    </div>
  </mat-tab>

  <mat-tab class="step-tab" label="Step 3" disabled="true">
    <div class="step3-container">
      <div class="step3-content-container">
        <div class="line-container">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Created on</span>
              <mat-label class="value-label">{{rec_date}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container"  *ngIf="!nams">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Complainant name</span>
              <mat-label class="value-label">{{comp_fname}} {{comp_lname}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container"   *ngIf="!nams">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Contact method</span>
              <mat-label for="comp_name" class="value-label">{{source}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container"   *ngIf="!nams">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Complainant mobile number</span>
              <mat-label class="value-label">{{comp_phone}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="!nams">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Complaint against</span>
              <mat-label class="value-label" [matTooltip]="comp_against_company">{{comp_against_company | slice:0:
                40}}{{againstText}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams && nams_step === 'tams'">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span" *ngIf="nams && nams_step === 'tams'">Advertiser</span>
              <mat-label class="value-label" [matTooltip]="comp_against_company">{{comp_against_company | slice:0:
                40}}{{againstText}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams && nams_step === 'reech'">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Publication</span>
              <mat-label class="value-label">{{publication}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams && nams_step === 'reech'">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Medium</span>
              <mat-label class="value-label">Digital media</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams && nams_step === 'reech'">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Influencer</span>
              <mat-label class="value-label">{{influencer}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams && nams_step === 'reech'">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Profile URL</span>
              <mat-label class="value-label">{{profile_url}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams && nams_step === 'reech'">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Influencer contact no.</span>
              <mat-label class="value-label">{{influencer_contact_no}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams && nams_step === 'reech'">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Influencer email address</span>
              <mat-label class="value-label">{{influencer_email_address}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams && nams_step === 'reech'">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Platform</span>
              <mat-label class="value-label">{{network}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams && nams_step === 'tams'">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Brand</span>
              <mat-label class="value-label">{{comp_against_product}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams && nams_step === 'tams'">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Media Outlet</span>
              <mat-label class="value-label">{{media_outlet}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams && nams_step === 'tams'">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Product category</span>
              <mat-label class="value-label">{{product_category}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams && nams_step === 'tams'">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Media</span>
              <mat-label class="value-label">{{media}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="!nams">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Complaint details</span>
              <mat-label class="value-label" [matTooltip]="comp_details">{{comp_details | slice:0: 40}}{{detailText}}
              </mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="consumer">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Classification</span>
              <mat-label class="value-label">{{classfy}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="!nams">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Advertisement spotted on</span>
              <mat-label class="value-label" style="margin-left: -19px;">{{medium}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="!nams && (selectedAdvId != 4 && selectedAdvId != 6 && selectedAdvId != 7)">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span" *ngIf="selectedAdvId == 1 || selectedAdvId == 2">Channel</span>
              <span class="attribute-span" *ngIf="selectedAdvId == 3">Platform</span>
              <span class="attribute-span" *ngIf="selectedAdvId == 5">Seen medium</span>
              <!-- <span class="attribute-span" *ngIf="selectedAdvId == 6">Type of promotional material</span> -->
              <span class="attribute-span" *ngIf="selectedAdvId == 8">Sender</span>
              <span class="attribute-span" *ngIf="selectedAdvId == 9">Source</span>
              <mat-label class="value-label" *ngIf="selectedAdvId != 3 && selectedAdvId != 5 && selectedAdvId != 6">
                {{channel}}</mat-label>
              <mat-label class="value-label" *ngIf="selectedAdvId == 3">{{platform}}</mat-label>
              <mat-label class="value-label" *ngIf="selectedAdvId == 5">{{printMedium}}</mat-label>
              <!-- <mat-label class="value-label" *ngIf="selectedAdvId == 6">{{promotionalMaterial}}</mat-label> -->
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="!nams && (selectedAdvId == 4 || selectedAdvId == 5 || selectedAdvId == 6)">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span" *ngIf="selectedAdvId == 4">Place where you saw the hoarding</span>
              <span class="attribute-span" *ngIf="selectedAdvId == 5">Publication name & edition</span>
              <span class="attribute-span" *ngIf="selectedAdvId == 6">Place where you saw the ad</span>
              <mat-label class="value-label" *ngIf="selectedAdvId != 5">{{sourcePlace}}</mat-label>
              <mat-label class="value-label" *ngIf="selectedAdvId == 5">{{channel}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span" *ngIf="!nams">Advertisement seen date</span>
              <span class="attribute-span" *ngIf="nams">Date</span>
              <mat-label class="value-label" *ngIf="!nams">{{adv_seen_date}}</mat-label>
              <mat-label class="value-label" *ngIf="nams">{{namsDate}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">File attached</span>
              <mat-label class="value-label">{{documents}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams && nams_step === 'tams'">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Ad language</span>
              <mat-label class="value-label">{{ad_language}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="!nams">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Claims raised</span>
              <mat-label class="value-label">{{claims}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Media link</span>
              <mat-label class="value-label" [matTooltip]="media_link">
                <a (click)="openNewTab(media_link)" class="media-anchor">
                  {{media_link | slice:0:40}}{{medialLinkText}}
                </a>
              </mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams && nams_step === 'tams'">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Creative ID</span>
              <mat-label class="value-label">{{creative_id}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams && nams_step === 'tams'">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Duration</span>
              <mat-label class="value-label" [matTooltip]="duration">{{!duration ? '-' :  duration}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams && nams_step === 'tams'">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Transcription</span>
              <mat-label class="value-label" [matTooltip]="transcription">{{!transcription ? '-' : transcription | slice:0:40}}{{transcriptionText}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="nams">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">Complaint description</span>
              <mat-label class="value-label" [matTooltip]="comp_desc">{{comp_desc | slice:0:40}}{{compDescText}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>

        <div class="line-container" *ngIf="!(consumer || nams)">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">ASCI code violated</span>
              <mat-label class="value-label" *ngFor="let code of code_violated; let isFirst=first">Chapter {{code.CHAPTER_ID}} {{isFirst ? '  ' : ', '}}</mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>
        <div class="line-container" *ngIf="!(consumer || nams)">
          <div mat-line class="entry-container">
            <p class="line-text">
              <span class="attribute-span">ASCI Guidelines Violated</span>
              <mat-label class="value-label" *ngFor="let guideline of guidelines;let isFirst=first">
                <span *ngIf="guideline.G_CHAPTER_ID != 1">
                  Guideline {{guideline.G_CHAPTER_ID - 1}} {{isFirst ? '  ' : ', '}}
                </span>
                <span *ngIf="guideline.G_CHAPTER_ID == 1">
                  N/A
                </span>
              </mat-label>
            </p>
            <mat-divider class="line-divider"></mat-divider>
          </div>
        </div>
      </div>

      <div class="divider-container">
        <mat-divider></mat-divider>
      </div>

      <div class="btn-container">
        <div class="submit-container">
          <button mat-flat-button class="submit-btn" type="submit" color="warn" (click)="submitForm()">
            <span class="bolder">Submit</span>
          </button>
        </div>
        <div class="back-container">
          <button mat-stroked-button class="back-btn" (click)="tabGroup.selectedIndex=2">
            <span class="bolder">Back</span>
          </button>
        </div>
      </div>
    </div>
  </mat-tab>
</mat-tab-group>
