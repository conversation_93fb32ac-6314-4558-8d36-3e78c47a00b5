
<mat-toolbar class="toolbar">
  <h3 matSubheader class="inbox-head">Inbox</h3>
  <span class="toolbar-spacer"></span>
<div class="toolbar-btns" fxLayoutAlign="end center">
  <div class="search-div" fxLayout="row" fxLayoutGap="1%">
    <div class="before-search-container" *ngIf="!isSearch">
      <button mat-icon-button class="search-btn" (click)="search()" aria-label="Search icon">
        <mat-icon>search</mat-icon>
      </button>
    </div>
    <div class="after-search-container" *ngIf="isSearch" >
        <!-- <mat-form-field appearance="outline" class="input-field"> -->
          <!-- <div class="search-icon-container"> -->
            <!-- <button  matPrefix mat-icon-button class="searchbar-btn" id="searchbar-btn">
              <mat-icon class="search-icons" >search</mat-icon>
            </button> -->
<div class="input-container">
            <button  matPrefix mat-icon-button style="color:rgb(189, 189, 189)">
              <mat-icon class="toolbar-search-icon">search</mat-icon>
              <!-- <img class="hide-icon" src="../assets/images/toolbar_search-icon.png"/> -->
            </button>
          <!-- </div> -->
          <!-- <div class="input-container"> -->
            <!-- <input matInput type="text" class="search-input"> -->
            <!-- <input type="text" class="input-search" style="flex: 1;font-size: 1rem;"> -->
            <input matInput type="text" class="input-search">
          <!-- </div> -->
          <!-- <div class="close-container"> -->
            <!-- <button matSuffix mat-icon-button (click)="search()" class="searchbar-btn" id="close-btn">
              <mat-icon class="search-icons">close</mat-icon>
            </button> -->
            <button matSuffix mat-icon-button (click)="search()" style="color:rgb(189, 189, 189)">
              <mat-icon matSuffix>close</mat-icon>
            </button>
</div>
          <!-- </div> -->
        <!-- </mat-form-field> -->
    </div>
  </div>
  <div class="bell-div">
    <button mat-icon-button class="bell-btn" aria-label="bell notification icon">
      <!-- <mat-icon matBadge="0" matBadgeColor="warn">notifications_none</mat-icon> -->
      <mat-icon>notifications_none</mat-icon>
    </button>
  </div>
  <div class="admin-div">
    <button mat-button [matMenuTriggerFor]="admin" class="admin-btn">Admin<mat-icon>arrow_drop_down</mat-icon></button>
        <mat-menu #admin="matMenu">
            <div class="admin-option-container">
              <button mat-menu-item class="option-btn">
                <span class="option-text">My profile</span>
              </button>
              <!-- <button mat-menu-item class="option-btn">
                <span class="option-text">Preferences</span>
              </button> -->
              <button mat-menu-item class="option-btn">
                <span class="option-text">Change password</span>
              </button>
              <!-- <button mat-menu-item class="option-btn">
                <span class="option-text">My activity log</span>
              </button> -->
                  <mat-divider style="margin: 0% 5%;"></mat-divider>
              <button mat-menu-item class="option-btn" (click)='logout()'>
                <span class="option-text">Log out</span>
              </button>
            </div>
          
        </mat-menu>


  </div>
</div>

</mat-toolbar>
<mat-divider></mat-divider>
