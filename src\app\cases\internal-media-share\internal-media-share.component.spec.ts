import { ComponentFixture, TestBed } from '@angular/core/testing';

import { InternalMediaShareComponent } from './internal-media-share.component';

describe('InternalMediaShareComponent', () => {
  let component: InternalMediaShareComponent;
  let fixture: ComponentFixture<InternalMediaShareComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ InternalMediaShareComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(InternalMediaShareComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
