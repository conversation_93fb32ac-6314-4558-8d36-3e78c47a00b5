<div class="body" fxLayout="column">
  <div fxLayout="column" class="header">
    <div class="heading-container">
      <h3 class="heading">Related Complaints</h3>
    </div>
    <div class="head-contents">
      We found related content to this complaint, you can view this complaint details here or can create a separate complaint from this
    </div>
  </div>

  <div class="similar-content">
    <mat-card *ngIf="loading" style="display: flex; justify-content: center; align-items: center; background: white; box-shadow: none;">
      <mat-progress-spinner color="primary" mode="indeterminate" diameter="70">
      </mat-progress-spinner>
    </mat-card>
    <mat-card class="comp-matcard" *ngIf="!loading">
      <div fxLayout="row">
        <div fxFlex="30%" class="left-panel">
          <div *ngFor="let similarComp of similarityComplaintList; let in = index;" [ngClass]="{'chat-item-selected': in == advIndex && matchFound}">
            <div class="classfy-container" fxLayout="column" fxLayoutGap="2px" (click)="selectDetails(similarComp?.complaint_identification, in)">
              <div class="related-name-container" fxLayout="row" fxLayoutGap="2%">
                <h4 for="message">
                  <img src="../assets/images/arrow_circle-icon.svg" />
                  <span class="related-name" *ngIf="!matchFound">{{similarComp?.complaint_identification}}</span>
                  <span class="related-name" *ngIf="matchFound">{{similarComp?.prediction}} -
                    {{similarComp?.similarity_score}}% Matching</span>
                </h4>
              </div>
              <div mat-line class="related-detail-container" *ngIf="matchFound">
                <div fxLayout="row">
                  <div class="related-attribute">Case ID : </div>
                  <div fxFlex="8%"></div>
                  <div class="related-value">{{similarComp?.complaint_identification}}</div>
                </div>
                <div fxLayout="row" *ngIf="similarComp?.old_complaint_identification != 'None'" style="margin-top: 2%;">
                  <div fxFlex="33%"></div>
                  <div class="related-value">({{similarComp?.old_complaint_identification}})</div>
                </div>
              </div>
              <div mat-line class="related-detail-container" *ngIf="matchFound">
                <div fxLayout="row">
                  <div class="related-attribute">Company : </div>
                  <div fxFlex="4.5%"></div>
                  <div class="related-value" style="text-align: end;">{{similarComp?.company_name | slice:0:
                    20}}{{similarComp?.company_name.length>21? '..':''}}</div>
                </div>
              </div>
              <div mat-line class="related-detail-container" *ngIf="matchFound">
                <div fxLayout="row">
                  <div class="related-attribute">Brand : </div>
                  <div fxFlex="12.5%"></div>
                  <div class="related-value" style="text-align: end;">{{similarComp?.brand_name | slice:0:
                    20}}{{similarComp?.brand_name.length>21? '..':''}}</div>
                </div>
              </div>
              <div mat-line class="related-detail-container" *ngIf="matchFound">
                <div fxLayout="row">
                  <div class="related-attribute">Product : </div>
                  <div fxFlex="9%"></div>
                  <div class="related-value" style="text-align: end;">{{similarComp?.product_name | slice:0:
                    20}}{{similarComp?.product_name.length>21? '..':''}}</div>
                </div>
              </div>
              <div mat-line class="related-detail-container" *ngIf="matchFound">
                <div fxLayout="row">
                  <div class="related-attribute">Description : </div>
                  <div fxFlex="1%"></div>
                  <div class="related-value" [matTooltip]="similarComp?.user_description"
                    style="text-align: end;">{{similarComp?.user_description | slice:0:20}}</div>
                </div>
              </div>
              <button mat-button class="add-btn" (click)="addNewComplaint(similarComp)" *ngIf="matchFound">
                <mat-icon style="font-size: 15px;font-weight: bold; padding-top: 3px;">add</mat-icon>Add to this complaint
              </button>
            </div>

            <!-- <div class="chips-btn-container" fxLayout="row" fxLayoutGap="10px" *ngIf="matchFound">
              <div class="relcomp-container" *ngIf="extraSimilarComplaints > 0">
                <mat-chip-list>
                  <div fxLayout="row" fxLayoutGap="-15px">
                    <mat-chip class="person-chips" id="person-chip1" (click)='showFilter(msgvalue.company)' for="message">
                      {{extraSimilarComplaints}}
                    </mat-chip>
                  </div>
                </mat-chip-list>
              </div>
              <button mat-button class="add-btn" (click)="addNewComplaint()">
                <mat-icon style="font-size: 15px; padding-top: 3px;">add</mat-icon>Add to this
              </button>
            </div> -->
          </div>
          <div *ngIf="similarityComplaintList.length == 0">
            <button mat-stroked-button (click)="retry_btn()" *ngIf="retry">Retry</button>
          </div>
        </div>
        <div class="divider" *ngIf="matchFound"></div>
        <div fxFlex="70%" class="right-panel" *ngIf="matchFound">
          <div fxLayout="column">
            <div class="contents-scroll" *ngIf="compDetails.length == 0">
              <div class="details-container">
                  <p style="font-size: 15px; color: rgb(121, 120, 120); padding-left: 300px; padding-top: 20px;">Complaint does not exist...</p>
              </div>
            </div>
            <div>
              <div style=" margin-left: 10px;" *ngIf="compDetails.length != 0">
                <div fxLayout="row" fxLayoutGap="100px">
                  <div mat-line style="width:320px">
                    <p>
                      <span class="detail-attribute">Case ID : </span>
                      {{compDetails.CASE_ID}}
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Tracking ID : </span>
                      {{compDetails.ID}}
                    </p>
                  </div>
                </div>
                <div fxLayout="row" fxLayoutGap="100px">
                  <div mat-line style="width:320px" *ngIf="detail_company">
                    <p>
                      <span class="detail-attribute">Product : </span>
                      {{detail_company}}
                    </p>
                  </div>
                  <div mat-line *ngIf="classification_name">
                    <p>
                      <span class="detail-attribute">Classification : </span>
                      {{classification_name}}
                    </p>
                  </div>
                </div>
                <div fxLayout="row" fxLayoutGap="100px">
                  <div mat-line style="width:320px">
                    <p>
                      <span class="detail-attribute">Contact : </span>
                      {{compDetails.MOBILE}}
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Email Id : </span>
                      {{compDetails.EMAIL_ID}}
                    </p>
                  </div>
                </div>
                <div fxLayout="row" fxLayoutGap="100px" *ngFor="let comp of company_details; let i=index;">
                  <div mat-line style="width: 320px;">
                    <p>
                      <span class="detail-attribute">Advertiser company {{i+2}} : </span>
                      {{comp.COMPANY_NAME}}
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Advertiser company {{i+2}} <br> email : </span>
                      {{!!comp.EMAIL_ID ? comp.EMAIL_ID : 'Not available'}}
                    </p>
                  </div>
                </div>
                <div *ngFor="let source of detail_adsource; let i=index;">
                  <div fxLayout="row" fxLayoutGap="100px">
                    <div mat-line style="width: 320px;" *ngIf="source.ADVERTISEMENT_SOURCE_ID != 0">
                      <p>
                        <span class="detail-attribute" *ngIf="detail_adsource.length > 1">Media source {{i+1}} :
                        </span>
                        <span class="detail-attribute" *ngIf="detail_adsource.length == 1">Media source : </span>
                        <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 1">Television</span>
                        <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 2">Radio</span>
                        <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3">Digital Media</span>
                        <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 4">Hoardings</span>
                        <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 5">Print</span>
                        <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 6">Promotional Material</span>
                        <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 7">Packaging</span>
                        <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 8">SMS</span>
                        <span *ngIf="source.ADVERTISEMENT_SOURCE_ID == 9">Others</span>
                      </p>
                    </div>
                    <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3 && complaint_source_id != 8" style="margin-left: 100px;">
                      <p>
                        <span class="detail-attribute">Platform name : </span>
                        <span *ngIf="source.PLATFORM_ID  == 1">Facebook</span>
                        <span *ngIf="source.PLATFORM_ID  == 2">Instagram</span>
                        <span *ngIf="source.PLATFORM_ID  == 3">YouTube</span>
                        <span *ngIf="source.PLATFORM_ID  == 4">Twitter</span>
                        <span *ngIf="source.PLATFORM_ID  == 5">LinkedIn</span>
                        <span *ngIf="source.PLATFORM_ID  == 6">Website</span>
                        <span *ngIf="source.PLATFORM_ID  == 7">Google Ad</span>
                        <span *ngIf="source.PLATFORM_ID  == 8">Mobile App</span>
                        <span *ngIf="source.PLATFORM_ID  == 9">Others</span>
                      </p>
                    </div>
                    <div mat-line *ngIf="complaint_source_id == 8" style="margin-left: 100px;">
                      <p>
                        <span class="detail-attribute">Platform name : </span>
                        <span>{{network}}</span>
                      </p>
                    </div>
                    <div mat-line
                      *ngIf="source.ADVERTISEMENT_SOURCE_ID== 1 || source.ADVERTISEMENT_SOURCE_ID == 2"
                      style="margin-left: 100px;">
                      <p>
                        <span class="detail-attribute">Channel name : </span>
                        {{source.SOURCE_NAME}}
                      </p>
                    </div>
                    <div mat-line
                      *ngIf="source.ADVERTISEMENT_SOURCE_ID == 4 || source.ADVERTISEMENT_SOURCE_ID == 6 "
                      style="margin-left: 100px;">
                      <p>
                        <span class="detail-attribute">Place : </span>
                        {{source.SOURCE_PLACE}}
                      </p>
                    </div>
                    <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 5" style="margin-left: 100px;">
                      <p>
                        <span class="detail-attribute">Print source : </span>
                        {{source.SOURCE_NAME}}
                      </p>
                    </div>
                    <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 7" style="margin-left: 100px;">
                      <p>
                        <span class="detail-attribute">MFD/PKD Date : </span>
                        {{source.DATE| date:'dd/MM/yyyy'}}
                      </p>
                    </div>
                    <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 8" style="margin-left: 100px;">
                      <p>
                        <span class="detail-attribute">Sender : </span>
                        {{source.SOURCE_NAME}}
                      </p>
                    </div>
                    <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 9" style="margin-left: 100px;">
                      <p>
                        <span class="detail-attribute">Source : </span>
                        {{source.SOURCE_NAME}}
                      </p>
                    </div>
                    <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID != 0 && !source.ADVERTISEMENT_SOURCE_ID"
                      style="margin-left: 100px;">
                      <p>
                        <span class="detail-attribute">Source : </span>
                      </p>
                    </div>
                  </div>
                  <div fxLayout="row" fxLayoutGap="100px"
                    *ngIf="complaint_source_id != 7 && complaint_source_id != 8">
                    <div mat-line style="width: 320px;"
                      *ngIf="source.SOURCE_URL != '' && source.SOURCE_URL != null">
                      <p>
                        <span class="detail-attribute" *ngIf="detail_adsource.length > 1">Media URL {{i+1}} :
                        </span>
                        <span class="detail-attribute" *ngIf="detail_adsource.length == 1">Media URL : </span>
                        <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(source.SOURCE_URL)"
                          class="media-anchor" matTooltip="{{source.SOURCE_URL}}">{{source.SOURCE_URL | slice:0:
                          25}}{{source.SOURCE_URL.length>26? '..':''}} </a>
                      </p>
                    </div>
                    <div mat-line *ngIf="source.ADVERTISEMENT_SOURCE_ID == 3 && source.PLATFORM_ID == 9"
                      style="margin-left: 100px;">
                      <p>
                        <span class="detail-attribute">Source : </span>
                        {{source.SOURCE_PLACE}}
                      </p>
                    </div>
                  </div>
                </div>
                <div fxLayout="row" fxLayoutGap="100px">
                  <div mat-line style="width: 320px;">
                    <p>
                      <span class="detail-attribute">Complaint via : </span>
                      {{compDetails.COMPLAINT_SOURCE_NAME }}
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Registered date : </span>
                      {{comp_date | date:'dd/MM/yyyy'}} - {{comp_date | date:'HH:mm'}}
                    </p>
                  </div>
                </div>
                <div fxLayout="row" fxLayoutGap="100px">
                  <div mat-line style="width: 320px;">
                    <p>
                      <span class="detail-attribute">Created Date : </span>
                      {{detail_date | date:'dd/MM/yyyy'}} - {{detail_date | date:'HH:mm'}}
                    </p>
                  </div>
                </div>
                <div fxLayout="row" fxLayoutGap="100px">
                  <div mat-line style="width: 291px;" *ngIf="detail_addate">
                    <p>
                      <span class="detail-attribute">Advertisement Seen Date : </span>
                      {{detail_addate| date:'dd/MM/yyyy'}} <span *ngIf="detail_adtime">- {{detail_adtime}}</span>
                    </p>
                  </div>
                  <div mat-line style="width: 320px;" *ngIf="seen_date && (detail_addate == '' || detail_addate == null)">
                    <p>
                      <span class="detail-attribute">Advertisement Seen Date : </span>
                      {{seen_date| date:'dd/MM/yyyy'}} <span *ngIf="seen_time">- {{seen_time}}</span>
                    </p>
                  </div>
                </div>
                <div fxLayout="row" fxLayoutGap="100px">
                  <div mat-line *ngIf="complaint_source_id != 7 && complaint_source_id != 8">
                    <p *ngIf="noOfDocs != 0">
                      <span class="detail-attribute">Media file : </span>
                      <a style="font-size: 13px; cursor: pointer;" (click)="openNewTab()"
                        class="media-anchor">{{noOfDocs}} {{noOfDocs > 1 ? 'files': 'file'}} <img
                          src="../../assets/images/media_download.svg"
                          style="position: relative;left:8px;bottom:1px"></a>
                    </p>
                    <!-- <p *ngIf="noOfDocs == 0 && detail_link">
                      <span class="detail-attribute">Media file : </span>
                      <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(detail_link)" class="media-anchor"
                        matTooltip="{{detail_link}}">{{detail_link | slice:0: 28}}{{longText4}}</a>
                    </p> -->
                  </div>
                  <div mat-line style="width: 320px;" *ngIf="complaint_source_id == 7">
                    <p>
                      <span class="detail-attribute">Translation hyperlink : </span>
                      <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(translation_hyper)"
                        class="media-anchor" matTooltip="{{translation_hyper}}">{{translation_hyper | slice:0:
                        25}}{{longText5}}</a>
                    </p>
                  </div>
                  <div mat-line *ngIf="complaint_source_id == 7">
                    <p>
                      <span class="detail-attribute">Creative Id : </span>
                      {{creative_id}}
                    </p>
                  </div>
                  <div mat-line style="width: 320px;" *ngIf="complaint_source_id == 8">
                    <p>
                      <span class="detail-attribute">Influencer name : </span>
                      {{influencer_name}}
                    </p>
                  </div>
                  <div mat-line *ngIf="complaint_source_id == 8">
                    <p style="margin-left: 100px;">
                      <span class="detail-attribute">Engagements : </span>
                      {{engagements}}
                    </p>
                  </div>
                </div>
                <div fxLayout="row" fxLayoutGap="100px" *ngIf="complaint_source_id == 8">
                  <div mat-line style="width: 320px;" *ngIf="complaint_source_id == 8">
                    <p>
                      <span class="detail-attribute">Influencer contact no. : </span>
                      {{influencer_contact}}
                    </p>
                  </div>
                  <div mat-line *ngIf="complaint_source_id == 8">
                    <p style="margin-left: 100px;">
                      <span class="detail-attribute">Influencer email : </span>
                      {{influencer_email}}
                    </p>
                  </div>
                </div>
                <div fxLayout="row" fxLayoutGap="100px" *ngIf="complaint_source_id == 7">
                  <div mat-line style="width:320px">
                    <p>
                      <span class="detail-attribute">Media outlet : </span>
                      <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(media_outlet)"
                        class="media-anchor" matTooltip="{{media_outlet}}">{{media_outlet | slice:0:
                        27}}{{longText8}}</a>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Media : </span>
                      {{media}}
                    </p>
                  </div>
                </div>
                <div fxLayout="row" fxLayoutGap="100px" *ngIf="complaint_source_id == 8">
                  <div mat-line style="width:320px">
                    <p>
                      <span class="detail-attribute">Publication URL : </span>
                      <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(publication_url)"
                        class="media-anchor" matTooltip="{{publication_url}}">{{publication_url | slice:0:
                        26}}{{publication_url.length>27? '..':''}}</a>
                    </p>
                  </div>
                  <div mat-line>
                    <p>
                      <span class="detail-attribute">Profile URL : </span>
                      <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(profile_url)"
                        class="media-anchor" matTooltip="{{profile_url}}">{{profile_url | slice:0:
                        24}}{{profile_url.length>25? '..':''}}</a>
                    </p>
                  </div>
                </div>
                <div fxLayout="row" *ngIf="complaint_source_id == 7" fxLayoutGap="100px">
                  <div mat-line style="width:320px" *ngIf="suppliment != null">
                    <p>
                      <span class="detail-attribute">Supplement : </span>
                      {{suppliment}}
                    </p>
                  </div>
                  <div mat-line *ngIf="edition != null">
                    <p>
                      <span class="detail-attribute">Edition : </span>
                      {{edition}}
                    </p>
                  </div>
                </div>
                <div fxLayout="row" *ngIf="complaint_source_id == 7" fxLayoutGap="100px">
                  <div mat-line style="width:320px">
                    <p>
                      <span class="detail-attribute">Ad language : </span>
                      {{ad_language}}
                    </p>
                  </div>
                  <div mat-line>
                    <p matTooltip="{{super_category}}">
                      <span class="detail-attribute">Product category : </span>
                      {{super_category | slice:0:30}}{{super_category.length>31? '..':''}}
                    </p>
                  </div>
                </div>
                <div fxLayout="row" *ngIf="complaint_source_id == 7" fxLayoutGap="100px">
                  <div mat-line style="width:320px">
                    <p>
                      <span class="detail-attribute">Duration : </span>
                      {{duration}}
                    </p>
                  </div>
                </div>
                <div fxLayout="row" fxLayoutGap="100px"
                  *ngIf="complaint_source_id == 7 || complaint_source_id == 8">
                  <div mat-line style="width:320px" *ngIf="noOfDocs != 0">
                    <p>
                      <span class="detail-attribute">Media file : </span>
                      <a style="font-size: 13px; cursor: pointer;" (click)="openNewTab()"
                        class="media-anchor">{{noOfDocs}} {{noOfDocs > 1 ? 'files': 'file'}} <img
                          src="../../assets/images/media_download.svg"
                          style="position: relative;left:8px;bottom:1px"></a>
                    </p>
                  </div>
                  <div mat-line *ngIf="noOfDocs != 0">
                    <p *ngIf="similar_detail_link">
                      <span class="detail-attribute">Media URL : </span>
                      <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(similar_detail_link)"
                        class="media-anchor" matTooltip="{{similar_detail_link}}">{{similar_detail_link | slice:0:
                        28}}{{longText4}}</a>
                    </p>
                  </div>
                  <div mat-line *ngIf="noOfDocs == 0">
                    <p *ngIf="similar_detail_link">
                      <span class="detail-attribute">Media URL : </span>
                      <a style="font-size: 13px; cursor: pointer;" (click)="previewLink(similar_detail_link)"
                        class="media-anchor" matTooltip="{{similar_detail_link}}">{{similar_detail_link | slice:0:
                        28}}{{longText4}}</a>
                    </p>
                  </div>
                </div>
                <div mat-line *ngIf="complaint_source_id == 7 && transcription">
                  <p>
                    <span class="detail-attribute">Transcription :</span>
                  </p>
                </div>
                <div mat-line class="comp-msg-container" *ngIf="complaint_source_id == 7 && transcription">
                  <p class="comp-msg" [innerHTML]="safeHTML(transcription)">
                    {{transcription}}
                  </p>
                </div>
                <div mat-line *ngIf="detail_advert">
                  <p>
                    <span class="detail-attribute">Advertisement description :</span>
                  </p>
                </div>
                <div mat-line class="comp-msg-container" *ngIf="detail_advert">
                  <p class="comp-msg" [innerHTML]="safeHTML(detail_advert)">
                    {{detail_advert}}
                  </p>
                </div>
                <div mat-line>
                  <p>
                    <span class="detail-attribute">Objectionable frames :</span>
                  </p>
                </div>
                <div mat-line class="comp-msg-container">
                  <p class="comp-msg" [innerHTML]="safeHTML(detail_complaint)">
                    {{detail_complaint}}
                  </p>
                </div>
                <div mat-line class="comp-msg-container">
                  <p class="comp-msg">
                  </p>
                </div>
              </div>

              <div class="comp-tab-container" *ngIf="compDetails.length != 0">
                <mat-tab-group animationDuration="0ms" class="detail-subtab">
                  <mat-tab label="Documents">
                    <div class="doc-container" fxLayout="row" fxLayoutGap="4%">
                      <div class="content-head-container">
                        <mat-tab-group mat-align-tabs="end">
                          <mat-tab label="Complainant">
                            <mat-accordion multi *ngIf="adDocs.length != 0">
                              <mat-expansion-panel class="intra-expansion" (opened)="panelOpenState = true"
                                (closed)="panelOpenState = false" *ngFor="let docs of adMedium">
                                <mat-expansion-panel-header class="panel-header" *ngIf="adMedium.length != 0">
                                  <mat-panel-title class="panel-title"
                                    *ngIf="complaint_source_id != 7 && complaint_source_id != 8 && adDocs.length != 0">
                                    <div fxLayout="row">
                                      <div class="attribute-container1">
                                        <p>
                                          <span class="grey-text">Medium : </span>
                                          <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '1'">Television</span>
                                          <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '2'">Radio</span>
                                          <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '3'">Digital Media</span>
                                          <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '4'">Hoardings</span>
                                          <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '5'">Print</span>
                                          <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '6'">Promotional
                                            Material</span>
                                          <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '7'">Packaging</span>
                                          <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '8'">SMS</span>
                                          <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '9'">Others</span>
                                        </p>
                                      </div>
                                      <div class="attribute-container2">
                                        <p>
                                          <span
                                            *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '1' || docs.ADVERTISEMENT_SOURCE_ID == '2'">Channel
                                            : {{docs.SOURCE_NAME}}</span>
                                          <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '3'">Platform :
                                            <span *ngIf="docs.PLATFORM_ID == 1">Facebook</span>
                                            <span *ngIf="docs.PLATFORM_ID  == 2">Instagram</span>
                                            <span *ngIf="docs.PLATFORM_ID  == 3">YouTube</span>
                                            <span *ngIf="docs.PLATFORM_ID  == 4">Twitter</span>
                                            <span *ngIf="docs.PLATFORM_ID  == 5">LinkedIn</span>
                                            <span *ngIf="docs.PLATFORM_ID  == 6">Website</span>
                                            <span *ngIf="docs.PLATFORM_ID  == 7">Google Ad</span>
                                            <span *ngIf="docs.PLATFORM_ID  == 8">Mobile App</span>
                                            <span *ngIf="docs.PLATFORM_ID  == 9">{{docs.SOURCE_PLACE}}</span>
                                          </span>
                                          <span
                                            *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '4' || docs.ADVERTISEMENT_SOURCE_ID == '6'">Place
                                            : {{docs.SOURCE_PLACE}}</span>
                                          <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '5'">Print Source :
                                            {{docs.SOURCE_NAME}}</span>
                                          <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '7'">MFD/PKD Date :
                                            {{docs.DATE | date:'dd/MM/yyyy'}}</span>
                                          <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '8'">Sender :
                                            {{docs.SOURCE_NAME}}</span>
                                          <span *ngIf="docs.ADVERTISEMENT_SOURCE_ID == '9'">Source :
                                            {{docs.SOURCE_NAME}}</span>
                                        </p>
                                      </div>
                                    </div>
                                  </mat-panel-title>
                                  <mat-panel-title class="panel-title"
                                    *ngIf="complaint_source_id == 7 && adDocs.length != 0">
                                    <div fxLayout="row">
                                      <div class="attribute-container1">
                                        <p>
                                          <span class="grey-text">Medium : NAMS - Tams</span>
                                        </p>
                                      </div>
                                    </div>
                                  </mat-panel-title>
                                  <mat-panel-title class="panel-title"
                                    *ngIf="complaint_source_id == 8 && adDocs.length != 0">
                                    <div fxLayout="row">
                                      <div class="attribute-container1">
                                        <p>
                                          <span class="grey-text">Medium : NAMS - Reech</span>
                                        </p>
                                      </div>
                                    </div>
                                  </mat-panel-title>
                                </mat-expansion-panel-header>
                                <app-mat-spinner-overlay overlay="true" *ngIf="isUploadProgress">
                                </app-mat-spinner-overlay>
                                <div fxLayout="column" *ngFor="let file of adDocs; let ind = index">
                                  <div class="panel-body" fxLayout="row">
                                    <div class="complaint-doc-icon-container flex">
                                      <button mat-icon-button class="doc-btn flex" style="padding-left: 12px;">
                                        <img src="../../assets/images/media_doc.svg">
                                      </button>
                                    </div>
                                    <div class="complaint-doc-link-container"
                                      style="width:80%;position: relative;top: 3px;left: -4px;">
                                      <a class="doc-link"
                                        matTooltip="{{file.ATTACHMENT_NAME}}">{{file.ATTACHMENT_NAME | slice:
                                        0:60}}{{file.ATTACHMENT_NAME.length>61? '..': ''}}</a>
                                    </div>
                                    <div class="complaint-doc-icon-container flex">
                                      <button mat-icon-button class="icon-btn flex"
                                        (click)="preview(file.ATTACHMENT_SOURCE)">
                                        <img src="../../assets/images/media_eye.svg">
                                      </button>
                                    </div>
                                    <div class="complaint-doc-icon-container flex"
                                      (click)="download(file.ATTACHMENT_NAME,file.ATTACHMENT_SOURCE)">
                                      <button mat-icon-button class="icon-btn flex">
                                        <img src="../../assets/images/media_download.svg">
                                      </button>
                                    </div>
                                  </div>
                                  <div fxLayout="row" style="margin-bottom: 8px; margin-left: 15px;">
                                    <div>
                                      <img src="../../../assets/images/calender-icon.png">
                                    </div>
                                    <div
                                      style="font-size: 11px; margin-left: 12px; margin-top: 3px; color: rgba(0, 0, 0, 0.6);">
                                      {{file.CREATED_DATE | date:'dd/MM/yyyy h:mm a' }}
                                    </div>
                                  </div>
                                </div>
                              </mat-expansion-panel>
                            </mat-accordion>
                            <mat-accordion multi>
                              <mat-expansion-panel class="intra-expansion" (opened)="panelOpenState = true"
                                (closed)="panelOpenState = false"
                                *ngFor="let file of complainantFiles; let ind = index">
                                <mat-expansion-panel-header class="panel-header"
                                  *ngIf="complainantFiles.length != 0 && file.FIELD_TAB == 'complainant'">
                                  <mat-panel-title class="panel-title">
                                    <div fxLayout="row">
                                      <div class="attribute-container1">
                                        <p>
                                          <span class="grey-text">Uploaded by : </span>
                                          <span>Complainant</span>
                                        </p>
                                      </div>
                                    </div>
                                  </mat-panel-title>
                                </mat-expansion-panel-header>
                                <div fxLayout="column">
                                  <div class="panel-body" fxLayout="row">
                                    <div class="complaint-doc-icon-container flex"
                                      *ngIf="file.FIELD_TAB == 'complainant'">
                                      <button mat-icon-button class="doc-btn flex" style="padding-left: 12px;">
                                        <img src="../../assets/images/media_doc.svg">
                                      </button>
                                    </div>
                                    <div class="complaint-doc-link-container"
                                      style="width:80%; position: relative; top: 3px; left: -4px;"
                                      *ngIf="file.FIELD_TAB == 'complainant'">
                                      <a class="doc-link flex"
                                        matTooltip="{{file.ATTACHMENT_SOURCE_NAME}}">{{file.ATTACHMENT_SOURCE_NAME
                                        | slice: 0:60}}{{file.ATTACHMENT_SOURCE_NAME.length>61? '..': ''}}</a>
                                    </div>
                                    <div class="complaint-doc-icon-container flex"
                                      *ngIf="file.FIELD_TAB == 'complainant'">
                                      <button mat-icon-button class="icon-btn flex"
                                        (click)="preview(file.ATTACHMENT_SOURCE)">
                                        <img src="../../assets/images/media_eye.svg">
                                      </button>
                                    </div>
                                    <div class="complaint-doc-icon-container flex"
                                      *ngIf="file.FIELD_TAB == 'complainant'">
                                      <button mat-icon-button class="icon-btn flex"
                                        (click)="download(file.ATTACHMENT_SOURCE_NAME, file.ATTACHMENT_SOURCE)">
                                        <img src="../../assets/images/media_download.svg">
                                      </button>
                                    </div>
                                  </div>
                                  <div fxLayout="row" style="margin-bottom: 8px; margin-left: 15px;">
                                    <div>
                                      <img src="../../../assets/images/calender-icon.png">
                                    </div>
                                    <div
                                      style="font-size: 11px; margin-left: 12px; margin-top: 3px; color: rgba(0, 0, 0, 0.6);">
                                      {{file.CREATED_DATE | date:'dd/MM/yyyy h:mm a'}}
                                    </div>
                                  </div>
                                </div>
                              </mat-expansion-panel>
                            </mat-accordion>
                          </mat-tab>
                          <mat-tab label="Advertiser">
                            <app-mat-spinner-overlay overlay="true" *ngIf="isUploadProgress">
                            </app-mat-spinner-overlay>
                            <div class="doc-body" fxLayout="row">
                              <div class="doc-fxrow-container" fxLayout="row wrap" fxLayoutAlign="flex-start">
                                <div fxFlex fxLayout="column" fxLayoutGap="10px">
                                  <div fxLayout="row wrap" fxLayoutGap="20px">
                                    <div *ngFor="let file of advertiserFiles; let ind = index" fxFlex="26"
                                      fxFlex.md="25" fxFlex.sm="50" fxFlex.xs="100" fxLayout="column"
                                      style="padding: 5px;">
                                      <mat-card class="mat-card-doc mat-elevation-z1"
                                        *ngIf="file.FIELD_TAB == 'advertiser'">
                                        <div class="doc-icon-container">
                                          <div class="doc-image-container">
                                            <img src="../../../assets/images/File.png" width="20px" height="20px"
                                              alt="doc-icon">
                                          </div>
                                        </div>
                                        <div fxLayout="row">
                                          <div fxLayout="column">
                                            <div class="doc-caption">
                                              <p mat-line matTooltip="{{file.ATTACHMENT_SOURCE_NAME}}">
                                                {{file.ATTACHMENT_SOURCE_NAME}}</p>
                                              <div
                                                style="font-size: 11px; color: rgba(0, 0, 0, 0.6); margin-left: 5px;">
                                                {{file.CREATED_DATE | date:'dd/MM/yyyy h:mm a'}}
                                              </div>
                                            </div>
                                          </div>
                                          <button mat-icon-button [matMenuTriggerFor]="admin">
                                            <mat-icon>more_vert</mat-icon>
                                          </button>
                                          <mat-menu #admin="matMenu" class="action-buttons">
                                            <div class="admin-option-container">
                                              <button mat-menu-item class="option-btn"
                                                (click)="preview(file.ATTACHMENT_SOURCE)">
                                                <span class="option-text"><img
                                                    src="../../../assets/images/eye (1).png"></span>
                                                <span class="option-text">Preview</span>
                                              </button>
                                              <mat-divider class="option-divider">
                                              </mat-divider>
                                              <button mat-menu-item class="option-btn"
                                                (click)="download(file.ATTACHMENT_SOURCE_NAME, file.ATTACHMENT_SOURCE)">
                                                <span class="option-text"><img
                                                    src="../../../assets/images/Download (1).png"></span>
                                                <span class="option-text">Download</span>
                                              </button>
                                            </div>
                                          </mat-menu>
                                        </div>
                                      </mat-card>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </mat-tab>
                          <mat-tab label="Internal">
                            <app-mat-spinner-overlay overlay="true" *ngIf="isUploadProgress">
                            </app-mat-spinner-overlay>
                            <div class="doc-body" fxLayout="row">
                              <div class="doc-fxrow-container" fxLayout="row wrap" fxLayoutAlign="flex-start">
                                <div fxFlex fxLayout="column" fxLayoutGap="10px">
                                  <div fxLayout="row wrap" fxLayoutGap="20px">
                                    <div *ngFor="let file of internalFiles; let ind = index" fxFlex="30"
                                      fxFlex.md="30" fxFlex.sm="20" fxFlex.xs="100" fxLayout="column"
                                      style="padding: 5px;">
                                      <mat-card class="mat-card-doc mat-elevation-z1"
                                        *ngIf="file.FIELD_TAB == 'internal'">
                                        <div class="doc-icon-container">
                                          <div class="doc-image-container">
                                            <img src="../../../assets/images/File.png" width="20px" height="20px"
                                              alt="doc-icon">
                                          </div>
                                        </div>
                                        <div fxLayout="row">
                                          <div fxLayout="column">
                                            <div class="doc-caption">
                                              <p mat-line matTooltip="{{file.ATTACHMENT_SOURCE_NAME}}">
                                                {{file.ATTACHMENT_SOURCE_NAME}}</p>
                                              <div
                                                style="font-size: 11px; color: rgba(0, 0, 0, 0.6); margin-left: 5px;">
                                                {{file.CREATED_DATE | date:'dd/MM/yyyy h:mm a'}}
                                              </div>
                                            </div>
                                          </div>
                                          <button mat-icon-button [matMenuTriggerFor]="admin">
                                            <mat-icon>more_vert</mat-icon>
                                          </button>
                                          <mat-menu #admin="matMenu" class="action-buttons">
                                            <div class="admin-option-container">
                                              <button mat-menu-item class="option-btn"
                                                (click)="preview(file.ATTACHMENT_SOURCE)">
                                                <span class="option-text"><img
                                                    src="../../../assets/images/eye (1).png"></span>
                                                <span class="option-text">Preview</span>
                                              </button>
                                              <mat-divider class="option-divider"></mat-divider>
                                              <button mat-menu-item class="option-btn"
                                                (click)="download(file.ATTACHMENT_SOURCE_NAME, file.ATTACHMENT_SOURCE)">
                                                <span class="option-text"><img
                                                    src="../../../assets/images/Download (1).png"></span>
                                                <span class="option-text">Download</span>
                                              </button>
                                            </div>
                                          </mat-menu>
                                        </div>
                                      </mat-card>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </mat-tab>
                        </mat-tab-group>
                      </div>
                    </div>
                  </mat-tab>
                  <mat-tab label="Claims">
                    <div class="claims-subhead-container" fxLayout="column" fxLayoutGap="10px"
                      style="margin-bottom: 30px;">
                      <div class="no-claims"
                        *ngIf="complaintClaims.length == 0 && complaintCodeViolated.length == 0">
                        No claims raised...
                      </div>
                      <div fxLayout="column" fxLayoutGap="10px"
                        *ngIf="complaintClaims.length != 0 || complaintCodeViolated.length != 0">
                        <mat-accordion>
                          <mat-expansion-panel class="intra-expansion" (opened)="panelOpenState = true"
                            (closed)="panelOpenState = false">
                            <mat-expansion-panel-header class="panel-header">
                              <mat-panel-title class="panel-title">
                                Claim Details 1
                              </mat-panel-title>
                            </mat-expansion-panel-header>
                            <div class="intra-divider" style="width: 100%;border-radius: 1px">
                              <mat-divider></mat-divider>
                            </div>
                            <div class="intra-body" fxLayout="row" fxLayoutGap="10px">
                              <div class="raised-container" fxLayout="column">
                                <div class="head-container" fxLayout="row" fxLayoutGap="10px">
                                  <div class="arrow-icon-container">
                                    <img class="arrow-img-icon" src="../assets/images/arrow_circle-icon.svg " />
                                  </div>
                                  <div>
                                    <h3 class="intra-h3">Claims raised</h3>
                                  </div>
                                </div>
                                <div class="claim-challenges-container" fxLayout="column" fxLayoutGap="10px">
                                  <div class="challenge-container"
                                    *ngFor="let item of complaintClaims; let in = index;">
                                    <div class="panel-title">Claim challenged {{in+1}} </div>
                                    <div fxLayout="column">
                                      <div fxLayout="row" fxLayoutGap="10px">
                                        <div class="attribute-container width50">
                                          <p>
                                            <span class="grey-text">Name :</span><br> {{item.CLAIM_CHALLENGED}}
                                          </p>
                                        </div>
                                        <div class="attribute-container width50">
                                          <p>
                                            <span class="grey-text">Annexure no. :</span><br> {{item.ANNEXURE_NO}}
                                          </p>
                                        </div>
                                      </div>
                                      <div class="attribute-container">
                                        <p>
                                          <span class="grey-text"> Key objection :</span><br>
                                          <span [innerHTML]="safeHTML(item.KEY_OBJECTION)">{{item.KEY_OBJECTION}}</span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="violated-container" fxLayout="column" fxLayoutGap="5px">
                                <div class="head-container" fxLayout="row" fxLayoutGap="10px">
                                  <div class="arrow-icon-container">
                                    <img class="arrow-img-icon" src="../assets/images/arrow_circle-icon.svg " />
                                  </div>
                                  <div>
                                    <h3 class="intra-h3">ASCI code violated </h3>
                                  </div>
                                </div>
                                <!-- <div class="violated-body-container"> -->
                                <div class="chapters-container">
                                  <div class="chapter-text" *ngFor="let item of complaintCodeViolated">
                                    <!-- <div *ngFor="let clause of clauseList"> -->
                                    <div fxLayout="column">
                                      <!-- *ngIf="clause.ID == item.CLAUSES_ID && clause.CHAPTER_ID == item.CHAPTER_ID"> -->
                                      <span>
                                        <mat-icon class="circle-icon">lens</mat-icon>Chapter {{item.CHAPTER_ID}}
                                        <ng-container *ngIf="item.CLAUSES_ID"> : </ng-container>
                                      </span>
                                      <span style="width:90%;margin-left: 25px;">{{item.CLAUSES_ID}}</span>
                                    </div>
                                    <!-- </div> -->
                                  </div>
                                </div>

                                <div class="head-container" fxLayout="row" fxLayoutGap="10px">
                                  <div class="arrow-icon-container">
                                    <img class="arrow-img-icon" src="../assets/images/arrow_circle-icon.svg " />
                                  </div>
                                  <div>
                                    <h3 class="intra-h3">ASCI Guideline code violated </h3>
                                  </div>
                                </div>
                                <div class="chapters-container">
                                  <div class="chapter-text" *ngFor="let item of guidelines">
                                    <!-- <div *ngFor="let clause of clauseList"> -->
                                    <div fxLayout="column">
                                      <!-- *ngIf="clause.ID == item.CLAUSES_ID && clause.CHAPTER_ID == item.CHAPTER_ID"> -->
                                      <span>
                                        <mat-icon class="circle-icon">lens</mat-icon>Guideline <span
                                          *ngIf="item.G_CHAPTER_ID != 1">{{item.G_CHAPTER_ID - 1}}</span>
                                        <span *ngIf="item.G_CHAPTER_ID == 1">N/A</span>
                                        <ng-container
                                          *ngIf="item.G_CHAPTER_ID != 1 && item.G_CHAPTER_ID != 2 && item.G_CHAPTER_ID != 7 && item.G_CHAPTER_ID != 8">
                                          : </ng-container>
                                      </span>
                                      <span style="width:90%;margin-left: 25px;" *ngIf="item.G_CHAPTER_ID != 1">
                                        {{item.G_CLAUSES_ID}}
                                      </span>
                                    </div>
                                    <!-- </div> -->
                                  </div>
                                </div>

                                <div fxLayout="column" fxLayoutGap="10px">
                                  <button mat-button (click)="viewDocument()" class="violated-popups">
                                    <div>
                                      <span class="bolder">
                                        <img src="..\assets\images\folder-icon.svg">&nbsp; View documents
                                      </span>
                                    </div>
                                  </button>
                                </div>
                                <!-- </div> -->
                              </div>
                            </div>
                          </mat-expansion-panel>
                        </mat-accordion>
                      </div>
                    </div>
                  </mat-tab>
                </mat-tab-group>
              </div>
            </div>
          </div>
        </div>
      </div>
    </mat-card>
  </div>

  <div style="margin-bottom: 20px;">
    <mat-divider></mat-divider>
  </div>

  <div class="footer-content-container" fxLayout="column" fxLayoutGap="5px">
    <div class="comp-footer" fxLayout="row" fxLayoutAlign="end center">
      <button mat-stroked-button class="footer-btn" (click)="cancel()" [disabled]="disableSubmit">
        <img class="delete-icon" src="../assets/images/delete-icon.svg" />Delete
      </button>
      <div class="footer-right-btn" fxLayoutAlign="end center">
        <button mat-stroked-button class="theme-blue-button-admin" (click)="submit()" [disabled]="disableSubmit"
          style="font-weight: 500;">Create complaint</button>
      </div>
    </div>
  </div>

</div>