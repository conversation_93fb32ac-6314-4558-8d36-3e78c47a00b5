.close-div {
    position: relative;
    bottom: 9px;
}

.close-btn {
    color: solid rgba(47, 57, 65, 0.6);
    background: #F3F3F3;
    border: 1px solid rgba(47, 57, 65, 0.6);
}

.input-field {
    width: 100%;
}

.recommendation-box {
    background: #F5F5F5;
    box-shadow: 0px 1px 2px rgb(0 0 0 / 25%);
    border-radius: 4px;
    margin-top: 26px;
    padding: 11px;
    width: 100%;
    box-sizing: border-box;
}

.box-heading {
    color: rgba(0, 0, 0, 0.4);
    font-style: italic;
}

.cancel-btn {
    background: #FFFFFF;
    border-radius: 3px;
    border: none;
    margin-right: 10px;
}

button[disabled] {
    border: 1px solid #999999;
    background-color: #cccccc;
    color: #666666;
    opacity: 0.3;
}

.resolutions {
    height: 384px;
    overflow-y: scroll;
    overflow-x: hidden;
}

.resolutions::-webkit-scrollbar {
    display: none;
}

.resolutions {
    -ms-overflow-style: none;
    scrollbar-width: none;
}