<!-- <p>add-events works!</p> -->
<!-- <br /><br /><br /> -->
  <!-- <div>

      <h3>
        Add events -->

        <!-- <button class="btn btn-primary float-right" style="margin-left: 80%;" (click)="addEvent()">
          Add new
        </button>
        <button mat-icon-button class="search-btn" (click)="onClose()" >
          <mat-icon style="margin-bottom: 4px;">close</mat-icon>
        </button> -->
        <!-- <div class="clearfix"></div> -->

      <!-- </h3>
      <button class="btn btn-primary float-right" style="margin-left: 80%;" (click)="addEvent()">
        Add new
      </button>
      <button mat-icon-button class="search-btn" (click)="onClose()" >
        <mat-icon style="margin-bottom: 4px;">close</mat-icon>
      </button>
    </div> -->

    <mat-toolbar>
      <h3>
        Add Events 
      </h3>
      <!-- <button class="btn btn-primary float-right" style="margin-left: 80%;" (click)="addEvent()">
        Add new
      </button> -->
      <button class="btn btn-primary float-right" style="margin-left: 72%;" (click)="addEvent()">
        Add new
      </button>
      <button mat-icon-button class="search-btn" (click)="onClose()" >
        <mat-icon style="margin-bottom: 4px;">close</mat-icon>
      </button>
    </mat-toolbar>


      <div class="table-responsive">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th>Title</th>
              <th>Primary color</th>
              <th>Secondary color</th>
              <th>Starts at</th>
              <th>Ends at</th>
              <th>Remove</th>
            </tr>
          </thead>

          <tbody>
            <tr *ngFor="let event of events">
              <td>
                <input
                  type="text"
                  class="form-control"
                  [(ngModel)]="event.title"
                  (keyup)="refresh.next()"
                />
              </td>
              <td>
                <input
                  type="color"
                  [(ngModel)]="event.color.primary"
                  (change)="refresh.next()"
                />
              </td>
              <td>
                <input
                  type="color"
                  [(ngModel)]="event.color.secondary"
                  (change)="refresh.next()"
                />
              </td>
              <td>
                <input
                  class="form-control"
                  type="text"
                  mwlFlatpickr
                  [(ngModel)]="event.start"
                  (ngModelChange)="refresh.next()"
                  [altInput]="true"
                  [convertModelValue]="true"
                  [enableTime]="true"
                  dateFormat="Y-m-dTH:i"
                  altFormat="F j, Y H:i"
                  placeholder="Not set"
                />
              </td>
              <td>
                <input
                  class="form-control"
                  type="text"
                  mwlFlatpickr
                  [(ngModel)]="event.end"
                  (ngModelChange)="refresh.next()"
                  [altInput]="true"
                  [convertModelValue]="true"
                  [enableTime]="true"
                  dateFormat="Y-m-dTH:i"
                  altFormat="F j, Y H:i"
                  placeholder="Not set"
                />
              </td>
              <td>
                <button class="btn btn-danger" (click)="deleteEvent(event)">
                  Delete
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>