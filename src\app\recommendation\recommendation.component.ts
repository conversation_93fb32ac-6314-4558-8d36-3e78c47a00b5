import { Component, HostListener, Inject, OnInit, Renderer2, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormGroupDirective, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ComplaintsService } from '../services/complaints.service';
import { NotificationService } from '../services/notification.service';
import { colorObj } from '../shared/color-object';

@Component({
  selector: 'app-recommendation',
  templateUrl: './recommendation.component.html',
  styleUrls: ['./recommendation.component.scss']
})

export class RecommendationComponent implements OnInit {

  @ViewChild(FormGroupDirective) formGroupDirective: FormGroupDirective;
  recommendationForm: FormGroup;
  id: any;
  records: any[];
  isMobile: boolean;
  innerWidth: number;
  isResolution: boolean = false;
  isPublished: string;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  constructor(
    public fb: FormBuilder,
    public dialog: MatDialog,
    private renderer: Renderer2,
    private cs: ComplaintsService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<RecommendationComponent>,
    private notify: NotificationService) {
    this.recommendationForm = fb.group({
      subject: ['', Validators.required],
      recommendation: ['', Validators.required]
    })
  }

  // ngAfterViewInit() {
  //   setTimeout(() => {
  //     var elem = this.renderer.selectRootElement('#subject');
  //     elem.focus();
  //   }, 1000);
  // }

  ngOnInit(): void {
    this.getRecords();
    if (this.data['status_id'] == 7) {
      this.isResolution = true;
    } else {
      this.isResolution = false;
    }
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  publishOrUnpublishRecommendation(isPublished, id, complaint_id) {
    if (isPublished == 0) {
      this.isPublished = '1';
    } else if (isPublished == 1) {
      this.isPublished = '0';
    }
    this.cs.publishRecommendation(this.isPublished, id, complaint_id).subscribe(res => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      this.getRecords();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    })
  }

  deleteRecommendation(id, complaint_id) {
    this.cs.deleteRecommendation(id, complaint_id).subscribe(res => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      this.getRecords();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    })
  }

  getRecords() {
    this.cs.getRecommendationsById(this.data['complaint_id']).subscribe(res => {
      this.records = res.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    })
  }

  onSubmit(data) {
    this.id = this.data['complaint_id'];
    this.cs.addRecommendation(this.id, data).subscribe(val => {
      this.getRecords();
      this.formGroupDirective.resetForm();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    })
  }

  cancel() {
    this.formGroupDirective.resetForm();
  }

}