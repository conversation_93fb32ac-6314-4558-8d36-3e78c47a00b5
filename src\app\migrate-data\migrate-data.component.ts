import { COMM<PERSON>, ENTER } from '@angular/cdk/keycodes';
import { Component, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { MatChipInputEvent } from '@angular/material/chips';
import { MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { DateAdapter } from 'angular-calendar';
import moment from 'moment';
import { SubscriptionLike } from 'rxjs';
import { Observable } from 'rxjs/internal/Observable';
import { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { ConfirmationPopupComponent } from '../confirmation-popup/confirmation-popup.component';
import { CreateConfirmDialogComponent } from '../home/<USER>/create-confirm-dialog.component';
import { QuillConfiguration } from '../model/quill-configuration';
import { AuthService } from '../services/auth.service';
import { ComplaintsService } from '../services/complaints.service';
import { NotificationService } from '../services/notification.service';
import { UploadService } from '../services/upload.service';
import { MY_FORMATS } from '../shared/calendarFormat';
import { colorObj } from '../shared/color-object';

@Component({
  selector: 'app-migrate-data',
  templateUrl: './migrate-data.component.html',
  styleUrls: ['./migrate-data.component.scss'],
  providers: [
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
  ]
})
export class MigrateDataComponent implements OnInit {

  pagename: String;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }
  }
  userInfo: any;
  behalf: any[];
  processes: any[];
  compStatus: any[];
  resolutionList: any[];
  complianceList: any[];
  compMedium: any[];
  stageList: any[];
  subtags: any = [];
  subtagsId: any = [];
  classificationList: any[];
  advertiserForm: FormGroup;
  quillConfiguration = QuillConfiguration;
  companyList: any[];
  userIndex: number;
  transcriptionQuillText: any;
  adQuillText: any;
  complaintQuillText: any;
  companyId: number = 0;
  blankId: number;
  chapter: any[];
  clauses: any[];
  guidelines: any[];
  preamble: any;
  asciCodeObj: FormGroup;
  isChapterPreamble: boolean = false;
  isGuidelinePreamble: boolean = false;
  guidelineObj: FormGroup;
  guidelineClauses: any;
  disableGuideline: boolean = false;
  celebrityGuideline: boolean = false;
  keyQuillText = {};
  ads: any[];
  platforms: any[];
  printSources: any[];
  promotionTypes: any[];
  subTagList: Observable<any[]>;
  subTaglists: any = [];
  sysGenComplaintId: any = 0;
  files_attached = "No";
  general_files_attached = "No";
  filesProgress: any[] = [];
  filesAdvertiseProgress: any[] = [];
  @ViewChild('attachments') attachment: any;
  docFileList: File[] = [];
  docsFiles: any[] = [];
  isUploadProgress: boolean = false;
  blankComplaintId: number = 0;
  caseId: string;
  isUploadAdvProgress: boolean = false;
  platform_id: number;
  imgURL: string;
  media = '';
  public bucketUrl = `${environment.BUCKET_URL}`;
  public moveTabIndex = 0;
  loading: boolean = false;
  created_date: any;
  selectable = true;
  removable = true;
  addOnBlur = true;
  separatorKeysCodes: number[] = [ENTER, COMMA];
  maxDate = new Date();
  @ViewChild('tagInput') tagInput: ElementRef;
  subtagCtrl = new FormControl();
  selectedAdvId: number = 0;
  fileProgress: number = 0;
  fileInfoPath: string = '';
  emailPattern = environment.emailPatterm;
  mobilenopattern = /^[0-9]{10}$/;
  subscription: SubscriptionLike;
  advCompanyList: any[];
  is_FTC: number = 0;
  userTypeId: any;
  years: any = [];
  currentYear: number;
  networkName = "";
  confirmationMsg: any = {};

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private complaintService: ComplaintsService,
    private authService: AuthService,
    private uploadService: UploadService,
    private notify: NotificationService,
    public matDialog: MatDialog,
  ) {
    this.advertiserForm = this.fb.group({
      on_bahalf: new FormControl('', Validators.required),
      complainant_name: new FormControl(''),
      company: new FormControl('', Validators.required),
      brand: new FormControl('', Validators.required),
      product: new FormControl('', Validators.required),
      product_category: new FormControl(''),
      old_comp_num: new FormControl('', Validators.required),
      ref_id: new FormControl(''),
      gama_id: new FormControl(''),
      comp_medium: new FormControl('', Validators.required),
      whatsapp_id: new FormControl(''),
      whatsapp_profile: new FormControl(''),
      seen_medium: new FormControl('', Validators.required),
      date: new FormControl('', Validators.required),
      time: new FormControl('', Validators.required),
      channel: new FormControl('', Validators.required),
      printSource: new FormControl('', Validators.required),
      promotionType: new FormControl('', Validators.required),
      platform: new FormControl('', Validators.required),
      sourcePlace: new FormControl('', Validators.required),
      doc_file: new FormControl(''),
      file_array_general: new FormControl([]),
      add_url: new FormControl(''),
      advertiser: new FormControl('', Validators.required),
      media: new FormControl('', Validators.required),
      medium: new FormControl('', Validators.required),
      reech_platform: new FormControl('', Validators.required),
      media_outlet: new FormControl('', Validators.required),
      edition: new FormControl(''),
      suppliment: new FormControl(''),
      ad_language: new FormControl(''),
      creative_id: new FormControl('', Validators.required),
      duration: new FormControl(''),
      translation_hyperlink: new FormControl(''),
      transcription: new FormControl(''),
      publication_link: new FormControl('', Validators.required),
      influencer_name: new FormControl('', Validators.required),
      influencer_profile_URL: new FormControl(''),
      influencer_contact_no: new FormControl('', Validators.pattern(this.mobilenopattern)),
      influencer_email_address: new FormControl('', Validators.pattern(this.emailPattern)),
      engagements: new FormControl(''),
      ad_description: new FormControl('', Validators.required),
      complaint_description: new FormControl('', Validators.required),
      advCompany: fb.array([]),
      advMedium: fb.array([]),
      asciCode: fb.array([]),
      guideline: fb.array([]),
      claims: fb.array([]),
      claimsDocument: fb.array([]),
      recommendationArray: fb.array([]),
      financial_year: new FormControl(''),
      received_date: new FormControl('', Validators.required),
      registered_date: new FormControl('', Validators.required),
      comp_status: new FormControl('', Validators.required),
      resolution: new FormControl('', Validators.required),
      compliance: new FormControl(''),
      stage: new FormControl('', Validators.required),
      process: new FormControl(''),
      classification: new FormControl('', Validators.required),
      complaint_due_date: new FormControl(''),
      advertiser_due_date: new FormControl(''),
      review_requested: new FormControl(''),
      resolution_summary: new FormControl(''),
      resolution_desc: new FormControl(''),
      resolution_date: new FormControl('')
    })
    this.subTagList = this.subtagCtrl.valueChanges.pipe(
      startWith(''),
      map((subTagComplaint: string | null) => subTagComplaint ? this.filterSubtag(subTagComplaint) : this.subTaglists.slice())
    );
  }

  ngOnInit(): void {
    this.pagename = "Archive";
    if (window.innerWidth <= 1250) {
      this.isMobile = true;
    } else {
      this.isMobile = false;
    }

    this.userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.behalf = JSON.parse(window.localStorage.getItem('userType'));
    this.ads = JSON.parse(window.localStorage.getItem('advertisementSource'));
    this.chapter = JSON.parse(window.localStorage.getItem('chapter'));
    this.clauses = JSON.parse(window.localStorage.getItem('clause'));
    this.guidelines = JSON.parse(window.localStorage.getItem('guideline'));
    this.guidelineClauses = JSON.parse(window.localStorage.getItem('guidelineClause'));
    this.platforms = JSON.parse(window.localStorage.getItem('platform'));
    this.printSources = JSON.parse(window.localStorage.getItem('printSource'));
    this.promotionTypes = JSON.parse(window.localStorage.getItem('promotionalMaterialSource'));
    this.compStatus = JSON.parse(window.localStorage.getItem('complaintStatus'));
    this.resolutionList = JSON.parse(window.localStorage.getItem('resolutionStatus'));
    this.complianceList = JSON.parse(window.localStorage.getItem('complianceStatus'));
    this.stageList = JSON.parse(window.localStorage.getItem('stages'));
    this.processes = JSON.parse(window.localStorage.getItem('processes'));
    this.subTaglists = JSON.parse(window.localStorage.getItem('subTagList'));
    this.compMedium = JSON.parse(window.localStorage.getItem('complaintSource'));
    this.classificationList = JSON.parse(window.localStorage.getItem('classification'));
    this.classificationList = this.classificationList.filter(el => {
      return el.ADMINISTRATION_VISIBILITY == 1
    })

    this.advertiserForm.get('company')!.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0) {
          this.getCompanyList(value);
        }
      }, err => {
      })

    this.addAdve();
    this.addCodes();
    this.addClaimchallenges();
    this.addGuidelines();
    this.addRecommendation();
    var momentDate = moment(new Date())
    this.created_date = momentDate.format();
    this.selectYear();
  }

  openDialog() {
    const dialogRef = this.matDialog.open(CreateConfirmDialogComponent, {
      width: '500px',
      height: 'auto',
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        if (dialogResult.result == true) {
          this.is_FTC = 1;
        }
        else if (dialogResult.result == false) {
          this.is_FTC = 0;
        }
        this.complaintService.isFTCComplaint.next({ isFTC: dialogResult.result });
      }
      this.createBlankComplaintRecord();
    });
  }

  getCompanyList(value) {
    this.authService.getCompanies(value).subscribe(res => {
      this.companyList = res.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  onSelectionChange(event) {
    this.companyList.forEach(element => {
      if (event.option.value === element.COMPANY_NAME) {
        this.companyId = element.ID;
      }
    });
  }

  companyChange() {
    this.companyId = 0;
  }

  companyInput() {
    if (!(this.userInfo.roleId == 1 || this.userInfo.roleId == 2 || this.userInfo.roleId == 3 || this.userInfo.roleId == 6) && this.companyId == 0) {
      this.companyId = 0;
    } else if ((this.userInfo.roleId == 1 || this.userInfo.roleId == 2 || this.userInfo.roleId == 3 || this.userInfo.roleId == 6) && this.companyId == 0) {
      this.advertiserForm.get('company').setValue('');
    }
  }

  addAdve(advMedium?: any) {
    let advIndex = (<FormArray>this.advertiserForm.get('advMedium')).length;
    if (advIndex < 4) {
      let fg = this.fb.group({
        seen_medium: this.fb.control('', Validators.required),
        method: this.fb.control(''),
        sourcePlace: this.fb.control(''),
        printSource: this.fb.control(''),
        promotionType: this.fb.control(''),
        platform: this.fb.control(''),
        date: this.fb.control('', Validators.required),
        time: this.fb.control(''),
        add_url: this.fb.control(''),
        attachment_source: this.fb.control(''),
        attachment_name: this.fb.control(''),
        attachment_type: this.fb.control(''),
        attachment_size: this.fb.control(''),
        file_array: this.fb.control([]),
        advFileArray: this.fb.control([])
      });
      (<FormArray>this.advertiserForm.get('advMedium')).push(fg);
      this.userIndex = (<FormArray>this.advertiserForm.get('advMedium')).length - 1;
    }
  }

  addCodes(asciCode?: any) {
    let codeIndex = (<FormArray>this.advertiserForm.get('asciCode')).length;
    if (codeIndex < 5) {
      let fg = this.fb.group({
        chapter: this.fb.control('', Validators.required),
        preamble: this.fb.control(''),
        clause: this.fb.control(''),
        guidelines: this.fb.control(''),
        clauseArray: this.fb.control([])
      });
      (<FormArray>this.advertiserForm.get('asciCode')).push(fg);
      let userIndex = (<FormArray>this.advertiserForm.get('asciCode')).length - 1;
    }
  }

  addGuidelines() {
    let codeIndex = (<FormArray>this.advertiserForm.get('guideline')).length;
    let guidelines = [...this.guidelines];
    const guidelineArray = this.advertiserForm.get('guideline') as FormArray;
    if (guidelineArray.length > 0) {
      const guidelineObj = guidelineArray.controls[0] as FormGroup;
      if (guidelineObj.value.gChapter != 1) {
        guidelines.splice(0, 1);
      }
    }
    if (codeIndex < 5) {
      let fg = this.fb.group({
        gChapter: this.fb.control('', Validators.required),
        gClause: this.fb.control(''),
        gClauseArray: this.fb.control([]),
        gCelebrityArray: this.fb.control([]),
        gPreamble: this.fb.control(''),
        gGuidelineArray: this.fb.control(guidelines)
      });
      (<FormArray>this.advertiserForm.get('guideline')).push(fg);
      let gUserIndex = (<FormArray>this.advertiserForm.get('guideline')).length - 1;
    }
  }

  addClaimchallenges(claims?: any) {
    let claimIndex = (<FormArray>this.advertiserForm.get('claims')).length;
    if (claimIndex < 3) {
      let fg = this.fb.group({
        claimchallenge: this.fb.control(''),
        annexure: this.fb.control(''),
        objections: this.fb.control(''),
      });
      (<FormArray>this.advertiserForm.get('claims')).push(fg);
      let userIndex = (<FormArray>this.advertiserForm.get('claims')).length - 1;
    }
  }

  addClaimsDocument() {
    let claimIndex = (<FormArray>this.advertiserForm.get('claimsDocument')).length;
    if (claimIndex < 11) {
      let fg = this.fb.group({
        attachmentName: this.fb.control(''),
        attachmentSource: this.fb.control(''),
        attachementType: this.fb.control(''),
        documentType: this.fb.control(''),
        doc_name: this.fb.control(''),
        doc_annex: this.fb.control(''),
        size: this.fb.control('')
      });
      (<FormArray>this.advertiserForm.get('claimsDocument')).push(fg);
      let userIndex = (<FormArray>this.advertiserForm.get('claimsDocument')).length - 1;
    }
  }

  getControls1() {
    return (this.advertiserForm.get('advMedium') as FormArray).controls;
  }
  getControls2() {
    return (this.advertiserForm.get('asciCode') as FormArray).controls;
  }
  getControls3() {
    return (this.advertiserForm.get('claims') as FormArray).controls;
  }
  getControls4() {
    return (this.advertiserForm.get('claimsDocument') as FormArray).controls;
  }
  getControls5() {
    return (this.advertiserForm.get('guideline') as FormArray).controls;
  }

  removeAdve(index: number) {
    (<FormArray>this.advertiserForm.get('advMedium')).removeAt(index);
  }
  removeCodes(index: number) {
    (<FormArray>this.advertiserForm.get('asciCode')).removeAt(index);
  }
  removeGuidelines(index: number) {
    (<FormArray>this.advertiserForm.get('guideline')).removeAt(index);
  }
  removeClaims(index: number) {
    (<FormArray>this.advertiserForm.get('claims')).removeAt(index);
  }
  removeClaimsDocument(index: number) {
    (<FormArray>this.advertiserForm.get('claimsDocument')).removeAt(index);
  }

  getCompanyName(companyValue) {
    if (!!companyValue) {
      this.authService.getCompanies(companyValue).subscribe(res => {
        if (res.data.length > 0) {
          this.companyId = res.data[0].ID;
          this.advertiserForm.patchValue({
            'company': res.data[0].COMPANY_NAME
          })
        }
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
  }

  submitForm() {
    this.loading = true;
    if (this.advertiserForm.valid) {
    }
    let formData: any;
    formData = this.advertiserForm.value;
    let asciCodeFormArray = [];
    let guidelineFormArray = [];
    let claimsFormArray = [];
    let claimsDocFormArray = [];
    let advCompanyFormArray = [];
    let advMedFormArray = [];
    let advMedium = formData['advMedium'];
    if (this.userTypeId == 2 || this.userTypeId == 4 || this.userTypeId == 5) {
      for (let i = 0; i < advMedium.length; i++) {
        if (advMedium[i].seen_medium != 0) {
          let time = advMedium[i].time;
          if (!!time) {
            time = moment(time).format('HH:mm:ss')
          }
          advMedFormArray.push({
            "ADVERTISEMENT_SOURCE_ID": advMedium[i].seen_medium,
            "SOURCE_NAME": advMedium[i].method,
            "SOURCE_PLACE": advMedium[i].sourcePlace,
            "PLATFORM_ID": advMedium[i].platform,
            "DATE": moment(advMedium[i].date).format('yyyy-MM-DD'),
            "TIME": time,
            "SOURCE_URL": advMedium[i].add_url,
            "PRINT_SOURCE_ID": advMedium[i].printSource,
            "P_M_SOURCE_ID": advMedium[i].promotionType,
            "ATTACHMENT_DATA": advMedium[i].advFileArray
          })
        }
      }
      let asciCode = formData['asciCode'];
      for (let i = 0; i < asciCode.length; i++) {
        asciCodeFormArray.push({
          "CHAPTER_ID": asciCode[i].chapter,
          "CLAUSES_ID": asciCode[i].clause,
        })
      }
      let guideline = formData['guideline'];
      for (let i = 0; i < guideline.length; i++) {
        guidelineFormArray.push({
          "G_CHAPTER_ID": guideline[i].gChapter,
          "G_CLAUSES_ID": guideline[i].gClause,
          "CELEBRITY_NAME": guideline[i].gChapter == 10 ? guideline[i].gCelebrityArray : []
        })
      }
      let claims = formData['claims'];
      for (let i = 0; i < claims.length; i++) {
        claimsFormArray.push({
          "CLAIM_CHALLENGED": claims[i].claimchallenge,
          "ANNEXURE_NO": claims[i].annexure,
          "KEY_OBJECTION": this.keyQuillText[i]
        })
      }
      let claimsDoc = formData['claimsDocument'];
      for (let i = 0; i < claimsDoc.length; i++) {
        claimsDocFormArray.push({
          "ATTACHMENT_SOURCE": claimsDoc[i].attachmentSource,
          "ATTACHMENT_NAME": claimsDoc[i].attachmentName,
          "TYPE_OF_DOCUMENT": claimsDoc[i].attachementType,
          'DOC_TYPE': claimsDoc[i].documentType,
          'DOC_NAME': claimsDoc[i].doc_name,
          "ANNEXURE_NO": claimsDoc[i].doc_annex,
          "SIZE": claimsDoc[i].size
        })
      }
    }
    else if (this.userTypeId == 1 || this.userTypeId == 3) {
      advMedFormArray.push({
        "ADVERTISEMENT_SOURCE_ID": this.advertiserForm.value.seen_medium,
        "SOURCE_NAME": this.advertiserForm.value.channel,
        "SOURCE_PLACE": this.advertiserForm.value.sourcePlace,
        "PLATFORM_ID": this.advertiserForm.value.platform,
        "DATE": moment(this.advertiserForm.value.date).format('yyyy-MM-DD'),
        "TIME": moment(this.advertiserForm.value.time).format('HH:mm:ss'),
        "ATTACHMENT_DATA": this.advertiserForm.value.file_array_general,
        "SOURCE_URL": this.advertiserForm.value.add_url,
        "PRINT_SOURCE_ID": this.advertiserForm.value.printSource,
        "P_M_SOURCE_ID": this.advertiserForm.value.promotionType
      })
    }
    else if (this.userTypeId == 6) {
      advMedFormArray.push({
        "ADVERTISEMENT_SOURCE_ID": this.advertiserForm.value.media,
        "SOURCE_NAME": this.advertiserForm.value.media != 2 ? this.advertiserForm.value.media_outlet : '',
        "SOURCE_PLACE": this.advertiserForm.value.media == 2 ? this.advertiserForm.value.media_outlet : '',
        "PLATFORM_ID": this.advertiserForm.value.media == 2 ? 9 : 0,
        "DATE": moment(this.advertiserForm.value.date).format('yyyy-MM-DD'),
        "TIME": moment(this.advertiserForm.value.time).format('HH:mm:ss'),
        "ATTACHMENT_DATA": this.advertiserForm.value.file_array_general,
        "SOURCE_URL": this.advertiserForm.value.add_url,
        "PRINT_SOURCE_ID": this.advertiserForm.value.media == 5 ? 1 : 0,
        "P_M_SOURCE_ID": ""
      })
      for (let medium of this.ads) {
        if (this.advertiserForm.value.media == medium.ID) {
          this.media = medium.ADVERTISEMENT_SOURCE_NAME;
        }
      }
    }
    else if (this.userTypeId == 7) {
      advMedFormArray.push({
        "ADVERTISEMENT_SOURCE_ID": 3,
        "SOURCE_NAME": '',
        "SOURCE_PLACE": '',
        "PLATFORM_ID": this.advertiserForm.value.reech_platform,
        "DATE": moment(this.advertiserForm.value.date).format('yyyy-MM-DD'),
        "TIME": moment(this.advertiserForm.value.time).format('HH:mm:ss'),
        "ATTACHMENT_DATA": this.advertiserForm.value.file_array_general,
        "SOURCE_URL": this.advertiserForm.value.add_url,
        "PRINT_SOURCE_ID": '',
        "P_M_SOURCE_ID": ""
      })
      this.platforms.filter(el => {
        if (el.ID == this.advertiserForm.value.reech_platform) {
          this.networkName = el.PLATFORM_NAME;
        }
      })
      let advCompany = formData['advCompany'];
      for (let i = 0; i < advCompany.length; i++) {
        if (!!advCompany[i].advCompanyId) {
          advCompanyFormArray.push({
            "COMPANY_ID": advCompany[i].advCompanyId,
            "BRAND_NAME": '',
            "PRODUCT_NAME": '',
            "EMAIL_ID": advCompany[i].advEmail
          })
        }
      }
    }
    let recommendation = formData['recommendationArray'];
    let recommendationArray = [];
    for (let i = 0; i < recommendation.length; i++) {
      let is_publish: number;
      if (recommendation[i].recommendation_publish == true) {
        is_publish = 1;
      }
      else {
        is_publish = 0;
      }
      if (!!recommendation[i].recommendation_summary) {
        recommendationArray.push({
          "SUBJECT": recommendation[i].recommendation_summary,
          "RECOMMENDATION": recommendation[i].recommendation_desc,
          "CREATED_DATE": moment(recommendation[i].recommendation_date).format('yyyy-MM-DD'),
          "PUBLISH": is_publish
        })
      }
    }
    let resolutionArray = [];
    if (!!this.advertiserForm.value.resolution_summary) {
      resolutionArray.push({
        "SUBJECT": this.advertiserForm.value.resolution_summary,
        "RESOLUTION": this.advertiserForm.value.resolution_desc,
        "CREATED_DATE": moment(this.advertiserForm.value.resolution_date).format('yyyy-MM-DD')
      })
    }
    if (this.complaintQuillText) {
      this.advertiserForm.patchValue({
        'COMPLAINT_DESCRIPTION': this.complaintQuillText
      })
    }
    if (this.adQuillText) {
      this.advertiserForm.patchValue({
        'ADVERTISEMENT_DESCRIPTION': this.adQuillText
      })
    }
    this.subtags.filter(el => {
      this.subtagsId.push(el.ID)
    })
    let obj = {
      "ID": this.blankComplaintId,
      "PARENT_ID": null,
      "CASE_ID": this.caseId,
      "OLD_CASE_ID": this.advertiserForm.value.old_comp_num,
      "IRP_ID": this.advertiserForm.value.ref_id,
      "GAMA_TRACKING_ID": this.advertiserForm.value.gama_id,
      "REVIEW_REQUESTED": this.advertiserForm.value.review_requested,
      "COMPLAINANT_NAME": this.advertiserForm.value.complainant_name,
      "FINANCIAL_YEAR": this.advertiserForm.value.financial_year,
      "COMPLAINT_TYPE_ID": this.advertiserForm.value.on_bahalf,
      "COMPANY_ID": this.companyId,
      "COMPLAINT_SOURCE_ID": this.advertiserForm.value.comp_medium,
      "COMPLAINT_STATUS_ID": this.advertiserForm.value.comp_status,
      "PROCESS_ID": this.advertiserForm.value.process,
      "PRIORITY_ID": 1,
      "STAGE_ID": this.advertiserForm.value.stage,
      "CLASSIFICATION_ID": this.advertiserForm.value.classification,
      "GOVERNMENT_DEPARTEMENT_ID": null,
      "USER_TYPE_ID": this.advertiserForm.value.on_bahalf,
      "BRAND_NAME": this.advertiserForm.value.brand,
      "PRODUCT_NAME": this.advertiserForm.value.product,
      "ADVERTISEMENT_DESCRIPTION": this.adQuillText,
      "COMPLAINT_DESCRIPTION": this.complaintQuillText,
      "COMPANY_NAME": this.advertiserForm.value.company,
      "DUE_DATE": moment(this.advertiserForm.value.complaint_due_date).format('yyyy-MM-DD'),
      "ADVERTISER_DUE_DATE": moment(this.advertiserForm.value.advertiser_due_date).format('yyyy-MM-DD'),
      "REGISTERED_DATE": moment(this.advertiserForm.value.registered_date).format('yyyy-MM-DD'),
      "CREATED_DATE": moment(this.advertiserForm.value.received_date).format('yyyy-MM-DD'),
      "SUBTAGS": this.subtagsId,
      "ADVERTISOR_NAME": this.advertiserForm.value.advertiser,
      "PRODUCT_CATEGORY": this.advertiserForm.value.product_category,
      "RESOLUTION_ID": this.advertiserForm.value.resolution,
      "COMPLIANCE_STATUS_ID": this.advertiserForm.value.compliance,
      "TRANSCRIPTION": this.transcriptionQuillText,
      "DURATION": this.advertiserForm.value.duration,
      "MEDIA_OUTLET": this.advertiserForm.value.media_outlet,
      "MEDIA": this.media,
      "EDITION": this.advertiserForm.value.edition,
      "SUPPLIMENT": this.advertiserForm.value.suppliment,
      "AD_LANGUAGE": this.advertiserForm.value.ad_language,
      "CREATIVE_ID": this.advertiserForm.value.creative_id,
      "TRANSLATION_HYPERLINK": this.advertiserForm.value.translation_hyperlink,
      "DATE": this.userTypeId === 6 ? moment(this.advertiserForm.value.date).format('yyyy-MM-DD') : null,
      "TIME": this.userTypeId === 6 || this.userTypeId === 7 ? moment(this.advertiserForm.value.time).format('HH:mm:ss') : null,
      "ENGAGEMENTS": this.advertiserForm.value.engagements,
      "NETWORK": this.networkName,
      "INFLUENCER_NAME": this.advertiserForm.value.influencer_name,
      "PUBLICATION_URL": this.advertiserForm.value.publication_link,
      "PROFILE_URL": this.advertiserForm.value.influencer_profile_URL,
      "PUBLICATION_DATE": this.userTypeId === 7 ? moment(this.advertiserForm.value.date).format('yyyy-MM-DD') : null,
      "INFLUENCER_MOBILE": this.advertiserForm.value.influencer_contact_no,
      "INFLUENCER_EMAIL": this.advertiserForm.value.influencer_email_address,
      "PROFILE_NAME": this.advertiserForm.value.whatsapp_profile,
      "WHATSAPP_ID": this.advertiserForm.value.whatsapp_id,
      "CLAIMS": claimsFormArray,
      "CLAIMS_DOCUMENT": claimsDocFormArray,
      "ADVERTISEMENT_MEDIUM": advMedFormArray,
      "CODE_VIOLATED": asciCodeFormArray,
      "GUIDELINES": guidelineFormArray,
      "COMPANY_INFO": advCompanyFormArray,
      // "ML_DATA": {},
      "RECOMMENDATION_DATA": recommendationArray,
      "RESOLUTION_DATA": resolutionArray
    }
    this.complaintService.migrateOldComplaint(obj).subscribe(res => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      this.loading = false;
      this.complaintService.complaintRegister({}, 0);
      this.complaintService.complaintRegister({}, 1);
      this.complaintService.callManageCaseComponent();
      this.complaintService.updateStep('archive-data');
      this.router.navigate(['manage-cases']);
      this.advertiserForm.reset();
    }, err => {
      this.loading = false;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  cancel() {
    this.complaintService.complaintRegister({}, 0);
    this.complaintService.complaintRegister({}, 1);
    this.complaintService.updateStep('direct');
    this.advertiserForm.reset();
    this.filesProgress = [];
    this.filesAdvertiseProgress = [];
    this.docsFiles = [];
    this.docFileList = [];
    if (this.blankComplaintId != 0) {
      this.complaintService.deleteBlankComplaint(this.blankComplaintId).subscribe(res => {
        this.router.routeReuseStrategy.shouldReuseRoute = () => false;
        this.router.onSameUrlNavigation = 'reload';
        this.router.navigate(['archive']);
        this.loading = false;
      }, err => {
        this.router.routeReuseStrategy.shouldReuseRoute = () => false;
        this.router.onSameUrlNavigation = 'reload';
        this.router.navigate(['archive']);
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        )
        this.loading = false;
      })
    } else {
      this.router.routeReuseStrategy.shouldReuseRoute = () => false;
      this.router.onSameUrlNavigation = 'reload';
      this.router.navigate(['archive']);
      this.loading = false;
    }
  }

  createBlankComplaintRecord() {
    this.complaintService.createBlankComplaint(this.userTypeId, this.userInfo.userId, this.userInfo.userId, this.is_FTC, new Date()).subscribe(res => {
      this.blankComplaintId = res.data.COMPLAINT_ID;
      this.caseId = res.data.CASE_ID;
      this.complaintService.updateBlankComplaintId = this.blankComplaintId;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  getBlankDocumentID() {
    // this.complaintService.currentBlankComplaintId
    //   .pipe(first()).subscribe(id => {
    //     this.blankComplaintId = id;
    //   })
    this.blankComplaintId = this.complaintService.getBlankId;
  }

  async onFileSelected(event: any) {
    if (!this.uploadService.validateFileExtension(event)) {
      this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        "File format is not supported!",
        "top",
        "warning",
        0
      );
      return;
    }
    let sizeOfAllFiles = this.uploadService.getTotalFileSize(this.docFileList) + this.uploadService.getTotalFileSize(event.target.files);
    let totalFileSelected = this.docFileList.length + event.target.files.length;
    if (totalFileSelected < 11 && sizeOfAllFiles <= 36700160) {
      this.isUploadProgress = true;
      for (let i = 0; i <= event.target.files.length - 1; i++) {
        if (i === 0) {
          this.getBlankDocumentID();
        }
        var selectedFile = event.target.files[i];
        this.docFileList.push(selectedFile);
        this.docsFiles.push(selectedFile.name);
        let tempObjforGetsigned = {
          id: this.blankComplaintId,
          section: 'claims',
          filename: selectedFile['name'],
          type: selectedFile['type']
        }
        await this.uploadService.getSignedUrl(tempObjforGetsigned).then(async (res) => {
          if (res && res['data'] && res['data']['SIGNED_URL']) {
            let tempObjForUpload = {
              url: res['data']['SIGNED_URL'],
              file: selectedFile
            }
            let fg = this.fb.group({
              attachmentName: selectedFile['name'],
              attachmentSource: res.data['PATH'],
              documentType: this.fb.control(''),
              attachementType: selectedFile['type'],
              doc_name: this.fb.control(''),
              doc_annex: this.fb.control(''),
              size: selectedFile['size']
            });
            (<FormArray>this.advertiserForm.get('claimsDocument')).push(fg);
            let length = this.advertiserForm.controls['claimsDocument']['value']['length'] - 1;
            this.filesProgress[length] = { progress: 0 }
            await this.uploadSignedFile(tempObjForUpload);
          }
        })
          .catch((err) => {
            this.isUploadProgress = false;
            this.handleError(err);
          });
      }
      this.isUploadProgress = false;
      this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        'Claim documents uploaded successfully',
        "top",
        (!!colorObj[200] ? colorObj[200] : "success"),
        200
      );
    } else {
      this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        "Max size 35MB and Max 10 files allowed",
        "top",
        "warning",
        0
      );
    }
  }

  async uploadSignedFile(tempObj) {
    let progressInit = 0;
    let length = this.advertiserForm.controls['claimsDocument']['value']['length'] - 1;
    await this.uploadService.uploadFilethroughSignedUrl(tempObj, progressInit => {
      this.filesProgress[length] = { progress: progressInit };
    }).catch((err) => {
      this.isUploadProgress = false;
      this.handleError(err);
    });
  }

  async onFileReplaced(event: any, index) {
    if (!this.uploadService.validateFileExtension(event)) {
      this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        "File format is not supported!",
        "top",
        "warning",
        0
      );
      return;
    }
    let sizeOfAllFiles = this.uploadService.getTotalFileSize(this.docFileList) + this.uploadService.getTotalFileSize(event.target.files);
    if (sizeOfAllFiles <= 36700160) {
      this.isUploadProgress = true;
      var selectedFile = event.target.files[0];
      this.docFileList[index] = selectedFile;
      this.docsFiles[index] = selectedFile.name;
      this.attachment.nativeElement.value = '';
      let cliamsControl = this.advertiserForm.get('claimsDocument') as FormArray;
      let fileInfo = cliamsControl.at(index);
      let body = {
        ID: this.blankComplaintId,
        KEY: fileInfo['value']['attachmentSource']
      }
      await this.uploadService.deleteObjectFromS3(body).toPromise().then(async (res) => {
        let tempObj = {
          id: this.blankComplaintId,
          section: 'claims',
          filename: selectedFile['name'],
          type: selectedFile['type']
        }
        await this.uploadService.getSignedUrl(tempObj).then(async (presigned) => {
          if (presigned && presigned['data'] && presigned['data']['SIGNED_URL']) {
            let tempObjForUpload = {
              url: presigned['data']['SIGNED_URL'],
              file: selectedFile
            }
            await this.uploadService.uploadFilethroughSignedUrl(tempObjForUpload, progressInit => {
              this.filesProgress[index] = { progress: progressInit };
            }).then((resp) => {
              let cliamsControl = this.advertiserForm.get('claimsDocument') as FormArray;
              cliamsControl.at(index).patchValue({ "attachment_source": presigned['data']['PATH'] });
              cliamsControl.at(index).patchValue({ "attachmentName": selectedFile.name });
              this.isUploadProgress = false;
              this.attachment.nativeElement.value = '';
              this.notify.showNotification(
                'Claim document changed successfully',
                "top",
                (!!colorObj[200] ? colorObj[200] : "success"),
                200
              );
            }).catch((err) => {
              this.isUploadProgress = false;
              this.handleError(err);
            })
          }
        }).catch(err => {
          this.isUploadProgress = false;
          this.handleError(err)
        });
      }).catch((err) => {
        this.isUploadProgress = false;
        this.handleError(err);
      })
    } else {
      this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        "Max size 35MB allowed",
        "top",
        "warning",
        0
      );
    }
  }

  handleError(err: any) {
    this.notify.showNotification(
      err.error.message,
      "top",
      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
      err.error.status
    );
  }

  checkMandatoryFields() {
    if (this.userTypeId == 2 || this.userTypeId == 4 || this.userTypeId == 5) {
      const advMedArray = this.advertiserForm.get('advMedium') as FormArray;
      for (let i = 0; i < advMedArray.controls.length; i++) {
        if (advMedArray.controls[i].value.add_url !== "" || advMedArray.controls[i].value.advFileArray.length != 0) {
          this.files_attached = 'Yes';
        } else {
          this.files_attached = 'No';
          break;
        }
      }
      if (this.files_attached == 'Yes') {
        return false;
      } else {
        return true;
      }
    }
    else {
      return false;
    }
  }

  textChangedTranscription($event) {
    if ($event.editor.getLength() > 2000) {
      $event.editor.deleteText(2000, $event.editor.getLength());
    }
    this.transcriptionQuillText = $event.html;
  }

  textChangedAdDesc($event) {
    if ($event.editor.getLength() > 5000) {
      $event.editor.deleteText(5000, $event.editor.getLength());
    }
    this.adQuillText = $event.html;
  }

  textChangedComplaintDesc($event) {
    if ($event.editor.getLength() > 5000) {
      $event.editor.deleteText(5000, $event.editor.getLength());
    }
    this.complaintQuillText = $event.html;
  }

  textChangedKeyObjections($event, index) {
    if ($event.editor.getLength() > 5000) {
      $event.editor.deleteText(5000, $event.editor.getLength());
    }
    this.keyQuillText[index] = $event.html;
  }

  get chapters(): FormControl {
    return this.advertiserForm.value['asciCode'] as FormControl;
  }

  get guidelinesASCI(): FormControl {
    return this.advertiserForm.value['guideline'][0] as FormControl;
  }

  get cLauses(): FormControl {
    return this.advertiserForm.value['asciCode'][0].clauseArray as FormControl;
  }

  changePlatform(plat) {
    this.platform_id = plat.ID;
  }

  getFileVal(index) {
    const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
    const advMediumObj = advMediumArray.controls[index] as FormGroup;
    if (advMediumObj.get('file_array').value.length != 0 || advMediumObj.get('add_url').value) {
      return ' '
    } else {
      return 'Please enter the url or upload file'
    }
  }

  async onFileChanged(event: any, index) {
    if (!this.uploadService.validateFileExtension(event)) {
      this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        "File format is not supported!",
        "top",
        "warning",
        0
      );
      return;
    }
    const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
    const advMediumObj = advMediumArray.controls[index] as FormGroup;
    let docFileList = advMediumObj.get('file_array').value;
    let sizeOfAllFiles = this.uploadService.getTotalFileSize(docFileList) + this.uploadService.getTotalFileSize(event.target.files);;
    let totalFileSelected = docFileList.length + event.target.files.length;
    if (totalFileSelected < 11 && sizeOfAllFiles <= 36700160) {
      for (let i = 0; i <= event.target.files.length - 1; i++) {
        if (i === 0) {
          this.getBlankDocumentID();
        }
        var selectedFile = event.target.files[i];
        docFileList.push(selectedFile);
        let tempObjforGetsigned = {
          id: this.blankComplaintId,
          section: 'a_medium',
          filename: selectedFile['name'],
          type: selectedFile['type']
        }
        this.isUploadAdvProgress = true;
        this.filesAdvertiseProgress[index] = { progress: 0 }
        await this.uploadService.getSignedUrl(tempObjforGetsigned)
          .then(async (res) => {
            if (res && res['data'] && res['data']['SIGNED_URL']) {
              advMediumObj.value['advFileArray'].push({
                ATTACHMENT_SOURCE: res['data']['PATH'],
                ATTACHMENT_NAME: selectedFile['name'],
                TYPE_OF_DOCUMENT: selectedFile['type'],
                SIZE: selectedFile['size']
              });
              let tempObjForUpload = {
                url: res['data']['SIGNED_URL'],
                file: selectedFile
              }
              await this.uploadService.uploadFilethroughSignedUrl(tempObjForUpload, progressInit => {
                this.filesAdvertiseProgress[index] = { progress: progressInit };
              })
                .then((data) => {
                  this.isUploadAdvProgress = false;
                  this.notify.showNotification(
                    'File uploaded successfully',
                    "top",
                    (!!colorObj[200] ? colorObj[200] : "success"),
                    200
                  );
                }).catch((err) => {
                  this.isUploadAdvProgress = false;
                  this.handleError(err);
                  const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
                  const advMediumObj = advMediumArray.controls[index] as FormGroup;
                  this.filesAdvertiseProgress[index] = { progress: 0 };
                  advMediumObj.get('file_array').setValue([]);
                  advMediumObj.get('advFileArray').setValue([]);
                })
            }
          })
          .catch(err => {
            this.isUploadAdvProgress = false;
            this.handleError(err)
          });
      }
    } else {
      this.notify.showNotification(
        "Max size 35MB file allowed",
        "top",
        "warning",
        0
      );
    }
  }

  removeVideoFile(index, i) {
    const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
    const advMediumObj = advMediumArray.controls[index] as FormGroup;
    let fileArray = advMediumObj.get('file_array').value;
    let advFileArray = advMediumObj.get('advFileArray').value;
    this.filesAdvertiseProgress[index] = { progress: 0 };
    // advMediumObj.get('file_array').setValue([]);
    let body = {
      ID: this.blankComplaintId,
      KEY: advFileArray[i].ATTACHMENT_SOURCE
    }
    fileArray.splice(i, 1);
    advFileArray.splice(i, 1);
    this.uploadService.deleteObjectFromS3(body).subscribe((res) => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      );
    }, (err) => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  removeDocFile(index) {
    let cliamsControl = this.advertiserForm.get('claimsDocument') as FormArray;
    let fileInfo = cliamsControl.at(index);
    let body = {
      ID: this.blankComplaintId,
      KEY: fileInfo['value']['attachmentSource']
    }
    this.uploadService.deleteObjectFromS3(body).subscribe((res) => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      );
      this.docsFiles.splice(index, 1);
      this.docFileList.splice(index, 1);
      this.removeClaimsDocument(index);
    }, (err) => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    })
  }

  showFields(data, i) {
    this.platform_id = 0;
    const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
    const advMediumObj = advMediumArray.controls[i] as FormGroup;
    advMediumObj.get('method').clearValidators();
    // advMediumObj.get('method').updateValueAndValidity();
    advMediumObj.get('sourcePlace').clearValidators();
    // advMediumObj.get('sourcePlace').updateValueAndValidity();
    advMediumObj.get('time').clearValidators();
    // advMediumObj.get('time').updateValueAndValidity();
    advMediumObj.get('platform').clearValidators();
    // advMediumObj.get('platform').updateValueAndValidity();
    advMediumObj.get('printSource').clearValidators();
    // advMediumObj.get('printSource').updateValueAndValidity();
    advMediumObj.get('promotionType').clearValidators();
    // advMediumObj.get('promotionType').updateValueAndValidity;
    // advMediumObj.get('method').setValue('');
    // advMediumObj.get('sourcePlace').setValue('');
    // advMediumObj.get('time').setValue('');
    // advMediumObj.get('platform').setValue('');
    // advMediumObj.get('printSource').setValue('');
    // advMediumObj.get('promotionType').setValue('');
    if (data.ID == 1 || data.ID == 2 || data.ID == 5 || data.ID == 8 ||
      data.ID == 9) {
      advMediumObj.controls["method"].setValidators(Validators.required);
    }
    if (data.ID == 3 || data.ID == 4 || data.ID == 6) {
      advMediumObj.controls["sourcePlace"].setValidators(Validators.required);
    }
    if (data.ID == 1 || data.ID == 2 || data.ID == 3 || data.ID == 9) {
      advMediumObj.controls["time"].setValidators(Validators.required);
    }
    if (data.ID == 3) {
      advMediumObj.controls["platform"].setValidators(Validators.required);
    }
    if (data.ID == 5) {
      advMediumObj.controls["printSource"].setValidators(Validators.required);
    }
    if (data.ID == 6) {
      advMediumObj.controls["promotionType"].setValidators(Validators.required);
    }
    if (data.ID == 3 || data.ID == 4 || data.ID == 6 || data.ID == 7) {
      advMediumObj.controls["method"].reset();
    }
    if (data.ID == 1 || data.ID == 2 || data.ID == 7 ||
      data.ID == 8 || data.ID == 9) {
      advMediumObj.controls["sourcePlace"].reset();
    }
    if (data.ID == 4 || data.ID == 5 || data.ID == 6 || data.ID == 7 || data.ID == 8) {
      advMediumObj.controls["time"].reset();
    }
    if (!(data.ID == 3)) {
      advMediumObj.controls["platform"].reset();
    }
    if (!(data.ID == 5)) {
      advMediumObj.controls["printSource"].reset();
    }
    if (!(data.ID == 6)) {
      advMediumObj.controls["promotionType"].reset();
    }
  }

  selectChapter(chapter, index) {
    let clauseArray = this.clauses.filter(el => {
      return (el.CHAPTER_ID == chapter.ID)
    })
    this.preamble = this.chapter[chapter.ID - 1].PREAMBLE;
    const asciCodeArray = this.advertiserForm.get('asciCode') as FormArray;
    const ascoCodeObj = asciCodeArray.controls[index] as FormGroup;
    ascoCodeObj.get('clauseArray').patchValue(clauseArray);
    ascoCodeObj.get('preamble').patchValue(this.preamble);
    this.isChapterPreamble = true;
  }

  selectGuideline(chapter, index) {
    let guidelineClauseArray = this.guidelineClauses.filter(el => {
      return (el.GUIDELINE_ID == chapter.ID)
    })
    this.isGuidelinePreamble = this.guidelines[chapter.ID - 1].PREAMBLE;
    const guidelineArray = this.advertiserForm.get('guideline') as FormArray;
    const asciCodeObj = guidelineArray.controls[index] as FormGroup;
    asciCodeObj.get('gClauseArray').patchValue(guidelineClauseArray);
    asciCodeObj.get('gPreamble').patchValue(this.isGuidelinePreamble);
    this.isGuidelinePreamble = true;
    this.disableGuideline = chapter.ID == 1 ? true : false;
    this.celebrityGuideline = chapter['ID'] == 10 ? true : false;
  }

  selectClause(index) {
    this.advertiserForm.value;
  }

  preview(source) {
    this.imgURL = this.bucketUrl + source;
    window.open(this.imgURL, 'window 1', '');
  }

  removeCelebrity(celebrity, i) {
    const guidelineArray = this.advertiserForm.get('guideline') as FormArray;
    const guidelineObj = guidelineArray.controls[i] as FormGroup;
    const index = guidelineObj.value['gCelebrityArray'].indexOf(celebrity);
    if (index >= 0) {
      guidelineObj.value['gCelebrityArray'].splice(index, 1);
    }
  }

  addCelebrity(event, index) {
    const input = event.input;
    const value = (event.value || '').trim();
    const guidelineArray = this.advertiserForm.get('guideline') as FormArray;
    const guidelineObj = guidelineArray.controls[index] as FormGroup;
    if (value) {
      guidelineObj.value['gCelebrityArray'].push(event.value);
    }
    if (input) {
      input.value = '';
    }
  }

  addSubtag(event: MatChipInputEvent) {
    const input = event.input;
    const value = (event.value || '').trim();
    if (input) {
      input.value = '';
    }
  }

  removesSubtags(tag): void {
    const index = this.subtags.indexOf(tag);
    if (index >= 0) {
      this.subtags.splice(index, 1);
    }
  }

  selectedSubtag(subtag, event): void {
    let subTagExists = false;
    if (event.isUserInput) {
      for (let st_i = 0; st_i < this.subtags.length; st_i++) {
        if (this.subtags[st_i]["ID"] == subtag.ID) {
          subTagExists = true;
          break;
        }
      }
      if (subTagExists) {
        this.notify.showNotification(
          "Subtag already selected",
          "top",
          "warning",
          0
        );
      }
      else {
        this.subtags.push(subtag);
      }
      this.tagInput.nativeElement.value = '';
      this.subtagCtrl.setValue(null);
    }
  }

  filterSubtag(name: any): any[] {
    let filterValue = null;
    if (typeof name === 'string' || name instanceof String) {
      filterValue = name ? name.toLowerCase() : '';
    } else if (typeof name === 'object' && name !== null) {
      filterValue = name['SUB_TAG_NAME'] ? name['SUB_TAG_NAME'].toLowerCase() : '';
    }
    return this.subTaglists.filter(tag =>
      tag.SUB_TAG_NAME.toLowerCase().indexOf(filterValue) === 0);
  }

  changeComplainant(value) {
    if (!!this.userTypeId && this.userTypeId == value) {
      // do nothing
    }
    else if (!!this.userTypeId) {
      this.confirmationMsg.title = 'Are you sure you want to cancel the current form ?';
      const dialogRef = this.matDialog.open(ConfirmationPopupComponent, {
        data: { title: this.confirmationMsg.title },
        disableClose: true
      });
      dialogRef.afterClosed().subscribe(dialogResult => {
        if (dialogResult && dialogResult.state) {
          this.cancel();
        }
        else {
          this.advertiserForm.controls['on_bahalf'].setValue(this.userTypeId);
        }
      });
    }
    else {
      this.checkFTC(value);
    }
  }

  checkFTC(value) {
    this.userTypeId = value;
    if (value == 2 || value == 4 || value == 5) {
      this.openDialog();
    }
    else {
      this.createBlankComplaintRecord();
    }
    this.selectedAdvId = 0;
    this.showFormFields(this.userTypeId);
  }

  showFormFields(userType) {
    if (userType == 1 || userType == 3) {
      this.advertiserForm.get('advertiser').clearValidators();
      this.advertiserForm.get('advertiser').updateValueAndValidity();
      this.advertiserForm.get('media').clearValidators();
      this.advertiserForm.get('media').updateValueAndValidity();
      this.advertiserForm.get('media_outlet').clearValidators();
      this.advertiserForm.get('media_outlet').updateValueAndValidity();
      this.advertiserForm.get('medium').clearValidators();
      this.advertiserForm.get('medium').updateValueAndValidity();
      this.advertiserForm.get('reech_platform').clearValidators();
      this.advertiserForm.get('reech_platform').updateValueAndValidity();
      this.advertiserForm.get('publication_link').clearValidators();
      this.advertiserForm.get('publication_link').updateValueAndValidity();
      this.advertiserForm.get('influencer_name').clearValidators();
      this.advertiserForm.get('influencer_name').updateValueAndValidity();
      this.advertiserForm.get('creative_id').clearValidators();
      this.advertiserForm.get('creative_id').updateValueAndValidity();
      const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
      const advMediumObj = advMediumArray.controls[0] as FormGroup;
      advMediumObj.get('seen_medium').clearValidators();
      advMediumObj.get('seen_medium').updateValueAndValidity();
      advMediumObj.get('date').clearValidators();
      advMediumObj.get('date').updateValueAndValidity();
      advMediumObj.get('method').clearValidators();
      advMediumObj.get('method').updateValueAndValidity();
      advMediumObj.get('sourcePlace').clearValidators();
      advMediumObj.get('sourcePlace').updateValueAndValidity();
      advMediumObj.get('time').clearValidators();
      advMediumObj.get('time').updateValueAndValidity();
      advMediumObj.get('platform').clearValidators();
      advMediumObj.get('platform').updateValueAndValidity();
      advMediumObj.get('printSource').clearValidators();
      advMediumObj.get('printSource').updateValueAndValidity();
      advMediumObj.get('promotionType').clearValidators();
      advMediumObj.get('promotionType').updateValueAndValidity();
      const codeArray = this.advertiserForm.get('asciCode') as FormArray;
      const codeObj = codeArray.controls[0] as FormGroup;
      codeObj.get('chapter').clearValidators();
      codeObj.get('chapter').updateValueAndValidity();
      const guidelineArray = this.advertiserForm.get('guideline') as FormArray;
      const guidelineObj = guidelineArray.controls[0] as FormGroup;
      guidelineObj.get('gChapter').clearValidators();
      guidelineObj.get('gChapter').updateValueAndValidity();
      this.advertiserForm.controls["seen_medium"].setValidators(Validators.required);
      this.advertiserForm.controls["date"].setValidators(Validators.required);
    }
    else if (userType == 2 || userType == 4 || userType == 5) {
      const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
      const advMediumObj = advMediumArray.controls[0] as FormGroup;
      advMediumObj.get('seen_medium').setValidators(Validators.required);
      advMediumObj.get('date').setValidators(Validators.required);
      advMediumObj.get('method').setValidators(Validators.required);
      advMediumObj.get('sourcePlace').setValidators(Validators.required);
      advMediumObj.get('time').setValidators(Validators.required);
      advMediumObj.get('platform').setValidators(Validators.required);
      advMediumObj.get('printSource').setValidators(Validators.required);
      advMediumObj.get('promotionType').setValidators(Validators.required);
      const codeArray = this.advertiserForm.get('asciCode') as FormArray;
      const codeObj = codeArray.controls[0] as FormGroup;
      codeObj.get('chapter').setValidators(Validators.required);
      const guidelineArray = this.advertiserForm.get('guideline') as FormArray;
      const guidelineObj = guidelineArray.controls[0] as FormGroup;
      guidelineObj.get('gChapter').setValidators(Validators.required);
      this.advertiserForm.get('seen_medium').clearValidators();
      this.advertiserForm.get('seen_medium').updateValueAndValidity();
      this.advertiserForm.get('channel').clearValidators();
      this.advertiserForm.get('channel').updateValueAndValidity();
      this.advertiserForm.get('promotionType').clearValidators();
      this.advertiserForm.get('promotionType').updateValueAndValidity();
      this.advertiserForm.get('sourcePlace').clearValidators();
      this.advertiserForm.get('sourcePlace').updateValueAndValidity();
      this.advertiserForm.get('date').clearValidators();
      this.advertiserForm.get('date').updateValueAndValidity();
      this.advertiserForm.get('time').clearValidators();
      this.advertiserForm.get('time').updateValueAndValidity();
      this.advertiserForm.get('platform').clearValidators();
      this.advertiserForm.get('platform').updateValueAndValidity();
      this.advertiserForm.get('printSource').clearValidators();
      this.advertiserForm.get('printSource').updateValueAndValidity();
      this.advertiserForm.get('advertiser').clearValidators();
      this.advertiserForm.get('advertiser').updateValueAndValidity();
      this.advertiserForm.get('media').clearValidators();
      this.advertiserForm.get('media').updateValueAndValidity();
      this.advertiserForm.get('media_outlet').clearValidators();
      this.advertiserForm.get('media_outlet').updateValueAndValidity();
      this.advertiserForm.get('medium').clearValidators();
      this.advertiserForm.get('medium').updateValueAndValidity();
      this.advertiserForm.get('reech_platform').clearValidators();
      this.advertiserForm.get('reech_platform').updateValueAndValidity();
      this.advertiserForm.get('publication_link').clearValidators();
      this.advertiserForm.get('publication_link').updateValueAndValidity();
      this.advertiserForm.get('influencer_name').clearValidators();
      this.advertiserForm.get('influencer_name').updateValueAndValidity();
      this.advertiserForm.get('creative_id').clearValidators();
      this.advertiserForm.get('creative_id').updateValueAndValidity();
    }
    else if (userType == 6) {
      this.advertiserForm.get('seen_medium').clearValidators();
      this.advertiserForm.get('seen_medium').updateValueAndValidity();
      this.advertiserForm.get('channel').clearValidators();
      this.advertiserForm.get('channel').updateValueAndValidity();
      this.advertiserForm.get('promotionType').clearValidators();
      this.advertiserForm.get('promotionType').updateValueAndValidity();
      this.advertiserForm.get('sourcePlace').clearValidators();
      this.advertiserForm.get('sourcePlace').updateValueAndValidity();
      this.advertiserForm.get('platform').clearValidators();
      this.advertiserForm.get('platform').updateValueAndValidity();
      this.advertiserForm.get('printSource').clearValidators();
      this.advertiserForm.get('printSource').updateValueAndValidity();
      this.advertiserForm.controls["advertiser"].setValidators(Validators.required);
      this.advertiserForm.controls["media"].setValidators(Validators.required);
      this.advertiserForm.controls["media_outlet"].setValidators(Validators.required);
      this.advertiserForm.controls["creative_id"].setValidators(Validators.required);
      this.advertiserForm.get('medium').clearValidators();
      this.advertiserForm.get('medium').updateValueAndValidity();
      this.advertiserForm.get('reech_platform').clearValidators();
      this.advertiserForm.get('reech_platform').updateValueAndValidity();
      this.advertiserForm.get('publication_link').clearValidators();
      this.advertiserForm.get('publication_link').updateValueAndValidity();
      this.advertiserForm.get('influencer_name').clearValidators();
      this.advertiserForm.get('influencer_name').updateValueAndValidity();
      const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
      const advMediumObj = advMediumArray.controls[0] as FormGroup;
      advMediumObj.get('seen_medium').clearValidators();
      advMediumObj.get('seen_medium').updateValueAndValidity();
      advMediumObj.get('date').clearValidators();
      advMediumObj.get('date').updateValueAndValidity();
      advMediumObj.get('method').clearValidators();
      advMediumObj.get('method').updateValueAndValidity();
      advMediumObj.get('sourcePlace').clearValidators();
      advMediumObj.get('sourcePlace').updateValueAndValidity();
      advMediumObj.get('time').clearValidators();
      advMediumObj.get('time').updateValueAndValidity();
      advMediumObj.get('platform').clearValidators();
      advMediumObj.get('platform').updateValueAndValidity();
      advMediumObj.get('printSource').clearValidators();
      advMediumObj.get('printSource').updateValueAndValidity();
      advMediumObj.get('promotionType').clearValidators();
      advMediumObj.get('promotionType').updateValueAndValidity();
      const codeArray = this.advertiserForm.get('asciCode') as FormArray;
      const codeObj = codeArray.controls[0] as FormGroup;
      codeObj.get('chapter').clearValidators();
      codeObj.get('chapter').updateValueAndValidity();
      const guidelineArray = this.advertiserForm.get('guideline') as FormArray;
      const guidelineObj = guidelineArray.controls[0] as FormGroup;
      guidelineObj.get('gChapter').clearValidators();
      guidelineObj.get('gChapter').updateValueAndValidity();
    }
    else if (userType == 7) {
      this.advertiserForm.patchValue({
        'medium': 3
      });
      this.advertiserForm.get('seen_medium').clearValidators();
      this.advertiserForm.get('seen_medium').updateValueAndValidity();
      this.advertiserForm.get('channel').clearValidators();
      this.advertiserForm.get('channel').updateValueAndValidity();
      this.advertiserForm.get('promotionType').clearValidators();
      this.advertiserForm.get('promotionType').updateValueAndValidity();
      this.advertiserForm.get('sourcePlace').clearValidators();
      this.advertiserForm.get('sourcePlace').updateValueAndValidity();
      this.advertiserForm.get('platform').clearValidators();
      this.advertiserForm.get('platform').updateValueAndValidity();
      this.advertiserForm.get('printSource').clearValidators();
      this.advertiserForm.get('printSource').updateValueAndValidity();
      this.advertiserForm.get('advertiser').clearValidators();
      this.advertiserForm.get('advertiser').updateValueAndValidity();
      this.advertiserForm.get('media').clearValidators();
      this.advertiserForm.get('media').updateValueAndValidity();
      this.advertiserForm.get('media_outlet').clearValidators();
      this.advertiserForm.get('media_outlet').updateValueAndValidity();
      this.advertiserForm.get('creative_id').clearValidators();
      this.advertiserForm.get('creative_id').updateValueAndValidity();
      this.advertiserForm.controls["medium"].setValidators(Validators.required);
      this.advertiserForm.controls["reech_platform"].setValidators(Validators.required);
      this.advertiserForm.controls["publication_link"].setValidators(Validators.required);
      this.advertiserForm.controls["influencer_name"].setValidators(Validators.required);
      const advMediumArray = this.advertiserForm.get('advMedium') as FormArray;
      const advMediumObj = advMediumArray.controls[0] as FormGroup;
      advMediumObj.get('seen_medium').clearValidators();
      advMediumObj.get('seen_medium').updateValueAndValidity();
      advMediumObj.get('date').clearValidators();
      advMediumObj.get('date').updateValueAndValidity();
      advMediumObj.get('method').clearValidators();
      advMediumObj.get('method').updateValueAndValidity();
      advMediumObj.get('sourcePlace').clearValidators();
      advMediumObj.get('sourcePlace').updateValueAndValidity();
      advMediumObj.get('time').clearValidators();
      advMediumObj.get('time').updateValueAndValidity();
      advMediumObj.get('platform').clearValidators();
      advMediumObj.get('platform').updateValueAndValidity();
      advMediumObj.get('printSource').clearValidators();
      advMediumObj.get('printSource').updateValueAndValidity();
      advMediumObj.get('promotionType').clearValidators();
      advMediumObj.get('promotionType').updateValueAndValidity();
      const codeArray = this.advertiserForm.get('asciCode') as FormArray;
      const codeObj = codeArray.controls[0] as FormGroup;
      codeObj.get('chapter').clearValidators();
      codeObj.get('chapter').updateValueAndValidity();
      const guidelineArray = this.advertiserForm.get('guideline') as FormArray;
      const guidelineObj = guidelineArray.controls[0] as FormGroup;
      guidelineObj.get('gChapter').clearValidators();
      guidelineObj.get('gChapter').updateValueAndValidity();
    }
  }

  changeAdvType(advSource) {
    this.selectedAdvId = advSource.ID;
    this.platform_id = 0;
    this.advertiserForm.get('channel').clearValidators();
    this.advertiserForm.get('channel').updateValueAndValidity();
    this.advertiserForm.get('promotionType').clearValidators();
    this.advertiserForm.get('promotionType').updateValueAndValidity();
    this.advertiserForm.get('sourcePlace').clearValidators();
    this.advertiserForm.get('sourcePlace').updateValueAndValidity();
    this.advertiserForm.get('time').clearValidators();
    this.advertiserForm.get('time').updateValueAndValidity();
    this.advertiserForm.get('platform').clearValidators();
    this.advertiserForm.get('platform').updateValueAndValidity();
    this.advertiserForm.get('printSource').clearValidators();
    this.advertiserForm.get('printSource').updateValueAndValidity();
    if (advSource.ID == 1 || advSource.ID == 2 ||
      advSource.ID == 5 || advSource.ID == 8 || advSource.ID == 9) {
      this.advertiserForm.controls["channel"].setValidators(Validators.required);
    }
    if (advSource.ID == 4 || advSource.ID == 6) {
      this.advertiserForm.controls["sourcePlace"].setValidators(Validators.required);
    }
    if (advSource.ID == 1 || advSource.ID == 2 || advSource.ID == 3) {
      this.advertiserForm.controls["time"].setValidators(Validators.required);
    }
    if (advSource.ID == 3) {
      this.advertiserForm.controls["platform"].setValidators(Validators.required);
    }
    if (advSource.ID == 5) {
      this.advertiserForm.controls["printSource"].setValidators(Validators.required);
    }
    if (advSource.ID == 6) {
      this.advertiserForm.controls["promotionType"].setValidators(Validators.required);
    }
    if (advSource.ID == 3 || advSource.ID == 4 || advSource.ID == 6 || advSource.ID == 7) {
      this.advertiserForm.controls["channel"].reset();
    }
    if (advSource.ID == 1 || advSource.ID == 2 || advSource.ID == 7 || advSource.ID == 8 || advSource.ID == 9) {
      this.advertiserForm.controls["sourcePlace"].reset();
    }
    if (advSource.ID == 4 || advSource.ID == 5 || advSource.ID == 6 || advSource.ID == 7 ||
      advSource.ID == 8) {
      this.advertiserForm.controls["time"].reset();
    }
    if (!(advSource.ID == 3)) {
      this.advertiserForm.controls["platform"].reset();
    }
    if (!(advSource.ID == 5)) {
      this.advertiserForm.controls["printSource"].reset();
    }
    if (!(advSource.ID == 6)) {
      this.advertiserForm.controls["promotionType"].reset();
    }
  }

  async onFileSelectedGeneral(event: any) {
    let sizeOfFiles = this.uploadService.getTotalFileSize(event.target.files);
    let totalFileSelected = this.advertiserForm.value.file_array_general.length + event.target.files.length;
    if (totalFileSelected < 11 && sizeOfFiles <= 36700160) {
      if (event.target["files"] && event.target.files['length'] != 0) {
        for (let i = 0; i <= event.target.files.length - 1; i++) {
          this.general_files_attached = "Yes";
          let selectedFile = event.target.files[i];
          this.fileProgress = 0;
          // this.complaintService.currentBlankComplaintId
          //   .pipe(first()).subscribe(id => {
          //     this.blankComplaintId = id;
          //   })
          this.blankComplaintId = this.complaintService.getBlankId;
          let tempObjforGetsigned = {
            id: this.blankComplaintId,
            section: 'a_medium',
            filename: selectedFile['name'],
            type: selectedFile['type']
          }
          this.isUploadProgress = true;
          this.uploadService.getSignedUrl(tempObjforGetsigned)
            .then(async (res) => {
              if (res && res['data'] && res['data']['SIGNED_URL']) {
                this.fileInfoPath = res['data']['PATH'];
                this.advertiserForm.value['file_array_general'].push({
                  ATTACHMENT_SOURCE: res['data']['PATH'],
                  ATTACHMENT_NAME: selectedFile['name'],
                  TYPE_OF_DOCUMENT: selectedFile['type'],
                  SIZE: selectedFile['size']
                });
                let tempObjForUpload = {
                  url: res['data']['SIGNED_URL'],
                  file: selectedFile
                }
                let progressInit = 0;
                await this.uploadService.uploadFilethroughSignedUrl(tempObjForUpload, progressInit => {
                  this.fileProgress = progressInit;
                }).then((data12) => {
                  this.isUploadProgress = false;
                  //   this.fileProgress = 0;
                  this.notify.showNotification(
                    'File uploaded successfully',
                    "top",
                    (!!colorObj[200] ? colorObj[200] : "success"),
                    200
                  );
                }).catch((err) => {
                  this.fileProgress = 0;
                  this.isUploadProgress = false;
                  this.handleError(err);
                });
              }
            })
            .catch(err => {
              this.isUploadProgress = false;
              this.handleError(err);
            });
        }
      } else {
        this.general_files_attached = "No"
      }
    } else {
      this.notify.showNotification(
        "Max size 35MB file allowed",
        "top",
        "warning",
        0
      );
    }
  }

  removeGeneralFile(index, key) {
    if (this.advertiserForm.value['file_array_general'] != 0) {
      this.advertiserForm.value['file_array_general'].splice(index, 1);
      let body = {
        ID: this.blankComplaintId,
        KEY: key
      }
      this.uploadService.deleteObjectFromS3(body).toPromise()
        .then((data1) => {
          this.notify.showNotification(
            data1.message,
            "top",
            (!!colorObj[data1.status] ? colorObj[data1.status] : "success"),
            data1.status
          );
        }).catch(err => this.handleError(err));
    }
  }

  getCompanyReech() {
    return (this.advertiserForm.get('advCompany') as FormArray).controls;
  }

  removeCompanyReech(index: number) {
    (<FormArray>this.advertiserForm.get('advCompany')).removeAt(index);
  }

  addAdvCompany(advMedium?: any) {
    let advIndex = (<FormArray>this.advertiserForm.get('advCompany')).length;
    if (advIndex < 4) {
      let fg = this.fb.group({
        advCompany: this.fb.control(''),
        advCompanyId: this.fb.control(''),
        advCompanyList: this.fb.control(''),
        advEmail: this.fb.control('', Validators.pattern(this.emailPattern)),
        advBrand: this.fb.control(''),
        advProduct: this.fb.control(''),
        index: this.fb.control('')
      });
      (<FormArray>this.advertiserForm.get('advCompany')).push(fg);
      let userIndex = (<FormArray>this.advertiserForm.get('advCompany')).length - 1;
      fg.get("index").patchValue(userIndex);
      this.subscription = fg.get('advCompany').valueChanges.pipe(
        debounceTime(200)).subscribe(value =>
          this.onValueChanged(value, userIndex));
    }
  }

  onValueChanged(value, index): void {
    this.getAdvCompanyList(value, index);
  }

  getAdvCompanyList(value, index) {
    if (!!value && value.length > 0) {
      this.authService.getCompanies(value).subscribe(res => {
        const advCompanyArray = this.advertiserForm.get('advCompany') as FormArray;
        const adv_company_obj = advCompanyArray.controls[index] as FormGroup;
        adv_company_obj.get('advCompanyList').patchValue(res.data, { emitEvent: false, onlySelf: true });
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
  }

  onAdvCompSelectionChange(event, index) {
    const advCompanyArray = this.advertiserForm.get('advCompany') as FormArray;
    const advCompanyObj = advCompanyArray.controls[index] as FormGroup;
    let companyList = advCompanyObj.get('advCompanyList').value;
    companyList.forEach(element => {
      if (event.option.value === element.COMPANY_NAME) {
        advCompanyObj.get('advCompanyId').patchValue(element.ID);
      }
    });
    this.advCompanyList = [];
  }

  advCompanyInput(advCompanyObj) {
    if (advCompanyObj.value['advCompanyId'] == 0 || advCompanyObj.value['advCompanyId'] == '') {
      advCompanyObj.get('advCompany').setValue('');
    }
    this.advCompanyList = [];
  }

  getRecommendation() {
    return (this.advertiserForm.get('recommendationArray') as FormArray).controls;
  }

  removeRecommendation(index: number) {
    (<FormArray>this.advertiserForm.get('recommendationArray')).removeAt(index);
  }

  addRecommendation() {
    let codeIndex = (<FormArray>this.advertiserForm.get('recommendationArray')).length;
    if (codeIndex < 5) {
      let fg = this.fb.group({
        recommendation_summary: this.fb.control(''),
        recommendation_desc: this.fb.control(''),
        recommendation_date: this.fb.control(''),
        recommendation_publish: this.fb.control('')
      });
      (<FormArray>this.advertiserForm.get('recommendationArray')).push(fg);
      this.userIndex = (<FormArray>this.advertiserForm.get('recommendationArray')).length - 1;
    }
  }

  selectYear() {
    let currentDate = new Date();
    if (currentDate.getMonth() + 1 > 3) {
      this.currentYear = currentDate.getFullYear();
    } else {
      this.currentYear = currentDate.getFullYear() - 1;
    }
    let earliestYear = this.currentYear - 5;
    while (this.currentYear >= earliestYear) {
      let dateOption = {
        "viewValue": this.currentYear + ' ' + '-' + ' ' + (this.currentYear + 1),
        "value": this.currentYear
      };
      this.years.push(dateOption);
      this.currentYear -= 1;
    }
  }

}