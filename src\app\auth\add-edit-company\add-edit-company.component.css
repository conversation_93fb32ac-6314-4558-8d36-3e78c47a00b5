::ng-deep .mat-form-field-flex > .mat-form-field-infix { padding: 0px 0px 0.4em 0px !important; }
:host:ng-deep .mat-form-field-appearance-outline .mat-form-field-label { margin-top:-15px; }
:host:ng-deep label.ng-star-inserted { transform: translateY(-0.59375em) scale(.75) !important; }
:host:ng-deep .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    transform: translateY(-1.1em) scale(.75);
    width: 133.33333%;
}
:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-wrapper{
    margin: 0px !important;
}
.toolbar {
    height: 84px;
    padding-left: 15px;
    background-color: #F8F9F9;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #2F3941;
}
.contact-info {
    background: #FFFFFF;
    border: 1px solid #CFD7DF;
    box-sizing: border-box;
    border-radius: 4px;
    padding: 24px;
    width: 558px;
    margin-bottom: 15px;
    margin-top: 6px;
}
.remove-box {
    background: #FFFFFF;
    border: 1px solid #ED2F45;
    box-sizing: border-box;
    border-radius: 4px;
    width: 60px;
    height: 33px;
    margin-top: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.remove-icon {
    border-radius: 2px;
    color: #ED2F45;
}
.fill-space {
  width: 60px;
  height: 33px;
  margin-top: 6px;
  margin-left: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.search-btn {
    color: solid rgba(47, 57, 65, 0.6);
    background: #F3F3F3;
    border: 1px solid rgba(47, 57, 65, 0.6);
}
.form::-webkit-scrollbar {
    display: none;
}
.input-field_phno {
    width: 558px;
    margin-top: 6px;
}
.lastdivdr {
    padding-top: 5%;
    width: 560px;
}
.toolbar-btns2 {
    float: right;
    padding-top: 2%;
}
.mat-dialog-actions {
    margin-right: 15px;
}
.cancel-btn {
    color: #284255;
    background-color: white;
    border-radius: 14px;
    margin-right: 10px;
}
.register-btn {
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: white;
    background-color: #0088CB;
    border-radius: 12px;
}
.remove-btn {
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: #ED2F45;
    border: 1px solid #ED2F45;
    border-radius: 12px;
    box-sizing: border-box;
}
.toolbar2 {
    padding-top: 5%;
    margin-left: -5px;
    background-color: white;
}
.contents::-webkit-scrollbar {
    display: none;
}
.contents {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.contents {
    height: 360px;
    width: 580px;
    overflow-y: scroll;
    overflow-x: hidden;
    margin-top: 10px;
}
