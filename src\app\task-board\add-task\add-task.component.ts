import { Component, ElementRef, HostListener, Inject, Input, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import * as moment from 'moment';
import { DatePipe } from '@angular/common';
import { MatTabGroup } from '@angular/material/tabs';
import { TasksService } from 'src/app/services/tasks.service';
import { NotificationService } from 'src/app/services/notification.service';
import { ConfirmationPopupComponent } from 'src/app/confirmation-popup/confirmation-popup.component';
import { colorObj } from 'src/app/shared/color-object';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter } from 'angular-calendar';
import { MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MY_FORMATS } from 'src/app/shared/calendarFormat';
import { UploadService } from 'src/app/services/upload.service';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { saveAs as importedSaveAs } from "file-saver";
import { ComplaintsService } from 'src/app/services/complaints.service';

@Component({
  selector: 'app-add-task',
  templateUrl: './add-task.component.html',
  styleUrls: ['./add-task.component.css'],
  providers: [
    { provide: MAT_MOMENT_DATE_ADAPTER_OPTIONS, useValue: { useUtc: true } },
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
  ]
})

export class AddTaskComponent implements OnInit {

  addform: FormGroup;
  @Input() min: any;
  tomorrow = new Date();
  case: string;
  value = 'Clear me';
  user_id: number;
  complaints = [];
  assigneeList = [];
  statusList = [];
  priorityList = [];
  taskTimeline = [];
  taskComments = [];
  taskDocuments = [];
  timeLineExist: boolean = true;
  timelineLoading: boolean = false;
  commentLoading: boolean = false;
  documentLoading: boolean = false;
  loading: boolean = false;
  delete_task = false;
  priority_var = "";
  status_var = "";
  profile = "";
  createTask: boolean = true;
  @ViewChild('attachments') attachment: any;
  form: any;
  files: any[] = [];
  fileName = [];
  dat = {};
  name: string;
  date;
  fileList: File[] = [];
  listOfFiles: any[] = [];
  shouldDisable: boolean;
  confirmationMsg: any = {};
  commentID;
  dueDate;
  isUploadProgress: boolean = false;
  docFileList: any[] = [];
  filesProgress: any[] = [];
  isError: boolean = false;
  public bucketUrl = `${environment.BUCKET_URL}`;
  imgURL: string;
  url: string;
  isMobile: boolean;
  innerWidth: number;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.innerWidth = window.innerWidth;
    if (this.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  @ViewChild('tabGroup', { static: true }) tabGroup: MatTabGroup;
  @ViewChild('taskid') editTaskName: ElementRef;
  @ViewChild('cmtid') editComment: ElementRef;

  constructor(private fb: FormBuilder,
    private cs: ComplaintsService,
    private taskTableService: TasksService,
    private notify: NotificationService,
    private dialogRef: MatDialogRef<AddTaskComponent>,
    private uploadService: UploadService,
    public dialog: MatDialog,
    private router: Router,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private datePipe: DatePipe) {
    this.addform = this.fb.group({
      TASK_NAME: ['', [Validators.required]],
      TASK_DESCRIPTION: ['', [Validators.required]],
      COMPLAINT_DESCRIPTION: ['', [Validators.required]],
      CASE_ID: ['', [Validators.required]],
      COMPLAINT_ID: ['', Validators.required],
      TASK_STATUS_ID: [],
      PRIORITY_ID: [],
      ASSIGNEE_USER_ID: [],
      ASSIGNED_TO: [],
      DUE_DATE: [],
      comment_box: [],
      comment_text: [],
    })
  }

  @Input() values: number = 100;
  @Input() diameter: number = 50;
  @Input() mode: string = "indeterminate";
  @Input() strokeWidth: number = 10;
  @Input() overlay: boolean = false;
  @Input() color: string = "primary";

  ngOnInit(): void {
    const userInfo = JSON.parse(window.localStorage.getItem('userInfo'));
    this.user_id = userInfo.userId;
    this.priorityList = JSON.parse(window.localStorage.getItem('priority'));
    if (this.priorityList[0].ID == 0) {
      this.priorityList[0].PRIORITY_NAME = "None";
    }
    this.statusList = JSON.parse(window.localStorage.getItem('taskStatus'));
    if (this.statusList[0].ID == 0) {
      this.statusList[0].TASK_STATUS_NAME = "None";
    }

    if (this.data != undefined) {
      if (this.data.name == "manage-update") {
        this.getComments();
        this.createTask = false;
        this.addform.controls['TASK_NAME'].setValue(this.data.row.TASK_NAME);
        this.addform.controls['COMPLAINT_ID'].setValue(this.data.row.COMPLAINT_ID);
        this.addform.controls['COMPLAINT_DESCRIPTION'].setValue(this.data.row.COMPLAINT_DESCRIPTION);
        this.addform.controls['CASE_ID'].setValue(this.data.row.CASE_ID);
        this.status_var = this.data.row.TASK_STATUS_NAME;
        this.addform.controls['TASK_STATUS_ID'].setValue(this.data.row.TASK_STATUS_ID);
        this.addform.controls['TASK_DESCRIPTION'].setValue(this.data.row.TASK_DESCRIPTION);
        this.addform.controls['ASSIGNEE_USER_ID'].setValue(this.data.row.ASSIGNEE_USER_ID);
        this.addform.controls['ASSIGNED_TO'].setValue(this.data.row.ASSIGNEE_USER_NAME);
        this.priority_var = this.data.row.PRIORITY_NAME;
        this.addform.controls['PRIORITY_ID'].setValue(this.data.row.PRIORITY_ID);
        let currentDate = new Date(this.data.row.DUE_DATE);
        this.addform.controls['DUE_DATE'].setValue(moment(currentDate).format('yyyy-MM-DD'));
        this.dueDate = moment(currentDate).format('yyyy-MM-DD');
        this.profile = userInfo.firstName[0] + userInfo.lastName[0];
        if (this.data.row.CREATED_BY == this.user_id) {
          this.delete_task = true;
        }
      }

      else if (this.data.name == "task-update") {
        this.getComments();
        this.createTask = false;
        this.addform.controls['TASK_NAME'].setValue(this.data.row.TASK_NAME);
        this.addform.controls['COMPLAINT_ID'].setValue(this.data.row.COMPLAINT_ID);
        this.addform.controls['COMPLAINT_DESCRIPTION'].setValue(this.data.row.COMPLAINT_DESCRIPTION);
        this.addform.controls['CASE_ID'].setValue(this.data.row.CASE_ID);
        this.status_var = this.data.row.TASK_STATUS_NAME;
        this.addform.controls['ASSIGNEE_USER_ID'].setValue(this.data.row.ASSIGNEE_USER_ID);
        this.addform.controls['ASSIGNED_TO'].setValue(this.data.row.ASSIGNEE_USER_NAME);
        this.addform.controls['TASK_STATUS_ID'].setValue(this.data.row.TASK_STATUS_ID);
        this.addform.controls['PRIORITY_ID'].setValue(this.data.row.PRIORITY_ID);
        this.addform.controls['TASK_DESCRIPTION'].setValue(this.data.row.TASK_DESCRIPTION);
        this.priority_var = this.data.row.PRIORITY_NAME;
        let currentDate = new Date(this.data.row.DUE_DATE);
        this.addform.controls['DUE_DATE'].setValue(moment(currentDate).format('yyyy-MM-DD'));
        this.dueDate = moment(currentDate).format('yyyy-MM-DD');
        this.profile = userInfo.firstName[0] + userInfo.lastName[0];
        if (this.data.row.CREATED_BY == this.user_id) {
          this.delete_task = true;
        }
      }

      else if (this.data.name == "manage-new") {
        this.createTask = true;
        this.addform.controls['COMPLAINT_DESCRIPTION'].setValue(this.data.comp_name);
        this.addform.controls['CASE_ID'].setValue(this.data.case_id);
        this.addform.controls['COMPLAINT_ID'].setValue(this.data.comp_id);
        this.status_var = 'New';
        this.addform.controls['TASK_STATUS_ID'].setValue(1);
        this.priority_var = 'High';
        this.addform.controls['PRIORITY_ID'].setValue(3);
        let currentDate = new Date().toLocaleString();
        this.addform.controls['DUE_DATE'].setValue(moment(currentDate).format('yyyy-MM-DD'));
      }

      else if (this.data.name == "task-new") {
        this.createTask = true;
        this.status_var = 'New';
        this.addform.controls['TASK_STATUS_ID'].setValue(1);
        this.priority_var = 'High';
        this.addform.controls['PRIORITY_ID'].setValue(3);
        let currentDate = new Date().toLocaleString();
        this.addform.controls['DUE_DATE'].setValue(moment(currentDate).format('yyyy-MM-DD'));
      }
    }
    this.shouldDisable = true;
    this.addform.get('COMPLAINT_DESCRIPTION')!.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0) {
          this.selectComplaintName(value);
        }
      }, err => {
      })

    this.addform.get('ASSIGNED_TO')!.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()).subscribe(value => {
        if (value && value.length > 0) {
          this.getAssigneeList(value);
        }
      }, err => {
      })

    if (window.innerWidth <= 1250) {
      this.isMobile = true;
      this.dialogRef.close();
    } else {
      this.isMobile = false;
    }
  }

  getAssigneeList(key) {
    this.taskTableService.getAssigneList(key).subscribe(res => {
      this.assigneeList = res.data;
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    })
  }

  onSelectionChange(event) {
    this.assigneeList.forEach(element => {
      if (event.option.value === element.ASSIGNEE_USER_NAME) {
        this.addform.controls['ASSIGNED_TO'].setValue(element.ASSIGNEE_USER_NAME);
        this.addform.controls['ASSIGNEE_USER_ID'].setValue(element.ID);
      }
    });
  }

  async onFileChange(event: any) {
    this.isError = false;
    if (!this.uploadService.validateFileExtension(event)) {
      // this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        "File format is not supported!",
        "top",
        "warning",
        0
      );
      return;
    }
    let sizeOfAllFiles = this.uploadService.getTotalFileSize(this.docFileList) + this.uploadService.getTotalFileSize(event.target.files);
    let totalFileSelected = this.docFileList.length + event.target.files.length;
    if (totalFileSelected < 11 && sizeOfAllFiles <= 36700160) {
      this.isUploadProgress = true;
      for (let i = 0; i <= event.target.files.length - 1; i++) {
        let selectedFile = event.target.files[i];
        this.dat = { "ID": "", "FILENAME": selectedFile['name'] }
        this.fileName.push(this.dat);
        let tempObjforGetsigned = {
          id: this.data['row']['COMPLAINT_ID'],
          taskid: this.data['row']['ID'],
          filename: selectedFile['name'],
          type: selectedFile['type']
        }
        this.filesProgress[this.fileName['length'] - 1] = { progress: 0 }
        await this.uploadService.getSignedUrlForTask(tempObjforGetsigned).then(async (res) => {
          if (res && res['data'] && res['data']['SIGNED_URL']) {
            let tempObjForUpload = {
              url: res['data']['SIGNED_URL'],
              file: selectedFile
            }
            try {
              await this.uploadSignedFile(tempObjForUpload, i);
              let saveBody = {
                ID: this.data['row']['COMPLAINT_ID'],
                TASK_ID: this.data['row']['ID'],
                KEY: res['data']['PATH'],
                SOURCE_NAME: selectedFile['name'],
                TYPE: selectedFile['type'],
                SIZE: selectedFile['size'],
                TAB: 'internal'
              }
              await this.saveDocumentToDB(saveBody)
            } catch (error) {
              this.handleError(error);
              return;
            }

          }
        })
          .catch((err) => {
            this.isUploadProgress = false;
            this.handleError(err);
          });
      }
      this.isUploadProgress = false;
      // this.attachment.nativeElement.value = '';
      if (!this.isError) {
        this.notify.showNotification(
          'Documents uploaded successfully',
          "top",
          (!!colorObj[200] ? colorObj[200] : "success"),
          200
        );
      }

    } else {
      // this.attachment.nativeElement.value = '';
      this.notify.showNotification(
        "Max size 35MB and Max 10 files allowed",
        "top",
        "warning",
        0
      );
    }
  }

  async saveDocumentToDB(tempObj) {
    await this.taskTableService.saveTaskDocToDB(tempObj)
      .then((data) => {
        this.getDocuments();
        return data;
      })
      .catch(err => {
        this.isUploadProgress = false;

      })
  }

  async uploadSignedFile(tempObj, i) {
    let progressInit = 0;
    await this.uploadService.uploadFilethroughSignedUrl(tempObj, progressInit => {
      this.filesProgress[this.fileName['length'] - 1] = { progress: progressInit }
    }).catch((err) => {
      this.isUploadProgress = false;
      this.handleError(err);
    });
  }

  handleError(err: any) {
    this.notify.showNotification(
      err.error.message,
      "top",
      (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
      err.error.status
    );
  }

  onFileChanged(pFileList: File[]) {
    let task_id = this.data.row.ID;
    let comp_id = this.data.row.COMPLAINT_ID;
    this.files = Object.keys(pFileList).map(key => pFileList[key]);
    if (this.files.length != 0) {
      this.taskTableService.attachTaskDocuments(this.files, task_id, comp_id).subscribe(res => {
        for (var i = 0; i < this.files.length; i++) {
          this.dat = { "ID": "", "FILENAME": this.files[i].name }
          this.fileName.push(this.dat);
        }
        this.notify.showNotification(
          res.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status] : "success"),
          res.status
        );
        // this.getDocuments();
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
          err.error.status
        );
      })
    }
  }

  removeSelectedFile(ID, ATTACHMENT_SOURCE, i) {
    this.confirmationMsg.title = 'Are you sure you want to delete the file ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: ID, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.taskTableService
          .deleteTaskDocuments(dialogResult.id, ATTACHMENT_SOURCE)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              this.fileName.splice(i, 1);
              // this.getDocuments();
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  download(doc, source) {
    this.url = this.bucketUrl + source;
    this.cs.downloadFile(this.url).subscribe(data => {
      importedSaveAs(data, doc)
    }, err => {
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    });
  }

  preview(source) {
    this.imgURL = this.bucketUrl + source;
    window.open(this.imgURL, 'window 1', '');
  }

  changeValueStatus(event) {
    for (let stat of this.statusList) {
      if (stat.ID == event.value.toString()) {
        this.status_var = stat.TASK_STATUS_NAME;
      }
    }
  }

  changeValuePriority(event) {
    for (let prio of this.priorityList) {
      if (prio.ID == event.value.toString()) {
        this.priority_var = prio.PRIORITY_NAME;
      }
    }
  }

  complaintSelected(event) {
    this.complaints.forEach(element => {
      if (event.option.value === element.CASE_ID) {
        this.addform.controls['COMPLAINT_ID'].setValue(element.ID);
        this.addform.controls['CASE_ID'].setValue(element.CASE_ID);
      }
    });
  }

  editbtn() {
    this.shouldDisable = false;
    setTimeout(() => {
      this.editTaskName.nativeElement.focus();
    }, 0)
  }

  editCmt(id) {
    this.commentID = id;
    setTimeout(() => {
      this.editComment.nativeElement.focus();
    }, 0)
  }

  cancel() {
    this.commentID = "";
    this.getComments();
  }

  sendMessage(data) {
    if (this.addform.value['comment_box'] == null) {
      this.notify.showNotification(
        "Please enter a comment",
        "top",
        "warning",
        0
      )
    }
    else {
      this.taskTableService.createComment(data, this.data.row.ID, this.data.row.COMPLAINT_ID, this.user_id).subscribe(res => {
        this.addform.controls['comment_box'].reset();
        this.getComments();
      }, err => {
        this.notify.showNotification(
          err.error.message,
          'top',
          !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
          err.error.status
        );
      });
    }
  }

  updateCmt(ID, comment) {
    this.taskTableService.updateComment(ID, comment).subscribe(res => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      this.commentID = "";
      this.getComments();
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "warning"),
        err.error.status
      )
    })
  }

  deleteCmt(ID) {
    this.confirmationMsg.title = 'Are you sure you want to delete the comment ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: ID, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.taskTableService
          .deleteComment(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              this.getComments();
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  selectComplaintName(value) {
    this.taskTableService.getComplaintsList(value).subscribe((complaintList: any) => {
      this.complaints = complaintList.data;
      for (let complaint of this.complaints) {
        if (complaint.COMPLAINT_DESCRIPTION != null && complaint.COMPLAINT_DESCRIPTION != '') {
          complaint.COMPLAINT_DESCRIPTION = (complaint.COMPLAINT_DESCRIPTION).replace(/<[^>]+>/g, '');
        }
      }
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      );
    });
  }

  saveTask(values) {
    delete values['comment_box'];
    delete values['comment_text'];
    values['CREATED_BY'] = this.user_id;
    if (values['ASSIGNED_TO'] == null) {
      values['ASSIGNEE_USER_ID'] = null;
    }
    if (this.addform.controls['DUE_DATE'].value) {
      let d = new Date(this.addform.controls['DUE_DATE'].value);
      values['DUE_DATE'] = new Date(d.getFullYear(), d.getMonth(), d.getDate(), d.getHours(), d.getMinutes() - d.getTimezoneOffset()).toISOString();
    }
    this.taskTableService.createTask(values).subscribe(res => {
      this.notify.showNotification(
        res.message,
        "top",
        (!!colorObj[res.status] ? colorObj[res.status] : "success"),
        res.status
      )
      this.dialogRef.close('refresh');
    }, err => {
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "warning"),
        err.error.status
      )
    })
  }

  updateTask(values) {
    values['ID'] = this.data.row.ID;
    delete values['COMPLAINT_ID'];
    delete values['COMPLAINT_DESCRIPTION'];
    delete values['CASE_ID'];
    delete values['ASSIGNED_TO'];
    delete values['comment_box'];
    delete values['comment_text'];
    if (this.dueDate == this.addform.controls['DUE_DATE'].value) {
      delete values['DUE_DATE'];
    }
    if (this.data.row.ASSIGNEE_USER_ID == this.addform.controls['ASSIGNEE_USER_ID'].value) {
      delete values['ASSIGNEE_USER_ID'];
    }
    if (this.data.row.PRIORITY_ID == this.addform.controls['PRIORITY_ID'].value) {
      delete values['PRIORITY_ID'];
    }
    if (this.data.row.TASK_STATUS_ID == this.addform.controls['TASK_STATUS_ID'].value) {
      delete values['TASK_STATUS_ID'];
    }
    if (this.data.row.TASK_DESCRIPTION == this.addform.controls['TASK_DESCRIPTION'].value) {
      delete values['TASK_DESCRIPTION'];
    }
    if (this.data.row.TASK_NAME == this.addform.controls['TASK_NAME'].value) {
      delete values['TASK_NAME'];
    }
    if (!!values['DUE_DATE']) {
      let d = new Date(this.addform.controls['DUE_DATE'].value);
      values['DUE_DATE'] = new Date(d.getFullYear(), d.getMonth(), d.getDate(), d.getHours(), d.getMinutes() - d.getTimezoneOffset()).toISOString();
    }
    if (this.data.row.TASK_NAME != this.addform.controls['TASK_NAME'].value || this.data.row.TASK_DESCRIPTION != this.addform.controls['TASK_DESCRIPTION'].value || this.data.row.TASK_STATUS_ID != this.addform.controls['TASK_STATUS_ID'].value || this.data.row.PRIORITY_ID != this.addform.controls['PRIORITY_ID'].value || this.data.row.ASSIGNEE_USER_ID != this.addform.controls['ASSIGNEE_USER_ID'].value || this.dueDate != this.addform.controls['DUE_DATE'].value) {
      this.taskTableService.updateTask(values).subscribe(res => {
        this.notify.showNotification(
          res.message,
          "top",
          (!!colorObj[res.status] ? colorObj[res.status] : "success"),
          res.status
        )
        this.dialogRef.close('refresh');
      }, err => {
        this.notify.showNotification(
          err.error.message,
          "top",
          (!!colorObj[err.error.status] ? colorObj[err.error.status] : "warning"),
          err.error.status
        )
      })
    } else {
      this.notify.showNotification(
        "No changes to Update",
        "top",
        "warning",
        0,
      )
    }
  }

  onTabChanged(event) {
    if (event.index == 0) {
      this.commentLoading = true;
      this.getComments();
    }
    else if (event.index == 1) {
      this.timelineLoading = true;
      this.timeLineExist = true;
      this.getTimeline();
    }
    else if (event.index == 2) {
      this.documentLoading = true;
      this.getDocuments();
    }
  }

  getTimeline() {
    let task_id = this.data.row.ID;
    this.taskTableService.getTaskTimeline(task_id).subscribe((timeline: any) => {
      this.timelineLoading = false;
      this.timeLineExist = true;
      this.taskTimeline = timeline.data;
      for (let item of this.taskTimeline) {
        item.date = this.datePipe.transform(item.date, 'EEE, d MMM y - h:mm a');
        for (let val of item.updates) {
          if (val.label == 'Task Created On' || val.label == 'Due date changed to') {
            val.value = this.datePipe.transform(val.value, 'd MMMM, y, EEEE, h:mm a');
          }
        }
      }
    }, err => {
      this.timelineLoading = false;
      this.timeLineExist = false;
      this.notify.showNotification(
        err.error.message,
        "top",
        (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
        err.error.status
      )
    });
  }

  getComments() {
    let task_id = this.data.row.ID;
    this.taskTableService.getTaskComments(task_id).subscribe((comment: any) => {
      this.commentLoading = false;
      if (comment.data.length > 0) {
        this.taskComments = comment.data;
        for (let item of this.taskComments) {
          item.UPDATED_DATE = this.datePipe.transform(item.UPDATED_DATE, 'MMMM d, y, h:mm a');
        }
      }
      else {
        this.taskComments = [];
      }
    }, err => {
      this.commentLoading = false;
      this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    });
  }

  deleteTask() {
    this.confirmationMsg.title = 'Are you sure you want to delete the task ?';
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, {
      data: { id: this.data.row.ID, title: this.confirmationMsg.title },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult && dialogResult.state) {
        this.taskTableService
          .deleteTask(dialogResult.id)
          .subscribe(res => {
            if (res) {
              this.notify.showNotification(
                res.message,
                "top",
                (!!colorObj[res.status] ? colorObj[res.status] : "success"),
                res.status
              )
              this.dialogRef.close('delete');
            }
          }, err => {
            this.notify.showNotification(
              err.error.message,
              "top",
              (!!colorObj[err.error.status] ? colorObj[err.error.status] : "error"),
              err.error.status
            )
          });
      }
    });
  }

  getDocuments() {
    this.fileName = [];
    let task_id = this.data.row.ID;
    this.taskTableService.getTaskDocuments(task_id).subscribe((document: any) => {
      this.documentLoading = false;
      this.taskDocuments = document.data;
      if (document.data.length > 0) {
        let file = [];
        for (let item of this.taskDocuments) {
          file.push(item.ATTACHMENT_SOURCE.split("/"));
          for (let i of file) {
            let len = i.length;
            this.dat = { "ID": item.ID, "FILENAME": i[len - 1].slice(14), "UPLOADED_BY": item.UPLOADED_BY, "ATTACHMENT_SOURCE": item.ATTACHMENT_SOURCE, "DATE": item.CREATED_DATE }
          }
          this.fileName.push(this.dat);
        }
      }
    }, err => {
      this.documentLoading = false; this.notify.showNotification(
        err.error.message,
        'top',
        !!colorObj[err.error.status] ? colorObj[err.error.status] : 'error',
        err.error.status
      );
    });
  }

}