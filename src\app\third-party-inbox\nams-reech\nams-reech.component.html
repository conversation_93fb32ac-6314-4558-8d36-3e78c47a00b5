<div class="common-toolbar-mobile" style="width: 100%; height: auto;" *ngIf="isMobile">
    <app-mobile-header></app-mobile-header>
</div>
<span class="dashboard-admin-heading" *ngIf="isMobile">
    <p style="text-align: center;margin-top: 20px;margin-bottom: 3px;"> Welcome {{userName}}!</p>
</span>
<div class="dasboard-subheading" *ngIf="isMobile">
    <p style="text-align: center;">Dear ASCI Officers, please move to a larger device for seamlessly accessing all
        functionalities of the system</p>
</div>
<div class="dashboard-container" *ngIf="isMobile">
</div>

<app-icons *ngIf="!isMobile"></app-icons>
<div class="common-toolbar" fxLayout="row" style="width: 100%; height: 50px;" *ngIf="!isMobile">
    <div class="heading-container">
        <app-heading [pagename]="pagename"></app-heading>
    </div>
    <div class="options-container">
        <app-toolbar-options></app-toolbar-options>
    </div>
</div>

<app-mat-spinner-overlay overlay="true" *ngIf="loading && !isMobile">
</app-mat-spinner-overlay>

<mat-tab-group style="padding-left: 50px; background-color: rgb(245, 245, 245);" class="admin-tabs" *ngIf="!isMobile"
  (selectedTabChange)="onTabChanged($event)">
    <mat-tab label="Instagram" class="asci-member">
        <mat-toolbar class="toolbar2" fxLayout="row" *ngIf="!isMobile">
            <div fxFlex="0%"></div>
            <!-- <div class="header-search">
                <button mat-button class="header-button" style="font-style: normal; font-weight: normal; font-size: 14px;">
                    <mat-select placeholder="{{selectedNetwork}}" [formControl]="NETWORK" (selectionChange)="getNetwork(value)">
                        <mat-option *ngFor="let network of networkType;" [value]="network.ID">
                            {{network.NETWORK_NAME}}
                        </mat-option>
                    </mat-select>
                </button>
            </div>
            <mat-divider [vertical]="true" class="vertical-divider"></mat-divider> -->
            <div class="header-search">
                <input type="text" name="search" placeholder="Influencer Name" [formControl]="INFLUENCER" autocomplete="off"
                style="font-style: normal;
                font-weight: normal;
                font-size: 14px; color: #2F3941;" (keyup)="getInfluencer(INFLUENCER.value)">
            </div>
            <mat-divider [vertical]="true" class="vertical-divider"></mat-divider>
            <div class="header-search">
                <input type="text" name="search" placeholder="Uploaded By" [formControl]="UPLOADED_BY" autocomplete="off"
                style="font-style: normal;
                font-weight: normal;
                font-size: 14px; color: #2F3941;" (keyup)="getUploadedBy(UPLOADED_BY.value)">
            </div>
            <mat-divider [vertical]="true" class="vertical-divider"></mat-divider>
            <div class="header-search">
                <button mat-flat-button class="clear-button" (click)="clearFilter()">
                  <span>Clear Filter</span>
                </button>
              </div>
            <div fxFlex="75%"></div>
            <div fxLayout="row">
                <div>
                    <button mat-button class="send-btn" style="width: auto; margin-right: 10px;" (click)="upload()">
                      <img src="../../assets/images/upload.png" style="position: relative;bottom:1px;width: 10px;"> Upload file
                    </button>
                </div>
                <div>
                    <button mat-button [matMenuTriggerFor]="menu" class="theme-blue-button-admin">
                        <span class="bolder">Bulk action</span> 
                        <mat-icon>arrow_drop_down</mat-icon>
                    </button>
                    <mat-menu #menu="matMenu" class="admin-menu">
                        <div class="admin-option-container">
                            <button mat-menu-item class="option-btn" (click)="bulkShortlist()" [disabled]="disableAction">
                                <span class="option-text bolder">Shortlist</span>
                            </button>
                            <button mat-menu-item class="option-btn" (click)="bulkReject()" [disabled]="disableAction">
                                <span class="option-text bolder">Reject</span>
                            </button>
                            <mat-divider class="option-divider"></mat-divider>
                            <button mat-menu-item class="option-btn" (click)="bulkDelete()" [disabled]="disableDelete">
                                <span class="option-text bolder">Delete</span>
                            </button>
                        </div>
                    </mat-menu>
                </div>
            </div>
        </mat-toolbar>
        <mat-card class="card" [hidden]="loading || isMobile">
            <div class="table-scroll">
                <table mat-table class="admin-table" [dataSource]="dataSource" matSort #table>
                    <div>
                        <ng-container matColumnDef="check">
                            <th mat-header-cell *matHeaderCellDef style="width: 2%; padding-right: 10px;">
                                <mat-checkbox class="select-chkb" [(ngModel)]="select_all"
                                    (ngModelChange)="onSelectAll($event)">
                                </mat-checkbox>
                            </th>
                            <td mat-cell *matCellDef="let element" style="width: 2%; padding-right: 10px; padding-bottom: 5px;">
                                <mat-checkbox class="chkb-list" [(ngModel)]="element.isselected"
                                    (change)="selectInstaComplaint($event.checked, element.ID, element.status)"></mat-checkbox>
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="network">
                            <th mat-header-cell *matHeaderCellDef style="width: 6%; padding-right: 5px;"><b>Network</b></th>
                            <td mat-cell *matCellDef="let element" style="width: 6%; cursor: pointer; padding-right: 5px; padding-top: 10px;"
                                (click)="instaComplaintDetails(element.ID, element.status)">
                                <p style="cursor: pointer;">{{element.NETWORK}}</p>
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="publication_image_url">
                            <th mat-header-cell *matHeaderCellDef style="width: 13%; padding-right: 10px;"><b>Publication Image URL</b></th>
                            <td mat-cell *matCellDef="let element" style="width: 13%; padding-right: 10px;" [matTooltip]="element.PUBLICATION_IMAGE_URL"
                            (click)="openUrl(element, element.PUBLICATION_IMAGE_URL)">
                            <p class="ellipsis1" style="cursor: pointer; color: #0088CB;">{{element.PUBLICATION_IMAGE_URL}}</p>
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="publication_url">
                            <th mat-header-cell *matHeaderCellDef style="width: 10%; padding-right: 10px;"><b>Publication URL</b></th>
                            <td mat-cell *matCellDef="let element" style="width: 10%; padding-right: 10px;" [matTooltip]="element.PUBLICATION_URL"
                            (click)="openUrl(element, element.PUBLICATION_URL)">
                            <p class="ellipsis1" style="cursor: pointer; color: #0088CB;">{{element.PUBLICATION_URL}}</p>
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="publication_content">
                            <th mat-header-cell *matHeaderCellDef style="width: 12%; padding-right: 10px;"><b>Publication Content</b></th>
                            <td mat-cell *matCellDef="let element" style="width: 12%; padding-right: 10px;" [matTooltip]="element.PUBLICATION_CONTENT">
                            <p class="ellipsis1">{{element.PUBLICATION_CONTENT}}</p>
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="INFLUENCER_NAME">
                            <th mat-header-cell *matHeaderCellDef style="width: 5%"><b>Influencer Name</b>
                                <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!influencer_asc && !influencer_desc" (click)="sortInstaComplaints('INFLUENCER_NAME', 'ASC')">
                                <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="influencer_asc" (click)="sortInstaComplaints('INFLUENCER_NAME', 'DESC')">
                                <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="influencer_desc" (click)="sortInstaComplaints('', '')">
                            </th>
                            <td mat-cell *matCellDef="let element" style="width: 5%" [matTooltip]="element.INFLUENCER_NAME">
                            <p class="ellipsis1">{{element.INFLUENCER_NAME}}</p>
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="publication_date">
                            <th mat-header-cell *matHeaderCellDef style="width: 5%;"><b>Publication Date</b></th>
                            <td mat-cell *matCellDef="let element" style="width: 5%;">
                                <p class="ellipsis1">{{element.PUBLICATION_DATE | date:'dd-MM-yyyy HH:mm:ss':'GMT'}}</p>
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="uploaded_by">
                            <th mat-header-cell *matHeaderCellDef style="width: 5%; padding-right: 20px;white-space: nowrap;"><b>Uploaded By</b></th>
                            <td mat-cell *matCellDef="let element" style="width: 5%; padding-right: 10px; text-align: center;">{{element.UPLOADED_BY}}
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="engagements">
                            <th mat-header-cell *matHeaderCellDef style="width: 5%; padding-right: 10px;"><b>Engagements</b></th>
                            <td mat-cell *matCellDef="let element" style="width: 5%; padding-right: 10px; text-align: center;">{{element.ENGAGEMENTS}}
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="action">
                            <th mat-header-cell *matHeaderCellDef style="width: 7%; padding-left: 1%;"><b>Action</b></th>
                            <td mat-cell *matCellDef="let element" style="width: 10%;">
                                <div fxLayout="row" fxLayoutGap="15px">
                                    <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Confirmed'"
                                        (click)="instaComplaintDetails(element.ID, element.status)"
                                        style="position: relative; top: 11px; display: flex; align-items: center; cursor: pointer; left: 11px;">
                                        <img src="../../../assets/images/View.png" width="20px">
                                        <p style="
                                            font-style: normal;
                                            font-weight: normal;
                                            font-size: 12px;color: rgba(0, 136, 203, 0.6);">View</p>
                                    </div>
                                    <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Confirmed'"
                                        (click)="deleteComplaint(element.ID, 'Instagram')"
                                        style="position: relative; top: 10px; left: 16px; display: flex; align-items: center; cursor: pointer;">
                                        <img src="../../../assets/images/delete-red.png" width="15px">
                                        <p style="
                                            font-style: normal;
                                            font-weight: normal;
                                            font-size: 12px;color: rgba(237, 47, 69, 0.6);">Delete</p>
                                    </div>
                                    <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                                        (click)="shortList(element.ID)"
                                        style="position: relative; top: 10px; left: 0px; display: flex; align-items: center; cursor: pointer;">
                                        <img src="../../../assets/images/Admin-Approve.png" width="15px">
                                        <p style="
                                            font-style: normal;
                                            font-weight: normal;
                                            font-size: 12px;color: rgba(4, 165, 133, 0.6);">Shortlist</p>
                                    </div>
                                    <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                                        (click)="rejectComplaint(element.ID, 'Instagram')"
                                        style="position: relative; top: 10px; left: 0px; display: flex; align-items: center; cursor: pointer;">
                                        <img src="../../../assets/images/Reject-orange.png" width="15px">
                                        <p style="
                                            font-style: normal;
                                            font-weight: normal;
                                            font-size: 12px;color: rgba(248, 158, 27, 0.6);">Reject</p>
                                    </div>
                                </div>
                            </td>
                        </ng-container>

                        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
                        <tr mat-row *matRowDef="let row; columns: displayedColumns;">
                        </tr>
                    </div>
                </table>
            </div>
        </mat-card>
        <mat-paginator (page)="changePage($event)" [pageSize]="10" [pageIndex]="0" *ngIf="!zeroComplaints && !isMobile">
        </mat-paginator>
        <span class="label" *ngIf="!zeroComplaints && !isMobile"> {{rangeLabel}} of {{totalCount}}</span>
        <div class="text-message" *ngIf="zeroComplaints && !isMobile">
            <span>
                <br>
                No Complaints available ....
                <br>
            </span>
        </div>
    </mat-tab>
    <mat-tab label="Facebook" class="asci-member">
        <mat-toolbar class="toolbar2" fxLayout="row" *ngIf="!isMobile">
            <div fxFlex="0%"></div>
            <div class="header-search">
                <input type="text" name="search" placeholder="Brand Name" [formControl]="BRAND" autocomplete="off"
                style="font-style: normal;
                font-weight: normal;
                font-size: 14px; color: #2F3941;" (keyup)="getBrandName(BRAND.value)">
            </div>
            <mat-divider [vertical]="true" class="vertical-divider"></mat-divider>
            <div class="header-search">
                <input type="text" name="search" placeholder="Uploaded By" [formControl]="UPLOADED_BY" autocomplete="off"
                style="font-style: normal;
                font-weight: normal;
                font-size: 14px; color: #2F3941;" (keyup)="getUploadedBy(UPLOADED_BY.value)">
            </div>
            <mat-divider [vertical]="true" class="vertical-divider"></mat-divider>
            <div class="header-search">
                <button mat-flat-button class="clear-button" (click)="clearFilter()">
                  <span>Clear Filter</span>
                </button>
              </div>
            <div fxFlex="75%"></div>
            <div fxLayout="row">
                <div>
                    <button mat-button class="send-btn" style="width: auto; margin-right: 10px;" (click)="upload()">
                      <img src="../../assets/images/upload.png" style="position: relative;bottom:1px;width: 10px;"> Upload file
                    </button>
                </div>
                <div>
                    <button mat-button [matMenuTriggerFor]="menu" class="theme-blue-button-admin">
                        <span class="bolder">Bulk action</span> 
                        <mat-icon>arrow_drop_down</mat-icon>
                    </button>
                    <mat-menu #menu="matMenu" class="admin-menu">
                        <div class="admin-option-container">
                            <button mat-menu-item class="option-btn" (click)="bulkShortlist()" [disabled]="disableAction">
                                <span class="option-text bolder">Shortlist</span>
                            </button>
                            <button mat-menu-item class="option-btn" (click)="bulkReject()" [disabled]="disableAction">
                                <span class="option-text bolder">Reject</span>
                            </button>
                            <mat-divider class="option-divider"></mat-divider>
                            <button mat-menu-item class="option-btn" (click)="bulkDelete()" [disabled]="disableDelete">
                                <span class="option-text bolder">Delete</span>
                            </button>
                        </div>
                    </mat-menu>
                </div>
            </div>
        </mat-toolbar>
        <mat-card class="card" [hidden]="loading || isMobile">
            <div class="table-scroll">
                <table mat-table class="admin-table" [dataSource]="dataSource1" matSort #table>
                    <div>
                        <ng-container matColumnDef="check">
                            <th mat-header-cell *matHeaderCellDef style="width: 2%; padding-right: 10px;">
                                <mat-checkbox class="select-chkb" [(ngModel)]="select_all"
                                    (ngModelChange)="onSelectAll($event)">
                                </mat-checkbox>
                            </th>
                            <td mat-cell *matCellDef="let element" style="width: 2%; padding-right: 10px; padding-bottom: 5px;">
                                <mat-checkbox class="chkb-list" [(ngModel)]="element.isselected"
                                    (change)="selectFbComplaint($event.checked, element.ID, element.status)"></mat-checkbox>
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="network">
                            <th mat-header-cell *matHeaderCellDef style="width: 6%; padding-right: 5px;"><b>Network</b></th>
                            <td mat-cell *matCellDef="let element" style="width: 6%; cursor: pointer; padding-right: 5px; padding-top: 10px;"
                                (click)="fbComplaintDetails(element.ID, element.status)">
                                <p style="cursor: pointer;">{{element.NETWORK}}</p>
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="publication_image_url">
                            <th mat-header-cell *matHeaderCellDef style="width: 13%; padding-right: 10px;"><b>Publication Image URL</b></th>
                            <td mat-cell *matCellDef="let element" style="width: 13%; padding-right: 10px;" [matTooltip]="element.PUBLICATION_IMAGE_URL"
                            (click)="openUrl(element, element.PUBLICATION_IMAGE_URL)">
                            <p class="ellipsis1" style="cursor: pointer; color: #0088CB;">{{element.PUBLICATION_IMAGE_URL}}</p>
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="publication_url">
                            <th mat-header-cell *matHeaderCellDef style="width: 10%; padding-right: 10px;"><b>Publication URL</b></th>
                            <td mat-cell *matCellDef="let element" style="width: 10%; padding-right: 10px;" [matTooltip]="element.PUBLICATION_URL"
                            (click)="openUrl(element, element.PUBLICATION_URL)">
                            <p class="ellipsis1" style="cursor: pointer; color: #0088CB;">{{element.PUBLICATION_URL}}</p>
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="publication_content">
                            <th mat-header-cell *matHeaderCellDef style="width: 12%; padding-right: 10px;"><b>Publication Content</b></th>
                            <td mat-cell *matCellDef="let element" style="width: 12%; padding-right: 10px;" [matTooltip]="element.PUBLICATION_CONTENT">
                            <p class="ellipsis1">{{element.PUBLICATION_CONTENT}}</p>
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="BRAND_NAME">
                            <th mat-header-cell *matHeaderCellDef style="width: 5%"><b>Brand Name</b>
                                <img src="../../../assets/images/sort_icon.png" style="margin-left: 5px; cursor: pointer;" *ngIf="!brand_asc && !brand_desc" (click)="sortFbComplaints('BRAND_NAME', 'ASC')">
                                <img src="../../../assets/images/sort-up.png" style="margin-left: 5px; cursor: pointer;" *ngIf="brand_asc" (click)="sortFbComplaints('BRAND_NAME', 'DESC')">
                                <img src="../../../assets/images/sort-down.png" style="margin-left: 5px; cursor: pointer;" *ngIf="brand_desc" (click)="sortFbComplaints('', '')">
                            </th>
                            <td mat-cell *matCellDef="let element" style="width: 5%" [matTooltip]="element.BRAND">
                            <p class="ellipsis1">{{element.BRAND}}</p>
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="publication_date">
                            <th mat-header-cell *matHeaderCellDef style="width: 5%;"><b>Publication Date</b></th>
                            <td mat-cell *matCellDef="let element" style="width: 5%;">
                                <p class="ellipsis1">{{element.PUBLICATION_DATE | date:'dd-MM-yyyy HH:mm:ss':'GMT'}}</p>
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="engagements">
                            <th mat-header-cell *matHeaderCellDef style="width: 5%; padding-right: 10px;"><b>Engagements</b></th>
                            <td mat-cell *matCellDef="let element" style="width: 5%; padding-right: 10px; text-align: center;">{{element.ENGAGEMENTS}}
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="uploaded_by">
                            <th mat-header-cell *matHeaderCellDef style="width: 5%; padding-right: 20px;white-space: nowrap;"><b>Uploaded By</b></th>
                            <td mat-cell *matCellDef="let element" style="width: 5%; padding-right: 10px; text-align: center;">{{element.UPLOADED_BY}}
                            </td>
                        </ng-container>

                        <ng-container matColumnDef="action">
                            <th mat-header-cell *matHeaderCellDef style="width: 7%; padding-left: 1%;"><b>Action</b></th>
                            <td mat-cell *matCellDef="let element" style="width: 10%;">
                                <div fxLayout="row" fxLayoutGap="15px">
                                    <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Confirmed'"
                                        (click)="fbComplaintDetails(element.ID, element.status)"
                                        style="position: relative; top: 11px; display: flex; align-items: center; cursor: pointer; left: 11px;">
                                        <img src="../../../assets/images/View.png" width="20px">
                                        <p style="
                                            font-style: normal;
                                            font-weight: normal;
                                            font-size: 12px;color: rgba(0, 136, 203, 0.6);">View</p>
                                    </div>
                                    <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Confirmed'"
                                        (click)="deleteComplaint(element.ID, 'Facebook')"
                                        style="position: relative; top: 10px; left: 16px; display: flex; align-items: center; cursor: pointer;">
                                        <img src="../../../assets/images/delete-red.png" width="15px">
                                        <p style="
                                            font-style: normal;
                                            font-weight: normal;
                                            font-size: 12px;color: rgba(237, 47, 69, 0.6);">Delete</p>
                                    </div>
                                    <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                                        (click)="shortList(element.ID)"
                                        style="position: relative; top: 10px; left: 0px; display: flex; align-items: center; cursor: pointer;">
                                        <img src="../../../assets/images/Admin-Approve.png" width="15px">
                                        <p style="
                                            font-style: normal;
                                            font-weight: normal;
                                            font-size: 12px;color: rgba(4, 165, 133, 0.6);">Shortlist</p>
                                    </div>
                                    <div fxLayout="column" fxLayoutGap="3px" *ngIf="element.status == 'Unconfirmed'"
                                        (click)="rejectComplaint(element.ID, 'Facebook')"
                                        style="position: relative; top: 10px; left: 0px; display: flex; align-items: center; cursor: pointer;">
                                        <img src="../../../assets/images/Reject-orange.png" width="15px">
                                        <p style="
                                            font-style: normal;
                                            font-weight: normal;
                                            font-size: 12px;color: rgba(248, 158, 27, 0.6);">Reject</p>
                                    </div>
                                </div>
                            </td>
                        </ng-container>

                        <tr mat-header-row *matHeaderRowDef="displayedColumns1; sticky: true"></tr>
                        <tr mat-row *matRowDef="let row; columns: displayedColumns1;">
                        </tr>
                    </div>
                </table>
            </div>
        </mat-card>
        <mat-paginator (page)="changePage($event)" [pageSize]="10" [pageIndex]="0" *ngIf="!zeroComplaints && !isMobile">
        </mat-paginator>
        <span class="label" *ngIf="!zeroComplaints && !isMobile"> {{rangeLabel}} of {{totalCount}}</span>
        <div class="text-message" *ngIf="zeroComplaints && !isMobile">
            <span>
                <br>
                No Complaints available ....
                <br>
            </span>
        </div>
    </mat-tab>
</mat-tab-group>